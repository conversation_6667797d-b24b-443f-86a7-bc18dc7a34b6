{"ast": null, "code": "import { millisecondsToSeconds, secondsToMilliseconds, clamp } from 'motion-utils';\nimport { generateLinearEasing } from '../../waapi/utils/linear.mjs';\nimport { calcGeneratorDuration, maxGeneratorDuration } from '../utils/calc-duration.mjs';\nimport { createGeneratorEasing } from '../utils/create-generator-easing.mjs';\nimport { calcGeneratorVelocity } from '../utils/velocity.mjs';\nimport { springDefaults } from './defaults.mjs';\nimport { findSpring, calcAngularFreq } from './find.mjs';\nconst durationKeys = [\"duration\", \"bounce\"];\nconst physicsKeys = [\"stiffness\", \"damping\", \"mass\"];\nfunction isSpringType(options, keys) {\n  return keys.some(key => options[key] !== undefined);\n}\nfunction getSpringOptions(options) {\n  let springOptions = {\n    velocity: springDefaults.velocity,\n    stiffness: springDefaults.stiffness,\n    damping: springDefaults.damping,\n    mass: springDefaults.mass,\n    isResolvedFromDuration: false,\n    ...options\n  };\n  // stiffness/damping/mass overrides duration/bounce\n  if (!isSpringType(options, physicsKeys) && isSpringType(options, durationKeys)) {\n    if (options.visualDuration) {\n      const visualDuration = options.visualDuration;\n      const root = 2 * Math.PI / (visualDuration * 1.2);\n      const stiffness = root * root;\n      const damping = 2 * clamp(0.05, 1, 1 - (options.bounce || 0)) * Math.sqrt(stiffness);\n      springOptions = {\n        ...springOptions,\n        mass: springDefaults.mass,\n        stiffness,\n        damping\n      };\n    } else {\n      const derived = findSpring(options);\n      springOptions = {\n        ...springOptions,\n        ...derived,\n        mass: springDefaults.mass\n      };\n      springOptions.isResolvedFromDuration = true;\n    }\n  }\n  return springOptions;\n}\nfunction spring() {\n  let optionsOrVisualDuration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : springDefaults.visualDuration;\n  let bounce = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : springDefaults.bounce;\n  const options = typeof optionsOrVisualDuration !== \"object\" ? {\n    visualDuration: optionsOrVisualDuration,\n    keyframes: [0, 1],\n    bounce\n  } : optionsOrVisualDuration;\n  let {\n    restSpeed,\n    restDelta\n  } = options;\n  const origin = options.keyframes[0];\n  const target = options.keyframes[options.keyframes.length - 1];\n  /**\n   * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n   * to reduce GC during animation.\n   */\n  const state = {\n    done: false,\n    value: origin\n  };\n  const {\n    stiffness,\n    damping,\n    mass,\n    duration,\n    velocity,\n    isResolvedFromDuration\n  } = getSpringOptions({\n    ...options,\n    velocity: -millisecondsToSeconds(options.velocity || 0)\n  });\n  const initialVelocity = velocity || 0.0;\n  const dampingRatio = damping / (2 * Math.sqrt(stiffness * mass));\n  const initialDelta = target - origin;\n  const undampedAngularFreq = millisecondsToSeconds(Math.sqrt(stiffness / mass));\n  /**\n   * If we're working on a granular scale, use smaller defaults for determining\n   * when the spring is finished.\n   *\n   * These defaults have been selected emprically based on what strikes a good\n   * ratio between feeling good and finishing as soon as changes are imperceptible.\n   */\n  const isGranularScale = Math.abs(initialDelta) < 5;\n  restSpeed || (restSpeed = isGranularScale ? springDefaults.restSpeed.granular : springDefaults.restSpeed.default);\n  restDelta || (restDelta = isGranularScale ? springDefaults.restDelta.granular : springDefaults.restDelta.default);\n  let resolveSpring;\n  if (dampingRatio < 1) {\n    const angularFreq = calcAngularFreq(undampedAngularFreq, dampingRatio);\n    // Underdamped spring\n    resolveSpring = t => {\n      const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n      return target - envelope * ((initialVelocity + dampingRatio * undampedAngularFreq * initialDelta) / angularFreq * Math.sin(angularFreq * t) + initialDelta * Math.cos(angularFreq * t));\n    };\n  } else if (dampingRatio === 1) {\n    // Critically damped spring\n    resolveSpring = t => target - Math.exp(-undampedAngularFreq * t) * (initialDelta + (initialVelocity + undampedAngularFreq * initialDelta) * t);\n  } else {\n    // Overdamped spring\n    const dampedAngularFreq = undampedAngularFreq * Math.sqrt(dampingRatio * dampingRatio - 1);\n    resolveSpring = t => {\n      const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n      // When performing sinh or cosh values can hit Infinity so we cap them here\n      const freqForT = Math.min(dampedAngularFreq * t, 300);\n      return target - envelope * ((initialVelocity + dampingRatio * undampedAngularFreq * initialDelta) * Math.sinh(freqForT) + dampedAngularFreq * initialDelta * Math.cosh(freqForT)) / dampedAngularFreq;\n    };\n  }\n  const generator = {\n    calculatedDuration: isResolvedFromDuration ? duration || null : null,\n    next: t => {\n      const current = resolveSpring(t);\n      if (!isResolvedFromDuration) {\n        let currentVelocity = t === 0 ? initialVelocity : 0.0;\n        /**\n         * We only need to calculate velocity for under-damped springs\n         * as over- and critically-damped springs can't overshoot, so\n         * checking only for displacement is enough.\n         */\n        if (dampingRatio < 1) {\n          currentVelocity = t === 0 ? secondsToMilliseconds(initialVelocity) : calcGeneratorVelocity(resolveSpring, t, current);\n        }\n        const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n        const isBelowDisplacementThreshold = Math.abs(target - current) <= restDelta;\n        state.done = isBelowVelocityThreshold && isBelowDisplacementThreshold;\n      } else {\n        state.done = t >= duration;\n      }\n      state.value = state.done ? target : current;\n      return state;\n    },\n    toString: () => {\n      const calculatedDuration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n      const easing = generateLinearEasing(progress => generator.next(calculatedDuration * progress).value, calculatedDuration, 30);\n      return calculatedDuration + \"ms \" + easing;\n    },\n    toTransition: () => {}\n  };\n  return generator;\n}\nspring.applyToOptions = options => {\n  const generatorOptions = createGeneratorEasing(options, 100, spring);\n  options.ease = generatorOptions.ease;\n  options.duration = secondsToMilliseconds(generatorOptions.duration);\n  options.type = \"keyframes\";\n  return options;\n};\nexport { spring };", "map": {"version": 3, "names": ["millisecondsToSeconds", "secondsToMilliseconds", "clamp", "generateLinearEasing", "calcGeneratorDuration", "maxGeneratorDuration", "createGeneratorEasing", "calcGeneratorVelocity", "springDefaults", "findSpring", "calcAngularFreq", "durationKeys", "physicsKeys", "isSpringType", "options", "keys", "some", "key", "undefined", "getSpringOptions", "springOptions", "velocity", "stiffness", "damping", "mass", "isResolvedFromDuration", "visualDuration", "root", "Math", "PI", "bounce", "sqrt", "derived", "spring", "optionsOrVisualDuration", "arguments", "length", "keyframes", "restSpeed", "restDelta", "origin", "target", "state", "done", "value", "duration", "initialVelocity", "dampingRatio", "initialDelta", "undampedAngularFreq", "isGranularScale", "abs", "granular", "default", "resolveSpring", "angularFreq", "t", "envelope", "exp", "sin", "cos", "dampedAngularFreq", "freqForT", "min", "sinh", "cosh", "generator", "calculatedDuration", "next", "current", "currentVelocity", "isBelowVelocityThreshold", "isBelowDisplacementThreshold", "toString", "easing", "progress", "toTransition", "applyToOptions", "generatorOptions", "ease", "type"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/animation/generators/spring/index.mjs"], "sourcesContent": ["import { millisecondsToSeconds, secondsToMilliseconds, clamp } from 'motion-utils';\nimport { generateLinearEasing } from '../../waapi/utils/linear.mjs';\nimport { calcGeneratorDuration, maxGeneratorDuration } from '../utils/calc-duration.mjs';\nimport { createGeneratorEasing } from '../utils/create-generator-easing.mjs';\nimport { calcGeneratorVelocity } from '../utils/velocity.mjs';\nimport { springDefaults } from './defaults.mjs';\nimport { findSpring, calcAngularFreq } from './find.mjs';\n\nconst durationKeys = [\"duration\", \"bounce\"];\nconst physicsKeys = [\"stiffness\", \"damping\", \"mass\"];\nfunction isSpringType(options, keys) {\n    return keys.some((key) => options[key] !== undefined);\n}\nfunction getSpringOptions(options) {\n    let springOptions = {\n        velocity: springDefaults.velocity,\n        stiffness: springDefaults.stiffness,\n        damping: springDefaults.damping,\n        mass: springDefaults.mass,\n        isResolvedFromDuration: false,\n        ...options,\n    };\n    // stiffness/damping/mass overrides duration/bounce\n    if (!isSpringType(options, physicsKeys) &&\n        isSpringType(options, durationKeys)) {\n        if (options.visualDuration) {\n            const visualDuration = options.visualDuration;\n            const root = (2 * Math.PI) / (visualDuration * 1.2);\n            const stiffness = root * root;\n            const damping = 2 *\n                clamp(0.05, 1, 1 - (options.bounce || 0)) *\n                Math.sqrt(stiffness);\n            springOptions = {\n                ...springOptions,\n                mass: springDefaults.mass,\n                stiffness,\n                damping,\n            };\n        }\n        else {\n            const derived = findSpring(options);\n            springOptions = {\n                ...springOptions,\n                ...derived,\n                mass: springDefaults.mass,\n            };\n            springOptions.isResolvedFromDuration = true;\n        }\n    }\n    return springOptions;\n}\nfunction spring(optionsOrVisualDuration = springDefaults.visualDuration, bounce = springDefaults.bounce) {\n    const options = typeof optionsOrVisualDuration !== \"object\"\n        ? {\n            visualDuration: optionsOrVisualDuration,\n            keyframes: [0, 1],\n            bounce,\n        }\n        : optionsOrVisualDuration;\n    let { restSpeed, restDelta } = options;\n    const origin = options.keyframes[0];\n    const target = options.keyframes[options.keyframes.length - 1];\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = { done: false, value: origin };\n    const { stiffness, damping, mass, duration, velocity, isResolvedFromDuration, } = getSpringOptions({\n        ...options,\n        velocity: -millisecondsToSeconds(options.velocity || 0),\n    });\n    const initialVelocity = velocity || 0.0;\n    const dampingRatio = damping / (2 * Math.sqrt(stiffness * mass));\n    const initialDelta = target - origin;\n    const undampedAngularFreq = millisecondsToSeconds(Math.sqrt(stiffness / mass));\n    /**\n     * If we're working on a granular scale, use smaller defaults for determining\n     * when the spring is finished.\n     *\n     * These defaults have been selected emprically based on what strikes a good\n     * ratio between feeling good and finishing as soon as changes are imperceptible.\n     */\n    const isGranularScale = Math.abs(initialDelta) < 5;\n    restSpeed || (restSpeed = isGranularScale\n        ? springDefaults.restSpeed.granular\n        : springDefaults.restSpeed.default);\n    restDelta || (restDelta = isGranularScale\n        ? springDefaults.restDelta.granular\n        : springDefaults.restDelta.default);\n    let resolveSpring;\n    if (dampingRatio < 1) {\n        const angularFreq = calcAngularFreq(undampedAngularFreq, dampingRatio);\n        // Underdamped spring\n        resolveSpring = (t) => {\n            const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n            return (target -\n                envelope *\n                    (((initialVelocity +\n                        dampingRatio * undampedAngularFreq * initialDelta) /\n                        angularFreq) *\n                        Math.sin(angularFreq * t) +\n                        initialDelta * Math.cos(angularFreq * t)));\n        };\n    }\n    else if (dampingRatio === 1) {\n        // Critically damped spring\n        resolveSpring = (t) => target -\n            Math.exp(-undampedAngularFreq * t) *\n                (initialDelta +\n                    (initialVelocity + undampedAngularFreq * initialDelta) * t);\n    }\n    else {\n        // Overdamped spring\n        const dampedAngularFreq = undampedAngularFreq * Math.sqrt(dampingRatio * dampingRatio - 1);\n        resolveSpring = (t) => {\n            const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n            // When performing sinh or cosh values can hit Infinity so we cap them here\n            const freqForT = Math.min(dampedAngularFreq * t, 300);\n            return (target -\n                (envelope *\n                    ((initialVelocity +\n                        dampingRatio * undampedAngularFreq * initialDelta) *\n                        Math.sinh(freqForT) +\n                        dampedAngularFreq *\n                            initialDelta *\n                            Math.cosh(freqForT))) /\n                    dampedAngularFreq);\n        };\n    }\n    const generator = {\n        calculatedDuration: isResolvedFromDuration ? duration || null : null,\n        next: (t) => {\n            const current = resolveSpring(t);\n            if (!isResolvedFromDuration) {\n                let currentVelocity = t === 0 ? initialVelocity : 0.0;\n                /**\n                 * We only need to calculate velocity for under-damped springs\n                 * as over- and critically-damped springs can't overshoot, so\n                 * checking only for displacement is enough.\n                 */\n                if (dampingRatio < 1) {\n                    currentVelocity =\n                        t === 0\n                            ? secondsToMilliseconds(initialVelocity)\n                            : calcGeneratorVelocity(resolveSpring, t, current);\n                }\n                const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n                const isBelowDisplacementThreshold = Math.abs(target - current) <= restDelta;\n                state.done =\n                    isBelowVelocityThreshold && isBelowDisplacementThreshold;\n            }\n            else {\n                state.done = t >= duration;\n            }\n            state.value = state.done ? target : current;\n            return state;\n        },\n        toString: () => {\n            const calculatedDuration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n            const easing = generateLinearEasing((progress) => generator.next(calculatedDuration * progress).value, calculatedDuration, 30);\n            return calculatedDuration + \"ms \" + easing;\n        },\n        toTransition: () => { },\n    };\n    return generator;\n}\nspring.applyToOptions = (options) => {\n    const generatorOptions = createGeneratorEasing(options, 100, spring);\n    options.ease = generatorOptions.ease;\n    options.duration = secondsToMilliseconds(generatorOptions.duration);\n    options.type = \"keyframes\";\n    return options;\n};\n\nexport { spring };\n"], "mappings": "AAAA,SAASA,qBAAqB,EAAEC,qBAAqB,EAAEC,KAAK,QAAQ,cAAc;AAClF,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,4BAA4B;AACxF,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,UAAU,EAAEC,eAAe,QAAQ,YAAY;AAExD,MAAMC,YAAY,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;AAC3C,MAAMC,WAAW,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC;AACpD,SAASC,YAAYA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACjC,OAAOA,IAAI,CAACC,IAAI,CAAEC,GAAG,IAAKH,OAAO,CAACG,GAAG,CAAC,KAAKC,SAAS,CAAC;AACzD;AACA,SAASC,gBAAgBA,CAACL,OAAO,EAAE;EAC/B,IAAIM,aAAa,GAAG;IAChBC,QAAQ,EAAEb,cAAc,CAACa,QAAQ;IACjCC,SAAS,EAAEd,cAAc,CAACc,SAAS;IACnCC,OAAO,EAAEf,cAAc,CAACe,OAAO;IAC/BC,IAAI,EAAEhB,cAAc,CAACgB,IAAI;IACzBC,sBAAsB,EAAE,KAAK;IAC7B,GAAGX;EACP,CAAC;EACD;EACA,IAAI,CAACD,YAAY,CAACC,OAAO,EAAEF,WAAW,CAAC,IACnCC,YAAY,CAACC,OAAO,EAAEH,YAAY,CAAC,EAAE;IACrC,IAAIG,OAAO,CAACY,cAAc,EAAE;MACxB,MAAMA,cAAc,GAAGZ,OAAO,CAACY,cAAc;MAC7C,MAAMC,IAAI,GAAI,CAAC,GAAGC,IAAI,CAACC,EAAE,IAAKH,cAAc,GAAG,GAAG,CAAC;MACnD,MAAMJ,SAAS,GAAGK,IAAI,GAAGA,IAAI;MAC7B,MAAMJ,OAAO,GAAG,CAAC,GACbrB,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAIY,OAAO,CAACgB,MAAM,IAAI,CAAC,CAAC,CAAC,GACzCF,IAAI,CAACG,IAAI,CAACT,SAAS,CAAC;MACxBF,aAAa,GAAG;QACZ,GAAGA,aAAa;QAChBI,IAAI,EAAEhB,cAAc,CAACgB,IAAI;QACzBF,SAAS;QACTC;MACJ,CAAC;IACL,CAAC,MACI;MACD,MAAMS,OAAO,GAAGvB,UAAU,CAACK,OAAO,CAAC;MACnCM,aAAa,GAAG;QACZ,GAAGA,aAAa;QAChB,GAAGY,OAAO;QACVR,IAAI,EAAEhB,cAAc,CAACgB;MACzB,CAAC;MACDJ,aAAa,CAACK,sBAAsB,GAAG,IAAI;IAC/C;EACJ;EACA,OAAOL,aAAa;AACxB;AACA,SAASa,MAAMA,CAAA,EAA0F;EAAA,IAAzFC,uBAAuB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAjB,SAAA,GAAAiB,SAAA,MAAG3B,cAAc,CAACkB,cAAc;EAAA,IAAEI,MAAM,GAAAK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAjB,SAAA,GAAAiB,SAAA,MAAG3B,cAAc,CAACsB,MAAM;EACnG,MAAMhB,OAAO,GAAG,OAAOoB,uBAAuB,KAAK,QAAQ,GACrD;IACER,cAAc,EAAEQ,uBAAuB;IACvCG,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACjBP;EACJ,CAAC,GACCI,uBAAuB;EAC7B,IAAI;IAAEI,SAAS;IAAEC;EAAU,CAAC,GAAGzB,OAAO;EACtC,MAAM0B,MAAM,GAAG1B,OAAO,CAACuB,SAAS,CAAC,CAAC,CAAC;EACnC,MAAMI,MAAM,GAAG3B,OAAO,CAACuB,SAAS,CAACvB,OAAO,CAACuB,SAAS,CAACD,MAAM,GAAG,CAAC,CAAC;EAC9D;AACJ;AACA;AACA;EACI,MAAMM,KAAK,GAAG;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAEJ;EAAO,CAAC;EAC5C,MAAM;IAAElB,SAAS;IAAEC,OAAO;IAAEC,IAAI;IAAEqB,QAAQ;IAAExB,QAAQ;IAAEI;EAAwB,CAAC,GAAGN,gBAAgB,CAAC;IAC/F,GAAGL,OAAO;IACVO,QAAQ,EAAE,CAACrB,qBAAqB,CAACc,OAAO,CAACO,QAAQ,IAAI,CAAC;EAC1D,CAAC,CAAC;EACF,MAAMyB,eAAe,GAAGzB,QAAQ,IAAI,GAAG;EACvC,MAAM0B,YAAY,GAAGxB,OAAO,IAAI,CAAC,GAAGK,IAAI,CAACG,IAAI,CAACT,SAAS,GAAGE,IAAI,CAAC,CAAC;EAChE,MAAMwB,YAAY,GAAGP,MAAM,GAAGD,MAAM;EACpC,MAAMS,mBAAmB,GAAGjD,qBAAqB,CAAC4B,IAAI,CAACG,IAAI,CAACT,SAAS,GAAGE,IAAI,CAAC,CAAC;EAC9E;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAM0B,eAAe,GAAGtB,IAAI,CAACuB,GAAG,CAACH,YAAY,CAAC,GAAG,CAAC;EAClDV,SAAS,KAAKA,SAAS,GAAGY,eAAe,GACnC1C,cAAc,CAAC8B,SAAS,CAACc,QAAQ,GACjC5C,cAAc,CAAC8B,SAAS,CAACe,OAAO,CAAC;EACvCd,SAAS,KAAKA,SAAS,GAAGW,eAAe,GACnC1C,cAAc,CAAC+B,SAAS,CAACa,QAAQ,GACjC5C,cAAc,CAAC+B,SAAS,CAACc,OAAO,CAAC;EACvC,IAAIC,aAAa;EACjB,IAAIP,YAAY,GAAG,CAAC,EAAE;IAClB,MAAMQ,WAAW,GAAG7C,eAAe,CAACuC,mBAAmB,EAAEF,YAAY,CAAC;IACtE;IACAO,aAAa,GAAIE,CAAC,IAAK;MACnB,MAAMC,QAAQ,GAAG7B,IAAI,CAAC8B,GAAG,CAAC,CAACX,YAAY,GAAGE,mBAAmB,GAAGO,CAAC,CAAC;MAClE,OAAQf,MAAM,GACVgB,QAAQ,IACF,CAACX,eAAe,GACdC,YAAY,GAAGE,mBAAmB,GAAGD,YAAY,IACjDO,WAAW,GACX3B,IAAI,CAAC+B,GAAG,CAACJ,WAAW,GAAGC,CAAC,CAAC,GACzBR,YAAY,GAAGpB,IAAI,CAACgC,GAAG,CAACL,WAAW,GAAGC,CAAC,CAAC,CAAC;IACzD,CAAC;EACL,CAAC,MACI,IAAIT,YAAY,KAAK,CAAC,EAAE;IACzB;IACAO,aAAa,GAAIE,CAAC,IAAKf,MAAM,GACzBb,IAAI,CAAC8B,GAAG,CAAC,CAACT,mBAAmB,GAAGO,CAAC,CAAC,IAC7BR,YAAY,GACT,CAACF,eAAe,GAAGG,mBAAmB,GAAGD,YAAY,IAAIQ,CAAC,CAAC;EAC3E,CAAC,MACI;IACD;IACA,MAAMK,iBAAiB,GAAGZ,mBAAmB,GAAGrB,IAAI,CAACG,IAAI,CAACgB,YAAY,GAAGA,YAAY,GAAG,CAAC,CAAC;IAC1FO,aAAa,GAAIE,CAAC,IAAK;MACnB,MAAMC,QAAQ,GAAG7B,IAAI,CAAC8B,GAAG,CAAC,CAACX,YAAY,GAAGE,mBAAmB,GAAGO,CAAC,CAAC;MAClE;MACA,MAAMM,QAAQ,GAAGlC,IAAI,CAACmC,GAAG,CAACF,iBAAiB,GAAGL,CAAC,EAAE,GAAG,CAAC;MACrD,OAAQf,MAAM,GACTgB,QAAQ,IACJ,CAACX,eAAe,GACbC,YAAY,GAAGE,mBAAmB,GAAGD,YAAY,IACjDpB,IAAI,CAACoC,IAAI,CAACF,QAAQ,CAAC,GACnBD,iBAAiB,GACbb,YAAY,GACZpB,IAAI,CAACqC,IAAI,CAACH,QAAQ,CAAC,CAAC,GAC5BD,iBAAiB;IAC7B,CAAC;EACL;EACA,MAAMK,SAAS,GAAG;IACdC,kBAAkB,EAAE1C,sBAAsB,GAAGoB,QAAQ,IAAI,IAAI,GAAG,IAAI;IACpEuB,IAAI,EAAGZ,CAAC,IAAK;MACT,MAAMa,OAAO,GAAGf,aAAa,CAACE,CAAC,CAAC;MAChC,IAAI,CAAC/B,sBAAsB,EAAE;QACzB,IAAI6C,eAAe,GAAGd,CAAC,KAAK,CAAC,GAAGV,eAAe,GAAG,GAAG;QACrD;AAChB;AACA;AACA;AACA;QACgB,IAAIC,YAAY,GAAG,CAAC,EAAE;UAClBuB,eAAe,GACXd,CAAC,KAAK,CAAC,GACDvD,qBAAqB,CAAC6C,eAAe,CAAC,GACtCvC,qBAAqB,CAAC+C,aAAa,EAAEE,CAAC,EAAEa,OAAO,CAAC;QAC9D;QACA,MAAME,wBAAwB,GAAG3C,IAAI,CAACuB,GAAG,CAACmB,eAAe,CAAC,IAAIhC,SAAS;QACvE,MAAMkC,4BAA4B,GAAG5C,IAAI,CAACuB,GAAG,CAACV,MAAM,GAAG4B,OAAO,CAAC,IAAI9B,SAAS;QAC5EG,KAAK,CAACC,IAAI,GACN4B,wBAAwB,IAAIC,4BAA4B;MAChE,CAAC,MACI;QACD9B,KAAK,CAACC,IAAI,GAAGa,CAAC,IAAIX,QAAQ;MAC9B;MACAH,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACC,IAAI,GAAGF,MAAM,GAAG4B,OAAO;MAC3C,OAAO3B,KAAK;IAChB,CAAC;IACD+B,QAAQ,EAAEA,CAAA,KAAM;MACZ,MAAMN,kBAAkB,GAAGvC,IAAI,CAACmC,GAAG,CAAC3D,qBAAqB,CAAC8D,SAAS,CAAC,EAAE7D,oBAAoB,CAAC;MAC3F,MAAMqE,MAAM,GAAGvE,oBAAoB,CAAEwE,QAAQ,IAAKT,SAAS,CAACE,IAAI,CAACD,kBAAkB,GAAGQ,QAAQ,CAAC,CAAC/B,KAAK,EAAEuB,kBAAkB,EAAE,EAAE,CAAC;MAC9H,OAAOA,kBAAkB,GAAG,KAAK,GAAGO,MAAM;IAC9C,CAAC;IACDE,YAAY,EAAEA,CAAA,KAAM,CAAE;EAC1B,CAAC;EACD,OAAOV,SAAS;AACpB;AACAjC,MAAM,CAAC4C,cAAc,GAAI/D,OAAO,IAAK;EACjC,MAAMgE,gBAAgB,GAAGxE,qBAAqB,CAACQ,OAAO,EAAE,GAAG,EAAEmB,MAAM,CAAC;EACpEnB,OAAO,CAACiE,IAAI,GAAGD,gBAAgB,CAACC,IAAI;EACpCjE,OAAO,CAAC+B,QAAQ,GAAG5C,qBAAqB,CAAC6E,gBAAgB,CAACjC,QAAQ,CAAC;EACnE/B,OAAO,CAACkE,IAAI,GAAG,WAAW;EAC1B,OAAOlE,OAAO;AAClB,CAAC;AAED,SAASmB,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}