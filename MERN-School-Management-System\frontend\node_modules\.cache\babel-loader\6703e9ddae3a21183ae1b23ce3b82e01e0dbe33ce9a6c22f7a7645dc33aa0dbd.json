{"ast": null, "code": "import { convertBoundingBoxToBox, transformBoxPoints } from '../geometry/conversion.mjs';\nimport { translateAxis } from '../geometry/delta-apply.mjs';\nfunction measureViewportBox(instance, transformPoint) {\n  return convertBoundingBoxToBox(transformBoxPoints(instance.getBoundingClientRect(), transformPoint));\n}\nfunction measurePageBox(element, rootProjectionNode, transformPagePoint) {\n  const viewportBox = measureViewportBox(element, transformPagePoint);\n  const {\n    scroll\n  } = rootProjectionNode;\n  if (scroll) {\n    translateAxis(viewportBox.x, scroll.offset.x);\n    translateAxis(viewportBox.y, scroll.offset.y);\n  }\n  return viewportBox;\n}\nexport { measurePageBox, measureViewportBox };", "map": {"version": 3, "names": ["convertBoundingBoxToBox", "transformBoxPoints", "translateAxis", "measureViewportBox", "instance", "transformPoint", "getBoundingClientRect", "measurePageBox", "element", "rootProjectionNode", "transformPagePoint", "viewportBox", "scroll", "x", "offset", "y"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/projection/utils/measure.mjs"], "sourcesContent": ["import { convertBoundingBoxToBox, transformBoxPoints } from '../geometry/conversion.mjs';\nimport { translateAxis } from '../geometry/delta-apply.mjs';\n\nfunction measureViewportBox(instance, transformPoint) {\n    return convertBoundingBoxToBox(transformBoxPoints(instance.getBoundingClientRect(), transformPoint));\n}\nfunction measurePageBox(element, rootProjectionNode, transformPagePoint) {\n    const viewportBox = measureViewportBox(element, transformPagePoint);\n    const { scroll } = rootProjectionNode;\n    if (scroll) {\n        translateAxis(viewportBox.x, scroll.offset.x);\n        translateAxis(viewportBox.y, scroll.offset.y);\n    }\n    return viewportBox;\n}\n\nexport { measurePageBox, measureViewportBox };\n"], "mappings": "AAAA,SAASA,uBAAuB,EAAEC,kBAAkB,QAAQ,4BAA4B;AACxF,SAASC,aAAa,QAAQ,6BAA6B;AAE3D,SAASC,kBAAkBA,CAACC,QAAQ,EAAEC,cAAc,EAAE;EAClD,OAAOL,uBAAuB,CAACC,kBAAkB,CAACG,QAAQ,CAACE,qBAAqB,EAAE,EAAED,cAAc,CAAC,CAAC;AACxG;AACA,SAASE,cAAcA,CAACC,OAAO,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAE;EACrE,MAAMC,WAAW,GAAGR,kBAAkB,CAACK,OAAO,EAAEE,kBAAkB,CAAC;EACnE,MAAM;IAAEE;EAAO,CAAC,GAAGH,kBAAkB;EACrC,IAAIG,MAAM,EAAE;IACRV,aAAa,CAACS,WAAW,CAACE,CAAC,EAAED,MAAM,CAACE,MAAM,CAACD,CAAC,CAAC;IAC7CX,aAAa,CAACS,WAAW,CAACI,CAAC,EAAEH,MAAM,CAACE,MAAM,CAACC,CAAC,CAAC;EACjD;EACA,OAAOJ,WAAW;AACtB;AAEA,SAASJ,cAAc,EAAEJ,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}