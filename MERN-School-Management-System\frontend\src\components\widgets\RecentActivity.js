import React from 'react';
import { 
  Paper, 
  Typography, 
  Box, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText,
  Avatar,
  Chip
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  PersonAdd as PersonAddIcon,
  Assignment as AssignmentIcon,
  Announcement as AnnouncementIcon,
  School as SchoolIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  height: '400px',
  background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  overflow: 'hidden',
}));

const ActivityItem = styled(motion.div)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  padding: theme.spacing(1.5),
  borderRadius: '12px',
  background: 'rgba(255, 255, 255, 0.7)',
  backdropFilter: 'blur(10px)',
}));

const RecentActivity = () => {
  // Sample recent activities
  const activities = [
    {
      id: 1,
      type: 'student',
      icon: <PersonAddIcon />,
      title: 'New student enrolled',
      description: 'John Doe joined Class 10A',
      time: '2 hours ago',
      color: '#4ECDC4'
    },
    {
      id: 2,
      type: 'assignment',
      icon: <AssignmentIcon />,
      title: 'Assignment submitted',
      description: 'Math homework by Class 9B',
      time: '4 hours ago',
      color: '#45B7D1'
    },
    {
      id: 3,
      type: 'notice',
      icon: <AnnouncementIcon />,
      title: 'Notice published',
      description: 'Parent-Teacher meeting scheduled',
      time: '6 hours ago',
      color: '#96CEB4'
    },
    {
      id: 4,
      type: 'exam',
      icon: <CheckCircleIcon />,
      title: 'Exam completed',
      description: 'Science test for Class 8',
      time: '1 day ago',
      color: '#FFEAA7'
    },
    {
      id: 5,
      type: 'teacher',
      icon: <SchoolIcon />,
      title: 'Teacher assigned',
      description: 'Ms. Smith to Class 7A',
      time: '2 days ago',
      color: '#DDA0DD'
    },
  ];

  const getTimeColor = (time) => {
    if (time.includes('hour')) return 'success';
    if (time.includes('day')) return 'warning';
    return 'default';
  };

  return (
    <StyledPaper elevation={3}>
      <Box mb={2}>
        <Typography variant="h6" component="h3" fontWeight="bold" color="#333">
          🕒 Recent Activity
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.7, color: '#666' }}>
          Latest updates and actions
        </Typography>
      </Box>
      <Box sx={{ height: '320px', overflowY: 'auto' }}>
        <List sx={{ padding: 0 }}>
          {activities.map((activity, index) => (
            <ActivityItem
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <ListItem sx={{ padding: 0 }}>
                <ListItemIcon sx={{ minWidth: 40 }}>
                  <Avatar 
                    sx={{ 
                      bgcolor: activity.color, 
                      width: 32, 
                      height: 32 
                    }}
                  >
                    {React.cloneElement(activity.icon, { fontSize: 'small' })}
                  </Avatar>
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography variant="body2" fontWeight="bold" color="#333">
                      {activity.title}
                    </Typography>
                  }
                  secondary={
                    <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                      <Typography variant="caption" color="#666">
                        {activity.description}
                      </Typography>
                      <Chip 
                        label={activity.time} 
                        size="small" 
                        color={getTimeColor(activity.time)}
                        sx={{ fontSize: '0.7rem', height: 20 }}
                      />
                    </Box>
                  }
                />
              </ListItem>
            </ActivityItem>
          ))}
        </List>
      </Box>
    </StyledPaper>
  );
};

export default RecentActivity;
