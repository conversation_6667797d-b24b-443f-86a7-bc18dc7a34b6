{"ast": null, "code": "import { mixNumber } from 'motion-dom';\nimport { progress, clamp } from 'motion-utils';\nimport { calcLength } from '../../../projection/geometry/delta-calc.mjs';\n\n/**\n * Apply constraints to a point. These constraints are both physical along an\n * axis, and an elastic factor that determines how much to constrain the point\n * by if it does lie outside the defined parameters.\n */\nfunction applyConstraints(point, _ref, elastic) {\n  let {\n    min,\n    max\n  } = _ref;\n  if (min !== undefined && point < min) {\n    // If we have a min point defined, and this is outside of that, constrain\n    point = elastic ? mixNumber(min, point, elastic.min) : Math.max(point, min);\n  } else if (max !== undefined && point > max) {\n    // If we have a max point defined, and this is outside of that, constrain\n    point = elastic ? mixNumber(max, point, elastic.max) : Math.min(point, max);\n  }\n  return point;\n}\n/**\n * Calculate constraints in terms of the viewport when defined relatively to the\n * measured axis. This is measured from the nearest edge, so a max constraint of 200\n * on an axis with a max value of 300 would return a constraint of 500 - axis length\n */\nfunction calcRelativeAxisConstraints(axis, min, max) {\n  return {\n    min: min !== undefined ? axis.min + min : undefined,\n    max: max !== undefined ? axis.max + max - (axis.max - axis.min) : undefined\n  };\n}\n/**\n * Calculate constraints in terms of the viewport when\n * defined relatively to the measured bounding box.\n */\nfunction calcRelativeConstraints(layoutBox, _ref2) {\n  let {\n    top,\n    left,\n    bottom,\n    right\n  } = _ref2;\n  return {\n    x: calcRelativeAxisConstraints(layoutBox.x, left, right),\n    y: calcRelativeAxisConstraints(layoutBox.y, top, bottom)\n  };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative axis\n */\nfunction calcViewportAxisConstraints(layoutAxis, constraintsAxis) {\n  let min = constraintsAxis.min - layoutAxis.min;\n  let max = constraintsAxis.max - layoutAxis.max;\n  // If the constraints axis is actually smaller than the layout axis then we can\n  // flip the constraints\n  if (constraintsAxis.max - constraintsAxis.min < layoutAxis.max - layoutAxis.min) {\n    [min, max] = [max, min];\n  }\n  return {\n    min,\n    max\n  };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative box\n */\nfunction calcViewportConstraints(layoutBox, constraintsBox) {\n  return {\n    x: calcViewportAxisConstraints(layoutBox.x, constraintsBox.x),\n    y: calcViewportAxisConstraints(layoutBox.y, constraintsBox.y)\n  };\n}\n/**\n * Calculate a transform origin relative to the source axis, between 0-1, that results\n * in an asthetically pleasing scale/transform needed to project from source to target.\n */\nfunction calcOrigin(source, target) {\n  let origin = 0.5;\n  const sourceLength = calcLength(source);\n  const targetLength = calcLength(target);\n  if (targetLength > sourceLength) {\n    origin = progress(target.min, target.max - sourceLength, source.min);\n  } else if (sourceLength > targetLength) {\n    origin = progress(source.min, source.max - targetLength, target.min);\n  }\n  return clamp(0, 1, origin);\n}\n/**\n * Rebase the calculated viewport constraints relative to the layout.min point.\n */\nfunction rebaseAxisConstraints(layout, constraints) {\n  const relativeConstraints = {};\n  if (constraints.min !== undefined) {\n    relativeConstraints.min = constraints.min - layout.min;\n  }\n  if (constraints.max !== undefined) {\n    relativeConstraints.max = constraints.max - layout.min;\n  }\n  return relativeConstraints;\n}\nconst defaultElastic = 0.35;\n/**\n * Accepts a dragElastic prop and returns resolved elastic values for each axis.\n */\nfunction resolveDragElastic() {\n  let dragElastic = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : defaultElastic;\n  if (dragElastic === false) {\n    dragElastic = 0;\n  } else if (dragElastic === true) {\n    dragElastic = defaultElastic;\n  }\n  return {\n    x: resolveAxisElastic(dragElastic, \"left\", \"right\"),\n    y: resolveAxisElastic(dragElastic, \"top\", \"bottom\")\n  };\n}\nfunction resolveAxisElastic(dragElastic, minLabel, maxLabel) {\n  return {\n    min: resolvePointElastic(dragElastic, minLabel),\n    max: resolvePointElastic(dragElastic, maxLabel)\n  };\n}\nfunction resolvePointElastic(dragElastic, label) {\n  return typeof dragElastic === \"number\" ? dragElastic : dragElastic[label] || 0;\n}\nexport { applyConstraints, calcOrigin, calcRelativeAxisConstraints, calcRelativeConstraints, calcViewportAxisConstraints, calcViewportConstraints, defaultElastic, rebaseAxisConstraints, resolveAxisElastic, resolveDragElastic, resolvePointElastic };", "map": {"version": 3, "names": ["mixNumber", "progress", "clamp", "calcLength", "applyConstraints", "point", "_ref", "elastic", "min", "max", "undefined", "Math", "calcRelativeAxisConstraints", "axis", "calcRelativeConstraints", "layoutBox", "_ref2", "top", "left", "bottom", "right", "x", "y", "calcViewportAxisConstraints", "layoutAxis", "constraintsAxis", "calcViewportConstraints", "constraintsBox", "calcOrigin", "source", "target", "origin", "sourceLength", "targetLength", "rebaseAxisConstraints", "layout", "constraints", "relativeConstraints", "defaultElastic", "resolveDragElastic", "dragElastic", "arguments", "length", "resolveAxisElastic", "minLabel", "max<PERSON><PERSON><PERSON>", "resolvePointElastic", "label"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs"], "sourcesContent": ["import { mixNumber } from 'motion-dom';\nimport { progress, clamp } from 'motion-utils';\nimport { calcLength } from '../../../projection/geometry/delta-calc.mjs';\n\n/**\n * Apply constraints to a point. These constraints are both physical along an\n * axis, and an elastic factor that determines how much to constrain the point\n * by if it does lie outside the defined parameters.\n */\nfunction applyConstraints(point, { min, max }, elastic) {\n    if (min !== undefined && point < min) {\n        // If we have a min point defined, and this is outside of that, constrain\n        point = elastic\n            ? mixNumber(min, point, elastic.min)\n            : Math.max(point, min);\n    }\n    else if (max !== undefined && point > max) {\n        // If we have a max point defined, and this is outside of that, constrain\n        point = elastic\n            ? mixNumber(max, point, elastic.max)\n            : Math.min(point, max);\n    }\n    return point;\n}\n/**\n * Calculate constraints in terms of the viewport when defined relatively to the\n * measured axis. This is measured from the nearest edge, so a max constraint of 200\n * on an axis with a max value of 300 would return a constraint of 500 - axis length\n */\nfunction calcRelativeAxisConstraints(axis, min, max) {\n    return {\n        min: min !== undefined ? axis.min + min : undefined,\n        max: max !== undefined\n            ? axis.max + max - (axis.max - axis.min)\n            : undefined,\n    };\n}\n/**\n * Calculate constraints in terms of the viewport when\n * defined relatively to the measured bounding box.\n */\nfunction calcRelativeConstraints(layoutBox, { top, left, bottom, right }) {\n    return {\n        x: calcRelativeAxisConstraints(layoutBox.x, left, right),\n        y: calcRelativeAxisConstraints(layoutBox.y, top, bottom),\n    };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative axis\n */\nfunction calcViewportAxisConstraints(layoutAxis, constraintsAxis) {\n    let min = constraintsAxis.min - layoutAxis.min;\n    let max = constraintsAxis.max - layoutAxis.max;\n    // If the constraints axis is actually smaller than the layout axis then we can\n    // flip the constraints\n    if (constraintsAxis.max - constraintsAxis.min <\n        layoutAxis.max - layoutAxis.min) {\n        [min, max] = [max, min];\n    }\n    return { min, max };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative box\n */\nfunction calcViewportConstraints(layoutBox, constraintsBox) {\n    return {\n        x: calcViewportAxisConstraints(layoutBox.x, constraintsBox.x),\n        y: calcViewportAxisConstraints(layoutBox.y, constraintsBox.y),\n    };\n}\n/**\n * Calculate a transform origin relative to the source axis, between 0-1, that results\n * in an asthetically pleasing scale/transform needed to project from source to target.\n */\nfunction calcOrigin(source, target) {\n    let origin = 0.5;\n    const sourceLength = calcLength(source);\n    const targetLength = calcLength(target);\n    if (targetLength > sourceLength) {\n        origin = progress(target.min, target.max - sourceLength, source.min);\n    }\n    else if (sourceLength > targetLength) {\n        origin = progress(source.min, source.max - targetLength, target.min);\n    }\n    return clamp(0, 1, origin);\n}\n/**\n * Rebase the calculated viewport constraints relative to the layout.min point.\n */\nfunction rebaseAxisConstraints(layout, constraints) {\n    const relativeConstraints = {};\n    if (constraints.min !== undefined) {\n        relativeConstraints.min = constraints.min - layout.min;\n    }\n    if (constraints.max !== undefined) {\n        relativeConstraints.max = constraints.max - layout.min;\n    }\n    return relativeConstraints;\n}\nconst defaultElastic = 0.35;\n/**\n * Accepts a dragElastic prop and returns resolved elastic values for each axis.\n */\nfunction resolveDragElastic(dragElastic = defaultElastic) {\n    if (dragElastic === false) {\n        dragElastic = 0;\n    }\n    else if (dragElastic === true) {\n        dragElastic = defaultElastic;\n    }\n    return {\n        x: resolveAxisElastic(dragElastic, \"left\", \"right\"),\n        y: resolveAxisElastic(dragElastic, \"top\", \"bottom\"),\n    };\n}\nfunction resolveAxisElastic(dragElastic, minLabel, maxLabel) {\n    return {\n        min: resolvePointElastic(dragElastic, minLabel),\n        max: resolvePointElastic(dragElastic, maxLabel),\n    };\n}\nfunction resolvePointElastic(dragElastic, label) {\n    return typeof dragElastic === \"number\"\n        ? dragElastic\n        : dragElastic[label] || 0;\n}\n\nexport { applyConstraints, calcOrigin, calcRelativeAxisConstraints, calcRelativeConstraints, calcViewportAxisConstraints, calcViewportConstraints, defaultElastic, rebaseAxisConstraints, resolveAxisElastic, resolveDragElastic, resolvePointElastic };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,SAASC,QAAQ,EAAEC,KAAK,QAAQ,cAAc;AAC9C,SAASC,UAAU,QAAQ,6CAA6C;;AAExE;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAAC,IAAA,EAAgBC,OAAO,EAAE;EAAA,IAAvB;IAAEC,GAAG;IAAEC;EAAI,CAAC,GAAAH,IAAA;EACzC,IAAIE,GAAG,KAAKE,SAAS,IAAIL,KAAK,GAAGG,GAAG,EAAE;IAClC;IACAH,KAAK,GAAGE,OAAO,GACTP,SAAS,CAACQ,GAAG,EAAEH,KAAK,EAAEE,OAAO,CAACC,GAAG,CAAC,GAClCG,IAAI,CAACF,GAAG,CAACJ,KAAK,EAAEG,GAAG,CAAC;EAC9B,CAAC,MACI,IAAIC,GAAG,KAAKC,SAAS,IAAIL,KAAK,GAAGI,GAAG,EAAE;IACvC;IACAJ,KAAK,GAAGE,OAAO,GACTP,SAAS,CAACS,GAAG,EAAEJ,KAAK,EAAEE,OAAO,CAACE,GAAG,CAAC,GAClCE,IAAI,CAACH,GAAG,CAACH,KAAK,EAAEI,GAAG,CAAC;EAC9B;EACA,OAAOJ,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,2BAA2BA,CAACC,IAAI,EAAEL,GAAG,EAAEC,GAAG,EAAE;EACjD,OAAO;IACHD,GAAG,EAAEA,GAAG,KAAKE,SAAS,GAAGG,IAAI,CAACL,GAAG,GAAGA,GAAG,GAAGE,SAAS;IACnDD,GAAG,EAAEA,GAAG,KAAKC,SAAS,GAChBG,IAAI,CAACJ,GAAG,GAAGA,GAAG,IAAII,IAAI,CAACJ,GAAG,GAAGI,IAAI,CAACL,GAAG,CAAC,GACtCE;EACV,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,SAASI,uBAAuBA,CAACC,SAAS,EAAAC,KAAA,EAAgC;EAAA,IAA9B;IAAEC,GAAG;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAAJ,KAAA;EACpE,OAAO;IACHK,CAAC,EAAET,2BAA2B,CAACG,SAAS,CAACM,CAAC,EAAEH,IAAI,EAAEE,KAAK,CAAC;IACxDE,CAAC,EAAEV,2BAA2B,CAACG,SAAS,CAACO,CAAC,EAAEL,GAAG,EAAEE,MAAM;EAC3D,CAAC;AACL;AACA;AACA;AACA;AACA,SAASI,2BAA2BA,CAACC,UAAU,EAAEC,eAAe,EAAE;EAC9D,IAAIjB,GAAG,GAAGiB,eAAe,CAACjB,GAAG,GAAGgB,UAAU,CAAChB,GAAG;EAC9C,IAAIC,GAAG,GAAGgB,eAAe,CAAChB,GAAG,GAAGe,UAAU,CAACf,GAAG;EAC9C;EACA;EACA,IAAIgB,eAAe,CAAChB,GAAG,GAAGgB,eAAe,CAACjB,GAAG,GACzCgB,UAAU,CAACf,GAAG,GAAGe,UAAU,CAAChB,GAAG,EAAE;IACjC,CAACA,GAAG,EAAEC,GAAG,CAAC,GAAG,CAACA,GAAG,EAAED,GAAG,CAAC;EAC3B;EACA,OAAO;IAAEA,GAAG;IAAEC;EAAI,CAAC;AACvB;AACA;AACA;AACA;AACA,SAASiB,uBAAuBA,CAACX,SAAS,EAAEY,cAAc,EAAE;EACxD,OAAO;IACHN,CAAC,EAAEE,2BAA2B,CAACR,SAAS,CAACM,CAAC,EAAEM,cAAc,CAACN,CAAC,CAAC;IAC7DC,CAAC,EAAEC,2BAA2B,CAACR,SAAS,CAACO,CAAC,EAAEK,cAAc,CAACL,CAAC;EAChE,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,SAASM,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAChC,IAAIC,MAAM,GAAG,GAAG;EAChB,MAAMC,YAAY,GAAG7B,UAAU,CAAC0B,MAAM,CAAC;EACvC,MAAMI,YAAY,GAAG9B,UAAU,CAAC2B,MAAM,CAAC;EACvC,IAAIG,YAAY,GAAGD,YAAY,EAAE;IAC7BD,MAAM,GAAG9B,QAAQ,CAAC6B,MAAM,CAACtB,GAAG,EAAEsB,MAAM,CAACrB,GAAG,GAAGuB,YAAY,EAAEH,MAAM,CAACrB,GAAG,CAAC;EACxE,CAAC,MACI,IAAIwB,YAAY,GAAGC,YAAY,EAAE;IAClCF,MAAM,GAAG9B,QAAQ,CAAC4B,MAAM,CAACrB,GAAG,EAAEqB,MAAM,CAACpB,GAAG,GAAGwB,YAAY,EAAEH,MAAM,CAACtB,GAAG,CAAC;EACxE;EACA,OAAON,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE6B,MAAM,CAAC;AAC9B;AACA;AACA;AACA;AACA,SAASG,qBAAqBA,CAACC,MAAM,EAAEC,WAAW,EAAE;EAChD,MAAMC,mBAAmB,GAAG,CAAC,CAAC;EAC9B,IAAID,WAAW,CAAC5B,GAAG,KAAKE,SAAS,EAAE;IAC/B2B,mBAAmB,CAAC7B,GAAG,GAAG4B,WAAW,CAAC5B,GAAG,GAAG2B,MAAM,CAAC3B,GAAG;EAC1D;EACA,IAAI4B,WAAW,CAAC3B,GAAG,KAAKC,SAAS,EAAE;IAC/B2B,mBAAmB,CAAC5B,GAAG,GAAG2B,WAAW,CAAC3B,GAAG,GAAG0B,MAAM,CAAC3B,GAAG;EAC1D;EACA,OAAO6B,mBAAmB;AAC9B;AACA,MAAMC,cAAc,GAAG,IAAI;AAC3B;AACA;AACA;AACA,SAASC,kBAAkBA,CAAA,EAA+B;EAAA,IAA9BC,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA/B,SAAA,GAAA+B,SAAA,MAAGH,cAAc;EACpD,IAAIE,WAAW,KAAK,KAAK,EAAE;IACvBA,WAAW,GAAG,CAAC;EACnB,CAAC,MACI,IAAIA,WAAW,KAAK,IAAI,EAAE;IAC3BA,WAAW,GAAGF,cAAc;EAChC;EACA,OAAO;IACHjB,CAAC,EAAEsB,kBAAkB,CAACH,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC;IACnDlB,CAAC,EAAEqB,kBAAkB,CAACH,WAAW,EAAE,KAAK,EAAE,QAAQ;EACtD,CAAC;AACL;AACA,SAASG,kBAAkBA,CAACH,WAAW,EAAEI,QAAQ,EAAEC,QAAQ,EAAE;EACzD,OAAO;IACHrC,GAAG,EAAEsC,mBAAmB,CAACN,WAAW,EAAEI,QAAQ,CAAC;IAC/CnC,GAAG,EAAEqC,mBAAmB,CAACN,WAAW,EAAEK,QAAQ;EAClD,CAAC;AACL;AACA,SAASC,mBAAmBA,CAACN,WAAW,EAAEO,KAAK,EAAE;EAC7C,OAAO,OAAOP,WAAW,KAAK,QAAQ,GAChCA,WAAW,GACXA,WAAW,CAACO,KAAK,CAAC,IAAI,CAAC;AACjC;AAEA,SAAS3C,gBAAgB,EAAEwB,UAAU,EAAEhB,2BAA2B,EAAEE,uBAAuB,EAAES,2BAA2B,EAAEG,uBAAuB,EAAEY,cAAc,EAAEJ,qBAAqB,EAAES,kBAAkB,EAAEJ,kBAAkB,EAAEO,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}