{"ast": null, "code": "import { millisecondsToSeconds } from 'motion-utils';\nimport { calcGeneratorDuration, maxGeneratorDuration } from './calc-duration.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options) {\n  let scale = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100;\n  let createGenerator = arguments.length > 2 ? arguments[2] : undefined;\n  const generator = createGenerator({\n    ...options,\n    keyframes: [0, scale]\n  });\n  const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n  return {\n    type: \"keyframes\",\n    ease: progress => {\n      return generator.next(duration * progress).value / scale;\n    },\n    duration: millisecondsToSeconds(duration)\n  };\n}\nexport { createGeneratorEasing };", "map": {"version": 3, "names": ["millisecondsToSeconds", "calcGeneratorDuration", "maxGeneratorDuration", "createGeneratorEasing", "options", "scale", "arguments", "length", "undefined", "createGenerator", "generator", "keyframes", "duration", "Math", "min", "type", "ease", "progress", "next", "value"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs"], "sourcesContent": ["import { millisecondsToSeconds } from 'motion-utils';\nimport { calcGeneratorDuration, maxGeneratorDuration } from './calc-duration.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100, createGenerator) {\n    const generator = createGenerator({ ...options, keyframes: [0, scale] });\n    const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => {\n            return generator.next(duration * progress).value / scale;\n        },\n        duration: millisecondsToSeconds(duration),\n    };\n}\n\nexport { createGeneratorEasing };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,cAAc;AACpD,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,qBAAqB;;AAEjF;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,OAAO,EAAgC;EAAA,IAA9BC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAAA,IAAEG,eAAe,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAChE,MAAME,SAAS,GAAGD,eAAe,CAAC;IAAE,GAAGL,OAAO;IAAEO,SAAS,EAAE,CAAC,CAAC,EAAEN,KAAK;EAAE,CAAC,CAAC;EACxE,MAAMO,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACb,qBAAqB,CAACS,SAAS,CAAC,EAAER,oBAAoB,CAAC;EACjF,OAAO;IACHa,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAGC,QAAQ,IAAK;MAChB,OAAOP,SAAS,CAACQ,IAAI,CAACN,QAAQ,GAAGK,QAAQ,CAAC,CAACE,KAAK,GAAGd,KAAK;IAC5D,CAAC;IACDO,QAAQ,EAAEZ,qBAAqB,CAACY,QAAQ;EAC5C,CAAC;AACL;AAEA,SAAST,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}