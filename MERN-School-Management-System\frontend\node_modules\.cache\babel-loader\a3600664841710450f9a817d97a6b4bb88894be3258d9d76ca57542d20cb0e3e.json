{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\components\\\\theme\\\\ThemeToggle.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { IconButton, Tooltip } from '@mui/material';\nimport { Brightness4, Brightness7 } from '@mui/icons-material';\nimport { useTheme } from './ThemeProvider';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeToggle = () => {\n  _s();\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    title: isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode',\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      children: /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: toggleTheme,\n        color: \"inherit\",\n        sx: {\n          transition: 'all 0.3s ease',\n          '&:hover': {\n            backgroundColor: 'rgba(255, 255, 255, 0.1)'\n          }\n        },\n        children: isDarkMode ? /*#__PURE__*/_jsxDEV(Brightness7, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(Brightness4, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeToggle, \"MY/fJVj7pNG84xK2IRXuobEs7Rg=\", false, function () {\n  return [useTheme];\n});\n_c = ThemeToggle;\nexport default ThemeToggle;\nvar _c;\n$RefreshReg$(_c, \"ThemeToggle\");", "map": {"version": 3, "names": ["React", "IconButton", "<PERSON><PERSON><PERSON>", "Brightness4", "Brightness7", "useTheme", "motion", "jsxDEV", "_jsxDEV", "ThemeToggle", "_s", "isDarkMode", "toggleTheme", "title", "children", "div", "whileHover", "scale", "whileTap", "onClick", "color", "sx", "transition", "backgroundColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/components/theme/ThemeToggle.js"], "sourcesContent": ["import React from 'react';\nimport { IconButton, Tooltip } from '@mui/material';\nimport { Brightness4, Brightness7 } from '@mui/icons-material';\nimport { useTheme } from './ThemeProvider';\nimport { motion } from 'framer-motion';\n\nconst ThemeToggle = () => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <Tooltip title={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>\n      <motion.div\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n      >\n        <IconButton \n          onClick={toggleTheme} \n          color=\"inherit\"\n          sx={{\n            transition: 'all 0.3s ease',\n            '&:hover': {\n              backgroundColor: 'rgba(255, 255, 255, 0.1)',\n            }\n          }}\n        >\n          {isDarkMode ? <Brightness7 /> : <Brightness4 />}\n        </IconButton>\n      </motion.div>\n    </Tooltip>\n  );\n};\n\nexport default ThemeToggle;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,OAAO,QAAQ,eAAe;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,qBAAqB;AAC9D,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAGP,QAAQ,EAAE;EAE9C,oBACEG,OAAA,CAACN,OAAO;IAACW,KAAK,EAAEF,UAAU,GAAG,sBAAsB,GAAG,qBAAsB;IAAAG,QAAA,eAC1EN,OAAA,CAACF,MAAM,CAACS,GAAG;MACTC,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAI,CAAE;MAAAH,QAAA,eAEzBN,OAAA,CAACP,UAAU;QACTkB,OAAO,EAAEP,WAAY;QACrBQ,KAAK,EAAC,SAAS;QACfC,EAAE,EAAE;UACFC,UAAU,EAAE,eAAe;UAC3B,SAAS,EAAE;YACTC,eAAe,EAAE;UACnB;QACF,CAAE;QAAAT,QAAA,EAEDH,UAAU,gBAAGH,OAAA,CAACJ,WAAW;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,gBAAGnB,OAAA,CAACL,WAAW;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACpC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACF;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACL;AAEd,CAAC;AAACjB,EAAA,CAxBID,WAAW;EAAA,QACqBJ,QAAQ;AAAA;AAAAuB,EAAA,GADxCnB,WAAW;AA0BjB,eAAeA,WAAW;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}