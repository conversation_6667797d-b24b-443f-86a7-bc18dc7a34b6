# 🗄️ MongoDB Local Installation Guide

## Option 1: Download and Install MongoDB Community Edition

### Step 1: Download MongoDB
1. Go to: https://www.mongodb.com/try/download/community
2. Select:
   - Version: 8.0 (Latest)
   - Platform: Windows
   - Package: msi
3. Click "Download"

### Step 2: Install MongoDB
1. Run the downloaded .msi file **as Administrator**
2. Choose "Complete" installation
3. ✅ **IMPORTANT:** Check "Install MongoDB as a Service"
4. ✅ Check "Run service as Network Service user"
5. Install MongoDB Compass (optional GUI tool)
6. Complete the installation

### Step 3: Verify Installation
Open Command Prompt as Administrator and run:
```cmd
mongod --version
mongo --version
```

### Step 4: Start MongoDB Service
```cmd
net start MongoDB
```

### Step 5: Test Connection
```cmd
mongo
```
You should see the MongoDB shell prompt.

## Option 2: Using Chocolatey (Package Manager)

If you have Chocolatey installed:
```cmd
choco install mongodb
```

## Option 3: Using Winget (Windows Package Manager)

```cmd
winget install MongoDB.Server
```

## Option 4: Portable Installation

1. Download MongoDB ZIP from the same download page
2. Extract to `C:\mongodb`
3. Create data directory: `C:\data\db`
4. Run: `C:\mongodb\bin\mongod.exe --dbpath C:\data\db`

## After Installation

1. MongoDB will run on: `mongodb://localhost:27017`
2. Your backend is already configured to use this URL
3. Restart your backend server
4. Run the seed scripts to create admin user and classes

## Troubleshooting

### If MongoDB service won't start:
```cmd
# Stop the service
net stop MongoDB

# Start manually to see errors
mongod --dbpath C:\data\db

# Or reinstall as service
mongod --install --serviceName MongoDB --dbpath C:\data\db
```

### If port 27017 is busy:
```cmd
netstat -ano | findstr :27017
taskkill /PID <PID_NUMBER> /F
```

## Quick Test Commands

After installation, test with:
```cmd
# Connect to MongoDB
mongo

# In MongoDB shell:
show dbs
use school_management_system
show collections
```
