{"ast": null, "code": "import axios from 'axios';\nimport { getRequest, getSuccess, getFailed, getError } from './noticeSlice';\nconst REACT_APP_BASE_URL = \"http://localhost:5000\";\nexport const getAllNotices = (id, address) => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${REACT_APP_BASE_URL}/${address}List/${id}`);\n    if (result.data.message) {\n      dispatch(getFailed(result.data.message));\n    } else {\n      dispatch(getSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};", "map": {"version": 3, "names": ["axios", "getRequest", "getSuccess", "getFailed", "getError", "REACT_APP_BASE_URL", "getAllNotices", "id", "address", "dispatch", "result", "get", "data", "message", "error"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/redux/noticeRelated/noticeHandle.js"], "sourcesContent": ["import axios from 'axios';\r\nimport {\r\n    getRequest,\r\n    getSuccess,\r\n    getFailed,\r\n    getError\r\n} from './noticeSlice';\r\n\r\nconst REACT_APP_BASE_URL = \"http://localhost:5000\";\r\n\r\nexport const getAllNotices = (id, address) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${REACT_APP_BASE_URL}/${address}List/${id}`);\r\n        if (result.data.message) {\r\n            dispatch(getFailed(result.data.message));\r\n        } else {\r\n            dispatch(getSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,UAAU,EACVC,UAAU,EACVC,SAAS,EACTC,QAAQ,QACL,eAAe;AAEtB,MAAMC,kBAAkB,GAAG,uBAAuB;AAElD,OAAO,MAAMC,aAAa,GAAGA,CAACC,EAAE,EAAEC,OAAO,KAAK,MAAOC,QAAQ,IAAK;EAC9DA,QAAQ,CAACR,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMS,MAAM,GAAG,MAAMV,KAAK,CAACW,GAAG,CAAE,GAAEN,kBAAmB,IAAGG,OAAQ,QAAOD,EAAG,EAAC,CAAC;IAC5E,IAAIG,MAAM,CAACE,IAAI,CAACC,OAAO,EAAE;MACrBJ,QAAQ,CAACN,SAAS,CAACO,MAAM,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC;IAC5C,CAAC,MAAM;MACHJ,QAAQ,CAACP,UAAU,CAACQ,MAAM,CAACE,IAAI,CAAC,CAAC;IACrC;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZL,QAAQ,CAACL,QAAQ,CAACU,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}