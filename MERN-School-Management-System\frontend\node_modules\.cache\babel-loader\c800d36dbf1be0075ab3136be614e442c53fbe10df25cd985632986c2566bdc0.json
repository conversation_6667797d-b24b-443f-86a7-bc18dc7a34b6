{"ast": null, "code": "function isAnimationControls(v) {\n  return v !== null && typeof v === \"object\" && typeof v.start === \"function\";\n}\nexport { isAnimationControls };", "map": {"version": 3, "names": ["isAnimationControls", "v", "start"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs"], "sourcesContent": ["function isAnimationControls(v) {\n    return (v !== null &&\n        typeof v === \"object\" &&\n        typeof v.start === \"function\");\n}\n\nexport { isAnimationControls };\n"], "mappings": "AAAA,SAASA,mBAAmBA,CAACC,CAAC,EAAE;EAC5B,OAAQA,CAAC,KAAK,IAAI,IACd,OAAOA,CAAC,KAAK,QAAQ,IACrB,OAAOA,CAAC,CAACC,KAAK,KAAK,UAAU;AACrC;AAEA,SAASF,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}