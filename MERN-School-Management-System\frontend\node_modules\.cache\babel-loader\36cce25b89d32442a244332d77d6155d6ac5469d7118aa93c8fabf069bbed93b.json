{"ast": null, "code": "import { isMotionValue, defaultOffset, isGenerator, createGeneratorEasing, fillOffset } from 'motion-dom';\nimport { progress, secondsToMilliseconds, invariant, getEasingForSegment } from 'motion-utils';\nimport { resolveSubjects } from '../animate/resolve-subjects.mjs';\nimport { calculateRepeatDuration } from './utils/calc-repeat-duration.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { normalizeTimes } from './utils/normalize-times.mjs';\nimport { compareByTime } from './utils/sort.mjs';\nconst defaultSegmentEasing = \"easeInOut\";\nconst MAX_REPEAT = 20;\nfunction createAnimationsFromSequence(sequence) {\n  let {\n    defaultTransition = {},\n    ...sequenceTransition\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let scope = arguments.length > 2 ? arguments[2] : undefined;\n  let generators = arguments.length > 3 ? arguments[3] : undefined;\n  const defaultDuration = defaultTransition.duration || 0.3;\n  const animationDefinitions = new Map();\n  const sequences = new Map();\n  const elementCache = {};\n  const timeLabels = new Map();\n  let prevTime = 0;\n  let currentTime = 0;\n  let totalDuration = 0;\n  /**\n   * Build the timeline by mapping over the sequence array and converting\n   * the definitions into keyframes and offsets with absolute time values.\n   * These will later get converted into relative offsets in a second pass.\n   */\n  for (let i = 0; i < sequence.length; i++) {\n    const segment = sequence[i];\n    /**\n     * If this is a timeline label, mark it and skip the rest of this iteration.\n     */\n    if (typeof segment === \"string\") {\n      timeLabels.set(segment, currentTime);\n      continue;\n    } else if (!Array.isArray(segment)) {\n      timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n      continue;\n    }\n    let [subject, keyframes, transition = {}] = segment;\n    /**\n     * If a relative or absolute time value has been specified we need to resolve\n     * it in relation to the currentTime.\n     */\n    if (transition.at !== undefined) {\n      currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n    }\n    /**\n     * Keep track of the maximum duration in this definition. This will be\n     * applied to currentTime once the definition has been parsed.\n     */\n    let maxDuration = 0;\n    const resolveValueSequence = function (valueKeyframes, valueTransition, valueSequence) {\n      let elementIndex = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n      let numSubjects = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;\n      const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n      const {\n        delay = 0,\n        times = defaultOffset(valueKeyframesAsList),\n        type = \"keyframes\",\n        repeat,\n        repeatType,\n        repeatDelay = 0,\n        ...remainingTransition\n      } = valueTransition;\n      let {\n        ease = defaultTransition.ease || \"easeOut\",\n        duration\n      } = valueTransition;\n      /**\n       * Resolve stagger() if defined.\n       */\n      const calculatedDelay = typeof delay === \"function\" ? delay(elementIndex, numSubjects) : delay;\n      /**\n       * If this animation should and can use a spring, generate a spring easing function.\n       */\n      const numKeyframes = valueKeyframesAsList.length;\n      const createGenerator = isGenerator(type) ? type : generators?.[type || \"keyframes\"];\n      if (numKeyframes <= 2 && createGenerator) {\n        /**\n         * As we're creating an easing function from a spring,\n         * ideally we want to generate it using the real distance\n         * between the two keyframes. However this isn't always\n         * possible - in these situations we use 0-100.\n         */\n        let absoluteDelta = 100;\n        if (numKeyframes === 2 && isNumberKeyframesArray(valueKeyframesAsList)) {\n          const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n          absoluteDelta = Math.abs(delta);\n        }\n        const springTransition = {\n          ...remainingTransition\n        };\n        if (duration !== undefined) {\n          springTransition.duration = secondsToMilliseconds(duration);\n        }\n        const springEasing = createGeneratorEasing(springTransition, absoluteDelta, createGenerator);\n        ease = springEasing.ease;\n        duration = springEasing.duration;\n      }\n      duration ?? (duration = defaultDuration);\n      const startTime = currentTime + calculatedDelay;\n      /**\n       * If there's only one time offset of 0, fill in a second with length 1\n       */\n      if (times.length === 1 && times[0] === 0) {\n        times[1] = 1;\n      }\n      /**\n       * Fill out if offset if fewer offsets than keyframes\n       */\n      const remainder = times.length - valueKeyframesAsList.length;\n      remainder > 0 && fillOffset(times, remainder);\n      /**\n       * If only one value has been set, ie [1], push a null to the start of\n       * the keyframe array. This will let us mark a keyframe at this point\n       * that will later be hydrated with the previous value.\n       */\n      valueKeyframesAsList.length === 1 && valueKeyframesAsList.unshift(null);\n      /**\n       * Handle repeat options\n       */\n      if (repeat) {\n        invariant(repeat < MAX_REPEAT, \"Repeat count too high, must be less than 20\");\n        duration = calculateRepeatDuration(duration, repeat);\n        const originalKeyframes = [...valueKeyframesAsList];\n        const originalTimes = [...times];\n        ease = Array.isArray(ease) ? [...ease] : [ease];\n        const originalEase = [...ease];\n        for (let repeatIndex = 0; repeatIndex < repeat; repeatIndex++) {\n          valueKeyframesAsList.push(...originalKeyframes);\n          for (let keyframeIndex = 0; keyframeIndex < originalKeyframes.length; keyframeIndex++) {\n            times.push(originalTimes[keyframeIndex] + (repeatIndex + 1));\n            ease.push(keyframeIndex === 0 ? \"linear\" : getEasingForSegment(originalEase, keyframeIndex - 1));\n          }\n        }\n        normalizeTimes(times, repeat);\n      }\n      const targetTime = startTime + duration;\n      /**\n       * Add keyframes, mapping offsets to absolute time.\n       */\n      addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n      maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n      totalDuration = Math.max(targetTime, totalDuration);\n    };\n    if (isMotionValue(subject)) {\n      const subjectSequence = getSubjectSequence(subject, sequences);\n      resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n    } else {\n      const subjects = resolveSubjects(subject, keyframes, scope, elementCache);\n      const numSubjects = subjects.length;\n      /**\n       * For every element in this segment, process the defined values.\n       */\n      for (let subjectIndex = 0; subjectIndex < numSubjects; subjectIndex++) {\n        /**\n         * Cast necessary, but we know these are of this type\n         */\n        keyframes = keyframes;\n        transition = transition;\n        const thisSubject = subjects[subjectIndex];\n        const subjectSequence = getSubjectSequence(thisSubject, sequences);\n        for (const key in keyframes) {\n          resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), subjectIndex, numSubjects);\n        }\n      }\n    }\n    prevTime = currentTime;\n    currentTime += maxDuration;\n  }\n  /**\n   * For every element and value combination create a new animation.\n   */\n  sequences.forEach((valueSequences, element) => {\n    for (const key in valueSequences) {\n      const valueSequence = valueSequences[key];\n      /**\n       * Arrange all the keyframes in ascending time order.\n       */\n      valueSequence.sort(compareByTime);\n      const keyframes = [];\n      const valueOffset = [];\n      const valueEasing = [];\n      /**\n       * For each keyframe, translate absolute times into\n       * relative offsets based on the total duration of the timeline.\n       */\n      for (let i = 0; i < valueSequence.length; i++) {\n        const {\n          at,\n          value,\n          easing\n        } = valueSequence[i];\n        keyframes.push(value);\n        valueOffset.push(progress(0, totalDuration, at));\n        valueEasing.push(easing || \"easeOut\");\n      }\n      /**\n       * If the first keyframe doesn't land on offset: 0\n       * provide one by duplicating the initial keyframe. This ensures\n       * it snaps to the first keyframe when the animation starts.\n       */\n      if (valueOffset[0] !== 0) {\n        valueOffset.unshift(0);\n        keyframes.unshift(keyframes[0]);\n        valueEasing.unshift(defaultSegmentEasing);\n      }\n      /**\n       * If the last keyframe doesn't land on offset: 1\n       * provide one with a null wildcard value. This will ensure it\n       * stays static until the end of the animation.\n       */\n      if (valueOffset[valueOffset.length - 1] !== 1) {\n        valueOffset.push(1);\n        keyframes.push(null);\n      }\n      if (!animationDefinitions.has(element)) {\n        animationDefinitions.set(element, {\n          keyframes: {},\n          transition: {}\n        });\n      }\n      const definition = animationDefinitions.get(element);\n      definition.keyframes[key] = keyframes;\n      definition.transition[key] = {\n        ...defaultTransition,\n        duration: totalDuration,\n        ease: valueEasing,\n        times: valueOffset,\n        ...sequenceTransition\n      };\n    }\n  });\n  return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n  !sequences.has(subject) && sequences.set(subject, {});\n  return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n  if (!sequences[name]) sequences[name] = [];\n  return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n  return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n  return transition && transition[key] ? {\n    ...transition,\n    ...transition[key]\n  } : {\n    ...transition\n  };\n}\nconst isNumber = keyframe => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = keyframes => keyframes.every(isNumber);\nexport { createAnimationsFromSequence, getValueTransition };", "map": {"version": 3, "names": ["isMotionValue", "defaultOffset", "isGenerator", "createGeneratorEasing", "fillOffset", "progress", "secondsToMilliseconds", "invariant", "getEasingForSegment", "resolveSubjects", "calculateRepeatDuration", "calcNextTime", "addKeyframes", "normalizeTimes", "compareByTime", "defaultSegmentEasing", "MAX_REPEAT", "createAnimationsFromSequence", "sequence", "defaultTransition", "sequenceTransition", "arguments", "length", "undefined", "scope", "generators", "defaultDuration", "duration", "animationDefinitions", "Map", "sequences", "elementCache", "time<PERSON><PERSON><PERSON>", "prevTime", "currentTime", "totalDuration", "i", "segment", "set", "Array", "isArray", "name", "at", "subject", "keyframes", "transition", "maxDuration", "resolveValueSequence", "valueKeyframes", "valueTransition", "valueSequence", "elementIndex", "numSubjects", "valueKeyframesAsList", "keyframesAsList", "delay", "times", "type", "repeat", "repeatType", "repeatDelay", "remainingTransition", "ease", "calculatedDelay", "numKeyframes", "createGenerator", "absoluteDelta", "isNumberKeyframesArray", "delta", "Math", "abs", "springTransition", "springEasing", "startTime", "remainder", "unshift", "originalKeyframes", "originalTimes", "originalEase", "repeatIndex", "push", "keyframeIndex", "targetTime", "max", "subjectSequence", "getSubjectSequence", "getValueSequence", "subjects", "subjectIndex", "thisSubject", "key", "getValueTransition", "for<PERSON>ach", "valueSequences", "element", "sort", "valueOffset", "valueEasing", "value", "easing", "has", "definition", "get", "isNumber", "keyframe", "every"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/animation/sequence/create.mjs"], "sourcesContent": ["import { isMotionValue, defaultOffset, isGenerator, createGeneratorEasing, fillOffset } from 'motion-dom';\nimport { progress, secondsToMilliseconds, invariant, getEasingForSegment } from 'motion-utils';\nimport { resolveSubjects } from '../animate/resolve-subjects.mjs';\nimport { calculateRepeatDuration } from './utils/calc-repeat-duration.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { normalizeTimes } from './utils/normalize-times.mjs';\nimport { compareByTime } from './utils/sort.mjs';\n\nconst defaultSegmentEasing = \"easeInOut\";\nconst MAX_REPEAT = 20;\nfunction createAnimationsFromSequence(sequence, { defaultTransition = {}, ...sequenceTransition } = {}, scope, generators) {\n    const defaultDuration = defaultTransition.duration || 0.3;\n    const animationDefinitions = new Map();\n    const sequences = new Map();\n    const elementCache = {};\n    const timeLabels = new Map();\n    let prevTime = 0;\n    let currentTime = 0;\n    let totalDuration = 0;\n    /**\n     * Build the timeline by mapping over the sequence array and converting\n     * the definitions into keyframes and offsets with absolute time values.\n     * These will later get converted into relative offsets in a second pass.\n     */\n    for (let i = 0; i < sequence.length; i++) {\n        const segment = sequence[i];\n        /**\n         * If this is a timeline label, mark it and skip the rest of this iteration.\n         */\n        if (typeof segment === \"string\") {\n            timeLabels.set(segment, currentTime);\n            continue;\n        }\n        else if (!Array.isArray(segment)) {\n            timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n            continue;\n        }\n        let [subject, keyframes, transition = {}] = segment;\n        /**\n         * If a relative or absolute time value has been specified we need to resolve\n         * it in relation to the currentTime.\n         */\n        if (transition.at !== undefined) {\n            currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n        }\n        /**\n         * Keep track of the maximum duration in this definition. This will be\n         * applied to currentTime once the definition has been parsed.\n         */\n        let maxDuration = 0;\n        const resolveValueSequence = (valueKeyframes, valueTransition, valueSequence, elementIndex = 0, numSubjects = 0) => {\n            const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n            const { delay = 0, times = defaultOffset(valueKeyframesAsList), type = \"keyframes\", repeat, repeatType, repeatDelay = 0, ...remainingTransition } = valueTransition;\n            let { ease = defaultTransition.ease || \"easeOut\", duration } = valueTransition;\n            /**\n             * Resolve stagger() if defined.\n             */\n            const calculatedDelay = typeof delay === \"function\"\n                ? delay(elementIndex, numSubjects)\n                : delay;\n            /**\n             * If this animation should and can use a spring, generate a spring easing function.\n             */\n            const numKeyframes = valueKeyframesAsList.length;\n            const createGenerator = isGenerator(type)\n                ? type\n                : generators?.[type || \"keyframes\"];\n            if (numKeyframes <= 2 && createGenerator) {\n                /**\n                 * As we're creating an easing function from a spring,\n                 * ideally we want to generate it using the real distance\n                 * between the two keyframes. However this isn't always\n                 * possible - in these situations we use 0-100.\n                 */\n                let absoluteDelta = 100;\n                if (numKeyframes === 2 &&\n                    isNumberKeyframesArray(valueKeyframesAsList)) {\n                    const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n                    absoluteDelta = Math.abs(delta);\n                }\n                const springTransition = { ...remainingTransition };\n                if (duration !== undefined) {\n                    springTransition.duration = secondsToMilliseconds(duration);\n                }\n                const springEasing = createGeneratorEasing(springTransition, absoluteDelta, createGenerator);\n                ease = springEasing.ease;\n                duration = springEasing.duration;\n            }\n            duration ?? (duration = defaultDuration);\n            const startTime = currentTime + calculatedDelay;\n            /**\n             * If there's only one time offset of 0, fill in a second with length 1\n             */\n            if (times.length === 1 && times[0] === 0) {\n                times[1] = 1;\n            }\n            /**\n             * Fill out if offset if fewer offsets than keyframes\n             */\n            const remainder = times.length - valueKeyframesAsList.length;\n            remainder > 0 && fillOffset(times, remainder);\n            /**\n             * If only one value has been set, ie [1], push a null to the start of\n             * the keyframe array. This will let us mark a keyframe at this point\n             * that will later be hydrated with the previous value.\n             */\n            valueKeyframesAsList.length === 1 &&\n                valueKeyframesAsList.unshift(null);\n            /**\n             * Handle repeat options\n             */\n            if (repeat) {\n                invariant(repeat < MAX_REPEAT, \"Repeat count too high, must be less than 20\");\n                duration = calculateRepeatDuration(duration, repeat);\n                const originalKeyframes = [...valueKeyframesAsList];\n                const originalTimes = [...times];\n                ease = Array.isArray(ease) ? [...ease] : [ease];\n                const originalEase = [...ease];\n                for (let repeatIndex = 0; repeatIndex < repeat; repeatIndex++) {\n                    valueKeyframesAsList.push(...originalKeyframes);\n                    for (let keyframeIndex = 0; keyframeIndex < originalKeyframes.length; keyframeIndex++) {\n                        times.push(originalTimes[keyframeIndex] + (repeatIndex + 1));\n                        ease.push(keyframeIndex === 0\n                            ? \"linear\"\n                            : getEasingForSegment(originalEase, keyframeIndex - 1));\n                    }\n                }\n                normalizeTimes(times, repeat);\n            }\n            const targetTime = startTime + duration;\n            /**\n             * Add keyframes, mapping offsets to absolute time.\n             */\n            addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n            maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n            totalDuration = Math.max(targetTime, totalDuration);\n        };\n        if (isMotionValue(subject)) {\n            const subjectSequence = getSubjectSequence(subject, sequences);\n            resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n        }\n        else {\n            const subjects = resolveSubjects(subject, keyframes, scope, elementCache);\n            const numSubjects = subjects.length;\n            /**\n             * For every element in this segment, process the defined values.\n             */\n            for (let subjectIndex = 0; subjectIndex < numSubjects; subjectIndex++) {\n                /**\n                 * Cast necessary, but we know these are of this type\n                 */\n                keyframes = keyframes;\n                transition = transition;\n                const thisSubject = subjects[subjectIndex];\n                const subjectSequence = getSubjectSequence(thisSubject, sequences);\n                for (const key in keyframes) {\n                    resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), subjectIndex, numSubjects);\n                }\n            }\n        }\n        prevTime = currentTime;\n        currentTime += maxDuration;\n    }\n    /**\n     * For every element and value combination create a new animation.\n     */\n    sequences.forEach((valueSequences, element) => {\n        for (const key in valueSequences) {\n            const valueSequence = valueSequences[key];\n            /**\n             * Arrange all the keyframes in ascending time order.\n             */\n            valueSequence.sort(compareByTime);\n            const keyframes = [];\n            const valueOffset = [];\n            const valueEasing = [];\n            /**\n             * For each keyframe, translate absolute times into\n             * relative offsets based on the total duration of the timeline.\n             */\n            for (let i = 0; i < valueSequence.length; i++) {\n                const { at, value, easing } = valueSequence[i];\n                keyframes.push(value);\n                valueOffset.push(progress(0, totalDuration, at));\n                valueEasing.push(easing || \"easeOut\");\n            }\n            /**\n             * If the first keyframe doesn't land on offset: 0\n             * provide one by duplicating the initial keyframe. This ensures\n             * it snaps to the first keyframe when the animation starts.\n             */\n            if (valueOffset[0] !== 0) {\n                valueOffset.unshift(0);\n                keyframes.unshift(keyframes[0]);\n                valueEasing.unshift(defaultSegmentEasing);\n            }\n            /**\n             * If the last keyframe doesn't land on offset: 1\n             * provide one with a null wildcard value. This will ensure it\n             * stays static until the end of the animation.\n             */\n            if (valueOffset[valueOffset.length - 1] !== 1) {\n                valueOffset.push(1);\n                keyframes.push(null);\n            }\n            if (!animationDefinitions.has(element)) {\n                animationDefinitions.set(element, {\n                    keyframes: {},\n                    transition: {},\n                });\n            }\n            const definition = animationDefinitions.get(element);\n            definition.keyframes[key] = keyframes;\n            definition.transition[key] = {\n                ...defaultTransition,\n                duration: totalDuration,\n                ease: valueEasing,\n                times: valueOffset,\n                ...sequenceTransition,\n            };\n        }\n    });\n    return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n    !sequences.has(subject) && sequences.set(subject, {});\n    return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n    if (!sequences[name])\n        sequences[name] = [];\n    return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n    return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n    return transition && transition[key]\n        ? {\n            ...transition,\n            ...transition[key],\n        }\n        : { ...transition };\n}\nconst isNumber = (keyframe) => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = (keyframes) => keyframes.every(isNumber);\n\nexport { createAnimationsFromSequence, getValueTransition };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,aAAa,EAAEC,WAAW,EAAEC,qBAAqB,EAAEC,UAAU,QAAQ,YAAY;AACzG,SAASC,QAAQ,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,mBAAmB,QAAQ,cAAc;AAC9F,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,aAAa,QAAQ,kBAAkB;AAEhD,MAAMC,oBAAoB,GAAG,WAAW;AACxC,MAAMC,UAAU,GAAG,EAAE;AACrB,SAASC,4BAA4BA,CAACC,QAAQ,EAA6E;EAAA,IAA3E;IAAEC,iBAAiB,GAAG,CAAC,CAAC;IAAE,GAAGC;EAAmB,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEG,KAAK,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEE,UAAU,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACrH,MAAMG,eAAe,GAAGP,iBAAiB,CAACQ,QAAQ,IAAI,GAAG;EACzD,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,EAAE;EACtC,MAAMC,SAAS,GAAG,IAAID,GAAG,EAAE;EAC3B,MAAME,YAAY,GAAG,CAAC,CAAC;EACvB,MAAMC,UAAU,GAAG,IAAIH,GAAG,EAAE;EAC5B,IAAII,QAAQ,GAAG,CAAC;EAChB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,aAAa,GAAG,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACI,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,QAAQ,CAACI,MAAM,EAAEc,CAAC,EAAE,EAAE;IACtC,MAAMC,OAAO,GAAGnB,QAAQ,CAACkB,CAAC,CAAC;IAC3B;AACR;AACA;IACQ,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MAC7BL,UAAU,CAACM,GAAG,CAACD,OAAO,EAAEH,WAAW,CAAC;MACpC;IACJ,CAAC,MACI,IAAI,CAACK,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;MAC9BL,UAAU,CAACM,GAAG,CAACD,OAAO,CAACI,IAAI,EAAE9B,YAAY,CAACuB,WAAW,EAAEG,OAAO,CAACK,EAAE,EAAET,QAAQ,EAAED,UAAU,CAAC,CAAC;MACzF;IACJ;IACA,IAAI,CAACW,OAAO,EAAEC,SAAS,EAAEC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAGR,OAAO;IACnD;AACR;AACA;AACA;IACQ,IAAIQ,UAAU,CAACH,EAAE,KAAKnB,SAAS,EAAE;MAC7BW,WAAW,GAAGvB,YAAY,CAACuB,WAAW,EAAEW,UAAU,CAACH,EAAE,EAAET,QAAQ,EAAED,UAAU,CAAC;IAChF;IACA;AACR;AACA;AACA;IACQ,IAAIc,WAAW,GAAG,CAAC;IACnB,MAAMC,oBAAoB,GAAG,SAAAA,CAACC,cAAc,EAAEC,eAAe,EAAEC,aAAa,EAAwC;MAAA,IAAtCC,YAAY,GAAA9B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,IAAE+B,WAAW,GAAA/B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAC3G,MAAMgC,oBAAoB,GAAGC,eAAe,CAACN,cAAc,CAAC;MAC5D,MAAM;QAAEO,KAAK,GAAG,CAAC;QAAEC,KAAK,GAAGvD,aAAa,CAACoD,oBAAoB,CAAC;QAAEI,IAAI,GAAG,WAAW;QAAEC,MAAM;QAAEC,UAAU;QAAEC,WAAW,GAAG,CAAC;QAAE,GAAGC;MAAoB,CAAC,GAAGZ,eAAe;MACnK,IAAI;QAAEa,IAAI,GAAG3C,iBAAiB,CAAC2C,IAAI,IAAI,SAAS;QAAEnC;MAAS,CAAC,GAAGsB,eAAe;MAC9E;AACZ;AACA;MACY,MAAMc,eAAe,GAAG,OAAOR,KAAK,KAAK,UAAU,GAC7CA,KAAK,CAACJ,YAAY,EAAEC,WAAW,CAAC,GAChCG,KAAK;MACX;AACZ;AACA;MACY,MAAMS,YAAY,GAAGX,oBAAoB,CAAC/B,MAAM;MAChD,MAAM2C,eAAe,GAAG/D,WAAW,CAACuD,IAAI,CAAC,GACnCA,IAAI,GACJhC,UAAU,GAAGgC,IAAI,IAAI,WAAW,CAAC;MACvC,IAAIO,YAAY,IAAI,CAAC,IAAIC,eAAe,EAAE;QACtC;AAChB;AACA;AACA;AACA;AACA;QACgB,IAAIC,aAAa,GAAG,GAAG;QACvB,IAAIF,YAAY,KAAK,CAAC,IAClBG,sBAAsB,CAACd,oBAAoB,CAAC,EAAE;UAC9C,MAAMe,KAAK,GAAGf,oBAAoB,CAAC,CAAC,CAAC,GAAGA,oBAAoB,CAAC,CAAC,CAAC;UAC/Da,aAAa,GAAGG,IAAI,CAACC,GAAG,CAACF,KAAK,CAAC;QACnC;QACA,MAAMG,gBAAgB,GAAG;UAAE,GAAGV;QAAoB,CAAC;QACnD,IAAIlC,QAAQ,KAAKJ,SAAS,EAAE;UACxBgD,gBAAgB,CAAC5C,QAAQ,GAAGrB,qBAAqB,CAACqB,QAAQ,CAAC;QAC/D;QACA,MAAM6C,YAAY,GAAGrE,qBAAqB,CAACoE,gBAAgB,EAAEL,aAAa,EAAED,eAAe,CAAC;QAC5FH,IAAI,GAAGU,YAAY,CAACV,IAAI;QACxBnC,QAAQ,GAAG6C,YAAY,CAAC7C,QAAQ;MACpC;MACAA,QAAQ,KAAKA,QAAQ,GAAGD,eAAe,CAAC;MACxC,MAAM+C,SAAS,GAAGvC,WAAW,GAAG6B,eAAe;MAC/C;AACZ;AACA;MACY,IAAIP,KAAK,CAAClC,MAAM,KAAK,CAAC,IAAIkC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtCA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;MAChB;MACA;AACZ;AACA;MACY,MAAMkB,SAAS,GAAGlB,KAAK,CAAClC,MAAM,GAAG+B,oBAAoB,CAAC/B,MAAM;MAC5DoD,SAAS,GAAG,CAAC,IAAItE,UAAU,CAACoD,KAAK,EAAEkB,SAAS,CAAC;MAC7C;AACZ;AACA;AACA;AACA;MACYrB,oBAAoB,CAAC/B,MAAM,KAAK,CAAC,IAC7B+B,oBAAoB,CAACsB,OAAO,CAAC,IAAI,CAAC;MACtC;AACZ;AACA;MACY,IAAIjB,MAAM,EAAE;QACRnD,SAAS,CAACmD,MAAM,GAAG1C,UAAU,EAAE,6CAA6C,CAAC;QAC7EW,QAAQ,GAAGjB,uBAAuB,CAACiB,QAAQ,EAAE+B,MAAM,CAAC;QACpD,MAAMkB,iBAAiB,GAAG,CAAC,GAAGvB,oBAAoB,CAAC;QACnD,MAAMwB,aAAa,GAAG,CAAC,GAAGrB,KAAK,CAAC;QAChCM,IAAI,GAAGvB,KAAK,CAACC,OAAO,CAACsB,IAAI,CAAC,GAAG,CAAC,GAAGA,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC;QAC/C,MAAMgB,YAAY,GAAG,CAAC,GAAGhB,IAAI,CAAC;QAC9B,KAAK,IAAIiB,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAGrB,MAAM,EAAEqB,WAAW,EAAE,EAAE;UAC3D1B,oBAAoB,CAAC2B,IAAI,CAAC,GAAGJ,iBAAiB,CAAC;UAC/C,KAAK,IAAIK,aAAa,GAAG,CAAC,EAAEA,aAAa,GAAGL,iBAAiB,CAACtD,MAAM,EAAE2D,aAAa,EAAE,EAAE;YACnFzB,KAAK,CAACwB,IAAI,CAACH,aAAa,CAACI,aAAa,CAAC,IAAIF,WAAW,GAAG,CAAC,CAAC,CAAC;YAC5DjB,IAAI,CAACkB,IAAI,CAACC,aAAa,KAAK,CAAC,GACvB,QAAQ,GACRzE,mBAAmB,CAACsE,YAAY,EAAEG,aAAa,GAAG,CAAC,CAAC,CAAC;UAC/D;QACJ;QACApE,cAAc,CAAC2C,KAAK,EAAEE,MAAM,CAAC;MACjC;MACA,MAAMwB,UAAU,GAAGT,SAAS,GAAG9C,QAAQ;MACvC;AACZ;AACA;MACYf,YAAY,CAACsC,aAAa,EAAEG,oBAAoB,EAAES,IAAI,EAAEN,KAAK,EAAEiB,SAAS,EAAES,UAAU,CAAC;MACrFpC,WAAW,GAAGuB,IAAI,CAACc,GAAG,CAACpB,eAAe,GAAGpC,QAAQ,EAAEmB,WAAW,CAAC;MAC/DX,aAAa,GAAGkC,IAAI,CAACc,GAAG,CAACD,UAAU,EAAE/C,aAAa,CAAC;IACvD,CAAC;IACD,IAAInC,aAAa,CAAC2C,OAAO,CAAC,EAAE;MACxB,MAAMyC,eAAe,GAAGC,kBAAkB,CAAC1C,OAAO,EAAEb,SAAS,CAAC;MAC9DiB,oBAAoB,CAACH,SAAS,EAAEC,UAAU,EAAEyC,gBAAgB,CAAC,SAAS,EAAEF,eAAe,CAAC,CAAC;IAC7F,CAAC,MACI;MACD,MAAMG,QAAQ,GAAG9E,eAAe,CAACkC,OAAO,EAAEC,SAAS,EAAEpB,KAAK,EAAEO,YAAY,CAAC;MACzE,MAAMqB,WAAW,GAAGmC,QAAQ,CAACjE,MAAM;MACnC;AACZ;AACA;MACY,KAAK,IAAIkE,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAGpC,WAAW,EAAEoC,YAAY,EAAE,EAAE;QACnE;AAChB;AACA;QACgB5C,SAAS,GAAGA,SAAS;QACrBC,UAAU,GAAGA,UAAU;QACvB,MAAM4C,WAAW,GAAGF,QAAQ,CAACC,YAAY,CAAC;QAC1C,MAAMJ,eAAe,GAAGC,kBAAkB,CAACI,WAAW,EAAE3D,SAAS,CAAC;QAClE,KAAK,MAAM4D,GAAG,IAAI9C,SAAS,EAAE;UACzBG,oBAAoB,CAACH,SAAS,CAAC8C,GAAG,CAAC,EAAEC,kBAAkB,CAAC9C,UAAU,EAAE6C,GAAG,CAAC,EAAEJ,gBAAgB,CAACI,GAAG,EAAEN,eAAe,CAAC,EAAEI,YAAY,EAAEpC,WAAW,CAAC;QAChJ;MACJ;IACJ;IACAnB,QAAQ,GAAGC,WAAW;IACtBA,WAAW,IAAIY,WAAW;EAC9B;EACA;AACJ;AACA;EACIhB,SAAS,CAAC8D,OAAO,CAAC,CAACC,cAAc,EAAEC,OAAO,KAAK;IAC3C,KAAK,MAAMJ,GAAG,IAAIG,cAAc,EAAE;MAC9B,MAAM3C,aAAa,GAAG2C,cAAc,CAACH,GAAG,CAAC;MACzC;AACZ;AACA;MACYxC,aAAa,CAAC6C,IAAI,CAACjF,aAAa,CAAC;MACjC,MAAM8B,SAAS,GAAG,EAAE;MACpB,MAAMoD,WAAW,GAAG,EAAE;MACtB,MAAMC,WAAW,GAAG,EAAE;MACtB;AACZ;AACA;AACA;MACY,KAAK,IAAI7D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,aAAa,CAAC5B,MAAM,EAAEc,CAAC,EAAE,EAAE;QAC3C,MAAM;UAAEM,EAAE;UAAEwD,KAAK;UAAEC;QAAO,CAAC,GAAGjD,aAAa,CAACd,CAAC,CAAC;QAC9CQ,SAAS,CAACoC,IAAI,CAACkB,KAAK,CAAC;QACrBF,WAAW,CAAChB,IAAI,CAAC3E,QAAQ,CAAC,CAAC,EAAE8B,aAAa,EAAEO,EAAE,CAAC,CAAC;QAChDuD,WAAW,CAACjB,IAAI,CAACmB,MAAM,IAAI,SAAS,CAAC;MACzC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtBA,WAAW,CAACrB,OAAO,CAAC,CAAC,CAAC;QACtB/B,SAAS,CAAC+B,OAAO,CAAC/B,SAAS,CAAC,CAAC,CAAC,CAAC;QAC/BqD,WAAW,CAACtB,OAAO,CAAC5D,oBAAoB,CAAC;MAC7C;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIiF,WAAW,CAACA,WAAW,CAAC1E,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAC3C0E,WAAW,CAAChB,IAAI,CAAC,CAAC,CAAC;QACnBpC,SAAS,CAACoC,IAAI,CAAC,IAAI,CAAC;MACxB;MACA,IAAI,CAACpD,oBAAoB,CAACwE,GAAG,CAACN,OAAO,CAAC,EAAE;QACpClE,oBAAoB,CAACU,GAAG,CAACwD,OAAO,EAAE;UAC9BlD,SAAS,EAAE,CAAC,CAAC;UACbC,UAAU,EAAE,CAAC;QACjB,CAAC,CAAC;MACN;MACA,MAAMwD,UAAU,GAAGzE,oBAAoB,CAAC0E,GAAG,CAACR,OAAO,CAAC;MACpDO,UAAU,CAACzD,SAAS,CAAC8C,GAAG,CAAC,GAAG9C,SAAS;MACrCyD,UAAU,CAACxD,UAAU,CAAC6C,GAAG,CAAC,GAAG;QACzB,GAAGvE,iBAAiB;QACpBQ,QAAQ,EAAEQ,aAAa;QACvB2B,IAAI,EAAEmC,WAAW;QACjBzC,KAAK,EAAEwC,WAAW;QAClB,GAAG5E;MACP,CAAC;IACL;EACJ,CAAC,CAAC;EACF,OAAOQ,oBAAoB;AAC/B;AACA,SAASyD,kBAAkBA,CAAC1C,OAAO,EAAEb,SAAS,EAAE;EAC5C,CAACA,SAAS,CAACsE,GAAG,CAACzD,OAAO,CAAC,IAAIb,SAAS,CAACQ,GAAG,CAACK,OAAO,EAAE,CAAC,CAAC,CAAC;EACrD,OAAOb,SAAS,CAACwE,GAAG,CAAC3D,OAAO,CAAC;AACjC;AACA,SAAS2C,gBAAgBA,CAAC7C,IAAI,EAAEX,SAAS,EAAE;EACvC,IAAI,CAACA,SAAS,CAACW,IAAI,CAAC,EAChBX,SAAS,CAACW,IAAI,CAAC,GAAG,EAAE;EACxB,OAAOX,SAAS,CAACW,IAAI,CAAC;AAC1B;AACA,SAASa,eAAeA,CAACV,SAAS,EAAE;EAChC,OAAOL,KAAK,CAACC,OAAO,CAACI,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;AAC7D;AACA,SAAS+C,kBAAkBA,CAAC9C,UAAU,EAAE6C,GAAG,EAAE;EACzC,OAAO7C,UAAU,IAAIA,UAAU,CAAC6C,GAAG,CAAC,GAC9B;IACE,GAAG7C,UAAU;IACb,GAAGA,UAAU,CAAC6C,GAAG;EACrB,CAAC,GACC;IAAE,GAAG7C;EAAW,CAAC;AAC3B;AACA,MAAM0D,QAAQ,GAAIC,QAAQ,IAAK,OAAOA,QAAQ,KAAK,QAAQ;AAC3D,MAAMrC,sBAAsB,GAAIvB,SAAS,IAAKA,SAAS,CAAC6D,KAAK,CAACF,QAAQ,CAAC;AAEvE,SAAStF,4BAA4B,EAAE0E,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}