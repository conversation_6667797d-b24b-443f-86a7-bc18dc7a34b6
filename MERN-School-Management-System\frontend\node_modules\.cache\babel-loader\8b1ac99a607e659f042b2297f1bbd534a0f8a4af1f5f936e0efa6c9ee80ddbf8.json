{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\pages\\\\EnhancedLoginPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Box, Container, Grid, Typography, TextField, Button, Paper, Avatar, Rating, Card, CardContent, CardMedia, IconButton, AppBar, Toolbar, Fab, Checkbox, FormControlLabel, InputAdornment, CircularProgress, Select, MenuItem, FormControl, InputLabel } from '@mui/material';\nimport { Visibility, VisibilityOff, Facebook, Instagram, YouTube, LinkedIn, Phone, Email, LocationOn, ArrowUpward, AccountCircle, School, Group } from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { loginUser } from '../redux/userRelated/userHandle';\nimport Popup from '../components/Popup';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EnhancedLoginPage = () => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    status,\n    currentUser,\n    response,\n    error,\n    currentRole\n  } = useSelector(state => state.user);\n\n  // Login form states\n  const [selectedRole, setSelectedRole] = useState('Admin');\n  const [showPassword, setShowPassword] = useState(false);\n  const [loader, setLoader] = useState(false);\n  const [showPopup, setShowPopup] = useState(false);\n  const [message, setMessage] = useState(\"\");\n  const [emailError, setEmailError] = useState(false);\n  const [passwordError, setPasswordError] = useState(false);\n  const [rollNumberError, setRollNumberError] = useState(false);\n  const [studentNameError, setStudentNameError] = useState(false);\n\n  // CMS Content states\n  const [cmsContent, setCmsContent] = useState({\n    heroTitle: 'Empowering Minds, Shaping Future Leaders',\n    heroSubtitle: 'Discover a dynamic educational experience with interactive visuals, engaging content, and personalized learning paths designed to inspire and empower students.',\n    schoolName: 'Amar Vidya Mandir',\n    contactInfo: {\n      address: 'Partaj, Anantapuram, AP, India',\n      phone: '7799505005',\n      alternatePhone: '9866358067',\n      email: '<EMAIL>'\n    },\n    testimonials: [{\n      name: 'Aisha Khan',\n      role: 'Student',\n      message: \"Amar Vidya Mandir's interactive platform transformed my learning experience. The engaging visuals and personalized content made complex topics easy to understand. I highly recommend it to any student seeking academic success.\",\n      rating: 5,\n      image: '/api/placeholder/60/60'\n    }],\n    gallery: [{\n      title: 'The Future of Education',\n      image: '/api/placeholder/300/200',\n      category: 'education'\n    }, {\n      title: 'Tips for Parents',\n      image: '/api/placeholder/300/200',\n      category: 'tips'\n    }, {\n      title: 'Effective Study Strategies',\n      image: '/api/placeholder/300/200',\n      category: 'education'\n    }],\n    blogPosts: [{\n      title: 'The Future of Education',\n      excerpt: 'Exploring innovative teaching methods...',\n      date: '25 October 2024',\n      author: 'admin',\n      image: '/api/placeholder/300/200'\n    }, {\n      title: 'Tips for Parents',\n      excerpt: 'How to support your child\\'s learning...',\n      date: '25 October 2024',\n      author: 'admin',\n      image: '/api/placeholder/300/200'\n    }, {\n      title: 'Effective Study Strategies',\n      excerpt: 'Proven methods for academic success...',\n      date: '26 October 2024',\n      author: 'admin',\n      image: '/api/placeholder/300/200'\n    }]\n  });\n  const handleSubmit = event => {\n    event.preventDefault();\n    if (selectedRole === \"Student\") {\n      const rollNum = event.target.rollNumber.value;\n      const studentName = event.target.studentName.value;\n      const password = event.target.password.value;\n      if (!rollNum || !studentName || !password) {\n        if (!rollNum) setRollNumberError(true);\n        if (!studentName) setStudentNameError(true);\n        if (!password) setPasswordError(true);\n        return;\n      }\n      const fields = {\n        rollNum,\n        studentName,\n        password\n      };\n      setLoader(true);\n      dispatch(loginUser(fields, selectedRole));\n    } else {\n      const email = event.target.email.value;\n      const password = event.target.password.value;\n      if (!email || !password) {\n        if (!email) setEmailError(true);\n        if (!password) setPasswordError(true);\n        return;\n      }\n      const fields = {\n        email,\n        password\n      };\n      setLoader(true);\n      dispatch(loginUser(fields, selectedRole));\n    }\n  };\n  const handleInputChange = event => {\n    const {\n      name\n    } = event.target;\n    if (name === 'email') setEmailError(false);\n    if (name === 'password') setPasswordError(false);\n    if (name === 'rollNumber') setRollNumberError(false);\n    if (name === 'studentName') setStudentNameError(false);\n  };\n  useEffect(() => {\n    if (status === 'success' || currentUser !== null) {\n      if (currentRole === 'Admin') {\n        navigate('/Admin/dashboard');\n      } else if (currentRole === 'Student') {\n        navigate('/Student/dashboard');\n      } else if (currentRole === 'Teacher') {\n        navigate('/Teacher/dashboard');\n      }\n    } else if (status === 'failed') {\n      setMessage(response);\n      setShowPopup(true);\n      setLoader(false);\n    } else if (status === 'error') {\n      setMessage(\"Network Error\");\n      setShowPopup(true);\n      setLoader(false);\n    }\n  }, [status, currentRole, navigate, error, response, currentUser]);\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: '#0a0e27'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"static\",\n      sx: {\n        bgcolor: 'transparent',\n        boxShadow: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          justifyContent: 'space-between',\n          py: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            component: \"img\",\n            src: \"/api/placeholder/40/40\",\n            alt: \"Logo\",\n            sx: {\n              width: 40,\n              height: 40\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              color: '#ff6b35',\n              fontWeight: 'bold'\n            },\n            children: cmsContent.schoolName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            sx: {\n              color: 'white'\n            },\n            children: \"HOME\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            sx: {\n              color: 'white'\n            },\n            children: \"ABOUT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            sx: {\n              color: 'white'\n            },\n            children: \"SERVICE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            sx: {\n              bgcolor: '#4f46e5',\n              '&:hover': {\n                bgcolor: '#4338ca'\n              }\n            },\n            children: \"CONTACT US\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      sx: {\n        py: 8\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 4,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h2\",\n              sx: {\n                color: 'white',\n                fontWeight: 'bold',\n                mb: 3,\n                fontSize: {\n                  xs: '2rem',\n                  md: '3.5rem'\n                }\n              },\n              children: cmsContent.heroTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                color: '#94a3b8',\n                mb: 4,\n                lineHeight: 1.6\n              },\n              children: cmsContent.heroSubtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                sx: {\n                  bgcolor: '#4f46e5',\n                  px: 4,\n                  py: 1.5,\n                  '&:hover': {\n                    bgcolor: '#4338ca'\n                  }\n                },\n                children: \"Join\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                sx: {\n                  borderColor: 'white',\n                  color: 'white',\n                  px: 4,\n                  py: 1.5,\n                  '&:hover': {\n                    borderColor: '#4f46e5',\n                    color: '#4f46e5'\n                  }\n                },\n                children: \"Learn\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: 'relative'\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                borderRadius: '20px',\n                p: 4,\n                position: 'relative',\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Paper, {\n                elevation: 24,\n                sx: {\n                  p: 4,\n                  borderRadius: '16px',\n                  bgcolor: 'rgba(255, 255, 255, 0.95)',\n                  backdropFilter: 'blur(10px)',\n                  position: 'relative',\n                  zIndex: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    mb: 2,\n                    color: '#2c2143',\n                    textAlign: 'center',\n                    fontWeight: 'bold'\n                  },\n                  children: \"Login to Your Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    mb: 3,\n                    color: '#64748b',\n                    textAlign: 'center'\n                  },\n                  children: \"Welcome back! Please select your role and enter your details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      mb: 2,\n                      color: '#2c2143',\n                      textAlign: 'center',\n                      fontWeight: 'bold'\n                    },\n                    children: \"Select Your Role\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    container: true,\n                    spacing: 2,\n                    justifyContent: \"center\",\n                    children: [/*#__PURE__*/_jsxDEV(Grid, {\n                      item: true,\n                      xs: 4,\n                      children: /*#__PURE__*/_jsxDEV(Paper, {\n                        elevation: selectedRole === 'Admin' ? 8 : 2,\n                        onClick: () => setSelectedRole('Admin'),\n                        sx: {\n                          p: 2,\n                          textAlign: 'center',\n                          cursor: 'pointer',\n                          bgcolor: selectedRole === 'Admin' ? '#7f56da' : 'white',\n                          color: selectedRole === 'Admin' ? 'white' : '#2c2143',\n                          transition: 'all 0.3s ease',\n                          border: selectedRole === 'Admin' ? '2px solid #7f56da' : '2px solid transparent',\n                          '&:hover': {\n                            transform: 'translateY(-2px)',\n                            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(AccountCircle, {\n                          sx: {\n                            fontSize: 40,\n                            mb: 1\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 351,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          fontWeight: \"bold\",\n                          children: \"Admin\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 352,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 334,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                      item: true,\n                      xs: 4,\n                      children: /*#__PURE__*/_jsxDEV(Paper, {\n                        elevation: selectedRole === 'Student' ? 8 : 2,\n                        onClick: () => setSelectedRole('Student'),\n                        sx: {\n                          p: 2,\n                          textAlign: 'center',\n                          cursor: 'pointer',\n                          bgcolor: selectedRole === 'Student' ? '#7f56da' : 'white',\n                          color: selectedRole === 'Student' ? 'white' : '#2c2143',\n                          transition: 'all 0.3s ease',\n                          border: selectedRole === 'Student' ? '2px solid #7f56da' : '2px solid transparent',\n                          '&:hover': {\n                            transform: 'translateY(-2px)',\n                            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(School, {\n                          sx: {\n                            fontSize: 40,\n                            mb: 1\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 375,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          fontWeight: \"bold\",\n                          children: \"Student\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 376,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 358,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                      item: true,\n                      xs: 4,\n                      children: /*#__PURE__*/_jsxDEV(Paper, {\n                        elevation: selectedRole === 'Teacher' ? 8 : 2,\n                        onClick: () => setSelectedRole('Teacher'),\n                        sx: {\n                          p: 2,\n                          textAlign: 'center',\n                          cursor: 'pointer',\n                          bgcolor: selectedRole === 'Teacher' ? '#7f56da' : 'white',\n                          color: selectedRole === 'Teacher' ? 'white' : '#2c2143',\n                          transition: 'all 0.3s ease',\n                          border: selectedRole === 'Teacher' ? '2px solid #7f56da' : '2px solid transparent',\n                          '&:hover': {\n                            transform: 'translateY(-2px)',\n                            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Group, {\n                          sx: {\n                            fontSize: 40,\n                            mb: 1\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 399,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          fontWeight: \"bold\",\n                          children: \"Teacher\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 400,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 382,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 381,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"form\",\n                  onSubmit: handleSubmit,\n                  children: [role === \"Student\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(TextField, {\n                      margin: \"normal\",\n                      required: true,\n                      fullWidth: true,\n                      id: \"rollNumber\",\n                      label: \"Enter your Roll Number\",\n                      name: \"rollNumber\",\n                      type: \"number\",\n                      error: rollNumberError,\n                      helperText: rollNumberError && 'Roll Number is required',\n                      onChange: handleInputChange,\n                      sx: {\n                        mb: 2\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                      margin: \"normal\",\n                      required: true,\n                      fullWidth: true,\n                      id: \"studentName\",\n                      label: \"Enter your name\",\n                      name: \"studentName\",\n                      error: studentNameError,\n                      helperText: studentNameError && 'Name is required',\n                      onChange: handleInputChange,\n                      sx: {\n                        mb: 2\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(TextField, {\n                    margin: \"normal\",\n                    required: true,\n                    fullWidth: true,\n                    id: \"email\",\n                    label: \"Enter your email\",\n                    name: \"email\",\n                    autoComplete: \"email\",\n                    error: emailError,\n                    helperText: emailError && 'Email is required',\n                    onChange: handleInputChange,\n                    sx: {\n                      mb: 2\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                    margin: \"normal\",\n                    required: true,\n                    fullWidth: true,\n                    name: \"password\",\n                    label: \"Password\",\n                    type: showPassword ? 'text' : 'password',\n                    id: \"password\",\n                    autoComplete: \"current-password\",\n                    error: passwordError,\n                    helperText: passwordError && 'Password is required',\n                    onChange: handleInputChange,\n                    InputProps: {\n                      endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                        position: \"end\",\n                        children: /*#__PURE__*/_jsxDEV(IconButton, {\n                          onClick: () => setShowPassword(!showPassword),\n                          edge: \"end\",\n                          children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 472,\n                            columnNumber: 47\n                          }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 472,\n                            columnNumber: 67\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 468,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 467,\n                        columnNumber: 27\n                      }, this)\n                    },\n                    sx: {\n                      mb: 2\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      mb: 3\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                      control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 482,\n                        columnNumber: 34\n                      }, this),\n                      label: \"Remember me\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"text\",\n                      sx: {\n                        color: '#7f56da'\n                      },\n                      children: \"Forgot password?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 485,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"submit\",\n                    fullWidth: true,\n                    variant: \"contained\",\n                    sx: {\n                      py: 1.5,\n                      bgcolor: '#7f56da',\n                      '&:hover': {\n                        bgcolor: '#6d48c7'\n                      },\n                      borderRadius: '8px',\n                      fontWeight: 'bold'\n                    },\n                    children: loader ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                      size: 24,\n                      color: \"inherit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 503,\n                      columnNumber: 25\n                    }, this) : \"Login\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: 'absolute',\n                  top: '20%',\n                  right: '10%',\n                  width: 60,\n                  height: 60,\n                  bgcolor: '#fbbf24',\n                  borderRadius: '50%',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  zIndex: 1\n                },\n                children: \"\\uD83D\\uDCA1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 8,\n        bgcolor: '#0f172a'\n      },\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"lg\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              color: '#4f46e5',\n              textAlign: 'center',\n              mb: 2,\n              fontWeight: 'bold'\n            },\n            children: \"Our Students\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h3\",\n            sx: {\n              color: 'white',\n              textAlign: 'center',\n              mb: 6,\n              fontWeight: 'bold'\n            },\n            children: \"Testimonials Speak\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            justifyContent: \"center\",\n            children: cmsContent.testimonials.map((testimonial, index) => /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                sx: {\n                  bgcolor: 'rgba(255, 255, 255, 0.05)',\n                  backdropFilter: 'blur(10px)',\n                  border: '1px solid rgba(255, 255, 255, 0.1)',\n                  borderRadius: '16px',\n                  p: 4,\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  src: testimonial.image,\n                  sx: {\n                    width: 80,\n                    height: 80,\n                    mx: 'auto',\n                    mb: 3,\n                    border: '3px solid #4f46e5'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  sx: {\n                    color: 'white',\n                    mb: 1,\n                    fontWeight: 'bold'\n                  },\n                  children: testimonial.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    color: '#94a3b8',\n                    mb: 3,\n                    lineHeight: 1.6\n                  },\n                  children: testimonial.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Rating, {\n                  value: testimonial.rating,\n                  readOnly: true,\n                  sx: {\n                    '& .MuiRating-iconFilled': {\n                      color: '#fbbf24'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 8,\n        bgcolor: '#0a0e27'\n      },\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"lg\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 4,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 3,\n                children: cmsContent.blogPosts.map((post, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 6,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    sx: {\n                      bgcolor: 'rgba(255, 255, 255, 0.05)',\n                      backdropFilter: 'blur(10px)',\n                      border: '1px solid rgba(255, 255, 255, 0.1)',\n                      borderRadius: '16px',\n                      overflow: 'hidden',\n                      transition: 'transform 0.3s ease',\n                      '&:hover': {\n                        transform: 'translateY(-5px)'\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n                      component: \"img\",\n                      height: \"200\",\n                      image: post.image,\n                      alt: post.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 646,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                      sx: {\n                        p: 3\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        sx: {\n                          color: 'white',\n                          mb: 2,\n                          fontWeight: 'bold'\n                        },\n                        children: post.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 653,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          color: '#94a3b8',\n                          mb: 2\n                        },\n                        children: [\"\\uD83D\\uDCC5 \", post.date, \" | by \", post.author]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 659,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 23\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  bgcolor: 'rgba(255, 255, 255, 0.05)',\n                  backdropFilter: 'blur(10px)',\n                  border: '1px solid rgba(255, 255, 255, 0.1)',\n                  borderRadius: '16px',\n                  p: 4,\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    color: '#4f46e5',\n                    mb: 2,\n                    fontWeight: 'bold'\n                  },\n                  children: \"Our Insights\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    color: 'white',\n                    mb: 3,\n                    fontWeight: 'bold'\n                  },\n                  children: \"Explore Our Blog\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    color: '#94a3b8',\n                    mb: 4,\n                    lineHeight: 1.6\n                  },\n                  children: \"Dive into our blog for insightful articles, expert tips, and the latest trends in education. Stay informed, inspired, and empowered on your journey to academic success and personal growth.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"contained\",\n                  sx: {\n                    bgcolor: '#4f46e5',\n                    px: 4,\n                    py: 1.5,\n                    '&:hover': {\n                      bgcolor: '#4338ca'\n                    }\n                  },\n                  children: \"See\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 8,\n        bgcolor: '#0f172a'\n      },\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"lg\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              color: '#4f46e5',\n              textAlign: 'center',\n              mb: 2\n            },\n            children: \"Get In Touch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h3\",\n            sx: {\n              color: 'white',\n              textAlign: 'center',\n              mb: 6,\n              fontWeight: 'bold'\n            },\n            children: \"Contact Us Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 4,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 3,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 6,\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    fullWidth: true,\n                    label: \"First name\",\n                    variant: \"outlined\",\n                    sx: {\n                      '& .MuiOutlinedInput-root': {\n                        bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        '& fieldset': {\n                          borderColor: 'rgba(255, 255, 255, 0.2)'\n                        },\n                        '&:hover fieldset': {\n                          borderColor: 'rgba(255, 255, 255, 0.3)'\n                        },\n                        '&.Mui-focused fieldset': {\n                          borderColor: '#4f46e5'\n                        }\n                      },\n                      '& .MuiInputLabel-root': {\n                        color: '#94a3b8'\n                      },\n                      '& .MuiOutlinedInput-input': {\n                        color: 'white'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 6,\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    fullWidth: true,\n                    label: \"Email\",\n                    variant: \"outlined\",\n                    sx: {\n                      '& .MuiOutlinedInput-root': {\n                        bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        '& fieldset': {\n                          borderColor: 'rgba(255, 255, 255, 0.2)'\n                        },\n                        '&:hover fieldset': {\n                          borderColor: 'rgba(255, 255, 255, 0.3)'\n                        },\n                        '&.Mui-focused fieldset': {\n                          borderColor: '#4f46e5'\n                        }\n                      },\n                      '& .MuiInputLabel-root': {\n                        color: '#94a3b8'\n                      },\n                      '& .MuiOutlinedInput-input': {\n                        color: 'white'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 6,\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    fullWidth: true,\n                    label: \"Phone\",\n                    variant: \"outlined\",\n                    sx: {\n                      '& .MuiOutlinedInput-root': {\n                        bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        '& fieldset': {\n                          borderColor: 'rgba(255, 255, 255, 0.2)'\n                        },\n                        '&:hover fieldset': {\n                          borderColor: 'rgba(255, 255, 255, 0.3)'\n                        },\n                        '&.Mui-focused fieldset': {\n                          borderColor: '#4f46e5'\n                        }\n                      },\n                      '& .MuiInputLabel-root': {\n                        color: '#94a3b8'\n                      },\n                      '& .MuiOutlinedInput-input': {\n                        color: 'white'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 784,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 6,\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    fullWidth: true,\n                    label: \"Subject\",\n                    variant: \"outlined\",\n                    sx: {\n                      '& .MuiOutlinedInput-root': {\n                        bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        '& fieldset': {\n                          borderColor: 'rgba(255, 255, 255, 0.2)'\n                        },\n                        '&:hover fieldset': {\n                          borderColor: 'rgba(255, 255, 255, 0.3)'\n                        },\n                        '&.Mui-focused fieldset': {\n                          borderColor: '#4f46e5'\n                        }\n                      },\n                      '& .MuiInputLabel-root': {\n                        color: '#94a3b8'\n                      },\n                      '& .MuiOutlinedInput-input': {\n                        color: 'white'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    fullWidth: true,\n                    multiline: true,\n                    rows: 4,\n                    label: \"Message\",\n                    variant: \"outlined\",\n                    sx: {\n                      '& .MuiOutlinedInput-root': {\n                        bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        '& fieldset': {\n                          borderColor: 'rgba(255, 255, 255, 0.2)'\n                        },\n                        '&:hover fieldset': {\n                          borderColor: 'rgba(255, 255, 255, 0.3)'\n                        },\n                        '&.Mui-focused fieldset': {\n                          borderColor: '#4f46e5'\n                        }\n                      },\n                      '& .MuiInputLabel-root': {\n                        color: '#94a3b8'\n                      },\n                      '& .MuiOutlinedInput-input': {\n                        color: 'white'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 818,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 817,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                    control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                      sx: {\n                        color: '#94a3b8'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 838,\n                      columnNumber: 32\n                    }, this),\n                    label: \"You agree to our friendly policy\",\n                    sx: {\n                      color: '#94a3b8'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    fullWidth: true,\n                    variant: \"contained\",\n                    sx: {\n                      bgcolor: '#4f46e5',\n                      py: 1.5,\n                      '&:hover': {\n                        bgcolor: '#4338ca'\n                      }\n                    },\n                    children: \"Submit Form\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 844,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '16px',\n                  p: 4,\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    color: 'white',\n                    mb: 4,\n                    fontWeight: 'bold'\n                  },\n                  children: \"Get in touch with us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 871,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(LocationOn, {\n                      sx: {\n                        color: 'white'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 879,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      sx: {\n                        color: 'white'\n                      },\n                      children: cmsContent.contactInfo.address\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 880,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 878,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Phone, {\n                      sx: {\n                        color: 'white'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 885,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      sx: {\n                        color: 'white'\n                      },\n                      children: cmsContent.contactInfo.phone\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 886,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 884,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Phone, {\n                      sx: {\n                        color: 'white'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 891,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      sx: {\n                        color: 'white'\n                      },\n                      children: cmsContent.contactInfo.alternatePhone\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 892,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Email, {\n                      sx: {\n                        color: 'white'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 897,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      sx: {\n                        color: 'white'\n                      },\n                      children: cmsContent.contactInfo.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 898,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 896,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 877,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 721,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 720,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 6,\n        bgcolor: '#0a0e27',\n        borderTop: '1px solid rgba(255, 255, 255, 0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"lg\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2,\n                mb: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                component: \"img\",\n                src: \"/api/placeholder/40/40\",\n                alt: \"Logo\",\n                sx: {\n                  width: 40,\n                  height: 40\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 916,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  color: '#ff6b35',\n                  fontWeight: 'bold'\n                },\n                children: cmsContent.schoolName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                color: '#94a3b8',\n                mb: 3,\n                lineHeight: 1.6\n              },\n              children: \"Amar Vidya Mandir: Nurturing young minds, fostering growth, and inspiring a lifelong love for learning. Excellence in education.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                sx: {\n                  color: '#94a3b8',\n                  '&:hover': {\n                    color: '#4f46e5'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(Facebook, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 930,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                sx: {\n                  color: '#94a3b8',\n                  '&:hover': {\n                    color: '#4f46e5'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(Instagram, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 934,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 933,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                sx: {\n                  color: '#94a3b8',\n                  '&:hover': {\n                    color: '#4f46e5'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(YouTube, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                sx: {\n                  color: '#94a3b8',\n                  '&:hover': {\n                    color: '#4f46e5'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(LinkedIn, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 939,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 914,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                color: 'white',\n                mb: 3,\n                fontWeight: 'bold'\n              },\n              children: \"QUICK LINKS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                sx: {\n                  color: '#94a3b8',\n                  justifyContent: 'flex-start',\n                  '&:hover': {\n                    color: '#4f46e5'\n                  }\n                },\n                children: \"ABOUT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                sx: {\n                  color: '#94a3b8',\n                  justifyContent: 'flex-start',\n                  '&:hover': {\n                    color: '#4f46e5'\n                  }\n                },\n                children: \"SERVICE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 953,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                sx: {\n                  color: '#94a3b8',\n                  justifyContent: 'flex-start',\n                  '&:hover': {\n                    color: '#4f46e5'\n                  }\n                },\n                children: \"CONTACT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 956,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 949,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 945,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                color: 'white',\n                mb: 3,\n                fontWeight: 'bold'\n              },\n              children: \"CONTACT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(LocationOn, {\n                  sx: {\n                    color: '#4f46e5'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 968,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    color: '#94a3b8'\n                  },\n                  children: cmsContent.contactInfo.address\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 969,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 967,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Phone, {\n                  sx: {\n                    color: '#4f46e5'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 974,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    color: '#94a3b8'\n                  },\n                  children: cmsContent.contactInfo.phone\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 975,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 973,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Phone, {\n                  sx: {\n                    color: '#4f46e5'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 980,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    color: '#94a3b8'\n                  },\n                  children: cmsContent.contactInfo.alternatePhone\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 981,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 979,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Email, {\n                  sx: {\n                    color: '#4f46e5'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 986,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    color: '#94a3b8'\n                  },\n                  children: cmsContent.contactInfo.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 987,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 985,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 913,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 6,\n            pt: 4,\n            borderTop: '1px solid rgba(255, 255, 255, 0.1)',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: '#94a3b8'\n            },\n            children: [\"\\xA9 2024 \", cmsContent.schoolName, \". All rights reserved.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 912,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 911,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Popup, {\n      message: message,\n      setShowPopup: setShowPopup,\n      showPopup: showPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1010,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Fab, {\n      color: \"primary\",\n      size: \"small\",\n      onClick: scrollToTop,\n      sx: {\n        position: 'fixed',\n        bottom: 16,\n        right: 16,\n        bgcolor: '#4f46e5',\n        '&:hover': {\n          bgcolor: '#4338ca'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(ArrowUpward, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1025,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1013,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedLoginPage, \"rn45sOfeaMJ2DDW87rKA2nMOD/M=\", false, function () {\n  return [useDispatch, useNavigate, useSelector];\n});\n_c = EnhancedLoginPage;\nexport default EnhancedLoginPage;\nvar _c;\n$RefreshReg$(_c, \"EnhancedLoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useDispatch", "useSelector", "Box", "Container", "Grid", "Typography", "TextField", "<PERSON><PERSON>", "Paper", "Avatar", "Rating", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "IconButton", "AppBar", "<PERSON><PERSON><PERSON>", "Fab", "Checkbox", "FormControlLabel", "InputAdornment", "CircularProgress", "Select", "MenuItem", "FormControl", "InputLabel", "Visibility", "VisibilityOff", "Facebook", "Instagram", "YouTube", "LinkedIn", "Phone", "Email", "LocationOn", "ArrowUpward", "AccountCircle", "School", "Group", "motion", "loginUser", "Popup", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EnhancedLoginPage", "_s", "dispatch", "navigate", "status", "currentUser", "response", "error", "currentRole", "state", "user", "selectedR<PERSON>", "setSelectedRole", "showPassword", "setShowPassword", "loader", "<PERSON><PERSON><PERSON><PERSON>", "showPopup", "setShowPopup", "message", "setMessage", "emailError", "setEmailError", "passwordError", "setPasswordError", "rollNumberError", "setRollNumberError", "studentNameError", "setStudentNameError", "cmsContent", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "heroSubtitle", "schoolName", "contactInfo", "address", "phone", "alternatePhone", "email", "testimonials", "name", "role", "rating", "image", "gallery", "title", "category", "blogPosts", "excerpt", "date", "author", "handleSubmit", "event", "preventDefault", "rollNum", "target", "rollNumber", "value", "studentName", "password", "fields", "handleInputChange", "scrollToTop", "window", "scrollTo", "top", "behavior", "sx", "minHeight", "bgcolor", "children", "position", "boxShadow", "justifyContent", "py", "display", "alignItems", "gap", "component", "src", "alt", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "fontWeight", "max<PERSON><PERSON><PERSON>", "container", "spacing", "item", "xs", "md", "div", "initial", "opacity", "x", "animate", "transition", "duration", "mb", "fontSize", "lineHeight", "px", "borderColor", "background", "borderRadius", "p", "overflow", "elevation", "<PERSON><PERSON>ilter", "zIndex", "textAlign", "onClick", "cursor", "border", "transform", "onSubmit", "margin", "required", "fullWidth", "id", "label", "type", "helperText", "onChange", "autoComplete", "InputProps", "endAdornment", "edge", "control", "size", "right", "y", "whileInView", "viewport", "once", "map", "testimonial", "index", "mx", "readOnly", "post", "sm", "multiline", "rows", "flexDirection", "borderTop", "mt", "pt", "bottom", "_c", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/pages/EnhancedLoginPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport {\n  Box,\n  Container,\n  Grid,\n  Typography,\n  TextField,\n  Button,\n  Paper,\n  Avatar,\n  Rating,\n  Card,\n  CardContent,\n  CardMedia,\n  IconButton,\n  AppBar,\n  Toolbar,\n  Fab,\n  Checkbox,\n  FormControlLabel,\n  InputAdornment,\n  CircularProgress,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel\n} from '@mui/material';\nimport {\n  Visibility,\n  VisibilityOff,\n  Facebook,\n  Instagram,\n  YouTube,\n  LinkedIn,\n  Phone,\n  Email,\n  LocationOn,\n  ArrowUpward,\n  AccountCircle,\n  School,\n  Group\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { loginUser } from '../redux/userRelated/userHandle';\nimport Popup from '../components/Popup';\n\nconst EnhancedLoginPage = () => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const { status, currentUser, response, error, currentRole } = useSelector(state => state.user);\n\n  // Login form states\n  const [selectedRole, setSelectedRole] = useState('Admin');\n  const [showPassword, setShowPassword] = useState(false);\n  const [loader, setLoader] = useState(false);\n  const [showPopup, setShowPopup] = useState(false);\n  const [message, setMessage] = useState(\"\");\n  const [emailError, setEmailError] = useState(false);\n  const [passwordError, setPasswordError] = useState(false);\n  const [rollNumberError, setRollNumberError] = useState(false);\n  const [studentNameError, setStudentNameError] = useState(false);\n\n  // CMS Content states\n  const [cmsContent, setCmsContent] = useState({\n    heroTitle: 'Empowering Minds, Shaping Future Leaders',\n    heroSubtitle: 'Discover a dynamic educational experience with interactive visuals, engaging content, and personalized learning paths designed to inspire and empower students.',\n    schoolName: 'Amar Vidya Mandir',\n    contactInfo: {\n      address: 'Partaj, Anantapuram, AP, India',\n      phone: '7799505005',\n      alternatePhone: '9866358067',\n      email: '<EMAIL>'\n    },\n    testimonials: [\n      {\n        name: 'Aisha Khan',\n        role: 'Student',\n        message: \"Amar Vidya Mandir's interactive platform transformed my learning experience. The engaging visuals and personalized content made complex topics easy to understand. I highly recommend it to any student seeking academic success.\",\n        rating: 5,\n        image: '/api/placeholder/60/60'\n      }\n    ],\n    gallery: [\n      { title: 'The Future of Education', image: '/api/placeholder/300/200', category: 'education' },\n      { title: 'Tips for Parents', image: '/api/placeholder/300/200', category: 'tips' },\n      { title: 'Effective Study Strategies', image: '/api/placeholder/300/200', category: 'education' }\n    ],\n    blogPosts: [\n      {\n        title: 'The Future of Education',\n        excerpt: 'Exploring innovative teaching methods...',\n        date: '25 October 2024',\n        author: 'admin',\n        image: '/api/placeholder/300/200'\n      },\n      {\n        title: 'Tips for Parents',\n        excerpt: 'How to support your child\\'s learning...',\n        date: '25 October 2024',\n        author: 'admin',\n        image: '/api/placeholder/300/200'\n      },\n      {\n        title: 'Effective Study Strategies',\n        excerpt: 'Proven methods for academic success...',\n        date: '26 October 2024',\n        author: 'admin',\n        image: '/api/placeholder/300/200'\n      }\n    ]\n  });\n\n  const handleSubmit = (event) => {\n    event.preventDefault();\n\n    if (selectedRole === \"Student\") {\n      const rollNum = event.target.rollNumber.value;\n      const studentName = event.target.studentName.value;\n      const password = event.target.password.value;\n\n      if (!rollNum || !studentName || !password) {\n        if (!rollNum) setRollNumberError(true);\n        if (!studentName) setStudentNameError(true);\n        if (!password) setPasswordError(true);\n        return;\n      }\n      const fields = { rollNum, studentName, password };\n      setLoader(true);\n      dispatch(loginUser(fields, selectedRole));\n    } else {\n      const email = event.target.email.value;\n      const password = event.target.password.value;\n\n      if (!email || !password) {\n        if (!email) setEmailError(true);\n        if (!password) setPasswordError(true);\n        return;\n      }\n\n      const fields = { email, password };\n      setLoader(true);\n      dispatch(loginUser(fields, selectedRole));\n    }\n  };\n\n  const handleInputChange = (event) => {\n    const { name } = event.target;\n    if (name === 'email') setEmailError(false);\n    if (name === 'password') setPasswordError(false);\n    if (name === 'rollNumber') setRollNumberError(false);\n    if (name === 'studentName') setStudentNameError(false);\n  };\n\n  useEffect(() => {\n    if (status === 'success' || currentUser !== null) {\n      if (currentRole === 'Admin') {\n        navigate('/Admin/dashboard');\n      } else if (currentRole === 'Student') {\n        navigate('/Student/dashboard');\n      } else if (currentRole === 'Teacher') {\n        navigate('/Teacher/dashboard');\n      }\n    } else if (status === 'failed') {\n      setMessage(response);\n      setShowPopup(true);\n      setLoader(false);\n    } else if (status === 'error') {\n      setMessage(\"Network Error\");\n      setShowPopup(true);\n      setLoader(false);\n    }\n  }, [status, currentRole, navigate, error, response, currentUser]);\n\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  return (\n    <Box sx={{ minHeight: '100vh', bgcolor: '#0a0e27' }}>\n      {/* Navigation Bar */}\n      <AppBar position=\"static\" sx={{ bgcolor: 'transparent', boxShadow: 'none' }}>\n        <Toolbar sx={{ justifyContent: 'space-between', py: 2 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n            <Box\n              component=\"img\"\n              src=\"/api/placeholder/40/40\"\n              alt=\"Logo\"\n              sx={{ width: 40, height: 40 }}\n            />\n            <Typography variant=\"h6\" sx={{ color: '#ff6b35', fontWeight: 'bold' }}>\n              {cmsContent.schoolName}\n            </Typography>\n          </Box>\n          <Box sx={{ display: 'flex', gap: 3 }}>\n            <Button sx={{ color: 'white' }}>HOME</Button>\n            <Button sx={{ color: 'white' }}>ABOUT</Button>\n            <Button sx={{ color: 'white' }}>SERVICE</Button>\n            <Button\n              variant=\"contained\"\n              sx={{\n                bgcolor: '#4f46e5',\n                '&:hover': { bgcolor: '#4338ca' }\n              }}\n            >\n              CONTACT US\n            </Button>\n          </Box>\n        </Toolbar>\n      </AppBar>\n\n      {/* Hero Section */}\n      <Container maxWidth=\"xl\" sx={{ py: 8 }}>\n        <Grid container spacing={4} alignItems=\"center\">\n          {/* Left Side - Content */}\n          <Grid item xs={12} md={6}>\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n            >\n              <Typography\n                variant=\"h2\"\n                sx={{\n                  color: 'white',\n                  fontWeight: 'bold',\n                  mb: 3,\n                  fontSize: { xs: '2rem', md: '3.5rem' }\n                }}\n              >\n                {cmsContent.heroTitle}\n              </Typography>\n              <Typography\n                variant=\"h6\"\n                sx={{\n                  color: '#94a3b8',\n                  mb: 4,\n                  lineHeight: 1.6\n                }}\n              >\n                {cmsContent.heroSubtitle}\n              </Typography>\n              <Box sx={{ display: 'flex', gap: 2 }}>\n                <Button\n                  variant=\"contained\"\n                  sx={{\n                    bgcolor: '#4f46e5',\n                    px: 4,\n                    py: 1.5,\n                    '&:hover': { bgcolor: '#4338ca' }\n                  }}\n                >\n                  Join\n                </Button>\n                <Button\n                  variant=\"outlined\"\n                  sx={{\n                    borderColor: 'white',\n                    color: 'white',\n                    px: 4,\n                    py: 1.5,\n                    '&:hover': { borderColor: '#4f46e5', color: '#4f46e5' }\n                  }}\n                >\n                  Learn\n                </Button>\n              </Box>\n            </motion.div>\n          </Grid>\n\n          {/* Right Side - Login Form + Illustration */}\n          <Grid item xs={12} md={6}>\n            <Box sx={{ position: 'relative' }}>\n              {/* Background Illustration */}\n              <Box\n                sx={{\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '20px',\n                  p: 4,\n                  position: 'relative',\n                  overflow: 'hidden'\n                }}\n              >\n                {/* Login Form */}\n                <Paper\n                  elevation={24}\n                  sx={{\n                    p: 4,\n                    borderRadius: '16px',\n                    bgcolor: 'rgba(255, 255, 255, 0.95)',\n                    backdropFilter: 'blur(10px)',\n                    position: 'relative',\n                    zIndex: 2\n                  }}\n                >\n                  <Typography\n                    variant=\"h4\"\n                    sx={{\n                      mb: 2,\n                      color: '#2c2143',\n                      textAlign: 'center',\n                      fontWeight: 'bold'\n                    }}\n                  >\n                    Login to Your Account\n                  </Typography>\n                  <Typography\n                    variant=\"body1\"\n                    sx={{\n                      mb: 3,\n                      color: '#64748b',\n                      textAlign: 'center'\n                    }}\n                  >\n                    Welcome back! Please select your role and enter your details\n                  </Typography>\n\n                  {/* Role Selection */}\n                  <Box sx={{ mb: 3 }}>\n                    <Typography\n                      variant=\"h6\"\n                      sx={{\n                        mb: 2,\n                        color: '#2c2143',\n                        textAlign: 'center',\n                        fontWeight: 'bold'\n                      }}\n                    >\n                      Select Your Role\n                    </Typography>\n                    <Grid container spacing={2} justifyContent=\"center\">\n                      <Grid item xs={4}>\n                        <Paper\n                          elevation={selectedRole === 'Admin' ? 8 : 2}\n                          onClick={() => setSelectedRole('Admin')}\n                          sx={{\n                            p: 2,\n                            textAlign: 'center',\n                            cursor: 'pointer',\n                            bgcolor: selectedRole === 'Admin' ? '#7f56da' : 'white',\n                            color: selectedRole === 'Admin' ? 'white' : '#2c2143',\n                            transition: 'all 0.3s ease',\n                            border: selectedRole === 'Admin' ? '2px solid #7f56da' : '2px solid transparent',\n                            '&:hover': {\n                              transform: 'translateY(-2px)',\n                              boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'\n                            }\n                          }}\n                        >\n                          <AccountCircle sx={{ fontSize: 40, mb: 1 }} />\n                          <Typography variant=\"body2\" fontWeight=\"bold\">\n                            Admin\n                          </Typography>\n                        </Paper>\n                      </Grid>\n                      <Grid item xs={4}>\n                        <Paper\n                          elevation={selectedRole === 'Student' ? 8 : 2}\n                          onClick={() => setSelectedRole('Student')}\n                          sx={{\n                            p: 2,\n                            textAlign: 'center',\n                            cursor: 'pointer',\n                            bgcolor: selectedRole === 'Student' ? '#7f56da' : 'white',\n                            color: selectedRole === 'Student' ? 'white' : '#2c2143',\n                            transition: 'all 0.3s ease',\n                            border: selectedRole === 'Student' ? '2px solid #7f56da' : '2px solid transparent',\n                            '&:hover': {\n                              transform: 'translateY(-2px)',\n                              boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'\n                            }\n                          }}\n                        >\n                          <School sx={{ fontSize: 40, mb: 1 }} />\n                          <Typography variant=\"body2\" fontWeight=\"bold\">\n                            Student\n                          </Typography>\n                        </Paper>\n                      </Grid>\n                      <Grid item xs={4}>\n                        <Paper\n                          elevation={selectedRole === 'Teacher' ? 8 : 2}\n                          onClick={() => setSelectedRole('Teacher')}\n                          sx={{\n                            p: 2,\n                            textAlign: 'center',\n                            cursor: 'pointer',\n                            bgcolor: selectedRole === 'Teacher' ? '#7f56da' : 'white',\n                            color: selectedRole === 'Teacher' ? 'white' : '#2c2143',\n                            transition: 'all 0.3s ease',\n                            border: selectedRole === 'Teacher' ? '2px solid #7f56da' : '2px solid transparent',\n                            '&:hover': {\n                              transform: 'translateY(-2px)',\n                              boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'\n                            }\n                          }}\n                        >\n                          <Group sx={{ fontSize: 40, mb: 1 }} />\n                          <Typography variant=\"body2\" fontWeight=\"bold\">\n                            Teacher\n                          </Typography>\n                        </Paper>\n                      </Grid>\n                    </Grid>\n                  </Box>\n\n                  <Box component=\"form\" onSubmit={handleSubmit}>\n                    {role === \"Student\" ? (\n                      <>\n                        <TextField\n                          margin=\"normal\"\n                          required\n                          fullWidth\n                          id=\"rollNumber\"\n                          label=\"Enter your Roll Number\"\n                          name=\"rollNumber\"\n                          type=\"number\"\n                          error={rollNumberError}\n                          helperText={rollNumberError && 'Roll Number is required'}\n                          onChange={handleInputChange}\n                          sx={{ mb: 2 }}\n                        />\n                        <TextField\n                          margin=\"normal\"\n                          required\n                          fullWidth\n                          id=\"studentName\"\n                          label=\"Enter your name\"\n                          name=\"studentName\"\n                          error={studentNameError}\n                          helperText={studentNameError && 'Name is required'}\n                          onChange={handleInputChange}\n                          sx={{ mb: 2 }}\n                        />\n                      </>\n                    ) : (\n                      <TextField\n                        margin=\"normal\"\n                        required\n                        fullWidth\n                        id=\"email\"\n                        label=\"Enter your email\"\n                        name=\"email\"\n                        autoComplete=\"email\"\n                        error={emailError}\n                        helperText={emailError && 'Email is required'}\n                        onChange={handleInputChange}\n                        sx={{ mb: 2 }}\n                      />\n                    )}\n\n                    <TextField\n                      margin=\"normal\"\n                      required\n                      fullWidth\n                      name=\"password\"\n                      label=\"Password\"\n                      type={showPassword ? 'text' : 'password'}\n                      id=\"password\"\n                      autoComplete=\"current-password\"\n                      error={passwordError}\n                      helperText={passwordError && 'Password is required'}\n                      onChange={handleInputChange}\n                      InputProps={{\n                        endAdornment: (\n                          <InputAdornment position=\"end\">\n                            <IconButton\n                              onClick={() => setShowPassword(!showPassword)}\n                              edge=\"end\"\n                            >\n                              {showPassword ? <VisibilityOff /> : <Visibility />}\n                            </IconButton>\n                          </InputAdornment>\n                        ),\n                      }}\n                      sx={{ mb: 2 }}\n                    />\n\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>\n                      <FormControlLabel\n                        control={<Checkbox color=\"primary\" />}\n                        label=\"Remember me\"\n                      />\n                      <Button variant=\"text\" sx={{ color: '#7f56da' }}>\n                        Forgot password?\n                      </Button>\n                    </Box>\n\n                    <Button\n                      type=\"submit\"\n                      fullWidth\n                      variant=\"contained\"\n                      sx={{\n                        py: 1.5,\n                        bgcolor: '#7f56da',\n                        '&:hover': { bgcolor: '#6d48c7' },\n                        borderRadius: '8px',\n                        fontWeight: 'bold'\n                      }}\n                    >\n                      {loader ? (\n                        <CircularProgress size={24} color=\"inherit\" />\n                      ) : (\n                        \"Login\"\n                      )}\n                    </Button>\n                  </Box>\n                </Paper>\n\n                {/* Decorative Elements */}\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    top: '20%',\n                    right: '10%',\n                    width: 60,\n                    height: 60,\n                    bgcolor: '#fbbf24',\n                    borderRadius: '50%',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1\n                  }}\n                >\n                  💡\n                </Box>\n              </Box>\n            </Box>\n          </Grid>\n        </Grid>\n      </Container>\n\n      {/* Testimonials Section */}\n      <Box sx={{ py: 8, bgcolor: '#0f172a' }}>\n        <Container maxWidth=\"lg\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Typography\n              variant=\"h4\"\n              sx={{\n                color: '#4f46e5',\n                textAlign: 'center',\n                mb: 2,\n                fontWeight: 'bold'\n              }}\n            >\n              Our Students\n            </Typography>\n            <Typography\n              variant=\"h3\"\n              sx={{\n                color: 'white',\n                textAlign: 'center',\n                mb: 6,\n                fontWeight: 'bold'\n              }}\n            >\n              Testimonials Speak\n            </Typography>\n\n            <Grid container justifyContent=\"center\">\n              {cmsContent.testimonials.map((testimonial, index) => (\n                <Grid item xs={12} md={8} key={index}>\n                  <Card\n                    sx={{\n                      bgcolor: 'rgba(255, 255, 255, 0.05)',\n                      backdropFilter: 'blur(10px)',\n                      border: '1px solid rgba(255, 255, 255, 0.1)',\n                      borderRadius: '16px',\n                      p: 4,\n                      textAlign: 'center'\n                    }}\n                  >\n                    <Avatar\n                      src={testimonial.image}\n                      sx={{\n                        width: 80,\n                        height: 80,\n                        mx: 'auto',\n                        mb: 3,\n                        border: '3px solid #4f46e5'\n                      }}\n                    />\n                    <Typography\n                      variant=\"h5\"\n                      sx={{ color: 'white', mb: 1, fontWeight: 'bold' }}\n                    >\n                      {testimonial.name}\n                    </Typography>\n                    <Typography\n                      variant=\"body1\"\n                      sx={{ color: '#94a3b8', mb: 3, lineHeight: 1.6 }}\n                    >\n                      {testimonial.message}\n                    </Typography>\n                    <Rating\n                      value={testimonial.rating}\n                      readOnly\n                      sx={{\n                        '& .MuiRating-iconFilled': {\n                          color: '#fbbf24'\n                        }\n                      }}\n                    />\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </motion.div>\n        </Container>\n      </Box>\n\n      {/* Blog Section */}\n      <Box sx={{ py: 8, bgcolor: '#0a0e27' }}>\n        <Container maxWidth=\"lg\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Grid container spacing={4}>\n              <Grid item xs={12} md={8}>\n                <Grid container spacing={3}>\n                  {cmsContent.blogPosts.map((post, index) => (\n                    <Grid item xs={12} sm={6} key={index}>\n                      <Card\n                        sx={{\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                          backdropFilter: 'blur(10px)',\n                          border: '1px solid rgba(255, 255, 255, 0.1)',\n                          borderRadius: '16px',\n                          overflow: 'hidden',\n                          transition: 'transform 0.3s ease',\n                          '&:hover': {\n                            transform: 'translateY(-5px)'\n                          }\n                        }}\n                      >\n                        <CardMedia\n                          component=\"img\"\n                          height=\"200\"\n                          image={post.image}\n                          alt={post.title}\n                        />\n                        <CardContent sx={{ p: 3 }}>\n                          <Typography\n                            variant=\"h6\"\n                            sx={{ color: 'white', mb: 2, fontWeight: 'bold' }}\n                          >\n                            {post.title}\n                          </Typography>\n                          <Typography\n                            variant=\"body2\"\n                            sx={{ color: '#94a3b8', mb: 2 }}\n                          >\n                            📅 {post.date} | by {post.author}\n                          </Typography>\n                        </CardContent>\n                      </Card>\n                    </Grid>\n                  ))}\n                </Grid>\n              </Grid>\n\n              <Grid item xs={12} md={4}>\n                <Box\n                  sx={{\n                    bgcolor: 'rgba(255, 255, 255, 0.05)',\n                    backdropFilter: 'blur(10px)',\n                    border: '1px solid rgba(255, 255, 255, 0.1)',\n                    borderRadius: '16px',\n                    p: 4,\n                    textAlign: 'center'\n                  }}\n                >\n                  <Typography\n                    variant=\"h6\"\n                    sx={{ color: '#4f46e5', mb: 2, fontWeight: 'bold' }}\n                  >\n                    Our Insights\n                  </Typography>\n                  <Typography\n                    variant=\"h4\"\n                    sx={{ color: 'white', mb: 3, fontWeight: 'bold' }}\n                  >\n                    Explore Our Blog\n                  </Typography>\n                  <Typography\n                    variant=\"body1\"\n                    sx={{ color: '#94a3b8', mb: 4, lineHeight: 1.6 }}\n                  >\n                    Dive into our blog for insightful articles, expert tips, and the latest trends in education. Stay informed, inspired, and empowered on your journey to academic success and personal growth.\n                  </Typography>\n                  <Button\n                    variant=\"contained\"\n                    sx={{\n                      bgcolor: '#4f46e5',\n                      px: 4,\n                      py: 1.5,\n                      '&:hover': { bgcolor: '#4338ca' }\n                    }}\n                  >\n                    See\n                  </Button>\n                </Box>\n              </Grid>\n            </Grid>\n          </motion.div>\n        </Container>\n      </Box>\n\n      {/* Contact Section */}\n      <Box sx={{ py: 8, bgcolor: '#0f172a' }}>\n        <Container maxWidth=\"lg\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Typography\n              variant=\"h6\"\n              sx={{ color: '#4f46e5', textAlign: 'center', mb: 2 }}\n            >\n              Get In Touch\n            </Typography>\n            <Typography\n              variant=\"h3\"\n              sx={{\n                color: 'white',\n                textAlign: 'center',\n                mb: 6,\n                fontWeight: 'bold'\n              }}\n            >\n              Contact Us Today\n            </Typography>\n\n            <Grid container spacing={4}>\n              <Grid item xs={12} md={6}>\n                <Grid container spacing={3}>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"First name\"\n                      variant=\"outlined\"\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                          '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.2)' },\n                          '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.3)' },\n                          '&.Mui-focused fieldset': { borderColor: '#4f46e5' }\n                        },\n                        '& .MuiInputLabel-root': { color: '#94a3b8' },\n                        '& .MuiOutlinedInput-input': { color: 'white' }\n                      }}\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Email\"\n                      variant=\"outlined\"\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                          '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.2)' },\n                          '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.3)' },\n                          '&.Mui-focused fieldset': { borderColor: '#4f46e5' }\n                        },\n                        '& .MuiInputLabel-root': { color: '#94a3b8' },\n                        '& .MuiOutlinedInput-input': { color: 'white' }\n                      }}\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Phone\"\n                      variant=\"outlined\"\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                          '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.2)' },\n                          '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.3)' },\n                          '&.Mui-focused fieldset': { borderColor: '#4f46e5' }\n                        },\n                        '& .MuiInputLabel-root': { color: '#94a3b8' },\n                        '& .MuiOutlinedInput-input': { color: 'white' }\n                      }}\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Subject\"\n                      variant=\"outlined\"\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                          '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.2)' },\n                          '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.3)' },\n                          '&.Mui-focused fieldset': { borderColor: '#4f46e5' }\n                        },\n                        '& .MuiInputLabel-root': { color: '#94a3b8' },\n                        '& .MuiOutlinedInput-input': { color: 'white' }\n                      }}\n                    />\n                  </Grid>\n                  <Grid item xs={12}>\n                    <TextField\n                      fullWidth\n                      multiline\n                      rows={4}\n                      label=\"Message\"\n                      variant=\"outlined\"\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                          '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.2)' },\n                          '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.3)' },\n                          '&.Mui-focused fieldset': { borderColor: '#4f46e5' }\n                        },\n                        '& .MuiInputLabel-root': { color: '#94a3b8' },\n                        '& .MuiOutlinedInput-input': { color: 'white' }\n                      }}\n                    />\n                  </Grid>\n                  <Grid item xs={12}>\n                    <FormControlLabel\n                      control={<Checkbox sx={{ color: '#94a3b8' }} />}\n                      label=\"You agree to our friendly policy\"\n                      sx={{ color: '#94a3b8' }}\n                    />\n                  </Grid>\n                  <Grid item xs={12}>\n                    <Button\n                      fullWidth\n                      variant=\"contained\"\n                      sx={{\n                        bgcolor: '#4f46e5',\n                        py: 1.5,\n                        '&:hover': { bgcolor: '#4338ca' }\n                      }}\n                    >\n                      Submit Form\n                    </Button>\n                  </Grid>\n                </Grid>\n              </Grid>\n\n              <Grid item xs={12} md={6}>\n                <Box\n                  sx={{\n                    height: '100%',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '16px',\n                    p: 4,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    justifyContent: 'center'\n                  }}\n                >\n                  <Typography\n                    variant=\"h4\"\n                    sx={{ color: 'white', mb: 4, fontWeight: 'bold' }}\n                  >\n                    Get in touch with us\n                  </Typography>\n                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                      <LocationOn sx={{ color: 'white' }} />\n                      <Typography sx={{ color: 'white' }}>\n                        {cmsContent.contactInfo.address}\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                      <Phone sx={{ color: 'white' }} />\n                      <Typography sx={{ color: 'white' }}>\n                        {cmsContent.contactInfo.phone}\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                      <Phone sx={{ color: 'white' }} />\n                      <Typography sx={{ color: 'white' }}>\n                        {cmsContent.contactInfo.alternatePhone}\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                      <Email sx={{ color: 'white' }} />\n                      <Typography sx={{ color: 'white' }}>\n                        {cmsContent.contactInfo.email}\n                      </Typography>\n                    </Box>\n                  </Box>\n                </Box>\n              </Grid>\n            </Grid>\n          </motion.div>\n        </Container>\n      </Box>\n\n      {/* Footer */}\n      <Box sx={{ py: 6, bgcolor: '#0a0e27', borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>\n        <Container maxWidth=\"lg\">\n          <Grid container spacing={4}>\n            <Grid item xs={12} md={4}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>\n                <Box\n                  component=\"img\"\n                  src=\"/api/placeholder/40/40\"\n                  alt=\"Logo\"\n                  sx={{ width: 40, height: 40 }}\n                />\n                <Typography variant=\"h6\" sx={{ color: '#ff6b35', fontWeight: 'bold' }}>\n                  {cmsContent.schoolName}\n                </Typography>\n              </Box>\n              <Typography sx={{ color: '#94a3b8', mb: 3, lineHeight: 1.6 }}>\n                Amar Vidya Mandir: Nurturing young minds, fostering growth, and inspiring a lifelong love for learning. Excellence in education.\n              </Typography>\n              <Box sx={{ display: 'flex', gap: 2 }}>\n                <IconButton sx={{ color: '#94a3b8', '&:hover': { color: '#4f46e5' } }}>\n                  <Facebook />\n                </IconButton>\n                <IconButton sx={{ color: '#94a3b8', '&:hover': { color: '#4f46e5' } }}>\n                  <Instagram />\n                </IconButton>\n                <IconButton sx={{ color: '#94a3b8', '&:hover': { color: '#4f46e5' } }}>\n                  <YouTube />\n                </IconButton>\n                <IconButton sx={{ color: '#94a3b8', '&:hover': { color: '#4f46e5' } }}>\n                  <LinkedIn />\n                </IconButton>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"h6\" sx={{ color: 'white', mb: 3, fontWeight: 'bold' }}>\n                QUICK LINKS\n              </Typography>\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n                <Button sx={{ color: '#94a3b8', justifyContent: 'flex-start', '&:hover': { color: '#4f46e5' } }}>\n                  ABOUT\n                </Button>\n                <Button sx={{ color: '#94a3b8', justifyContent: 'flex-start', '&:hover': { color: '#4f46e5' } }}>\n                  SERVICE\n                </Button>\n                <Button sx={{ color: '#94a3b8', justifyContent: 'flex-start', '&:hover': { color: '#4f46e5' } }}>\n                  CONTACT\n                </Button>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"h6\" sx={{ color: 'white', mb: 3, fontWeight: 'bold' }}>\n                CONTACT\n              </Typography>\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                  <LocationOn sx={{ color: '#4f46e5' }} />\n                  <Typography sx={{ color: '#94a3b8' }}>\n                    {cmsContent.contactInfo.address}\n                  </Typography>\n                </Box>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                  <Phone sx={{ color: '#4f46e5' }} />\n                  <Typography sx={{ color: '#94a3b8' }}>\n                    {cmsContent.contactInfo.phone}\n                  </Typography>\n                </Box>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                  <Phone sx={{ color: '#4f46e5' }} />\n                  <Typography sx={{ color: '#94a3b8' }}>\n                    {cmsContent.contactInfo.alternatePhone}\n                  </Typography>\n                </Box>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                  <Email sx={{ color: '#4f46e5' }} />\n                  <Typography sx={{ color: '#94a3b8' }}>\n                    {cmsContent.contactInfo.email}\n                  </Typography>\n                </Box>\n              </Box>\n            </Grid>\n          </Grid>\n\n          <Box\n            sx={{\n              mt: 6,\n              pt: 4,\n              borderTop: '1px solid rgba(255, 255, 255, 0.1)',\n              textAlign: 'center'\n            }}\n          >\n            <Typography sx={{ color: '#94a3b8' }}>\n              © 2024 {cmsContent.schoolName}. All rights reserved.\n            </Typography>\n          </Box>\n        </Container>\n      </Box>\n\n      <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\n\n      {/* Scroll to Top Button */}\n      <Fab\n        color=\"primary\"\n        size=\"small\"\n        onClick={scrollToTop}\n        sx={{\n          position: 'fixed',\n          bottom: 16,\n          right: 16,\n          bgcolor: '#4f46e5',\n          '&:hover': { bgcolor: '#4338ca' }\n        }}\n      >\n        <ArrowUpward />\n      </Fab>\n    </Box>\n  );\n};\n\nexport default EnhancedLoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,QACL,eAAe;AACtB,SACEC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,UAAU,EACVC,WAAW,EACXC,aAAa,EACbC,MAAM,EACNC,KAAK,QACA,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,OAAOC,KAAK,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGhD,WAAW,EAAE;EAC9B,MAAMiD,QAAQ,GAAGlD,WAAW,EAAE;EAC9B,MAAM;IAAEmD,MAAM;IAAEC,WAAW;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAY,CAAC,GAAGrD,WAAW,CAACsD,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;;EAE9F;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,OAAO,CAAC;EACzD,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgE,MAAM,EAAEC,SAAS,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACkE,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0E,eAAe,EAAEC,kBAAkB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC;IAC3CgF,SAAS,EAAE,0CAA0C;IACrDC,YAAY,EAAE,iKAAiK;IAC/KC,UAAU,EAAE,mBAAmB;IAC/BC,WAAW,EAAE;MACXC,OAAO,EAAE,gCAAgC;MACzCC,KAAK,EAAE,YAAY;MACnBC,cAAc,EAAE,YAAY;MAC5BC,KAAK,EAAE;IACT,CAAC;IACDC,YAAY,EAAE,CACZ;MACEC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,SAAS;MACftB,OAAO,EAAE,mOAAmO;MAC5OuB,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE;IACT,CAAC,CACF;IACDC,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,yBAAyB;MAAEF,KAAK,EAAE,0BAA0B;MAAEG,QAAQ,EAAE;IAAY,CAAC,EAC9F;MAAED,KAAK,EAAE,kBAAkB;MAAEF,KAAK,EAAE,0BAA0B;MAAEG,QAAQ,EAAE;IAAO,CAAC,EAClF;MAAED,KAAK,EAAE,4BAA4B;MAAEF,KAAK,EAAE,0BAA0B;MAAEG,QAAQ,EAAE;IAAY,CAAC,CAClG;IACDC,SAAS,EAAE,CACT;MACEF,KAAK,EAAE,yBAAyB;MAChCG,OAAO,EAAE,0CAA0C;MACnDC,IAAI,EAAE,iBAAiB;MACvBC,MAAM,EAAE,OAAO;MACfP,KAAK,EAAE;IACT,CAAC,EACD;MACEE,KAAK,EAAE,kBAAkB;MACzBG,OAAO,EAAE,0CAA0C;MACnDC,IAAI,EAAE,iBAAiB;MACvBC,MAAM,EAAE,OAAO;MACfP,KAAK,EAAE;IACT,CAAC,EACD;MACEE,KAAK,EAAE,4BAA4B;MACnCG,OAAO,EAAE,wCAAwC;MACjDC,IAAI,EAAE,iBAAiB;MACvBC,MAAM,EAAE,OAAO;MACfP,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,CAAC;EAEF,MAAMQ,YAAY,GAAIC,KAAK,IAAK;IAC9BA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI1C,YAAY,KAAK,SAAS,EAAE;MAC9B,MAAM2C,OAAO,GAAGF,KAAK,CAACG,MAAM,CAACC,UAAU,CAACC,KAAK;MAC7C,MAAMC,WAAW,GAAGN,KAAK,CAACG,MAAM,CAACG,WAAW,CAACD,KAAK;MAClD,MAAME,QAAQ,GAAGP,KAAK,CAACG,MAAM,CAACI,QAAQ,CAACF,KAAK;MAE5C,IAAI,CAACH,OAAO,IAAI,CAACI,WAAW,IAAI,CAACC,QAAQ,EAAE;QACzC,IAAI,CAACL,OAAO,EAAE5B,kBAAkB,CAAC,IAAI,CAAC;QACtC,IAAI,CAACgC,WAAW,EAAE9B,mBAAmB,CAAC,IAAI,CAAC;QAC3C,IAAI,CAAC+B,QAAQ,EAAEnC,gBAAgB,CAAC,IAAI,CAAC;QACrC;MACF;MACA,MAAMoC,MAAM,GAAG;QAAEN,OAAO;QAAEI,WAAW;QAAEC;MAAS,CAAC;MACjD3C,SAAS,CAAC,IAAI,CAAC;MACfd,QAAQ,CAACR,SAAS,CAACkE,MAAM,EAAEjD,YAAY,CAAC,CAAC;IAC3C,CAAC,MAAM;MACL,MAAM2B,KAAK,GAAGc,KAAK,CAACG,MAAM,CAACjB,KAAK,CAACmB,KAAK;MACtC,MAAME,QAAQ,GAAGP,KAAK,CAACG,MAAM,CAACI,QAAQ,CAACF,KAAK;MAE5C,IAAI,CAACnB,KAAK,IAAI,CAACqB,QAAQ,EAAE;QACvB,IAAI,CAACrB,KAAK,EAAEhB,aAAa,CAAC,IAAI,CAAC;QAC/B,IAAI,CAACqC,QAAQ,EAAEnC,gBAAgB,CAAC,IAAI,CAAC;QACrC;MACF;MAEA,MAAMoC,MAAM,GAAG;QAAEtB,KAAK;QAAEqB;MAAS,CAAC;MAClC3C,SAAS,CAAC,IAAI,CAAC;MACfd,QAAQ,CAACR,SAAS,CAACkE,MAAM,EAAEjD,YAAY,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMkD,iBAAiB,GAAIT,KAAK,IAAK;IACnC,MAAM;MAAEZ;IAAK,CAAC,GAAGY,KAAK,CAACG,MAAM;IAC7B,IAAIf,IAAI,KAAK,OAAO,EAAElB,aAAa,CAAC,KAAK,CAAC;IAC1C,IAAIkB,IAAI,KAAK,UAAU,EAAEhB,gBAAgB,CAAC,KAAK,CAAC;IAChD,IAAIgB,IAAI,KAAK,YAAY,EAAEd,kBAAkB,CAAC,KAAK,CAAC;IACpD,IAAIc,IAAI,KAAK,aAAa,EAAEZ,mBAAmB,CAAC,KAAK,CAAC;EACxD,CAAC;EAED5E,SAAS,CAAC,MAAM;IACd,IAAIoD,MAAM,KAAK,SAAS,IAAIC,WAAW,KAAK,IAAI,EAAE;MAChD,IAAIG,WAAW,KAAK,OAAO,EAAE;QAC3BL,QAAQ,CAAC,kBAAkB,CAAC;MAC9B,CAAC,MAAM,IAAIK,WAAW,KAAK,SAAS,EAAE;QACpCL,QAAQ,CAAC,oBAAoB,CAAC;MAChC,CAAC,MAAM,IAAIK,WAAW,KAAK,SAAS,EAAE;QACpCL,QAAQ,CAAC,oBAAoB,CAAC;MAChC;IACF,CAAC,MAAM,IAAIC,MAAM,KAAK,QAAQ,EAAE;MAC9BgB,UAAU,CAACd,QAAQ,CAAC;MACpBY,YAAY,CAAC,IAAI,CAAC;MAClBF,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC,MAAM,IAAIZ,MAAM,KAAK,OAAO,EAAE;MAC7BgB,UAAU,CAAC,eAAe,CAAC;MAC3BF,YAAY,CAAC,IAAI,CAAC;MAClBF,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC,EAAE,CAACZ,MAAM,EAAEI,WAAW,EAAEL,QAAQ,EAAEI,KAAK,EAAED,QAAQ,EAAED,WAAW,CAAC,CAAC;EAEjE,MAAMyD,WAAW,GAAGA,CAAA,KAAM;IACxBC,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,oBACErE,OAAA,CAACzC,GAAG;IAAC+G,EAAE,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAU,CAAE;IAAAC,QAAA,gBAElDzE,OAAA,CAAC5B,MAAM;MAACsG,QAAQ,EAAC,QAAQ;MAACJ,EAAE,EAAE;QAAEE,OAAO,EAAE,aAAa;QAAEG,SAAS,EAAE;MAAO,CAAE;MAAAF,QAAA,eAC1EzE,OAAA,CAAC3B,OAAO;QAACiG,EAAE,EAAE;UAAEM,cAAc,EAAE,eAAe;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACtDzE,OAAA,CAACzC,GAAG;UAAC+G,EAAE,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAP,QAAA,gBACzDzE,OAAA,CAACzC,GAAG;YACF0H,SAAS,EAAC,KAAK;YACfC,GAAG,EAAC,wBAAwB;YAC5BC,GAAG,EAAC,MAAM;YACVb,EAAE,EAAE;cAAEc,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAG;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAC9B,eACFzF,OAAA,CAACtC,UAAU;YAACgI,OAAO,EAAC,IAAI;YAACpB,EAAE,EAAE;cAAEqB,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAAnB,QAAA,EACnEzC,UAAU,CAACI;UAAU;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACT,eACNzF,OAAA,CAACzC,GAAG;UAAC+G,EAAE,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAE,CAAE;UAAAP,QAAA,gBACnCzE,OAAA,CAACpC,MAAM;YAAC0G,EAAE,EAAE;cAAEqB,KAAK,EAAE;YAAQ,CAAE;YAAAlB,QAAA,EAAC;UAAI;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAS,eAC7CzF,OAAA,CAACpC,MAAM;YAAC0G,EAAE,EAAE;cAAEqB,KAAK,EAAE;YAAQ,CAAE;YAAAlB,QAAA,EAAC;UAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAS,eAC9CzF,OAAA,CAACpC,MAAM;YAAC0G,EAAE,EAAE;cAAEqB,KAAK,EAAE;YAAQ,CAAE;YAAAlB,QAAA,EAAC;UAAO;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAS,eAChDzF,OAAA,CAACpC,MAAM;YACL8H,OAAO,EAAC,WAAW;YACnBpB,EAAE,EAAE;cACFE,OAAO,EAAE,SAAS;cAClB,SAAS,EAAE;gBAAEA,OAAO,EAAE;cAAU;YAClC,CAAE;YAAAC,QAAA,EACH;UAED;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAS;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACL;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACH,eAGTzF,OAAA,CAACxC,SAAS;MAACqI,QAAQ,EAAC,IAAI;MAACvB,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACrCzE,OAAA,CAACvC,IAAI;QAACqI,SAAS;QAACC,OAAO,EAAE,CAAE;QAAChB,UAAU,EAAC,QAAQ;QAAAN,QAAA,gBAE7CzE,OAAA,CAACvC,IAAI;UAACuI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,eACvBzE,OAAA,CAACJ,MAAM,CAACuG,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAhC,QAAA,gBAE9BzE,OAAA,CAACtC,UAAU;cACTgI,OAAO,EAAC,IAAI;cACZpB,EAAE,EAAE;gBACFqB,KAAK,EAAE,OAAO;gBACdC,UAAU,EAAE,MAAM;gBAClBc,EAAE,EAAE,CAAC;gBACLC,QAAQ,EAAE;kBAAEV,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAS;cACvC,CAAE;cAAAzB,QAAA,EAEDzC,UAAU,CAACE;YAAS;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACV,eACbzF,OAAA,CAACtC,UAAU;cACTgI,OAAO,EAAC,IAAI;cACZpB,EAAE,EAAE;gBACFqB,KAAK,EAAE,SAAS;gBAChBe,EAAE,EAAE,CAAC;gBACLE,UAAU,EAAE;cACd,CAAE;cAAAnC,QAAA,EAEDzC,UAAU,CAACG;YAAY;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACb,eACbzF,OAAA,CAACzC,GAAG;cAAC+G,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEE,GAAG,EAAE;cAAE,CAAE;cAAAP,QAAA,gBACnCzE,OAAA,CAACpC,MAAM;gBACL8H,OAAO,EAAC,WAAW;gBACnBpB,EAAE,EAAE;kBACFE,OAAO,EAAE,SAAS;kBAClBqC,EAAE,EAAE,CAAC;kBACLhC,EAAE,EAAE,GAAG;kBACP,SAAS,EAAE;oBAAEL,OAAO,EAAE;kBAAU;gBAClC,CAAE;gBAAAC,QAAA,EACH;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAS,eACTzF,OAAA,CAACpC,MAAM;gBACL8H,OAAO,EAAC,UAAU;gBAClBpB,EAAE,EAAE;kBACFwC,WAAW,EAAE,OAAO;kBACpBnB,KAAK,EAAE,OAAO;kBACdkB,EAAE,EAAE,CAAC;kBACLhC,EAAE,EAAE,GAAG;kBACP,SAAS,EAAE;oBAAEiC,WAAW,EAAE,SAAS;oBAAEnB,KAAK,EAAE;kBAAU;gBACxD,CAAE;gBAAAlB,QAAA,EACH;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAS;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACK;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACR,eAGPzF,OAAA,CAACvC,IAAI;UAACuI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,eACvBzE,OAAA,CAACzC,GAAG;YAAC+G,EAAE,EAAE;cAAEI,QAAQ,EAAE;YAAW,CAAE;YAAAD,QAAA,eAEhCzE,OAAA,CAACzC,GAAG;cACF+G,EAAE,EAAE;gBACFyC,UAAU,EAAE,mDAAmD;gBAC/DC,YAAY,EAAE,MAAM;gBACpBC,CAAC,EAAE,CAAC;gBACJvC,QAAQ,EAAE,UAAU;gBACpBwC,QAAQ,EAAE;cACZ,CAAE;cAAAzC,QAAA,gBAGFzE,OAAA,CAACnC,KAAK;gBACJsJ,SAAS,EAAE,EAAG;gBACd7C,EAAE,EAAE;kBACF2C,CAAC,EAAE,CAAC;kBACJD,YAAY,EAAE,MAAM;kBACpBxC,OAAO,EAAE,2BAA2B;kBACpC4C,cAAc,EAAE,YAAY;kBAC5B1C,QAAQ,EAAE,UAAU;kBACpB2C,MAAM,EAAE;gBACV,CAAE;gBAAA5C,QAAA,gBAEFzE,OAAA,CAACtC,UAAU;kBACTgI,OAAO,EAAC,IAAI;kBACZpB,EAAE,EAAE;oBACFoC,EAAE,EAAE,CAAC;oBACLf,KAAK,EAAE,SAAS;oBAChB2B,SAAS,EAAE,QAAQ;oBACnB1B,UAAU,EAAE;kBACd,CAAE;kBAAAnB,QAAA,EACH;gBAED;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAa,eACbzF,OAAA,CAACtC,UAAU;kBACTgI,OAAO,EAAC,OAAO;kBACfpB,EAAE,EAAE;oBACFoC,EAAE,EAAE,CAAC;oBACLf,KAAK,EAAE,SAAS;oBAChB2B,SAAS,EAAE;kBACb,CAAE;kBAAA7C,QAAA,EACH;gBAED;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAa,eAGbzF,OAAA,CAACzC,GAAG;kBAAC+G,EAAE,EAAE;oBAAEoC,EAAE,EAAE;kBAAE,CAAE;kBAAAjC,QAAA,gBACjBzE,OAAA,CAACtC,UAAU;oBACTgI,OAAO,EAAC,IAAI;oBACZpB,EAAE,EAAE;sBACFoC,EAAE,EAAE,CAAC;sBACLf,KAAK,EAAE,SAAS;sBAChB2B,SAAS,EAAE,QAAQ;sBACnB1B,UAAU,EAAE;oBACd,CAAE;oBAAAnB,QAAA,EACH;kBAED;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAa,eACbzF,OAAA,CAACvC,IAAI;oBAACqI,SAAS;oBAACC,OAAO,EAAE,CAAE;oBAACnB,cAAc,EAAC,QAAQ;oBAAAH,QAAA,gBACjDzE,OAAA,CAACvC,IAAI;sBAACuI,IAAI;sBAACC,EAAE,EAAE,CAAE;sBAAAxB,QAAA,eACfzE,OAAA,CAACnC,KAAK;wBACJsJ,SAAS,EAAErG,YAAY,KAAK,OAAO,GAAG,CAAC,GAAG,CAAE;wBAC5CyG,OAAO,EAAEA,CAAA,KAAMxG,eAAe,CAAC,OAAO,CAAE;wBACxCuD,EAAE,EAAE;0BACF2C,CAAC,EAAE,CAAC;0BACJK,SAAS,EAAE,QAAQ;0BACnBE,MAAM,EAAE,SAAS;0BACjBhD,OAAO,EAAE1D,YAAY,KAAK,OAAO,GAAG,SAAS,GAAG,OAAO;0BACvD6E,KAAK,EAAE7E,YAAY,KAAK,OAAO,GAAG,OAAO,GAAG,SAAS;0BACrD0F,UAAU,EAAE,eAAe;0BAC3BiB,MAAM,EAAE3G,YAAY,KAAK,OAAO,GAAG,mBAAmB,GAAG,uBAAuB;0BAChF,SAAS,EAAE;4BACT4G,SAAS,EAAE,kBAAkB;4BAC7B/C,SAAS,EAAE;0BACb;wBACF,CAAE;wBAAAF,QAAA,gBAEFzE,OAAA,CAACP,aAAa;0BAAC6E,EAAE,EAAE;4BAAEqC,QAAQ,EAAE,EAAE;4BAAED,EAAE,EAAE;0BAAE;wBAAE;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,QAAG,eAC9CzF,OAAA,CAACtC,UAAU;0BAACgI,OAAO,EAAC,OAAO;0BAACE,UAAU,EAAC,MAAM;0BAAAnB,QAAA,EAAC;wBAE9C;0BAAAa,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,QAAa;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBACP;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACH,eACPzF,OAAA,CAACvC,IAAI;sBAACuI,IAAI;sBAACC,EAAE,EAAE,CAAE;sBAAAxB,QAAA,eACfzE,OAAA,CAACnC,KAAK;wBACJsJ,SAAS,EAAErG,YAAY,KAAK,SAAS,GAAG,CAAC,GAAG,CAAE;wBAC9CyG,OAAO,EAAEA,CAAA,KAAMxG,eAAe,CAAC,SAAS,CAAE;wBAC1CuD,EAAE,EAAE;0BACF2C,CAAC,EAAE,CAAC;0BACJK,SAAS,EAAE,QAAQ;0BACnBE,MAAM,EAAE,SAAS;0BACjBhD,OAAO,EAAE1D,YAAY,KAAK,SAAS,GAAG,SAAS,GAAG,OAAO;0BACzD6E,KAAK,EAAE7E,YAAY,KAAK,SAAS,GAAG,OAAO,GAAG,SAAS;0BACvD0F,UAAU,EAAE,eAAe;0BAC3BiB,MAAM,EAAE3G,YAAY,KAAK,SAAS,GAAG,mBAAmB,GAAG,uBAAuB;0BAClF,SAAS,EAAE;4BACT4G,SAAS,EAAE,kBAAkB;4BAC7B/C,SAAS,EAAE;0BACb;wBACF,CAAE;wBAAAF,QAAA,gBAEFzE,OAAA,CAACN,MAAM;0BAAC4E,EAAE,EAAE;4BAAEqC,QAAQ,EAAE,EAAE;4BAAED,EAAE,EAAE;0BAAE;wBAAE;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,QAAG,eACvCzF,OAAA,CAACtC,UAAU;0BAACgI,OAAO,EAAC,OAAO;0BAACE,UAAU,EAAC,MAAM;0BAAAnB,QAAA,EAAC;wBAE9C;0BAAAa,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,QAAa;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBACP;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACH,eACPzF,OAAA,CAACvC,IAAI;sBAACuI,IAAI;sBAACC,EAAE,EAAE,CAAE;sBAAAxB,QAAA,eACfzE,OAAA,CAACnC,KAAK;wBACJsJ,SAAS,EAAErG,YAAY,KAAK,SAAS,GAAG,CAAC,GAAG,CAAE;wBAC9CyG,OAAO,EAAEA,CAAA,KAAMxG,eAAe,CAAC,SAAS,CAAE;wBAC1CuD,EAAE,EAAE;0BACF2C,CAAC,EAAE,CAAC;0BACJK,SAAS,EAAE,QAAQ;0BACnBE,MAAM,EAAE,SAAS;0BACjBhD,OAAO,EAAE1D,YAAY,KAAK,SAAS,GAAG,SAAS,GAAG,OAAO;0BACzD6E,KAAK,EAAE7E,YAAY,KAAK,SAAS,GAAG,OAAO,GAAG,SAAS;0BACvD0F,UAAU,EAAE,eAAe;0BAC3BiB,MAAM,EAAE3G,YAAY,KAAK,SAAS,GAAG,mBAAmB,GAAG,uBAAuB;0BAClF,SAAS,EAAE;4BACT4G,SAAS,EAAE,kBAAkB;4BAC7B/C,SAAS,EAAE;0BACb;wBACF,CAAE;wBAAAF,QAAA,gBAEFzE,OAAA,CAACL,KAAK;0BAAC2E,EAAE,EAAE;4BAAEqC,QAAQ,EAAE,EAAE;4BAAED,EAAE,EAAE;0BAAE;wBAAE;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,QAAG,eACtCzF,OAAA,CAACtC,UAAU;0BAACgI,OAAO,EAAC,OAAO;0BAACE,UAAU,EAAC,MAAM;0BAAAnB,QAAA,EAAC;wBAE9C;0BAAAa,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,QAAa;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBACP;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACH,eAENzF,OAAA,CAACzC,GAAG;kBAAC0H,SAAS,EAAC,MAAM;kBAAC0C,QAAQ,EAAErE,YAAa;kBAAAmB,QAAA,GAC1C7B,IAAI,KAAK,SAAS,gBACjB5C,OAAA,CAAAE,SAAA;oBAAAuE,QAAA,gBACEzE,OAAA,CAACrC,SAAS;sBACRiK,MAAM,EAAC,QAAQ;sBACfC,QAAQ;sBACRC,SAAS;sBACTC,EAAE,EAAC,YAAY;sBACfC,KAAK,EAAC,wBAAwB;sBAC9BrF,IAAI,EAAC,YAAY;sBACjBsF,IAAI,EAAC,QAAQ;sBACbvH,KAAK,EAAEkB,eAAgB;sBACvBsG,UAAU,EAAEtG,eAAe,IAAI,yBAA0B;sBACzDuG,QAAQ,EAAEnE,iBAAkB;sBAC5BM,EAAE,EAAE;wBAAEoC,EAAE,EAAE;sBAAE;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACd,eACFzF,OAAA,CAACrC,SAAS;sBACRiK,MAAM,EAAC,QAAQ;sBACfC,QAAQ;sBACRC,SAAS;sBACTC,EAAE,EAAC,aAAa;sBAChBC,KAAK,EAAC,iBAAiB;sBACvBrF,IAAI,EAAC,aAAa;sBAClBjC,KAAK,EAAEoB,gBAAiB;sBACxBoG,UAAU,EAAEpG,gBAAgB,IAAI,kBAAmB;sBACnDqG,QAAQ,EAAEnE,iBAAkB;sBAC5BM,EAAE,EAAE;wBAAEoC,EAAE,EAAE;sBAAE;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACd;kBAAA,gBACD,gBAEHzF,OAAA,CAACrC,SAAS;oBACRiK,MAAM,EAAC,QAAQ;oBACfC,QAAQ;oBACRC,SAAS;oBACTC,EAAE,EAAC,OAAO;oBACVC,KAAK,EAAC,kBAAkB;oBACxBrF,IAAI,EAAC,OAAO;oBACZyF,YAAY,EAAC,OAAO;oBACpB1H,KAAK,EAAEc,UAAW;oBAClB0G,UAAU,EAAE1G,UAAU,IAAI,mBAAoB;oBAC9C2G,QAAQ,EAAEnE,iBAAkB;oBAC5BM,EAAE,EAAE;sBAAEoC,EAAE,EAAE;oBAAE;kBAAE;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAEjB,eAEDzF,OAAA,CAACrC,SAAS;oBACRiK,MAAM,EAAC,QAAQ;oBACfC,QAAQ;oBACRC,SAAS;oBACTnF,IAAI,EAAC,UAAU;oBACfqF,KAAK,EAAC,UAAU;oBAChBC,IAAI,EAAEjH,YAAY,GAAG,MAAM,GAAG,UAAW;oBACzC+G,EAAE,EAAC,UAAU;oBACbK,YAAY,EAAC,kBAAkB;oBAC/B1H,KAAK,EAAEgB,aAAc;oBACrBwG,UAAU,EAAExG,aAAa,IAAI,sBAAuB;oBACpDyG,QAAQ,EAAEnE,iBAAkB;oBAC5BqE,UAAU,EAAE;sBACVC,YAAY,eACVtI,OAAA,CAACvB,cAAc;wBAACiG,QAAQ,EAAC,KAAK;wBAAAD,QAAA,eAC5BzE,OAAA,CAAC7B,UAAU;0BACToJ,OAAO,EAAEA,CAAA,KAAMtG,eAAe,CAAC,CAACD,YAAY,CAAE;0BAC9CuH,IAAI,EAAC,KAAK;0BAAA9D,QAAA,EAETzD,YAAY,gBAAGhB,OAAA,CAAChB,aAAa;4BAAAsG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,QAAG,gBAAGzF,OAAA,CAACjB,UAAU;4BAAAuG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA;wBAAG;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA;sBACvC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAGnB,CAAE;oBACFnB,EAAE,EAAE;sBAAEoC,EAAE,EAAE;oBAAE;kBAAE;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACd,eAEFzF,OAAA,CAACzC,GAAG;oBAAC+G,EAAE,EAAE;sBAAEQ,OAAO,EAAE,MAAM;sBAAEF,cAAc,EAAE,eAAe;sBAAE8B,EAAE,EAAE;oBAAE,CAAE;oBAAAjC,QAAA,gBACnEzE,OAAA,CAACxB,gBAAgB;sBACfgK,OAAO,eAAExI,OAAA,CAACzB,QAAQ;wBAACoH,KAAK,EAAC;sBAAS;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,QAAI;sBACtCuC,KAAK,EAAC;oBAAa;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACnB,eACFzF,OAAA,CAACpC,MAAM;sBAAC8H,OAAO,EAAC,MAAM;sBAACpB,EAAE,EAAE;wBAAEqB,KAAK,EAAE;sBAAU,CAAE;sBAAAlB,QAAA,EAAC;oBAEjD;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAS;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACL,eAENzF,OAAA,CAACpC,MAAM;oBACLqK,IAAI,EAAC,QAAQ;oBACbH,SAAS;oBACTpC,OAAO,EAAC,WAAW;oBACnBpB,EAAE,EAAE;sBACFO,EAAE,EAAE,GAAG;sBACPL,OAAO,EAAE,SAAS;sBAClB,SAAS,EAAE;wBAAEA,OAAO,EAAE;sBAAU,CAAC;sBACjCwC,YAAY,EAAE,KAAK;sBACnBpB,UAAU,EAAE;oBACd,CAAE;oBAAAnB,QAAA,EAEDvD,MAAM,gBACLlB,OAAA,CAACtB,gBAAgB;sBAAC+J,IAAI,EAAE,EAAG;sBAAC9C,KAAK,EAAC;oBAAS;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,GAE9C;kBACD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACM;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACL;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACA,eAGRzF,OAAA,CAACzC,GAAG;gBACF+G,EAAE,EAAE;kBACFI,QAAQ,EAAE,UAAU;kBACpBN,GAAG,EAAE,KAAK;kBACVsE,KAAK,EAAE,KAAK;kBACZtD,KAAK,EAAE,EAAE;kBACTC,MAAM,EAAE,EAAE;kBACVb,OAAO,EAAE,SAAS;kBAClBwC,YAAY,EAAE,KAAK;kBACnBlC,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBH,cAAc,EAAE,QAAQ;kBACxByC,MAAM,EAAE;gBACV,CAAE;gBAAA5C,QAAA,EACH;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAM;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACG,eAGZzF,OAAA,CAACzC,GAAG;MAAC+G,EAAE,EAAE;QAAEO,EAAE,EAAE,CAAC;QAAEL,OAAO,EAAE;MAAU,CAAE;MAAAC,QAAA,eACrCzE,OAAA,CAACxC,SAAS;QAACqI,QAAQ,EAAC,IAAI;QAAApB,QAAA,eACtBzE,OAAA,CAACJ,MAAM,CAACuG,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEsC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEvC,OAAO,EAAE,CAAC;YAAEsC,CAAC,EAAE;UAAE,CAAE;UAClCnC,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BoC,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAArE,QAAA,gBAEzBzE,OAAA,CAACtC,UAAU;YACTgI,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFqB,KAAK,EAAE,SAAS;cAChB2B,SAAS,EAAE,QAAQ;cACnBZ,EAAE,EAAE,CAAC;cACLd,UAAU,EAAE;YACd,CAAE;YAAAnB,QAAA,EACH;UAED;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAa,eACbzF,OAAA,CAACtC,UAAU;YACTgI,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFqB,KAAK,EAAE,OAAO;cACd2B,SAAS,EAAE,QAAQ;cACnBZ,EAAE,EAAE,CAAC;cACLd,UAAU,EAAE;YACd,CAAE;YAAAnB,QAAA,EACH;UAED;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAa,eAEbzF,OAAA,CAACvC,IAAI;YAACqI,SAAS;YAAClB,cAAc,EAAC,QAAQ;YAAAH,QAAA,EACpCzC,UAAU,CAACU,YAAY,CAACqG,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBAC9CjJ,OAAA,CAACvC,IAAI;cAACuI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzB,QAAA,eACvBzE,OAAA,CAAChC,IAAI;gBACHsG,EAAE,EAAE;kBACFE,OAAO,EAAE,2BAA2B;kBACpC4C,cAAc,EAAE,YAAY;kBAC5BK,MAAM,EAAE,oCAAoC;kBAC5CT,YAAY,EAAE,MAAM;kBACpBC,CAAC,EAAE,CAAC;kBACJK,SAAS,EAAE;gBACb,CAAE;gBAAA7C,QAAA,gBAEFzE,OAAA,CAAClC,MAAM;kBACLoH,GAAG,EAAE8D,WAAW,CAAClG,KAAM;kBACvBwB,EAAE,EAAE;oBACFc,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACV6D,EAAE,EAAE,MAAM;oBACVxC,EAAE,EAAE,CAAC;oBACLe,MAAM,EAAE;kBACV;gBAAE;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACF,eACFzF,OAAA,CAACtC,UAAU;kBACTgI,OAAO,EAAC,IAAI;kBACZpB,EAAE,EAAE;oBAAEqB,KAAK,EAAE,OAAO;oBAAEe,EAAE,EAAE,CAAC;oBAAEd,UAAU,EAAE;kBAAO,CAAE;kBAAAnB,QAAA,EAEjDuE,WAAW,CAACrG;gBAAI;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACN,eACbzF,OAAA,CAACtC,UAAU;kBACTgI,OAAO,EAAC,OAAO;kBACfpB,EAAE,EAAE;oBAAEqB,KAAK,EAAE,SAAS;oBAAEe,EAAE,EAAE,CAAC;oBAAEE,UAAU,EAAE;kBAAI,CAAE;kBAAAnC,QAAA,EAEhDuE,WAAW,CAAC1H;gBAAO;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACT,eACbzF,OAAA,CAACjC,MAAM;kBACL6F,KAAK,EAAEoF,WAAW,CAACnG,MAAO;kBAC1BsG,QAAQ;kBACR7E,EAAE,EAAE;oBACF,yBAAyB,EAAE;sBACzBqB,KAAK,EAAE;oBACT;kBACF;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACG,GA1CsBwD,KAAK;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QA4CrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACR,eAGNzF,OAAA,CAACzC,GAAG;MAAC+G,EAAE,EAAE;QAAEO,EAAE,EAAE,CAAC;QAAEL,OAAO,EAAE;MAAU,CAAE;MAAAC,QAAA,eACrCzE,OAAA,CAACxC,SAAS;QAACqI,QAAQ,EAAC,IAAI;QAAApB,QAAA,eACtBzE,OAAA,CAACJ,MAAM,CAACuG,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEsC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEvC,OAAO,EAAE,CAAC;YAAEsC,CAAC,EAAE;UAAE,CAAE;UAClCnC,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BoC,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAArE,QAAA,eAEzBzE,OAAA,CAACvC,IAAI;YAACqI,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAtB,QAAA,gBACzBzE,OAAA,CAACvC,IAAI;cAACuI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzB,QAAA,eACvBzE,OAAA,CAACvC,IAAI;gBAACqI,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAtB,QAAA,EACxBzC,UAAU,CAACkB,SAAS,CAAC6F,GAAG,CAAC,CAACK,IAAI,EAAEH,KAAK,kBACpCjJ,OAAA,CAACvC,IAAI;kBAACuI,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACoD,EAAE,EAAE,CAAE;kBAAA5E,QAAA,eACvBzE,OAAA,CAAChC,IAAI;oBACHsG,EAAE,EAAE;sBACFE,OAAO,EAAE,2BAA2B;sBACpC4C,cAAc,EAAE,YAAY;sBAC5BK,MAAM,EAAE,oCAAoC;sBAC5CT,YAAY,EAAE,MAAM;sBACpBE,QAAQ,EAAE,QAAQ;sBAClBV,UAAU,EAAE,qBAAqB;sBACjC,SAAS,EAAE;wBACTkB,SAAS,EAAE;sBACb;oBACF,CAAE;oBAAAjD,QAAA,gBAEFzE,OAAA,CAAC9B,SAAS;sBACR+G,SAAS,EAAC,KAAK;sBACfI,MAAM,EAAC,KAAK;sBACZvC,KAAK,EAAEsG,IAAI,CAACtG,KAAM;sBAClBqC,GAAG,EAAEiE,IAAI,CAACpG;oBAAM;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAChB,eACFzF,OAAA,CAAC/B,WAAW;sBAACqG,EAAE,EAAE;wBAAE2C,CAAC,EAAE;sBAAE,CAAE;sBAAAxC,QAAA,gBACxBzE,OAAA,CAACtC,UAAU;wBACTgI,OAAO,EAAC,IAAI;wBACZpB,EAAE,EAAE;0BAAEqB,KAAK,EAAE,OAAO;0BAAEe,EAAE,EAAE,CAAC;0BAAEd,UAAU,EAAE;wBAAO,CAAE;wBAAAnB,QAAA,EAEjD2E,IAAI,CAACpG;sBAAK;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,QACA,eACbzF,OAAA,CAACtC,UAAU;wBACTgI,OAAO,EAAC,OAAO;wBACfpB,EAAE,EAAE;0BAAEqB,KAAK,EAAE,SAAS;0BAAEe,EAAE,EAAE;wBAAE,CAAE;wBAAAjC,QAAA,GACjC,eACI,EAAC2E,IAAI,CAAChG,IAAI,EAAC,QAAM,EAACgG,IAAI,CAAC/F,MAAM;sBAAA;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,QACrB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACT,GAlCsBwD,KAAK;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAoCrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACF,eAEPzF,OAAA,CAACvC,IAAI;cAACuI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzB,QAAA,eACvBzE,OAAA,CAACzC,GAAG;gBACF+G,EAAE,EAAE;kBACFE,OAAO,EAAE,2BAA2B;kBACpC4C,cAAc,EAAE,YAAY;kBAC5BK,MAAM,EAAE,oCAAoC;kBAC5CT,YAAY,EAAE,MAAM;kBACpBC,CAAC,EAAE,CAAC;kBACJK,SAAS,EAAE;gBACb,CAAE;gBAAA7C,QAAA,gBAEFzE,OAAA,CAACtC,UAAU;kBACTgI,OAAO,EAAC,IAAI;kBACZpB,EAAE,EAAE;oBAAEqB,KAAK,EAAE,SAAS;oBAAEe,EAAE,EAAE,CAAC;oBAAEd,UAAU,EAAE;kBAAO,CAAE;kBAAAnB,QAAA,EACrD;gBAED;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAa,eACbzF,OAAA,CAACtC,UAAU;kBACTgI,OAAO,EAAC,IAAI;kBACZpB,EAAE,EAAE;oBAAEqB,KAAK,EAAE,OAAO;oBAAEe,EAAE,EAAE,CAAC;oBAAEd,UAAU,EAAE;kBAAO,CAAE;kBAAAnB,QAAA,EACnD;gBAED;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAa,eACbzF,OAAA,CAACtC,UAAU;kBACTgI,OAAO,EAAC,OAAO;kBACfpB,EAAE,EAAE;oBAAEqB,KAAK,EAAE,SAAS;oBAAEe,EAAE,EAAE,CAAC;oBAAEE,UAAU,EAAE;kBAAI,CAAE;kBAAAnC,QAAA,EAClD;gBAED;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAa,eACbzF,OAAA,CAACpC,MAAM;kBACL8H,OAAO,EAAC,WAAW;kBACnBpB,EAAE,EAAE;oBACFE,OAAO,EAAE,SAAS;oBAClBqC,EAAE,EAAE,CAAC;oBACLhC,EAAE,EAAE,GAAG;oBACP,SAAS,EAAE;sBAAEL,OAAO,EAAE;oBAAU;kBAClC,CAAE;kBAAAC,QAAA,EACH;gBAED;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAS;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACR,eAGNzF,OAAA,CAACzC,GAAG;MAAC+G,EAAE,EAAE;QAAEO,EAAE,EAAE,CAAC;QAAEL,OAAO,EAAE;MAAU,CAAE;MAAAC,QAAA,eACrCzE,OAAA,CAACxC,SAAS;QAACqI,QAAQ,EAAC,IAAI;QAAApB,QAAA,eACtBzE,OAAA,CAACJ,MAAM,CAACuG,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEsC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEvC,OAAO,EAAE,CAAC;YAAEsC,CAAC,EAAE;UAAE,CAAE;UAClCnC,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BoC,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAArE,QAAA,gBAEzBzE,OAAA,CAACtC,UAAU;YACTgI,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cAAEqB,KAAK,EAAE,SAAS;cAAE2B,SAAS,EAAE,QAAQ;cAAEZ,EAAE,EAAE;YAAE,CAAE;YAAAjC,QAAA,EACtD;UAED;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAa,eACbzF,OAAA,CAACtC,UAAU;YACTgI,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFqB,KAAK,EAAE,OAAO;cACd2B,SAAS,EAAE,QAAQ;cACnBZ,EAAE,EAAE,CAAC;cACLd,UAAU,EAAE;YACd,CAAE;YAAAnB,QAAA,EACH;UAED;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAa,eAEbzF,OAAA,CAACvC,IAAI;YAACqI,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAtB,QAAA,gBACzBzE,OAAA,CAACvC,IAAI;cAACuI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzB,QAAA,eACvBzE,OAAA,CAACvC,IAAI;gBAACqI,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAtB,QAAA,gBACzBzE,OAAA,CAACvC,IAAI;kBAACuI,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACoD,EAAE,EAAE,CAAE;kBAAA5E,QAAA,eACvBzE,OAAA,CAACrC,SAAS;oBACRmK,SAAS;oBACTE,KAAK,EAAC,YAAY;oBAClBtC,OAAO,EAAC,UAAU;oBAClBpB,EAAE,EAAE;sBACF,0BAA0B,EAAE;wBAC1BE,OAAO,EAAE,2BAA2B;wBACpC,YAAY,EAAE;0BAAEsC,WAAW,EAAE;wBAA2B,CAAC;wBACzD,kBAAkB,EAAE;0BAAEA,WAAW,EAAE;wBAA2B,CAAC;wBAC/D,wBAAwB,EAAE;0BAAEA,WAAW,EAAE;wBAAU;sBACrD,CAAC;sBACD,uBAAuB,EAAE;wBAAEnB,KAAK,EAAE;sBAAU,CAAC;sBAC7C,2BAA2B,EAAE;wBAAEA,KAAK,EAAE;sBAAQ;oBAChD;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACG,eACPzF,OAAA,CAACvC,IAAI;kBAACuI,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACoD,EAAE,EAAE,CAAE;kBAAA5E,QAAA,eACvBzE,OAAA,CAACrC,SAAS;oBACRmK,SAAS;oBACTE,KAAK,EAAC,OAAO;oBACbtC,OAAO,EAAC,UAAU;oBAClBpB,EAAE,EAAE;sBACF,0BAA0B,EAAE;wBAC1BE,OAAO,EAAE,2BAA2B;wBACpC,YAAY,EAAE;0BAAEsC,WAAW,EAAE;wBAA2B,CAAC;wBACzD,kBAAkB,EAAE;0BAAEA,WAAW,EAAE;wBAA2B,CAAC;wBAC/D,wBAAwB,EAAE;0BAAEA,WAAW,EAAE;wBAAU;sBACrD,CAAC;sBACD,uBAAuB,EAAE;wBAAEnB,KAAK,EAAE;sBAAU,CAAC;sBAC7C,2BAA2B,EAAE;wBAAEA,KAAK,EAAE;sBAAQ;oBAChD;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACG,eACPzF,OAAA,CAACvC,IAAI;kBAACuI,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACoD,EAAE,EAAE,CAAE;kBAAA5E,QAAA,eACvBzE,OAAA,CAACrC,SAAS;oBACRmK,SAAS;oBACTE,KAAK,EAAC,OAAO;oBACbtC,OAAO,EAAC,UAAU;oBAClBpB,EAAE,EAAE;sBACF,0BAA0B,EAAE;wBAC1BE,OAAO,EAAE,2BAA2B;wBACpC,YAAY,EAAE;0BAAEsC,WAAW,EAAE;wBAA2B,CAAC;wBACzD,kBAAkB,EAAE;0BAAEA,WAAW,EAAE;wBAA2B,CAAC;wBAC/D,wBAAwB,EAAE;0BAAEA,WAAW,EAAE;wBAAU;sBACrD,CAAC;sBACD,uBAAuB,EAAE;wBAAEnB,KAAK,EAAE;sBAAU,CAAC;sBAC7C,2BAA2B,EAAE;wBAAEA,KAAK,EAAE;sBAAQ;oBAChD;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACG,eACPzF,OAAA,CAACvC,IAAI;kBAACuI,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACoD,EAAE,EAAE,CAAE;kBAAA5E,QAAA,eACvBzE,OAAA,CAACrC,SAAS;oBACRmK,SAAS;oBACTE,KAAK,EAAC,SAAS;oBACftC,OAAO,EAAC,UAAU;oBAClBpB,EAAE,EAAE;sBACF,0BAA0B,EAAE;wBAC1BE,OAAO,EAAE,2BAA2B;wBACpC,YAAY,EAAE;0BAAEsC,WAAW,EAAE;wBAA2B,CAAC;wBACzD,kBAAkB,EAAE;0BAAEA,WAAW,EAAE;wBAA2B,CAAC;wBAC/D,wBAAwB,EAAE;0BAAEA,WAAW,EAAE;wBAAU;sBACrD,CAAC;sBACD,uBAAuB,EAAE;wBAAEnB,KAAK,EAAE;sBAAU,CAAC;sBAC7C,2BAA2B,EAAE;wBAAEA,KAAK,EAAE;sBAAQ;oBAChD;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACG,eACPzF,OAAA,CAACvC,IAAI;kBAACuI,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAAAxB,QAAA,eAChBzE,OAAA,CAACrC,SAAS;oBACRmK,SAAS;oBACTwB,SAAS;oBACTC,IAAI,EAAE,CAAE;oBACRvB,KAAK,EAAC,SAAS;oBACftC,OAAO,EAAC,UAAU;oBAClBpB,EAAE,EAAE;sBACF,0BAA0B,EAAE;wBAC1BE,OAAO,EAAE,2BAA2B;wBACpC,YAAY,EAAE;0BAAEsC,WAAW,EAAE;wBAA2B,CAAC;wBACzD,kBAAkB,EAAE;0BAAEA,WAAW,EAAE;wBAA2B,CAAC;wBAC/D,wBAAwB,EAAE;0BAAEA,WAAW,EAAE;wBAAU;sBACrD,CAAC;sBACD,uBAAuB,EAAE;wBAAEnB,KAAK,EAAE;sBAAU,CAAC;sBAC7C,2BAA2B,EAAE;wBAAEA,KAAK,EAAE;sBAAQ;oBAChD;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACG,eACPzF,OAAA,CAACvC,IAAI;kBAACuI,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAAAxB,QAAA,eAChBzE,OAAA,CAACxB,gBAAgB;oBACfgK,OAAO,eAAExI,OAAA,CAACzB,QAAQ;sBAAC+F,EAAE,EAAE;wBAAEqB,KAAK,EAAE;sBAAU;oBAAE;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAI;oBAChDuC,KAAK,EAAC,kCAAkC;oBACxC1D,EAAE,EAAE;sBAAEqB,KAAK,EAAE;oBAAU;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACzB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACG,eACPzF,OAAA,CAACvC,IAAI;kBAACuI,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAAAxB,QAAA,eAChBzE,OAAA,CAACpC,MAAM;oBACLkK,SAAS;oBACTpC,OAAO,EAAC,WAAW;oBACnBpB,EAAE,EAAE;sBACFE,OAAO,EAAE,SAAS;sBAClBK,EAAE,EAAE,GAAG;sBACP,SAAS,EAAE;wBAAEL,OAAO,EAAE;sBAAU;oBAClC,CAAE;oBAAAC,QAAA,EACH;kBAED;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBAAS;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACF,eAEPzF,OAAA,CAACvC,IAAI;cAACuI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzB,QAAA,eACvBzE,OAAA,CAACzC,GAAG;gBACF+G,EAAE,EAAE;kBACFe,MAAM,EAAE,MAAM;kBACd0B,UAAU,EAAE,mDAAmD;kBAC/DC,YAAY,EAAE,MAAM;kBACpBC,CAAC,EAAE,CAAC;kBACJnC,OAAO,EAAE,MAAM;kBACf0E,aAAa,EAAE,QAAQ;kBACvB5E,cAAc,EAAE;gBAClB,CAAE;gBAAAH,QAAA,gBAEFzE,OAAA,CAACtC,UAAU;kBACTgI,OAAO,EAAC,IAAI;kBACZpB,EAAE,EAAE;oBAAEqB,KAAK,EAAE,OAAO;oBAAEe,EAAE,EAAE,CAAC;oBAAEd,UAAU,EAAE;kBAAO,CAAE;kBAAAnB,QAAA,EACnD;gBAED;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAa,eACbzF,OAAA,CAACzC,GAAG;kBAAC+G,EAAE,EAAE;oBAAEQ,OAAO,EAAE,MAAM;oBAAE0E,aAAa,EAAE,QAAQ;oBAAExE,GAAG,EAAE;kBAAE,CAAE;kBAAAP,QAAA,gBAC5DzE,OAAA,CAACzC,GAAG;oBAAC+G,EAAE,EAAE;sBAAEQ,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEC,GAAG,EAAE;oBAAE,CAAE;oBAAAP,QAAA,gBACzDzE,OAAA,CAACT,UAAU;sBAAC+E,EAAE,EAAE;wBAAEqB,KAAK,EAAE;sBAAQ;oBAAE;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eACtCzF,OAAA,CAACtC,UAAU;sBAAC4G,EAAE,EAAE;wBAAEqB,KAAK,EAAE;sBAAQ,CAAE;sBAAAlB,QAAA,EAChCzC,UAAU,CAACK,WAAW,CAACC;oBAAO;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACpB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACT,eACNzF,OAAA,CAACzC,GAAG;oBAAC+G,EAAE,EAAE;sBAAEQ,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEC,GAAG,EAAE;oBAAE,CAAE;oBAAAP,QAAA,gBACzDzE,OAAA,CAACX,KAAK;sBAACiF,EAAE,EAAE;wBAAEqB,KAAK,EAAE;sBAAQ;oBAAE;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eACjCzF,OAAA,CAACtC,UAAU;sBAAC4G,EAAE,EAAE;wBAAEqB,KAAK,EAAE;sBAAQ,CAAE;sBAAAlB,QAAA,EAChCzC,UAAU,CAACK,WAAW,CAACE;oBAAK;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAClB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACT,eACNzF,OAAA,CAACzC,GAAG;oBAAC+G,EAAE,EAAE;sBAAEQ,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEC,GAAG,EAAE;oBAAE,CAAE;oBAAAP,QAAA,gBACzDzE,OAAA,CAACX,KAAK;sBAACiF,EAAE,EAAE;wBAAEqB,KAAK,EAAE;sBAAQ;oBAAE;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eACjCzF,OAAA,CAACtC,UAAU;sBAAC4G,EAAE,EAAE;wBAAEqB,KAAK,EAAE;sBAAQ,CAAE;sBAAAlB,QAAA,EAChCzC,UAAU,CAACK,WAAW,CAACG;oBAAc;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAC3B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACT,eACNzF,OAAA,CAACzC,GAAG;oBAAC+G,EAAE,EAAE;sBAAEQ,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEC,GAAG,EAAE;oBAAE,CAAE;oBAAAP,QAAA,gBACzDzE,OAAA,CAACV,KAAK;sBAACgF,EAAE,EAAE;wBAAEqB,KAAK,EAAE;sBAAQ;oBAAE;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eACjCzF,OAAA,CAACtC,UAAU;sBAAC4G,EAAE,EAAE;wBAAEqB,KAAK,EAAE;sBAAQ,CAAE;sBAAAlB,QAAA,EAChCzC,UAAU,CAACK,WAAW,CAACI;oBAAK;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAClB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACR,eAGNzF,OAAA,CAACzC,GAAG;MAAC+G,EAAE,EAAE;QAAEO,EAAE,EAAE,CAAC;QAAEL,OAAO,EAAE,SAAS;QAAEiF,SAAS,EAAE;MAAqC,CAAE;MAAAhF,QAAA,eACtFzE,OAAA,CAACxC,SAAS;QAACqI,QAAQ,EAAC,IAAI;QAAApB,QAAA,gBACtBzE,OAAA,CAACvC,IAAI;UAACqI,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAtB,QAAA,gBACzBzE,OAAA,CAACvC,IAAI;YAACuI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzB,QAAA,gBACvBzE,OAAA,CAACzC,GAAG;cAAC+G,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE,CAAC;gBAAE0B,EAAE,EAAE;cAAE,CAAE;cAAAjC,QAAA,gBAChEzE,OAAA,CAACzC,GAAG;gBACF0H,SAAS,EAAC,KAAK;gBACfC,GAAG,EAAC,wBAAwB;gBAC5BC,GAAG,EAAC,MAAM;gBACVb,EAAE,EAAE;kBAAEc,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAG;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAC9B,eACFzF,OAAA,CAACtC,UAAU;gBAACgI,OAAO,EAAC,IAAI;gBAACpB,EAAE,EAAE;kBAAEqB,KAAK,EAAE,SAAS;kBAAEC,UAAU,EAAE;gBAAO,CAAE;gBAAAnB,QAAA,EACnEzC,UAAU,CAACI;cAAU;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACT,eACNzF,OAAA,CAACtC,UAAU;cAAC4G,EAAE,EAAE;gBAAEqB,KAAK,EAAE,SAAS;gBAAEe,EAAE,EAAE,CAAC;gBAAEE,UAAU,EAAE;cAAI,CAAE;cAAAnC,QAAA,EAAC;YAE9D;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAa,eACbzF,OAAA,CAACzC,GAAG;cAAC+G,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEE,GAAG,EAAE;cAAE,CAAE;cAAAP,QAAA,gBACnCzE,OAAA,CAAC7B,UAAU;gBAACmG,EAAE,EAAE;kBAAEqB,KAAK,EAAE,SAAS;kBAAE,SAAS,EAAE;oBAAEA,KAAK,EAAE;kBAAU;gBAAE,CAAE;gBAAAlB,QAAA,eACpEzE,OAAA,CAACf,QAAQ;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACD,eACbzF,OAAA,CAAC7B,UAAU;gBAACmG,EAAE,EAAE;kBAAEqB,KAAK,EAAE,SAAS;kBAAE,SAAS,EAAE;oBAAEA,KAAK,EAAE;kBAAU;gBAAE,CAAE;gBAAAlB,QAAA,eACpEzE,OAAA,CAACd,SAAS;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACF,eACbzF,OAAA,CAAC7B,UAAU;gBAACmG,EAAE,EAAE;kBAAEqB,KAAK,EAAE,SAAS;kBAAE,SAAS,EAAE;oBAAEA,KAAK,EAAE;kBAAU;gBAAE,CAAE;gBAAAlB,QAAA,eACpEzE,OAAA,CAACb,OAAO;kBAAAmG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACA,eACbzF,OAAA,CAAC7B,UAAU;gBAACmG,EAAE,EAAE;kBAAEqB,KAAK,EAAE,SAAS;kBAAE,SAAS,EAAE;oBAAEA,KAAK,EAAE;kBAAU;gBAAE,CAAE;gBAAAlB,QAAA,eACpEzE,OAAA,CAACZ,QAAQ;kBAAAkG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACD,eAEPzF,OAAA,CAACvC,IAAI;YAACuI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzB,QAAA,gBACvBzE,OAAA,CAACtC,UAAU;cAACgI,OAAO,EAAC,IAAI;cAACpB,EAAE,EAAE;gBAAEqB,KAAK,EAAE,OAAO;gBAAEe,EAAE,EAAE,CAAC;gBAAEd,UAAU,EAAE;cAAO,CAAE;cAAAnB,QAAA,EAAC;YAE5E;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAa,eACbzF,OAAA,CAACzC,GAAG;cAAC+G,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE0E,aAAa,EAAE,QAAQ;gBAAExE,GAAG,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAC5DzE,OAAA,CAACpC,MAAM;gBAAC0G,EAAE,EAAE;kBAAEqB,KAAK,EAAE,SAAS;kBAAEf,cAAc,EAAE,YAAY;kBAAE,SAAS,EAAE;oBAAEe,KAAK,EAAE;kBAAU;gBAAE,CAAE;gBAAAlB,QAAA,EAAC;cAEjG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAS,eACTzF,OAAA,CAACpC,MAAM;gBAAC0G,EAAE,EAAE;kBAAEqB,KAAK,EAAE,SAAS;kBAAEf,cAAc,EAAE,YAAY;kBAAE,SAAS,EAAE;oBAAEe,KAAK,EAAE;kBAAU;gBAAE,CAAE;gBAAAlB,QAAA,EAAC;cAEjG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAS,eACTzF,OAAA,CAACpC,MAAM;gBAAC0G,EAAE,EAAE;kBAAEqB,KAAK,EAAE,SAAS;kBAAEf,cAAc,EAAE,YAAY;kBAAE,SAAS,EAAE;oBAAEe,KAAK,EAAE;kBAAU;gBAAE,CAAE;gBAAAlB,QAAA,EAAC;cAEjG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAS;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACD,eAEPzF,OAAA,CAACvC,IAAI;YAACuI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzB,QAAA,gBACvBzE,OAAA,CAACtC,UAAU;cAACgI,OAAO,EAAC,IAAI;cAACpB,EAAE,EAAE;gBAAEqB,KAAK,EAAE,OAAO;gBAAEe,EAAE,EAAE,CAAC;gBAAEd,UAAU,EAAE;cAAO,CAAE;cAAAnB,QAAA,EAAC;YAE5E;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAa,eACbzF,OAAA,CAACzC,GAAG;cAAC+G,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE0E,aAAa,EAAE,QAAQ;gBAAExE,GAAG,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAC5DzE,OAAA,CAACzC,GAAG;gBAAC+G,EAAE,EAAE;kBAAEQ,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAP,QAAA,gBACzDzE,OAAA,CAACT,UAAU;kBAAC+E,EAAE,EAAE;oBAAEqB,KAAK,EAAE;kBAAU;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG,eACxCzF,OAAA,CAACtC,UAAU;kBAAC4G,EAAE,EAAE;oBAAEqB,KAAK,EAAE;kBAAU,CAAE;kBAAAlB,QAAA,EAClCzC,UAAU,CAACK,WAAW,CAACC;gBAAO;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACpB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACT,eACNzF,OAAA,CAACzC,GAAG;gBAAC+G,EAAE,EAAE;kBAAEQ,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAP,QAAA,gBACzDzE,OAAA,CAACX,KAAK;kBAACiF,EAAE,EAAE;oBAAEqB,KAAK,EAAE;kBAAU;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG,eACnCzF,OAAA,CAACtC,UAAU;kBAAC4G,EAAE,EAAE;oBAAEqB,KAAK,EAAE;kBAAU,CAAE;kBAAAlB,QAAA,EAClCzC,UAAU,CAACK,WAAW,CAACE;gBAAK;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAClB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACT,eACNzF,OAAA,CAACzC,GAAG;gBAAC+G,EAAE,EAAE;kBAAEQ,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAP,QAAA,gBACzDzE,OAAA,CAACX,KAAK;kBAACiF,EAAE,EAAE;oBAAEqB,KAAK,EAAE;kBAAU;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG,eACnCzF,OAAA,CAACtC,UAAU;kBAAC4G,EAAE,EAAE;oBAAEqB,KAAK,EAAE;kBAAU,CAAE;kBAAAlB,QAAA,EAClCzC,UAAU,CAACK,WAAW,CAACG;gBAAc;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAC3B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACT,eACNzF,OAAA,CAACzC,GAAG;gBAAC+G,EAAE,EAAE;kBAAEQ,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAP,QAAA,gBACzDzE,OAAA,CAACV,KAAK;kBAACgF,EAAE,EAAE;oBAAEqB,KAAK,EAAE;kBAAU;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG,eACnCzF,OAAA,CAACtC,UAAU;kBAAC4G,EAAE,EAAE;oBAAEqB,KAAK,EAAE;kBAAU,CAAE;kBAAAlB,QAAA,EAClCzC,UAAU,CAACK,WAAW,CAACI;gBAAK;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAClB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACF,eAEPzF,OAAA,CAACzC,GAAG;UACF+G,EAAE,EAAE;YACFoF,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,CAAC;YACLF,SAAS,EAAE,oCAAoC;YAC/CnC,SAAS,EAAE;UACb,CAAE;UAAA7C,QAAA,eAEFzE,OAAA,CAACtC,UAAU;YAAC4G,EAAE,EAAE;cAAEqB,KAAK,EAAE;YAAU,CAAE;YAAAlB,QAAA,GAAC,YAC7B,EAACzC,UAAU,CAACI,UAAU,EAAC,wBAChC;UAAA;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAa;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACI;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACR,eAENzF,OAAA,CAACF,KAAK;MAACwB,OAAO,EAAEA,OAAQ;MAACD,YAAY,EAAEA,YAAa;MAACD,SAAS,EAAEA;IAAU;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG,eAG7EzF,OAAA,CAAC1B,GAAG;MACFqH,KAAK,EAAC,SAAS;MACf8C,IAAI,EAAC,OAAO;MACZlB,OAAO,EAAEtD,WAAY;MACrBK,EAAE,EAAE;QACFI,QAAQ,EAAE,OAAO;QACjBkF,MAAM,EAAE,EAAE;QACVlB,KAAK,EAAE,EAAE;QACTlE,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE;UAAEA,OAAO,EAAE;QAAU;MAClC,CAAE;MAAAC,QAAA,eAEFzE,OAAA,CAACR,WAAW;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAG;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACX;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACF;AAEV,CAAC;AAACrF,EAAA,CAp9BID,iBAAiB;EAAA,QACJ9C,WAAW,EACXD,WAAW,EACkCE,WAAW;AAAA;AAAAuM,EAAA,GAHrE1J,iBAAiB;AAs9BvB,eAAeA,iBAAiB;AAAC,IAAA0J,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}