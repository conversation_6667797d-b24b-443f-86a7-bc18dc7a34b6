const mongoose = require('mongoose');

// Login Page Settings Schema
const loginPageSchema = new mongoose.Schema({
  title: {
    type: String,
    default: 'School Management System',
    trim: true
  },
  subtitle: {
    type: String,
    default: 'Welcome back! Please enter your details',
    trim: true
  },
  backgroundImage: {
    type: String,
    default: '/assets/designlogin.jpg'
  },
  logoUrl: {
    type: String,
    default: '/assets/school-logo.png'
  },
  primaryColor: {
    type: String,
    default: '#667eea',
    validate: {
      validator: function(v) {
        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
      },
      message: 'Invalid color format'
    }
  },
  secondaryColor: {
    type: String,
    default: '#764ba2',
    validate: {
      validator: function(v) {
        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
      },
      message: 'Invalid color format'
    }
  },
  enableRememberMe: {
    type: Boolean,
    default: true
  },
  enableForgotPassword: {
    type: Boolean,
    default: true
  },
  enableRegistration: {
    type: Boolean,
    default: false
  },
  // Hero Section
  heroTitle: {
    type: String,
    default: 'Empowering Minds, Shaping Future Leaders',
    trim: true
  },
  heroSubtitle: {
    type: String,
    default: 'Discover a dynamic educational experience with interactive visuals, engaging content, and personalized learning paths designed to inspire and empower students.',
    trim: true
  },
  heroImage: {
    type: String,
    default: ''
  },
  showHeroSection: {
    type: Boolean,
    default: true
  },
  // Navigation Menu
  navigationMenu: [{
    title: { type: String, required: true },
    url: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    order: { type: Number, default: 0 }
  }],
  // Contact Information
  contactInfo: {
    address: { type: String, default: 'Partaj, Anantapuram, AP, India' },
    phone: { type: String, default: '7799505005' },
    alternatePhone: { type: String, default: '9866358067' },
    email: { type: String, default: '<EMAIL>' },
    showContact: { type: Boolean, default: true }
  },
  customCSS: {
    type: String,
    default: ''
  },
  footerText: {
    type: String,
    default: '© 2024 School Management System. All rights reserved.',
    trim: true
  },
  enableSocialLogin: {
    type: Boolean,
    default: false
  },
  maintenanceMode: {
    type: Boolean,
    default: false
  },
  customMessage: {
    type: String,
    default: '',
    trim: true
  }
});

// Theme Settings Schema
const themeSchema = new mongoose.Schema({
  darkMode: {
    type: Boolean,
    default: false
  },
  primaryColor: {
    type: String,
    default: '#667eea'
  },
  secondaryColor: {
    type: String,
    default: '#764ba2'
  },
  fontFamily: {
    type: String,
    enum: ['Roboto', 'Arial', 'Helvetica', 'Times New Roman'],
    default: 'Roboto'
  },
  borderRadius: {
    type: Number,
    default: 8,
    min: 0,
    max: 50
  },
  customTheme: {
    type: Boolean,
    default: false
  },
  customCSS: {
    type: String,
    default: ''
  }
});

// Security Settings Schema
const securitySchema = new mongoose.Schema({
  enableCaptcha: {
    type: Boolean,
    default: false
  },
  maxLoginAttempts: {
    type: Number,
    default: 5,
    min: 1,
    max: 20
  },
  lockoutDuration: {
    type: Number,
    default: 30,
    min: 1,
    max: 1440 // 24 hours in minutes
  },
  enableTwoFactor: {
    type: Boolean,
    default: false
  },
  passwordComplexity: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  sessionTimeout: {
    type: Number,
    default: 60,
    min: 5,
    max: 480 // 8 hours in minutes
  },
  enableIPWhitelist: {
    type: Boolean,
    default: false
  },
  whitelistedIPs: [{
    type: String,
    validate: {
      validator: function(v) {
        return /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/.test(v);
      },
      message: 'Invalid IP address format'
    }
  }]
});

// Page Schema for Custom Pages
const pageSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  content: {
    type: String,
    default: ''
  },
  template: {
    type: String,
    enum: ['blank', 'login', 'form', 'landing'],
    default: 'blank'
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },
  metaTitle: {
    type: String,
    trim: true
  },
  metaDescription: {
    type: String,
    trim: true
  },
  customCSS: {
    type: String,
    default: ''
  },
  customJS: {
    type: String,
    default: ''
  },
  components: [{
    id: String,
    type: {
      type: String,
      enum: ['header', 'form', 'image', 'text', 'custom'],
      required: true
    },
    props: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    },
    order: {
      type: Number,
      default: 0
    }
  }],
  publishedAt: {
    type: Date
  },
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'admin',
    required: true
  }
}, {
  timestamps: true
});

// Media File Schema
const mediaSchema = new mongoose.Schema({
  filename: {
    type: String,
    required: true,
    trim: true
  },
  originalName: {
    type: String,
    required: true,
    trim: true
  },
  mimeType: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true
  },
  path: {
    type: String,
    required: true
  },
  url: {
    type: String,
    required: true
  },
  folder: {
    type: String,
    default: 'uploads',
    trim: true
  },
  altText: {
    type: String,
    trim: true
  },
  caption: {
    type: String,
    trim: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'admin',
    required: true
  },
  school: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'admin',
    required: true
  }
}, {
  timestamps: true
});

// Testimonials Schema
const testimonialSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  role: {
    type: String,
    default: 'Student',
    trim: true
  },
  message: {
    type: String,
    required: true,
    trim: true
  },
  rating: {
    type: Number,
    min: 1,
    max: 5,
    default: 5
  },
  image: {
    type: String,
    default: ''
  },
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  },
  school: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'admin',
    required: true
  }
}, {
  timestamps: true
});

// Gallery Schema
const gallerySchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  image: {
    type: String,
    required: true
  },
  category: {
    type: String,
    enum: ['events', 'facilities', 'achievements', 'activities', 'campus'],
    default: 'events'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  },
  school: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'admin',
    required: true
  }
}, {
  timestamps: true
});

// Blog Posts Schema
const blogPostSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  excerpt: {
    type: String,
    trim: true
  },
  content: {
    type: String,
    required: true
  },
  image: {
    type: String,
    default: ''
  },
  author: {
    type: String,
    default: 'admin',
    trim: true
  },
  category: {
    type: String,
    enum: ['education', 'tips', 'news', 'events'],
    default: 'education'
  },
  isPublished: {
    type: Boolean,
    default: true
  },
  publishedDate: {
    type: Date,
    default: Date.now
  },
  school: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'admin',
    required: true
  }
}, {
  timestamps: true
});

// Main CMS Settings Schema
const cmsSettingsSchema = new mongoose.Schema({
  school: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'admin',
    required: true,
    unique: true
  },
  loginPage: {
    type: loginPageSchema,
    default: () => ({})
  },
  theme: {
    type: themeSchema,
    default: () => ({})
  },
  security: {
    type: securitySchema,
    default: () => ({})
  },
  siteSettings: {
    siteName: {
      type: String,
      default: 'School Management System',
      trim: true
    },
    siteDescription: {
      type: String,
      default: 'Comprehensive school management solution',
      trim: true
    },
    contactEmail: {
      type: String,
      trim: true,
      lowercase: true
    },
    contactPhone: {
      type: String,
      trim: true
    },
    address: {
      type: String,
      trim: true
    },
    timezone: {
      type: String,
      default: 'UTC'
    },
    language: {
      type: String,
      default: 'en',
      enum: ['en', 'es', 'fr', 'de', 'it', 'pt', 'hi', 'ar']
    }
  },
  socialMedia: {
    facebook: String,
    twitter: String,
    instagram: String,
    linkedin: String,
    youtube: String
  },
  analytics: {
    googleAnalyticsId: String,
    facebookPixelId: String,
    enableTracking: {
      type: Boolean,
      default: false
    }
  },
  lastModified: {
    type: Date,
    default: Date.now
  },
  modifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'admin'
  }
}, {
  timestamps: true
});

// Indexes
pageSchema.index({ slug: 1 });
pageSchema.index({ status: 1 });
pageSchema.index({ author: 1 });
mediaSchema.index({ school: 1, folder: 1 });
mediaSchema.index({ mimeType: 1 });
cmsSettingsSchema.index({ school: 1 });

// Pre-save middleware
cmsSettingsSchema.pre('save', function(next) {
  this.lastModified = new Date();
  next();
});

pageSchema.pre('save', function(next) {
  if (this.status === 'published' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  next();
});

// Instance methods
cmsSettingsSchema.methods.updateLoginSettings = function(settings) {
  this.loginPage = { ...this.loginPage.toObject(), ...settings };
  return this.save();
};

cmsSettingsSchema.methods.updateThemeSettings = function(settings) {
  this.theme = { ...this.theme.toObject(), ...settings };
  return this.save();
};

cmsSettingsSchema.methods.updateSecuritySettings = function(settings) {
  this.security = { ...this.security.toObject(), ...settings };
  return this.save();
};

// Static methods
cmsSettingsSchema.statics.getSchoolSettings = function(schoolId) {
  return this.findOne({ school: schoolId });
};

pageSchema.statics.getPublishedPages = function() {
  return this.find({ status: 'published' }).sort({ createdAt: -1 });
};

mediaSchema.statics.getMediaByFolder = function(schoolId, folder) {
  return this.find({ school: schoolId, folder: folder }).sort({ createdAt: -1 });
};

// Export models
const CMSSettings = mongoose.model('CMSSettings', cmsSettingsSchema);
const Page = mongoose.model('Page', pageSchema);
const Media = mongoose.model('Media', mediaSchema);
const Testimonial = mongoose.model('Testimonial', testimonialSchema);
const Gallery = mongoose.model('Gallery', gallerySchema);
const BlogPost = mongoose.model('BlogPost', blogPostSchema);

module.exports = {
  CMSSettings,
  Page,
  Media,
  Testimonial,
  Gallery,
  BlogPost
};
