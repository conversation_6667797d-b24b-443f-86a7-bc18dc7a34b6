{"ast": null, "code": "import { transform } from '../utils/transform.mjs';\nimport { transformValue } from './transform-value.mjs';\n\n/**\n * Create a `MotionValue` that maps the output of another `MotionValue` by\n * mapping it from one range of values into another.\n *\n * @remarks\n *\n * Given an input range of `[-200, -100, 100, 200]` and an output range of\n * `[0, 1, 1, 0]`, the returned `MotionValue` will:\n *\n * - When provided a value between `-200` and `-100`, will return a value between `0` and  `1`.\n * - When provided a value between `-100` and `100`, will return `1`.\n * - When provided a value between `100` and `200`, will return a value between `1` and  `0`\n *\n * The input range must be a linear series of numbers. The output range\n * can be any value type supported by Motion: numbers, colors, shadows, etc.\n *\n * Every value in the output range must be of the same type and in the same format.\n *\n * ```jsx\n * const x = motionValue(0)\n * const xRange = [-200, -100, 100, 200]\n * const opacityRange = [0, 1, 1, 0]\n * const opacity = mapValue(x, xRange, opacityRange)\n * ```\n *\n * @param inputValue - `MotionValue`\n * @param inputRange - A linear series of numbers (either all increasing or decreasing)\n * @param outputRange - A series of numbers, colors or strings. Must be the same length as `inputRange`.\n * @param options -\n *\n *  - clamp: boolean. Clamp values to within the given range. Defaults to `true`\n *  - ease: EasingFunction[]. Easing functions to use on the interpolations between each value in the input and output ranges. If provided as an array, the array must be one item shorter than the input and output ranges, as the easings apply to the transition between each.\n *\n * @returns `MotionValue`\n *\n * @public\n */\nfunction mapValue(inputValue, inputRange, outputRange, options) {\n  const map = transform(inputRange, outputRange, options);\n  return transformValue(() => map(inputValue.get()));\n}\nexport { mapValue };", "map": {"version": 3, "names": ["transform", "transformValue", "mapValue", "inputValue", "inputRange", "outputRange", "options", "map", "get"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/value/map-value.mjs"], "sourcesContent": ["import { transform } from '../utils/transform.mjs';\nimport { transformValue } from './transform-value.mjs';\n\n/**\n * Create a `MotionValue` that maps the output of another `MotionValue` by\n * mapping it from one range of values into another.\n *\n * @remarks\n *\n * Given an input range of `[-200, -100, 100, 200]` and an output range of\n * `[0, 1, 1, 0]`, the returned `MotionValue` will:\n *\n * - When provided a value between `-200` and `-100`, will return a value between `0` and  `1`.\n * - When provided a value between `-100` and `100`, will return `1`.\n * - When provided a value between `100` and `200`, will return a value between `1` and  `0`\n *\n * The input range must be a linear series of numbers. The output range\n * can be any value type supported by Motion: numbers, colors, shadows, etc.\n *\n * Every value in the output range must be of the same type and in the same format.\n *\n * ```jsx\n * const x = motionValue(0)\n * const xRange = [-200, -100, 100, 200]\n * const opacityRange = [0, 1, 1, 0]\n * const opacity = mapValue(x, xRange, opacityRange)\n * ```\n *\n * @param inputValue - `MotionValue`\n * @param inputRange - A linear series of numbers (either all increasing or decreasing)\n * @param outputRange - A series of numbers, colors or strings. Must be the same length as `inputRange`.\n * @param options -\n *\n *  - clamp: boolean. Clamp values to within the given range. Defaults to `true`\n *  - ease: EasingFunction[]. Easing functions to use on the interpolations between each value in the input and output ranges. If provided as an array, the array must be one item shorter than the input and output ranges, as the easings apply to the transition between each.\n *\n * @returns `MotionValue`\n *\n * @public\n */\nfunction mapValue(inputValue, inputRange, outputRange, options) {\n    const map = transform(inputRange, outputRange, options);\n    return transformValue(() => map(inputValue.get()));\n}\n\nexport { mapValue };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,SAASC,cAAc,QAAQ,uBAAuB;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC5D,MAAMC,GAAG,GAAGP,SAAS,CAACI,UAAU,EAAEC,WAAW,EAAEC,OAAO,CAAC;EACvD,OAAOL,cAAc,CAAC,MAAMM,GAAG,CAACJ,UAAU,CAACK,GAAG,EAAE,CAAC,CAAC;AACtD;AAEA,SAASN,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}