{"ast": null, "code": "\"use client\";\n\nimport { createContext } from 'react';\nconst MotionContext = /* @__PURE__ */createContext({});\nexport { MotionContext };", "map": {"version": 3, "names": ["createContext", "MotionContext"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/context/MotionContext/index.mjs"], "sourcesContent": ["\"use client\";\nimport { createContext } from 'react';\n\nconst MotionContext = /* @__PURE__ */ createContext({});\n\nexport { MotionContext };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,aAAa,QAAQ,OAAO;AAErC,MAAMC,aAAa,GAAG,eAAgBD,aAAa,CAAC,CAAC,CAAC,CAAC;AAEvD,SAASC,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}