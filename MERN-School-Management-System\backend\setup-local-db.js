const mongoose = require('mongoose');
const Admin = require('./models/adminSchema.js');
const Sclass = require('./models/sclassSchema.js');
const Subject = require('./models/subjectSchema.js');

const setupLocalDatabase = async () => {
    try {
        console.log('🔧 Setting up local MongoDB database...');
        
        // Connect to local MongoDB
        await mongoose.connect('mongodb://localhost:27017/school_management_system', {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('✅ Connected to local MongoDB');

        // Check if admin already exists
        const existingAdmin = await Admin.findOne({ email: '<EMAIL>' });
        
        let admin;
        if (existingAdmin) {
            console.log('ℹ️ Admin user already exists');
            admin = existingAdmin;
        } else {
            // Create admin user
            admin = new Admin({
                name: 'Administrator',
                email: '<EMAIL>',
                password: 'admin123',
                schoolName: '<PERSON> Secondary School',
                phone: '**********',
                address: 'Partaj, Anantapuram, AP, India',
                description: 'Default administrator account'
            });
            await admin.save();
            console.log('✅ Admin user created successfully!');
        }

        // Check if classes already exist
        const existingClasses = await Sclass.find({ school: admin._id });
        
        if (existingClasses.length > 0) {
            console.log(`ℹ️ ${existingClasses.length} classes already exist`);
        } else {
            // Create sample classes
            const classesData = [
                { sclassName: 'Class 1', school: admin._id },
                { sclassName: 'Class 2', school: admin._id },
                { sclassName: 'Class 3', school: admin._id },
                { sclassName: 'Class 4', school: admin._id },
                { sclassName: 'Class 5', school: admin._id },
                { sclassName: 'Class 6', school: admin._id },
                { sclassName: 'Class 7', school: admin._id },
                { sclassName: 'Class 8', school: admin._id },
                { sclassName: 'Class 9', school: admin._id },
                { sclassName: 'Class 10', school: admin._id }
            ];

            const createdClasses = await Sclass.insertMany(classesData);
            console.log(`✅ Created ${createdClasses.length} sample classes`);

            // Create sample subjects for each class
            const subjectsData = [];
            
            const commonSubjects = [
                { name: 'Mathematics', code: 'MATH', sessions: '40' },
                { name: 'English', code: 'ENG', sessions: '35' },
                { name: 'Science', code: 'SCI', sessions: '45' },
                { name: 'Social Studies', code: 'SS', sessions: '30' },
                { name: 'Hindi', code: 'HIN', sessions: '30' }
            ];

            const higherClassSubjects = [
                { name: 'Computer Science', code: 'CS', sessions: '25' },
                { name: 'Physical Education', code: 'PE', sessions: '20' }
            ];

            for (const sclass of createdClasses) {
                // Add common subjects
                for (const subject of commonSubjects) {
                    subjectsData.push({
                        subName: subject.name,
                        subCode: subject.code,
                        sessions: subject.sessions,
                        sclassName: sclass._id,
                        school: admin._id
                    });
                }

                // Add additional subjects for classes 6-10
                const classNumber = parseInt(sclass.sclassName.split(' ')[1]);
                if (classNumber >= 6) {
                    for (const subject of higherClassSubjects) {
                        subjectsData.push({
                            subName: subject.name,
                            subCode: subject.code,
                            sessions: subject.sessions,
                            sclassName: sclass._id,
                            school: admin._id
                        });
                    }
                }
            }

            await Subject.insertMany(subjectsData);
            console.log(`✅ Created ${subjectsData.length} sample subjects`);
        }

        console.log('\n🎉 Local database setup complete!');
        console.log('📧 Admin Email: <EMAIL>');
        console.log('🔑 Admin Password: admin123');
        console.log('🌐 Frontend URL: http://localhost:3001');
        console.log('🗄️ Database: school_management_system');
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Setup failed:', error.message);
        console.log('\n💡 Make sure MongoDB is installed and running:');
        console.log('   1. Install MongoDB Community Edition');
        console.log('   2. Start MongoDB service: net start MongoDB');
        console.log('   3. Run this script again');
        process.exit(1);
    }
};

setupLocalDatabase();
