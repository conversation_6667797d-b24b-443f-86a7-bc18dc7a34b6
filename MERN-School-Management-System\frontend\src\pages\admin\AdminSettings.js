import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  Avatar,
  Card,
  CardContent,
  Divider,
  IconButton,
  Alert,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  Settings as SettingsIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  PhotoCamera as PhotoCameraIcon,
  Save as SaveIcon,
  Edit as EditIcon,
  Palette as PaletteIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Language as LanguageIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';

const StyledContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(3),
  paddingBottom: theme.spacing(3),
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  borderRadius: theme.spacing(2),
  background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
}));

const ProfileCard = styled(Card)(({ theme }) => ({
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  marginBottom: theme.spacing(3),
  borderRadius: theme.spacing(2),
}));

const SettingsCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  borderRadius: theme.spacing(1.5),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
  },
}));

const AvatarUpload = styled(Box)(({ theme }) => ({
  position: 'relative',
  display: 'inline-block',
  '& .upload-overlay': {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    opacity: 0,
    transition: 'opacity 0.3s ease',
    cursor: 'pointer',
    '&:hover': {
      opacity: 1,
    },
  },
}));

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const AdminSettings = () => {
  const dispatch = useDispatch();
  const { currentUser } = useSelector(state => state.user);
  
  const [tabValue, setTabValue] = useState(0);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertSeverity, setAlertSeverity] = useState('success');
  const [logoDialog, setLogoDialog] = useState(false);

  // Profile Settings
  const [profileData, setProfileData] = useState({
    name: currentUser?.name || '',
    email: currentUser?.email || '',
    schoolName: currentUser?.schoolName || '',
    phone: '',
    address: '',
    website: '',
    description: '',
    avatar: null
  });

  // School Settings
  const [schoolSettings, setSchoolSettings] = useState({
    logo: null,
    primaryColor: '#667eea',
    secondaryColor: '#764ba2',
    timezone: 'UTC',
    academicYear: '2024-2025',
    currency: 'USD',
    language: 'en'
  });

  // System Settings
  const [systemSettings, setSystemSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    darkMode: false,
    autoBackup: true,
    maintenanceMode: false
  });

  // Security Settings
  const [securityData, setSecurityData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    twoFactorAuth: false
  });

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleProfileChange = (field) => (event) => {
    setProfileData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleSchoolSettingsChange = (field) => (event) => {
    setSchoolSettings(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleSystemSettingsChange = (field) => (event) => {
    setSystemSettings(prev => ({
      ...prev,
      [field]: event.target.checked
    }));
  };

  const handleSecurityChange = (field) => (event) => {
    setSecurityData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleAvatarUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileData(prev => ({
          ...prev,
          avatar: e.target.result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleLogoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setSchoolSettings(prev => ({
          ...prev,
          logo: e.target.result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSaveProfile = () => {
    // TODO: Implement API call to update profile
    setAlertMessage('Profile updated successfully!');
    setAlertSeverity('success');
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  const handleSaveSchoolSettings = () => {
    // TODO: Implement API call to update school settings
    setAlertMessage('School settings updated successfully!');
    setAlertSeverity('success');
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  const handleSaveSystemSettings = () => {
    // TODO: Implement API call to update system settings
    setAlertMessage('System settings updated successfully!');
    setAlertSeverity('success');
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  const handleChangePassword = () => {
    if (securityData.newPassword !== securityData.confirmPassword) {
      setAlertMessage('Passwords do not match!');
      setAlertSeverity('error');
      setShowAlert(true);
      setTimeout(() => setShowAlert(false), 3000);
      return;
    }
    
    // TODO: Implement API call to change password
    setAlertMessage('Password changed successfully!');
    setAlertSeverity('success');
    setShowAlert(true);
    setSecurityData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
      twoFactorAuth: securityData.twoFactorAuth
    });
    setTimeout(() => setShowAlert(false), 3000);
  };

  return (
    <StyledContainer maxWidth="lg">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: '#2c3e50', mb: 3 }}>
          <SettingsIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
          Admin Settings
        </Typography>

        {showAlert && (
          <Alert severity={alertSeverity} sx={{ mb: 3 }}>
            {alertMessage}
          </Alert>
        )}

        <StyledPaper>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}
          >
            <Tab icon={<PersonIcon />} label="Profile" />
            <Tab icon={<SchoolIcon />} label="School" />
            <Tab icon={<SettingsIcon />} label="System" />
            <Tab icon={<SecurityIcon />} label="Security" />
          </Tabs>

          {/* Profile Tab */}
          <TabPanel value={tabValue} index={0}>
            <ProfileCard>
              <CardContent>
                <Box display="flex" alignItems="center" gap={3}>
                  <AvatarUpload>
                    <Avatar
                      src={profileData.avatar}
                      sx={{ width: 100, height: 100 }}
                    >
                      {profileData.name.charAt(0)}
                    </Avatar>
                    <div className="upload-overlay">
                      <input
                        accept="image/*"
                        style={{ display: 'none' }}
                        id="avatar-upload"
                        type="file"
                        onChange={handleAvatarUpload}
                      />
                      <label htmlFor="avatar-upload">
                        <PhotoCameraIcon sx={{ color: 'white', fontSize: 30 }} />
                      </label>
                    </div>
                  </AvatarUpload>
                  <Box>
                    <Typography variant="h5" fontWeight="bold">
                      {profileData.name}
                    </Typography>
                    <Typography variant="body1" sx={{ opacity: 0.9 }}>
                      {profileData.email}
                    </Typography>
                    <Chip 
                      label="Administrator" 
                      sx={{ 
                        mt: 1, 
                        backgroundColor: 'rgba(255,255,255,0.2)',
                        color: 'white'
                      }} 
                    />
                  </Box>
                </Box>
              </CardContent>
            </ProfileCard>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Full Name"
                  value={profileData.name}
                  onChange={handleProfileChange('name')}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  value={profileData.email}
                  onChange={handleProfileChange('email')}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Phone"
                  value={profileData.phone}
                  onChange={handleProfileChange('phone')}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Website"
                  value={profileData.website}
                  onChange={handleProfileChange('website')}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Address"
                  multiline
                  rows={2}
                  value={profileData.address}
                  onChange={handleProfileChange('address')}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={profileData.description}
                  onChange={handleProfileChange('description')}
                  margin="normal"
                />
              </Grid>
            </Grid>

            <Box mt={3}>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSaveProfile}
                sx={{
                  background: 'linear-gradient(45deg, #667eea 30%, #764ba2 90%)',
                  color: 'white',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #5a6fd8 30%, #6a4190 90%)',
                  }
                }}
              >
                Save Profile
              </Button>
            </Box>
          </TabPanel>

          {/* School Tab */}
          <TabPanel value={tabValue} index={1}>
            <SettingsCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <SchoolIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  School Information
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="School Name"
                      value={profileData.schoolName}
                      onChange={handleProfileChange('schoolName')}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Academic Year"
                      value={schoolSettings.academicYear}
                      onChange={handleSchoolSettingsChange('academicYear')}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Currency</InputLabel>
                      <Select
                        value={schoolSettings.currency}
                        onChange={handleSchoolSettingsChange('currency')}
                        label="Currency"
                      >
                        <MenuItem value="USD">USD - US Dollar</MenuItem>
                        <MenuItem value="EUR">EUR - Euro</MenuItem>
                        <MenuItem value="GBP">GBP - British Pound</MenuItem>
                        <MenuItem value="INR">INR - Indian Rupee</MenuItem>
                        <MenuItem value="CAD">CAD - Canadian Dollar</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Language</InputLabel>
                      <Select
                        value={schoolSettings.language}
                        onChange={handleSchoolSettingsChange('language')}
                        label="Language"
                      >
                        <MenuItem value="en">English</MenuItem>
                        <MenuItem value="es">Spanish</MenuItem>
                        <MenuItem value="fr">French</MenuItem>
                        <MenuItem value="de">German</MenuItem>
                        <MenuItem value="hi">Hindi</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </CardContent>
            </SettingsCard>

            <SettingsCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <PaletteIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Branding & Appearance
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={4}>
                    <Box textAlign="center">
                      <Typography variant="subtitle1" gutterBottom>
                        School Logo
                      </Typography>
                      <Avatar
                        src={schoolSettings.logo}
                        sx={{
                          width: 80,
                          height: 80,
                          mx: 'auto',
                          mb: 2,
                          backgroundColor: 'primary.main'
                        }}
                      >
                        <SchoolIcon fontSize="large" />
                      </Avatar>
                      <input
                        accept="image/*"
                        style={{ display: 'none' }}
                        id="logo-upload"
                        type="file"
                        onChange={handleLogoUpload}
                      />
                      <label htmlFor="logo-upload">
                        <Button
                          variant="outlined"
                          component="span"
                          startIcon={<CloudUploadIcon />}
                          size="small"
                        >
                          Upload Logo
                        </Button>
                      </label>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Primary Color"
                      type="color"
                      value={schoolSettings.primaryColor}
                      onChange={handleSchoolSettingsChange('primaryColor')}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Secondary Color"
                      type="color"
                      value={schoolSettings.secondaryColor}
                      onChange={handleSchoolSettingsChange('secondaryColor')}
                      margin="normal"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </SettingsCard>

            <Box mt={3}>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSaveSchoolSettings}
                sx={{
                  background: 'linear-gradient(45deg, #667eea 30%, #764ba2 90%)',
                  color: 'white',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #5a6fd8 30%, #6a4190 90%)',
                  }
                }}
              >
                Save School Settings
              </Button>
            </Box>
          </TabPanel>

          {/* System Tab */}
          <TabPanel value={tabValue} index={2}>
            <SettingsCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <NotificationsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Notification Settings
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.emailNotifications}
                          onChange={handleSystemSettingsChange('emailNotifications')}
                        />
                      }
                      label="Email Notifications"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.smsNotifications}
                          onChange={handleSystemSettingsChange('smsNotifications')}
                        />
                      }
                      label="SMS Notifications"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </SettingsCard>

            <SettingsCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  System Preferences
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.darkMode}
                          onChange={handleSystemSettingsChange('darkMode')}
                        />
                      }
                      label="Dark Mode"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.autoBackup}
                          onChange={handleSystemSettingsChange('autoBackup')}
                        />
                      }
                      label="Auto Backup"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.maintenanceMode}
                          onChange={handleSystemSettingsChange('maintenanceMode')}
                        />
                      }
                      label="Maintenance Mode"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </SettingsCard>

            <Box mt={3}>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSaveSystemSettings}
                sx={{
                  background: 'linear-gradient(45deg, #667eea 30%, #764ba2 90%)',
                  color: 'white',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #5a6fd8 30%, #6a4190 90%)',
                  }
                }}
              >
                Save System Settings
              </Button>
            </Box>
          </TabPanel>

          {/* Security Tab */}
          <TabPanel value={tabValue} index={3}>
            <SettingsCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <SecurityIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Change Password
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Current Password"
                      type="password"
                      value={securityData.currentPassword}
                      onChange={handleSecurityChange('currentPassword')}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="New Password"
                      type="password"
                      value={securityData.newPassword}
                      onChange={handleSecurityChange('newPassword')}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Confirm New Password"
                      type="password"
                      value={securityData.confirmPassword}
                      onChange={handleSecurityChange('confirmPassword')}
                      margin="normal"
                    />
                  </Grid>
                </Grid>
                <Box mt={2}>
                  <Button
                    variant="contained"
                    onClick={handleChangePassword}
                    disabled={!securityData.currentPassword || !securityData.newPassword || !securityData.confirmPassword}
                    sx={{
                      background: 'linear-gradient(45deg, #ff6b6b 30%, #ee5a24 90%)',
                      color: 'white',
                      '&:hover': {
                        background: 'linear-gradient(45deg, #ff5252 30%, #d63031 90%)',
                      }
                    }}
                  >
                    Change Password
                  </Button>
                </Box>
              </CardContent>
            </SettingsCard>

            <SettingsCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Security Options
                </Typography>
                <FormControlLabel
                  control={
                    <Switch
                      checked={securityData.twoFactorAuth}
                      onChange={(e) => setSecurityData(prev => ({
                        ...prev,
                        twoFactorAuth: e.target.checked
                      }))}
                    />
                  }
                  label="Enable Two-Factor Authentication"
                />
                <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                  Add an extra layer of security to your account
                </Typography>
              </CardContent>
            </SettingsCard>
          </TabPanel>
        </StyledPaper>
      </motion.div>
    </StyledContainer>
  );
};

export default AdminSettings;
