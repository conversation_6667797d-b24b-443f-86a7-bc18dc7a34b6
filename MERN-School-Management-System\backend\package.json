{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "nodemon index.js", "build": "npm i", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "bcrypt": "^5.1.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "mongoose": "^7.0.4", "nodemon": "^2.0.22"}}