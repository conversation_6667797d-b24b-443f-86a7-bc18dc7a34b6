{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\components\\\\AccountMenu.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Avatar, Menu, MenuItem, ListItemIcon, Divider, IconButton, Tooltip } from '@mui/material';\nimport { Settings, Logout } from '@mui/icons-material';\nimport { Link } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AccountMenu = () => {\n  _s();\n  const [anchorEl, setAnchorEl] = useState(null);\n  const open = Boolean(anchorEl);\n  const {\n    currentRole,\n    currentUser\n  } = useSelector(state => state.user);\n  const handleClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Account settings\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleClick,\n          size: \"small\",\n          sx: {\n            ml: 2\n          },\n          \"aria-controls\": open ? 'account-menu' : undefined,\n          \"aria-haspopup\": \"true\",\n          \"aria-expanded\": open ? 'true' : undefined,\n          children: /*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              width: 32,\n              height: 32\n            },\n            children: String(currentUser.name).charAt(0)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: anchorEl,\n      id: \"account-menu\",\n      open: open,\n      onClose: handleClose,\n      onClick: handleClose,\n      PaperProps: {\n        elevation: 0,\n        sx: styles.styledPaper\n      },\n      transformOrigin: {\n        horizontal: 'right',\n        vertical: 'top'\n      },\n      anchorOrigin: {\n        horizontal: 'right',\n        vertical: 'bottom'\n      },\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: `/${currentRole}/profile`,\n          children: \"Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleClose,\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(Settings, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: `/${currentRole}/settings`,\n          children: \"Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(Logout, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/logout\",\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(AccountMenu, \"wAjeEHZCONsRt3S0/6XZZAJI81U=\", false, function () {\n  return [useSelector];\n});\n_c = AccountMenu;\nexport default AccountMenu;\nconst styles = {\n  styledPaper: {\n    overflow: 'visible',\n    filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',\n    mt: 1.5,\n    '& .MuiAvatar-root': {\n      width: 32,\n      height: 32,\n      ml: -0.5,\n      mr: 1\n    },\n    '&:before': {\n      content: '\"\"',\n      display: 'block',\n      position: 'absolute',\n      top: 0,\n      right: 14,\n      width: 10,\n      height: 10,\n      bgcolor: 'background.paper',\n      transform: 'translateY(-50%) rotate(45deg)',\n      zIndex: 0\n    }\n  }\n};\nvar _c;\n$RefreshReg$(_c, \"AccountMenu\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Avatar", "<PERSON><PERSON>", "MenuItem", "ListItemIcon", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Settings", "Logout", "Link", "useSelector", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AccountMenu", "_s", "anchorEl", "setAnchorEl", "open", "Boolean", "currentRole", "currentUser", "state", "user", "handleClick", "event", "currentTarget", "handleClose", "children", "sx", "display", "alignItems", "textAlign", "title", "onClick", "size", "ml", "undefined", "width", "height", "String", "name", "char<PERSON>t", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "onClose", "PaperProps", "elevation", "styles", "styledPaper", "transform<PERSON><PERSON>in", "horizontal", "vertical", "anchor<PERSON><PERSON><PERSON>", "to", "fontSize", "_c", "overflow", "filter", "mt", "mr", "content", "position", "top", "right", "bgcolor", "transform", "zIndex", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/components/AccountMenu.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Box, Avatar, Menu, MenuItem, ListItemIcon, Divider, IconButton, Tooltip } from '@mui/material';\r\nimport { Settings, Logout } from '@mui/icons-material';\r\nimport { Link } from 'react-router-dom';\r\nimport { useSelector } from 'react-redux';\r\n\r\nconst AccountMenu = () => {\r\n    const [anchorEl, setAnchorEl] = useState(null);\r\n\r\n    const open = Boolean(anchorEl);\r\n\r\n    const { currentRole, currentUser } = useSelector(state => state.user);\r\n\r\n    const handleClick = (event) => {\r\n        setAnchorEl(event.currentTarget);\r\n    };\r\n    const handleClose = () => {\r\n        setAnchorEl(null);\r\n    };\r\n    return (\r\n        <>\r\n            <Box sx={{ display: 'flex', alignItems: 'center', textAlign: 'center' }}>\r\n                <Tooltip title=\"Account settings\">\r\n                    <IconButton\r\n                        onClick={handleClick}\r\n                        size=\"small\"\r\n                        sx={{ ml: 2 }}\r\n                        aria-controls={open ? 'account-menu' : undefined}\r\n                        aria-haspopup=\"true\"\r\n                        aria-expanded={open ? 'true' : undefined}\r\n                    >\r\n                        <Avatar sx={{ width: 32, height: 32 }}>\r\n                            {String(currentUser.name).charAt(0)}\r\n                        </Avatar>\r\n                    </IconButton>\r\n                </Tooltip>\r\n            </Box>\r\n            <Menu\r\n                anchorEl={anchorEl}\r\n                id=\"account-menu\"\r\n                open={open}\r\n                onClose={handleClose}\r\n                onClick={handleClose}\r\n                PaperProps={{\r\n                    elevation: 0,\r\n                    sx: styles.styledPaper,\r\n                }}\r\n                transformOrigin={{ horizontal: 'right', vertical: 'top' }}\r\n                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}\r\n            >\r\n                <MenuItem>\r\n                    <Avatar />\r\n                    <Link to={`/${currentRole}/profile`}>\r\n                        Profile\r\n                    </Link>\r\n                </MenuItem>\r\n                <Divider />\r\n                <MenuItem onClick={handleClose}>\r\n                    <ListItemIcon>\r\n                        <Settings fontSize=\"small\" />\r\n                    </ListItemIcon>\r\n                    <Link to={`/${currentRole}/settings`}>\r\n                        Settings\r\n                    </Link>\r\n                </MenuItem>\r\n                <MenuItem>\r\n                    <ListItemIcon>\r\n                        <Logout fontSize=\"small\" />\r\n                    </ListItemIcon>\r\n                    <Link to=\"/logout\">\r\n                        Logout\r\n                    </Link>\r\n                </MenuItem>\r\n            </Menu>\r\n        </>\r\n    );\r\n}\r\n\r\nexport default AccountMenu\r\n\r\nconst styles = {\r\n    styledPaper: {\r\n        overflow: 'visible',\r\n        filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',\r\n        mt: 1.5,\r\n        '& .MuiAvatar-root': {\r\n            width: 32,\r\n            height: 32,\r\n            ml: -0.5,\r\n            mr: 1,\r\n        },\r\n        '&:before': {\r\n            content: '\"\"',\r\n            display: 'block',\r\n            position: 'absolute',\r\n            top: 0,\r\n            right: 14,\r\n            width: 10,\r\n            height: 10,\r\n            bgcolor: 'background.paper',\r\n            transform: 'translateY(-50%) rotate(45deg)',\r\n            zIndex: 0,\r\n        },\r\n    }\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,QAAQ,eAAe;AACvG,SAASC,QAAQ,EAAEC,MAAM,QAAQ,qBAAqB;AACtD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAE9C,MAAMqB,IAAI,GAAGC,OAAO,CAACH,QAAQ,CAAC;EAE9B,MAAM;IAAEI,WAAW;IAAEC;EAAY,CAAC,GAAGZ,WAAW,CAACa,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EAErE,MAAMC,WAAW,GAAIC,KAAK,IAAK;IAC3BR,WAAW,CAACQ,KAAK,CAACC,aAAa,CAAC;EACpC,CAAC;EACD,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACtBV,WAAW,CAAC,IAAI,CAAC;EACrB,CAAC;EACD,oBACIN,OAAA,CAAAE,SAAA;IAAAe,QAAA,gBACIjB,OAAA,CAACb,GAAG;MAAC+B,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAJ,QAAA,eACpEjB,OAAA,CAACN,OAAO;QAAC4B,KAAK,EAAC,kBAAkB;QAAAL,QAAA,eAC7BjB,OAAA,CAACP,UAAU;UACP8B,OAAO,EAAEV,WAAY;UACrBW,IAAI,EAAC,OAAO;UACZN,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAE,CAAE;UACd,iBAAelB,IAAI,GAAG,cAAc,GAAGmB,SAAU;UACjD,iBAAc,MAAM;UACpB,iBAAenB,IAAI,GAAG,MAAM,GAAGmB,SAAU;UAAAT,QAAA,eAEzCjB,OAAA,CAACZ,MAAM;YAAC8B,EAAE,EAAE;cAAES,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAG,CAAE;YAAAX,QAAA,EACjCY,MAAM,CAACnB,WAAW,CAACoB,IAAI,CAAC,CAACC,MAAM,CAAC,CAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAC9B;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACR,eACNnC,OAAA,CAACX,IAAI;MACDgB,QAAQ,EAAEA,QAAS;MACnB+B,EAAE,EAAC,cAAc;MACjB7B,IAAI,EAAEA,IAAK;MACX8B,OAAO,EAAErB,WAAY;MACrBO,OAAO,EAAEP,WAAY;MACrBsB,UAAU,EAAE;QACRC,SAAS,EAAE,CAAC;QACZrB,EAAE,EAAEsB,MAAM,CAACC;MACf,CAAE;MACFC,eAAe,EAAE;QAAEC,UAAU,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAM,CAAE;MAC1DC,YAAY,EAAE;QAAEF,UAAU,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAA3B,QAAA,gBAE1DjB,OAAA,CAACV,QAAQ;QAAA2B,QAAA,gBACLjB,OAAA,CAACZ,MAAM;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,eACVnC,OAAA,CAACH,IAAI;UAACiD,EAAE,EAAG,IAAGrC,WAAY,UAAU;UAAAQ,QAAA,EAAC;QAErC;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAO;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACA,eACXnC,OAAA,CAACR,OAAO;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eACXnC,OAAA,CAACV,QAAQ;QAACiC,OAAO,EAAEP,WAAY;QAAAC,QAAA,gBAC3BjB,OAAA,CAACT,YAAY;UAAA0B,QAAA,eACTjB,OAAA,CAACL,QAAQ;YAACoD,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAG;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAClB,eACfnC,OAAA,CAACH,IAAI;UAACiD,EAAE,EAAG,IAAGrC,WAAY,WAAW;UAAAQ,QAAA,EAAC;QAEtC;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAO;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACA,eACXnC,OAAA,CAACV,QAAQ;QAAA2B,QAAA,gBACLjB,OAAA,CAACT,YAAY;UAAA0B,QAAA,eACTjB,OAAA,CAACJ,MAAM;YAACmD,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAG;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAChB,eACfnC,OAAA,CAACH,IAAI;UAACiD,EAAE,EAAC,SAAS;UAAA7B,QAAA,EAAC;QAEnB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAO;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACA;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACR;EAAA,gBACR;AAEX,CAAC;AAAA/B,EAAA,CAtEKD,WAAW;EAAA,QAKwBL,WAAW;AAAA;AAAAkD,EAAA,GAL9C7C,WAAW;AAwEjB,eAAeA,WAAW;AAE1B,MAAMqC,MAAM,GAAG;EACXC,WAAW,EAAE;IACTQ,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,2CAA2C;IACnDC,EAAE,EAAE,GAAG;IACP,mBAAmB,EAAE;MACjBxB,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVH,EAAE,EAAE,CAAC,GAAG;MACR2B,EAAE,EAAE;IACR,CAAC;IACD,UAAU,EAAE;MACRC,OAAO,EAAE,IAAI;MACblC,OAAO,EAAE,OAAO;MAChBmC,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,EAAE;MACT7B,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACV6B,OAAO,EAAE,kBAAkB;MAC3BC,SAAS,EAAE,gCAAgC;MAC3CC,MAAM,EAAE;IACZ;EACJ;AACJ,CAAC;AAAA,IAAAX,EAAA;AAAAY,YAAA,CAAAZ,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}