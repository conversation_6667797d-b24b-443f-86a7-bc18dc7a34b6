{"ast": null, "code": "import { isHTMLElement } from 'motion-dom';\nfunction calcInset(element, container) {\n  const inset = {\n    x: 0,\n    y: 0\n  };\n  let current = element;\n  while (current && current !== container) {\n    if (isHTMLElement(current)) {\n      inset.x += current.offsetLeft;\n      inset.y += current.offsetTop;\n      current = current.offsetParent;\n    } else if (current.tagName === \"svg\") {\n      /**\n       * This isn't an ideal approach to measuring the offset of <svg /> tags.\n       * It would be preferable, given they behave like HTMLElements in most ways\n       * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n       * can't use .getBBox() like most SVG elements as these provide the offset\n       * relative to the SVG itself, which for <svg /> is usually 0x0.\n       */\n      const svgBoundingBox = current.getBoundingClientRect();\n      current = current.parentElement;\n      const parentBoundingBox = current.getBoundingClientRect();\n      inset.x += svgBoundingBox.left - parentBoundingBox.left;\n      inset.y += svgBoundingBox.top - parentBoundingBox.top;\n    } else if (current instanceof SVGGraphicsElement) {\n      const {\n        x,\n        y\n      } = current.getBBox();\n      inset.x += x;\n      inset.y += y;\n      let svg = null;\n      let parent = current.parentNode;\n      while (!svg) {\n        if (parent.tagName === \"svg\") {\n          svg = parent;\n        }\n        parent = current.parentNode;\n      }\n      current = svg;\n    } else {\n      break;\n    }\n  }\n  return inset;\n}\nexport { calcInset };", "map": {"version": 3, "names": ["isHTMLElement", "calcInset", "element", "container", "inset", "x", "y", "current", "offsetLeft", "offsetTop", "offsetParent", "tagName", "svgBoundingBox", "getBoundingClientRect", "parentElement", "parentBoundingBox", "left", "top", "SVGGraphicsElement", "getBBox", "svg", "parent", "parentNode"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs"], "sourcesContent": ["import { isHTMLElement } from 'motion-dom';\n\nfunction calcInset(element, container) {\n    const inset = { x: 0, y: 0 };\n    let current = element;\n    while (current && current !== container) {\n        if (isHTMLElement(current)) {\n            inset.x += current.offsetLeft;\n            inset.y += current.offsetTop;\n            current = current.offsetParent;\n        }\n        else if (current.tagName === \"svg\") {\n            /**\n             * This isn't an ideal approach to measuring the offset of <svg /> tags.\n             * It would be preferable, given they behave like HTMLElements in most ways\n             * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n             * can't use .getBBox() like most SVG elements as these provide the offset\n             * relative to the SVG itself, which for <svg /> is usually 0x0.\n             */\n            const svgBoundingBox = current.getBoundingClientRect();\n            current = current.parentElement;\n            const parentBoundingBox = current.getBoundingClientRect();\n            inset.x += svgBoundingBox.left - parentBoundingBox.left;\n            inset.y += svgBoundingBox.top - parentBoundingBox.top;\n        }\n        else if (current instanceof SVGGraphicsElement) {\n            const { x, y } = current.getBBox();\n            inset.x += x;\n            inset.y += y;\n            let svg = null;\n            let parent = current.parentNode;\n            while (!svg) {\n                if (parent.tagName === \"svg\") {\n                    svg = parent;\n                }\n                parent = current.parentNode;\n            }\n            current = svg;\n        }\n        else {\n            break;\n        }\n    }\n    return inset;\n}\n\nexport { calcInset };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAY;AAE1C,SAASC,SAASA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACnC,MAAMC,KAAK,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;EAC5B,IAAIC,OAAO,GAAGL,OAAO;EACrB,OAAOK,OAAO,IAAIA,OAAO,KAAKJ,SAAS,EAAE;IACrC,IAAIH,aAAa,CAACO,OAAO,CAAC,EAAE;MACxBH,KAAK,CAACC,CAAC,IAAIE,OAAO,CAACC,UAAU;MAC7BJ,KAAK,CAACE,CAAC,IAAIC,OAAO,CAACE,SAAS;MAC5BF,OAAO,GAAGA,OAAO,CAACG,YAAY;IAClC,CAAC,MACI,IAAIH,OAAO,CAACI,OAAO,KAAK,KAAK,EAAE;MAChC;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,cAAc,GAAGL,OAAO,CAACM,qBAAqB,EAAE;MACtDN,OAAO,GAAGA,OAAO,CAACO,aAAa;MAC/B,MAAMC,iBAAiB,GAAGR,OAAO,CAACM,qBAAqB,EAAE;MACzDT,KAAK,CAACC,CAAC,IAAIO,cAAc,CAACI,IAAI,GAAGD,iBAAiB,CAACC,IAAI;MACvDZ,KAAK,CAACE,CAAC,IAAIM,cAAc,CAACK,GAAG,GAAGF,iBAAiB,CAACE,GAAG;IACzD,CAAC,MACI,IAAIV,OAAO,YAAYW,kBAAkB,EAAE;MAC5C,MAAM;QAAEb,CAAC;QAAEC;MAAE,CAAC,GAAGC,OAAO,CAACY,OAAO,EAAE;MAClCf,KAAK,CAACC,CAAC,IAAIA,CAAC;MACZD,KAAK,CAACE,CAAC,IAAIA,CAAC;MACZ,IAAIc,GAAG,GAAG,IAAI;MACd,IAAIC,MAAM,GAAGd,OAAO,CAACe,UAAU;MAC/B,OAAO,CAACF,GAAG,EAAE;QACT,IAAIC,MAAM,CAACV,OAAO,KAAK,KAAK,EAAE;UAC1BS,GAAG,GAAGC,MAAM;QAChB;QACAA,MAAM,GAAGd,OAAO,CAACe,UAAU;MAC/B;MACAf,OAAO,GAAGa,GAAG;IACjB,CAAC,MACI;MACD;IACJ;EACJ;EACA,OAAOhB,KAAK;AAChB;AAEA,SAASH,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}