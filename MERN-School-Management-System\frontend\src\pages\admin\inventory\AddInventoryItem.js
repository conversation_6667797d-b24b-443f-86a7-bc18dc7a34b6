import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Avatar,
  Divider
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Inventory as InventoryIcon,
  Computer as ComputerIcon,
  MenuBook as BookIcon,
  Sports as SportsIcon,
  Science as ScienceIcon,
  Build as BuildIcon,
  Chair as FurnitureIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
}));

const FormCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  borderRadius: theme.spacing(2),
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
}));

const AddInventoryItem = () => {
  const navigate = useNavigate();
  
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertSeverity, setAlertSeverity] = useState('success');

  const [formData, setFormData] = useState({
    name: '',
    category: '',
    quantity: '',
    minQuantity: '',
    location: '',
    condition: 'Excellent',
    cost: '',
    supplier: '',
    serialNumber: '',
    description: '',
    purchaseDate: new Date().toISOString().split('T')[0],
    image: null
  });

  const categories = [
    { value: 'Electronics', icon: <ComputerIcon />, color: '#1976d2' },
    { value: 'Books', icon: <BookIcon />, color: '#7b1fa2' },
    { value: 'Sports', icon: <SportsIcon />, color: '#388e3c' },
    { value: 'Laboratory', icon: <ScienceIcon />, color: '#f57c00' },
    { value: 'Furniture', icon: <FurnitureIcon />, color: '#c2185b' },
    { value: 'Maintenance', icon: <BuildIcon />, color: '#689f38' }
  ];

  const conditions = ['Excellent', 'Good', 'Fair', 'Poor'];
  const locations = [
    'Computer Lab',
    'Library',
    'Sports Room',
    'Biology Lab',
    'Chemistry Lab',
    'Physics Lab',
    'Classroom A',
    'Classroom B',
    'Classroom C',
    'Principal Office',
    'Staff Room',
    'Storage Room',
    'Auditorium',
    'Playground'
  ];

  const handleInputChange = (field) => (event) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setFormData(prev => ({
          ...prev,
          image: e.target.result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    
    // Validation
    if (!formData.name || !formData.category || !formData.quantity || !formData.location) {
      setAlertMessage('Please fill in all required fields.');
      setAlertSeverity('error');
      setShowAlert(true);
      setTimeout(() => setShowAlert(false), 3000);
      return;
    }

    // TODO: Implement API call to save inventory item
    console.log('Saving inventory item:', formData);
    
    setAlertMessage('Inventory item added successfully!');
    setAlertSeverity('success');
    setShowAlert(true);
    
    // Reset form
    setFormData({
      name: '',
      category: '',
      quantity: '',
      minQuantity: '',
      location: '',
      condition: 'Excellent',
      cost: '',
      supplier: '',
      serialNumber: '',
      description: '',
      purchaseDate: new Date().toISOString().split('T')[0],
      image: null
    });
    
    setTimeout(() => {
      setShowAlert(false);
      navigate('/Admin/inventory');
    }, 2000);
  };

  const handleCancel = () => {
    navigate('/Admin/inventory');
  };

  const getCategoryIcon = (category) => {
    const categoryData = categories.find(cat => cat.value === category);
    return categoryData ? categoryData.icon : <InventoryIcon />;
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 2, mb: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* Header */}
        <Box display="flex" alignItems="center" mb={4}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleCancel}
            sx={{ mr: 2 }}
            color="primary"
          >
            Back
          </Button>
          <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
            <InventoryIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
            Add New Inventory Item
          </Typography>
        </Box>

        {showAlert && (
          <Alert severity={alertSeverity} sx={{ mb: 3 }}>
            {alertMessage}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <StyledPaper>
            {/* Basic Information */}
            <FormCard>
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <InventoryIcon sx={{ mr: 1 }} />
                  Basic Information
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} md={8}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Item Name *"
                          value={formData.name}
                          onChange={handleInputChange('name')}
                          required
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth required>
                          <InputLabel>Category *</InputLabel>
                          <Select
                            value={formData.category}
                            onChange={handleInputChange('category')}
                            label="Category *"
                          >
                            {categories.map((category) => (
                              <MenuItem key={category.value} value={category.value}>
                                <Box display="flex" alignItems="center">
                                  <Avatar
                                    sx={{
                                      bgcolor: category.color,
                                      width: 24,
                                      height: 24,
                                      mr: 1
                                    }}
                                  >
                                    {React.cloneElement(category.icon, { fontSize: 'small' })}
                                  </Avatar>
                                  {category.value}
                                </Box>
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Quantity *"
                          type="number"
                          value={formData.quantity}
                          onChange={handleInputChange('quantity')}
                          required
                          inputProps={{ min: 0 }}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Minimum Quantity"
                          type="number"
                          value={formData.minQuantity}
                          onChange={handleInputChange('minQuantity')}
                          inputProps={{ min: 0 }}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth required>
                          <InputLabel>Location *</InputLabel>
                          <Select
                            value={formData.location}
                            onChange={handleInputChange('location')}
                            label="Location *"
                          >
                            {locations.map((location) => (
                              <MenuItem key={location} value={location}>
                                {location}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth>
                          <InputLabel>Condition</InputLabel>
                          <Select
                            value={formData.condition}
                            onChange={handleInputChange('condition')}
                            label="Condition"
                          >
                            {conditions.map((condition) => (
                              <MenuItem key={condition} value={condition}>
                                {condition}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Grid>
                  
                  {/* Image Upload */}
                  <Grid item xs={12} md={4}>
                    <Box
                      sx={{
                        border: '2px dashed',
                        borderColor: 'primary.main',
                        borderRadius: 2,
                        p: 3,
                        textAlign: 'center',
                        backgroundColor: 'background.paper',
                        cursor: 'pointer',
                        '&:hover': {
                          backgroundColor: 'action.hover',
                        },
                      }}
                    >
                      {formData.image ? (
                        <Box>
                          <img
                            src={formData.image}
                            alt="Item preview"
                            style={{
                              width: '100%',
                              maxHeight: '200px',
                              objectFit: 'cover',
                              borderRadius: '8px',
                              marginBottom: '16px'
                            }}
                          />
                          <input
                            accept="image/*"
                            style={{ display: 'none' }}
                            id="image-upload"
                            type="file"
                            onChange={handleImageUpload}
                          />
                          <label htmlFor="image-upload">
                            <Button
                              variant="outlined"
                              component="span"
                              startIcon={<CloudUploadIcon />}
                              size="small"
                            >
                              Change Image
                            </Button>
                          </label>
                        </Box>
                      ) : (
                        <Box>
                          <Avatar
                            sx={{
                              width: 80,
                              height: 80,
                              mx: 'auto',
                              mb: 2,
                              bgcolor: 'primary.main'
                            }}
                          >
                            {getCategoryIcon(formData.category)}
                          </Avatar>
                          <input
                            accept="image/*"
                            style={{ display: 'none' }}
                            id="image-upload"
                            type="file"
                            onChange={handleImageUpload}
                          />
                          <label htmlFor="image-upload">
                            <Button
                              variant="outlined"
                              component="span"
                              startIcon={<CloudUploadIcon />}
                            >
                              Upload Image
                            </Button>
                          </label>
                          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                            Recommended: 300x300px, Max 5MB
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </FormCard>

            {/* Purchase Information */}
            <FormCard>
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <SaveIcon sx={{ mr: 1 }} />
                  Purchase Information
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Cost per Item"
                      type="number"
                      value={formData.cost}
                      onChange={handleInputChange('cost')}
                      InputProps={{
                        startAdornment: <Typography sx={{ mr: 1 }}>$</Typography>,
                      }}
                      inputProps={{ min: 0, step: 0.01 }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Purchase Date"
                      type="date"
                      value={formData.purchaseDate}
                      onChange={handleInputChange('purchaseDate')}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Supplier"
                      value={formData.supplier}
                      onChange={handleInputChange('supplier')}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Serial Number"
                      value={formData.serialNumber}
                      onChange={handleInputChange('serialNumber')}
                      placeholder="e.g., DL001-025"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Description"
                      multiline
                      rows={3}
                      value={formData.description}
                      onChange={handleInputChange('description')}
                      placeholder="Enter detailed description of the item..."
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </FormCard>

            {/* Action Buttons */}
            <Box display="flex" justifyContent="flex-end" gap={2} mt={3}>
              <Button
                variant="outlined"
                startIcon={<CancelIcon />}
                onClick={handleCancel}
                size="large"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                startIcon={<SaveIcon />}
                size="large"
                sx={{
                  background: 'linear-gradient(45deg, #667eea 30%, #764ba2 90%)',
                  color: 'white',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #5a6fd8 30%, #6a4190 90%)',
                  }
                }}
              >
                Add Item
              </Button>
            </Box>
          </StyledPaper>
        </form>
      </motion.div>
    </Container>
  );
};

export default AddInventoryItem;
