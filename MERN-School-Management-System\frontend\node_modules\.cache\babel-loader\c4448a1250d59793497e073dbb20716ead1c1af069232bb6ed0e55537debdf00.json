{"ast": null, "code": "import { isPressing } from './state.mjs';\n\n/**\n * Filter out events that are not \"Enter\" keys.\n */\nfunction filterEvents(callback) {\n  return event => {\n    if (event.key !== \"Enter\") return;\n    callback(event);\n  };\n}\nfunction firePointerEvent(target, type) {\n  target.dispatchEvent(new PointerEvent(\"pointer\" + type, {\n    isPrimary: true,\n    bubbles: true\n  }));\n}\nconst enableKeyboardPress = (focusEvent, eventOptions) => {\n  const element = focusEvent.currentTarget;\n  if (!element) return;\n  const handleKeydown = filterEvents(() => {\n    if (isPressing.has(element)) return;\n    firePointerEvent(element, \"down\");\n    const handleKeyup = filterEvents(() => {\n      firePointerEvent(element, \"up\");\n    });\n    const handleBlur = () => firePointerEvent(element, \"cancel\");\n    element.addEventListener(\"keyup\", handleKeyup, eventOptions);\n    element.addEventListener(\"blur\", handleBlur, eventOptions);\n  });\n  element.addEventListener(\"keydown\", handleKeydown, eventOptions);\n  /**\n   * Add an event listener that fires on blur to remove the keydown events.\n   */\n  element.addEventListener(\"blur\", () => element.removeEventListener(\"keydown\", handleKeydown), eventOptions);\n};\nexport { enableKeyboardPress };", "map": {"version": 3, "names": ["isPressing", "filterEvents", "callback", "event", "key", "firePointerEvent", "target", "type", "dispatchEvent", "PointerEvent", "isPrimary", "bubbles", "enableKeyboardPress", "focusEvent", "eventOptions", "element", "currentTarget", "handleKeydown", "has", "handleKeyup", "handleBlur", "addEventListener", "removeEventListener"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs"], "sourcesContent": ["import { isPressing } from './state.mjs';\n\n/**\n * Filter out events that are not \"Enter\" keys.\n */\nfunction filterEvents(callback) {\n    return (event) => {\n        if (event.key !== \"Enter\")\n            return;\n        callback(event);\n    };\n}\nfunction firePointerEvent(target, type) {\n    target.dispatchEvent(new PointerEvent(\"pointer\" + type, { isPrimary: true, bubbles: true }));\n}\nconst enableKeyboardPress = (focusEvent, eventOptions) => {\n    const element = focusEvent.currentTarget;\n    if (!element)\n        return;\n    const handleKeydown = filterEvents(() => {\n        if (isPressing.has(element))\n            return;\n        firePointerEvent(element, \"down\");\n        const handleKeyup = filterEvents(() => {\n            firePointerEvent(element, \"up\");\n        });\n        const handleBlur = () => firePointerEvent(element, \"cancel\");\n        element.addEventListener(\"keyup\", handleKeyup, eventOptions);\n        element.addEventListener(\"blur\", handleBlur, eventOptions);\n    });\n    element.addEventListener(\"keydown\", handleKeydown, eventOptions);\n    /**\n     * Add an event listener that fires on blur to remove the keydown events.\n     */\n    element.addEventListener(\"blur\", () => element.removeEventListener(\"keydown\", handleKeydown), eventOptions);\n};\n\nexport { enableKeyboardPress };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,aAAa;;AAExC;AACA;AACA;AACA,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAC5B,OAAQC,KAAK,IAAK;IACd,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EACrB;IACJF,QAAQ,CAACC,KAAK,CAAC;EACnB,CAAC;AACL;AACA,SAASE,gBAAgBA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACpCD,MAAM,CAACE,aAAa,CAAC,IAAIC,YAAY,CAAC,SAAS,GAAGF,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC,CAAC;AAChG;AACA,MAAMC,mBAAmB,GAAGA,CAACC,UAAU,EAAEC,YAAY,KAAK;EACtD,MAAMC,OAAO,GAAGF,UAAU,CAACG,aAAa;EACxC,IAAI,CAACD,OAAO,EACR;EACJ,MAAME,aAAa,GAAGhB,YAAY,CAAC,MAAM;IACrC,IAAID,UAAU,CAACkB,GAAG,CAACH,OAAO,CAAC,EACvB;IACJV,gBAAgB,CAACU,OAAO,EAAE,MAAM,CAAC;IACjC,MAAMI,WAAW,GAAGlB,YAAY,CAAC,MAAM;MACnCI,gBAAgB,CAACU,OAAO,EAAE,IAAI,CAAC;IACnC,CAAC,CAAC;IACF,MAAMK,UAAU,GAAGA,CAAA,KAAMf,gBAAgB,CAACU,OAAO,EAAE,QAAQ,CAAC;IAC5DA,OAAO,CAACM,gBAAgB,CAAC,OAAO,EAAEF,WAAW,EAAEL,YAAY,CAAC;IAC5DC,OAAO,CAACM,gBAAgB,CAAC,MAAM,EAAED,UAAU,EAAEN,YAAY,CAAC;EAC9D,CAAC,CAAC;EACFC,OAAO,CAACM,gBAAgB,CAAC,SAAS,EAAEJ,aAAa,EAAEH,YAAY,CAAC;EAChE;AACJ;AACA;EACIC,OAAO,CAACM,gBAAgB,CAAC,MAAM,EAAE,MAAMN,OAAO,CAACO,mBAAmB,CAAC,SAAS,EAAEL,aAAa,CAAC,EAAEH,YAAY,CAAC;AAC/G,CAAC;AAED,SAASF,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}