{"ast": null, "code": "import { secondsToMilliseconds } from 'motion-utils';\nimport { GroupAnimation } from '../animation/GroupAnimation.mjs';\nimport { NativeAnimation } from '../animation/NativeAnimation.mjs';\nimport { NativeAnimationWrapper } from '../animation/NativeAnimationWrapper.mjs';\nimport { getValueTransition } from '../animation/utils/get-value-transition.mjs';\nimport { mapEasingToNativeEasing } from '../animation/waapi/easing/map-easing.mjs';\nimport { applyGeneratorOptions } from '../animation/waapi/utils/apply-generator.mjs';\nimport { chooseLayerType } from './utils/choose-layer-type.mjs';\nimport { css } from './utils/css.mjs';\nimport { getLayerName } from './utils/get-layer-name.mjs';\nimport { getViewAnimations } from './utils/get-view-animations.mjs';\nimport { hasTarget } from './utils/has-target.mjs';\nconst definitionNames = [\"layout\", \"enter\", \"exit\", \"new\", \"old\"];\nfunction startViewAnimation(builder) {\n  const {\n    update,\n    targets,\n    options: defaultOptions\n  } = builder;\n  if (!document.startViewTransition) {\n    return new Promise(async resolve => {\n      await update();\n      resolve(new GroupAnimation([]));\n    });\n  }\n  // TODO: Go over existing targets and ensure they all have ids\n  /**\n   * If we don't have any animations defined for the root target,\n   * remove it from being captured.\n   */\n  if (!hasTarget(\"root\", targets)) {\n    css.set(\":root\", {\n      \"view-transition-name\": \"none\"\n    });\n  }\n  /**\n   * Set the timing curve to linear for all view transition layers.\n   * This gets baked into the keyframes, which can't be changed\n   * without breaking the generated animation.\n   *\n   * This allows us to set easing via updateTiming - which can be changed.\n   */\n  css.set(\"::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)\", {\n    \"animation-timing-function\": \"linear !important\"\n  });\n  css.commit(); // Write\n  const transition = document.startViewTransition(async () => {\n    await update();\n    // TODO: Go over new targets and ensure they all have ids\n  });\n\n  transition.finished.finally(() => {\n    css.remove(); // Write\n  });\n\n  return new Promise(resolve => {\n    transition.ready.then(() => {\n      const generatedViewAnimations = getViewAnimations();\n      const animations = [];\n      /**\n       * Create animations for each of our explicitly-defined subjects.\n       */\n      targets.forEach((definition, target) => {\n        // TODO: If target is not \"root\", resolve elements\n        // and iterate over each\n        for (const key of definitionNames) {\n          if (!definition[key]) continue;\n          const {\n            keyframes,\n            options\n          } = definition[key];\n          for (let [valueName, valueKeyframes] of Object.entries(keyframes)) {\n            if (!valueKeyframes) continue;\n            const valueOptions = {\n              ...getValueTransition(defaultOptions, valueName),\n              ...getValueTransition(options, valueName)\n            };\n            const type = chooseLayerType(key);\n            /**\n             * If this is an opacity animation, and keyframes are not an array,\n             * we need to convert them into an array and set an initial value.\n             */\n            if (valueName === \"opacity\" && !Array.isArray(valueKeyframes)) {\n              const initialValue = type === \"new\" ? 0 : 1;\n              valueKeyframes = [initialValue, valueKeyframes];\n            }\n            /**\n             * Resolve stagger function if provided.\n             */\n            if (typeof valueOptions.delay === \"function\") {\n              valueOptions.delay = valueOptions.delay(0, 1);\n            }\n            valueOptions.duration && (valueOptions.duration = secondsToMilliseconds(valueOptions.duration));\n            valueOptions.delay && (valueOptions.delay = secondsToMilliseconds(valueOptions.delay));\n            const animation = new NativeAnimation({\n              ...valueOptions,\n              element: document.documentElement,\n              name: valueName,\n              pseudoElement: `::view-transition-${type}(${target})`,\n              keyframes: valueKeyframes\n            });\n            animations.push(animation);\n          }\n        }\n      });\n      /**\n       * Handle browser generated animations\n       */\n      for (const animation of generatedViewAnimations) {\n        if (animation.playState === \"finished\") continue;\n        const {\n          effect\n        } = animation;\n        if (!effect || !(effect instanceof KeyframeEffect)) continue;\n        const {\n          pseudoElement\n        } = effect;\n        if (!pseudoElement) continue;\n        const name = getLayerName(pseudoElement);\n        if (!name) continue;\n        const targetDefinition = targets.get(name.layer);\n        if (!targetDefinition) {\n          /**\n           * If transition name is group then update the timing of the animation\n           * whereas if it's old or new then we could possibly replace it using\n           * the above method.\n           */\n          const transitionName = name.type === \"group\" ? \"layout\" : \"\";\n          let animationTransition = {\n            ...getValueTransition(defaultOptions, transitionName)\n          };\n          animationTransition.duration && (animationTransition.duration = secondsToMilliseconds(animationTransition.duration));\n          animationTransition = applyGeneratorOptions(animationTransition);\n          const easing = mapEasingToNativeEasing(animationTransition.ease, animationTransition.duration);\n          effect.updateTiming({\n            delay: secondsToMilliseconds(animationTransition.delay ?? 0),\n            duration: animationTransition.duration,\n            easing\n          });\n          animations.push(new NativeAnimationWrapper(animation));\n        } else if (hasOpacity(targetDefinition, \"enter\") && hasOpacity(targetDefinition, \"exit\") && effect.getKeyframes().some(keyframe => keyframe.mixBlendMode)) {\n          animations.push(new NativeAnimationWrapper(animation));\n        } else {\n          animation.cancel();\n        }\n      }\n      resolve(new GroupAnimation(animations));\n    });\n  });\n}\nfunction hasOpacity(target, key) {\n  return target?.[key]?.keyframes.opacity;\n}\nexport { startViewAnimation };", "map": {"version": 3, "names": ["secondsToMilliseconds", "GroupAnimation", "NativeAnimation", "NativeAnimationWrapper", "getValueTransition", "mapEasingToNativeEasing", "applyGeneratorOptions", "chooseLayerType", "css", "getLayerName", "getViewAnimations", "<PERSON><PERSON><PERSON><PERSON>", "definitionNames", "startViewAnimation", "builder", "update", "targets", "options", "defaultOptions", "document", "startViewTransition", "Promise", "resolve", "set", "commit", "transition", "finished", "finally", "remove", "ready", "then", "generatedViewAnimations", "animations", "for<PERSON>ach", "definition", "target", "key", "keyframes", "valueName", "valueKeyframes", "Object", "entries", "valueOptions", "type", "Array", "isArray", "initialValue", "delay", "duration", "animation", "element", "documentElement", "name", "pseudoElement", "push", "playState", "effect", "KeyframeEffect", "targetDefinition", "get", "layer", "transitionName", "animationTransition", "easing", "ease", "updateTiming", "hasOpacity", "getKeyframes", "some", "keyframe", "mixBlendMode", "cancel", "opacity"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/view/start.mjs"], "sourcesContent": ["import { secondsToMilliseconds } from 'motion-utils';\nimport { GroupAnimation } from '../animation/GroupAnimation.mjs';\nimport { NativeAnimation } from '../animation/NativeAnimation.mjs';\nimport { NativeAnimationWrapper } from '../animation/NativeAnimationWrapper.mjs';\nimport { getValueTransition } from '../animation/utils/get-value-transition.mjs';\nimport { mapEasingToNativeEasing } from '../animation/waapi/easing/map-easing.mjs';\nimport { applyGeneratorOptions } from '../animation/waapi/utils/apply-generator.mjs';\nimport { chooseLayerType } from './utils/choose-layer-type.mjs';\nimport { css } from './utils/css.mjs';\nimport { getLayerName } from './utils/get-layer-name.mjs';\nimport { getViewAnimations } from './utils/get-view-animations.mjs';\nimport { hasTarget } from './utils/has-target.mjs';\n\nconst definitionNames = [\"layout\", \"enter\", \"exit\", \"new\", \"old\"];\nfunction startViewAnimation(builder) {\n    const { update, targets, options: defaultOptions } = builder;\n    if (!document.startViewTransition) {\n        return new Promise(async (resolve) => {\n            await update();\n            resolve(new GroupAnimation([]));\n        });\n    }\n    // TODO: Go over existing targets and ensure they all have ids\n    /**\n     * If we don't have any animations defined for the root target,\n     * remove it from being captured.\n     */\n    if (!hasTarget(\"root\", targets)) {\n        css.set(\":root\", {\n            \"view-transition-name\": \"none\",\n        });\n    }\n    /**\n     * Set the timing curve to linear for all view transition layers.\n     * This gets baked into the keyframes, which can't be changed\n     * without breaking the generated animation.\n     *\n     * This allows us to set easing via updateTiming - which can be changed.\n     */\n    css.set(\"::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)\", { \"animation-timing-function\": \"linear !important\" });\n    css.commit(); // Write\n    const transition = document.startViewTransition(async () => {\n        await update();\n        // TODO: Go over new targets and ensure they all have ids\n    });\n    transition.finished.finally(() => {\n        css.remove(); // Write\n    });\n    return new Promise((resolve) => {\n        transition.ready.then(() => {\n            const generatedViewAnimations = getViewAnimations();\n            const animations = [];\n            /**\n             * Create animations for each of our explicitly-defined subjects.\n             */\n            targets.forEach((definition, target) => {\n                // TODO: If target is not \"root\", resolve elements\n                // and iterate over each\n                for (const key of definitionNames) {\n                    if (!definition[key])\n                        continue;\n                    const { keyframes, options } = definition[key];\n                    for (let [valueName, valueKeyframes] of Object.entries(keyframes)) {\n                        if (!valueKeyframes)\n                            continue;\n                        const valueOptions = {\n                            ...getValueTransition(defaultOptions, valueName),\n                            ...getValueTransition(options, valueName),\n                        };\n                        const type = chooseLayerType(key);\n                        /**\n                         * If this is an opacity animation, and keyframes are not an array,\n                         * we need to convert them into an array and set an initial value.\n                         */\n                        if (valueName === \"opacity\" &&\n                            !Array.isArray(valueKeyframes)) {\n                            const initialValue = type === \"new\" ? 0 : 1;\n                            valueKeyframes = [initialValue, valueKeyframes];\n                        }\n                        /**\n                         * Resolve stagger function if provided.\n                         */\n                        if (typeof valueOptions.delay === \"function\") {\n                            valueOptions.delay = valueOptions.delay(0, 1);\n                        }\n                        valueOptions.duration && (valueOptions.duration = secondsToMilliseconds(valueOptions.duration));\n                        valueOptions.delay && (valueOptions.delay = secondsToMilliseconds(valueOptions.delay));\n                        const animation = new NativeAnimation({\n                            ...valueOptions,\n                            element: document.documentElement,\n                            name: valueName,\n                            pseudoElement: `::view-transition-${type}(${target})`,\n                            keyframes: valueKeyframes,\n                        });\n                        animations.push(animation);\n                    }\n                }\n            });\n            /**\n             * Handle browser generated animations\n             */\n            for (const animation of generatedViewAnimations) {\n                if (animation.playState === \"finished\")\n                    continue;\n                const { effect } = animation;\n                if (!effect || !(effect instanceof KeyframeEffect))\n                    continue;\n                const { pseudoElement } = effect;\n                if (!pseudoElement)\n                    continue;\n                const name = getLayerName(pseudoElement);\n                if (!name)\n                    continue;\n                const targetDefinition = targets.get(name.layer);\n                if (!targetDefinition) {\n                    /**\n                     * If transition name is group then update the timing of the animation\n                     * whereas if it's old or new then we could possibly replace it using\n                     * the above method.\n                     */\n                    const transitionName = name.type === \"group\" ? \"layout\" : \"\";\n                    let animationTransition = {\n                        ...getValueTransition(defaultOptions, transitionName),\n                    };\n                    animationTransition.duration && (animationTransition.duration = secondsToMilliseconds(animationTransition.duration));\n                    animationTransition =\n                        applyGeneratorOptions(animationTransition);\n                    const easing = mapEasingToNativeEasing(animationTransition.ease, animationTransition.duration);\n                    effect.updateTiming({\n                        delay: secondsToMilliseconds(animationTransition.delay ?? 0),\n                        duration: animationTransition.duration,\n                        easing,\n                    });\n                    animations.push(new NativeAnimationWrapper(animation));\n                }\n                else if (hasOpacity(targetDefinition, \"enter\") &&\n                    hasOpacity(targetDefinition, \"exit\") &&\n                    effect\n                        .getKeyframes()\n                        .some((keyframe) => keyframe.mixBlendMode)) {\n                    animations.push(new NativeAnimationWrapper(animation));\n                }\n                else {\n                    animation.cancel();\n                }\n            }\n            resolve(new GroupAnimation(animations));\n        });\n    });\n}\nfunction hasOpacity(target, key) {\n    return target?.[key]?.keyframes.opacity;\n}\n\nexport { startViewAnimation };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,cAAc;AACpD,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,sBAAsB,QAAQ,yCAAyC;AAChF,SAASC,kBAAkB,QAAQ,6CAA6C;AAChF,SAASC,uBAAuB,QAAQ,0CAA0C;AAClF,SAASC,qBAAqB,QAAQ,8CAA8C;AACpF,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,SAAS,QAAQ,wBAAwB;AAElD,MAAMC,eAAe,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;AACjE,SAASC,kBAAkBA,CAACC,OAAO,EAAE;EACjC,MAAM;IAAEC,MAAM;IAAEC,OAAO;IAAEC,OAAO,EAAEC;EAAe,CAAC,GAAGJ,OAAO;EAC5D,IAAI,CAACK,QAAQ,CAACC,mBAAmB,EAAE;IAC/B,OAAO,IAAIC,OAAO,CAAC,MAAOC,OAAO,IAAK;MAClC,MAAMP,MAAM,EAAE;MACdO,OAAO,CAAC,IAAIrB,cAAc,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC,CAAC;EACN;EACA;EACA;AACJ;AACA;AACA;EACI,IAAI,CAACU,SAAS,CAAC,MAAM,EAAEK,OAAO,CAAC,EAAE;IAC7BR,GAAG,CAACe,GAAG,CAAC,OAAO,EAAE;MACb,sBAAsB,EAAE;IAC5B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIf,GAAG,CAACe,GAAG,CAAC,gFAAgF,EAAE;IAAE,2BAA2B,EAAE;EAAoB,CAAC,CAAC;EAC/If,GAAG,CAACgB,MAAM,EAAE,CAAC,CAAC;EACd,MAAMC,UAAU,GAAGN,QAAQ,CAACC,mBAAmB,CAAC,YAAY;IACxD,MAAML,MAAM,EAAE;IACd;EACJ,CAAC,CAAC;;EACFU,UAAU,CAACC,QAAQ,CAACC,OAAO,CAAC,MAAM;IAC9BnB,GAAG,CAACoB,MAAM,EAAE,CAAC,CAAC;EAClB,CAAC,CAAC;;EACF,OAAO,IAAIP,OAAO,CAAEC,OAAO,IAAK;IAC5BG,UAAU,CAACI,KAAK,CAACC,IAAI,CAAC,MAAM;MACxB,MAAMC,uBAAuB,GAAGrB,iBAAiB,EAAE;MACnD,MAAMsB,UAAU,GAAG,EAAE;MACrB;AACZ;AACA;MACYhB,OAAO,CAACiB,OAAO,CAAC,CAACC,UAAU,EAAEC,MAAM,KAAK;QACpC;QACA;QACA,KAAK,MAAMC,GAAG,IAAIxB,eAAe,EAAE;UAC/B,IAAI,CAACsB,UAAU,CAACE,GAAG,CAAC,EAChB;UACJ,MAAM;YAAEC,SAAS;YAAEpB;UAAQ,CAAC,GAAGiB,UAAU,CAACE,GAAG,CAAC;UAC9C,KAAK,IAAI,CAACE,SAAS,EAAEC,cAAc,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACJ,SAAS,CAAC,EAAE;YAC/D,IAAI,CAACE,cAAc,EACf;YACJ,MAAMG,YAAY,GAAG;cACjB,GAAGtC,kBAAkB,CAACc,cAAc,EAAEoB,SAAS,CAAC;cAChD,GAAGlC,kBAAkB,CAACa,OAAO,EAAEqB,SAAS;YAC5C,CAAC;YACD,MAAMK,IAAI,GAAGpC,eAAe,CAAC6B,GAAG,CAAC;YACjC;AACxB;AACA;AACA;YACwB,IAAIE,SAAS,KAAK,SAAS,IACvB,CAACM,KAAK,CAACC,OAAO,CAACN,cAAc,CAAC,EAAE;cAChC,MAAMO,YAAY,GAAGH,IAAI,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC;cAC3CJ,cAAc,GAAG,CAACO,YAAY,EAAEP,cAAc,CAAC;YACnD;YACA;AACxB;AACA;YACwB,IAAI,OAAOG,YAAY,CAACK,KAAK,KAAK,UAAU,EAAE;cAC1CL,YAAY,CAACK,KAAK,GAAGL,YAAY,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YACjD;YACAL,YAAY,CAACM,QAAQ,KAAKN,YAAY,CAACM,QAAQ,GAAGhD,qBAAqB,CAAC0C,YAAY,CAACM,QAAQ,CAAC,CAAC;YAC/FN,YAAY,CAACK,KAAK,KAAKL,YAAY,CAACK,KAAK,GAAG/C,qBAAqB,CAAC0C,YAAY,CAACK,KAAK,CAAC,CAAC;YACtF,MAAME,SAAS,GAAG,IAAI/C,eAAe,CAAC;cAClC,GAAGwC,YAAY;cACfQ,OAAO,EAAE/B,QAAQ,CAACgC,eAAe;cACjCC,IAAI,EAAEd,SAAS;cACfe,aAAa,EAAG,qBAAoBV,IAAK,IAAGR,MAAO,GAAE;cACrDE,SAAS,EAAEE;YACf,CAAC,CAAC;YACFP,UAAU,CAACsB,IAAI,CAACL,SAAS,CAAC;UAC9B;QACJ;MACJ,CAAC,CAAC;MACF;AACZ;AACA;MACY,KAAK,MAAMA,SAAS,IAAIlB,uBAAuB,EAAE;QAC7C,IAAIkB,SAAS,CAACM,SAAS,KAAK,UAAU,EAClC;QACJ,MAAM;UAAEC;QAAO,CAAC,GAAGP,SAAS;QAC5B,IAAI,CAACO,MAAM,IAAI,EAAEA,MAAM,YAAYC,cAAc,CAAC,EAC9C;QACJ,MAAM;UAAEJ;QAAc,CAAC,GAAGG,MAAM;QAChC,IAAI,CAACH,aAAa,EACd;QACJ,MAAMD,IAAI,GAAG3C,YAAY,CAAC4C,aAAa,CAAC;QACxC,IAAI,CAACD,IAAI,EACL;QACJ,MAAMM,gBAAgB,GAAG1C,OAAO,CAAC2C,GAAG,CAACP,IAAI,CAACQ,KAAK,CAAC;QAChD,IAAI,CAACF,gBAAgB,EAAE;UACnB;AACpB;AACA;AACA;AACA;UACoB,MAAMG,cAAc,GAAGT,IAAI,CAACT,IAAI,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE;UAC5D,IAAImB,mBAAmB,GAAG;YACtB,GAAG1D,kBAAkB,CAACc,cAAc,EAAE2C,cAAc;UACxD,CAAC;UACDC,mBAAmB,CAACd,QAAQ,KAAKc,mBAAmB,CAACd,QAAQ,GAAGhD,qBAAqB,CAAC8D,mBAAmB,CAACd,QAAQ,CAAC,CAAC;UACpHc,mBAAmB,GACfxD,qBAAqB,CAACwD,mBAAmB,CAAC;UAC9C,MAAMC,MAAM,GAAG1D,uBAAuB,CAACyD,mBAAmB,CAACE,IAAI,EAAEF,mBAAmB,CAACd,QAAQ,CAAC;UAC9FQ,MAAM,CAACS,YAAY,CAAC;YAChBlB,KAAK,EAAE/C,qBAAqB,CAAC8D,mBAAmB,CAACf,KAAK,IAAI,CAAC,CAAC;YAC5DC,QAAQ,EAAEc,mBAAmB,CAACd,QAAQ;YACtCe;UACJ,CAAC,CAAC;UACF/B,UAAU,CAACsB,IAAI,CAAC,IAAInD,sBAAsB,CAAC8C,SAAS,CAAC,CAAC;QAC1D,CAAC,MACI,IAAIiB,UAAU,CAACR,gBAAgB,EAAE,OAAO,CAAC,IAC1CQ,UAAU,CAACR,gBAAgB,EAAE,MAAM,CAAC,IACpCF,MAAM,CACDW,YAAY,EAAE,CACdC,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,YAAY,CAAC,EAAE;UAChDtC,UAAU,CAACsB,IAAI,CAAC,IAAInD,sBAAsB,CAAC8C,SAAS,CAAC,CAAC;QAC1D,CAAC,MACI;UACDA,SAAS,CAACsB,MAAM,EAAE;QACtB;MACJ;MACAjD,OAAO,CAAC,IAAIrB,cAAc,CAAC+B,UAAU,CAAC,CAAC;IAC3C,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,SAASkC,UAAUA,CAAC/B,MAAM,EAAEC,GAAG,EAAE;EAC7B,OAAOD,MAAM,GAAGC,GAAG,CAAC,EAAEC,SAAS,CAACmC,OAAO;AAC3C;AAEA,SAAS3D,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}