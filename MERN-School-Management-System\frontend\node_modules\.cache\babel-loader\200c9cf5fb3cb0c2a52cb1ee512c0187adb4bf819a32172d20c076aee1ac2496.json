{"ast": null, "code": "import { interpolate } from './interpolate.mjs';\nfunction transform() {\n  const useImmediate = !Array.isArray(arguments.length <= 0 ? undefined : arguments[0]);\n  const argOffset = useImmediate ? 0 : -1;\n  const inputValue = 0 + argOffset < 0 || arguments.length <= 0 + argOffset ? undefined : arguments[0 + argOffset];\n  const inputRange = 1 + argOffset < 0 || arguments.length <= 1 + argOffset ? undefined : arguments[1 + argOffset];\n  const outputRange = 2 + argOffset < 0 || arguments.length <= 2 + argOffset ? undefined : arguments[2 + argOffset];\n  const options = 3 + argOffset < 0 || arguments.length <= 3 + argOffset ? undefined : arguments[3 + argOffset];\n  const interpolator = interpolate(inputRange, outputRange, options);\n  return useImmediate ? interpolator(inputValue) : interpolator;\n}\nexport { transform };", "map": {"version": 3, "names": ["interpolate", "transform", "useImmediate", "Array", "isArray", "arguments", "length", "undefined", "argOffset", "inputValue", "inputRange", "outputRange", "options", "interpolator"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/utils/transform.mjs"], "sourcesContent": ["import { interpolate } from './interpolate.mjs';\n\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = interpolate(inputRange, outputRange, options);\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\nexport { transform };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,mBAAmB;AAE/C,SAASC,SAASA,CAAA,EAAU;EACxB,MAAMC,YAAY,GAAG,CAACC,KAAK,CAACC,OAAO,CAAAC,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,IAAS;EAC5C,MAAMG,SAAS,GAAGN,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;EACvC,MAAMO,UAAU,GAAQ,CAAC,GAAGD,SAAS,QAAAH,SAAA,CAAAC,MAAA,IAAb,CAAC,GAAGE,SAAS,GAAAD,SAAA,GAAAF,SAAA,CAAb,CAAC,GAAGG,SAAS,CAAC;EACtC,MAAME,UAAU,GAAQ,CAAC,GAAGF,SAAS,QAAAH,SAAA,CAAAC,MAAA,IAAb,CAAC,GAAGE,SAAS,GAAAD,SAAA,GAAAF,SAAA,CAAb,CAAC,GAAGG,SAAS,CAAC;EACtC,MAAMG,WAAW,GAAQ,CAAC,GAAGH,SAAS,QAAAH,SAAA,CAAAC,MAAA,IAAb,CAAC,GAAGE,SAAS,GAAAD,SAAA,GAAAF,SAAA,CAAb,CAAC,GAAGG,SAAS,CAAC;EACvC,MAAMI,OAAO,GAAQ,CAAC,GAAGJ,SAAS,QAAAH,SAAA,CAAAC,MAAA,IAAb,CAAC,GAAGE,SAAS,GAAAD,SAAA,GAAAF,SAAA,CAAb,CAAC,GAAGG,SAAS,CAAC;EACnC,MAAMK,YAAY,GAAGb,WAAW,CAACU,UAAU,EAAEC,WAAW,EAAEC,OAAO,CAAC;EAClE,OAAOV,YAAY,GAAGW,YAAY,CAACJ,UAAU,CAAC,GAAGI,YAAY;AACjE;AAEA,SAASZ,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}