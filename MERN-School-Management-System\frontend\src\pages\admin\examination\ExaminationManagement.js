import React, { useState, useEffect } from 'react';
import {
    Box,
    Paper,
    Typography,
    Grid,
    Card,
    CardContent,
    Button,
    IconButton,
    Chip,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Avatar,
    LinearProgress,
    Tooltip,
    Fab,
    Tab,
    Tabs,
    Badge,
} from '@mui/material';
import {
    Assessment as ExamIcon,
    Add as AddIcon,
    Edit as EditIcon,
    Visibility as ViewIcon,
    Schedule as ScheduleIcon,
    Grade as GradeIcon,
    TrendingUp as TrendingUpIcon,
    Group as GroupIcon,
    Assignment as AssignmentIcon,
    CheckCircle as CompletedIcon,
    Pending as PendingIcon,
    CalendarToday as CalendarIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

const ExaminationManagement = () => {
    const [tabValue, setTabValue] = useState(0);
    const [selectedClass, setSelectedClass] = useState('');
    const [selectedExam, setSelectedExam] = useState('');
    const [examData, setExamData] = useState([]);
    const [openDialog, setOpenDialog] = useState(false);
    const [loading, setLoading] = useState(false);

    // Sample data - replace with actual API calls
    const classes = [
        { id: 1, name: 'Class 1', students: 25 },
        { id: 2, name: 'Class 2', students: 28 },
        { id: 3, name: 'Class 3', students: 30 },
        { id: 4, name: 'Class 4', students: 27 },
        { id: 5, name: 'Class 5', students: 32 },
    ];

    const examTypes = [
        { id: 1, name: 'Unit Test 1', type: 'unit', status: 'completed' },
        { id: 2, name: 'Mid Term', type: 'midterm', status: 'ongoing' },
        { id: 3, name: 'Unit Test 2', type: 'unit', status: 'scheduled' },
        { id: 4, name: 'Final Exam', type: 'final', status: 'scheduled' },
    ];

    const sampleExamResults = [
        { id: 1, name: 'John Doe', rollNo: '001', math: 85, english: 78, science: 92, total: 255, percentage: 85.0, grade: 'A' },
        { id: 2, name: 'Jane Smith', rollNo: '002', math: 92, english: 88, science: 95, total: 275, percentage: 91.7, grade: 'A+' },
        { id: 3, name: 'Mike Johnson', rollNo: '003', math: 76, english: 82, science: 79, total: 237, percentage: 79.0, grade: 'B+' },
        { id: 4, name: 'Sarah Wilson', rollNo: '004', math: 88, english: 85, science: 90, total: 263, percentage: 87.7, grade: 'A' },
        { id: 5, name: 'David Brown', rollNo: '005', math: 72, english: 75, science: 78, total: 225, percentage: 75.0, grade: 'B' },
    ];

    const examStats = {
        totalExams: 12,
        completedExams: 8,
        ongoingExams: 2,
        scheduledExams: 2,
        averagePerformance: 82.5
    };

    const upcomingExams = [
        { id: 1, subject: 'Mathematics', class: 'Class 5', date: '2024-01-15', time: '10:00 AM', duration: '2 hours' },
        { id: 2, subject: 'English', class: 'Class 4', date: '2024-01-16', time: '09:00 AM', duration: '1.5 hours' },
        { id: 3, subject: 'Science', class: 'Class 3', date: '2024-01-17', time: '11:00 AM', duration: '2 hours' },
    ];

    useEffect(() => {
        if (selectedClass && selectedExam) {
            setLoading(true);
            // Simulate API call
            setTimeout(() => {
                setExamData(sampleExamResults);
                setLoading(false);
            }, 1000);
        }
    }, [selectedClass, selectedExam]);

    const getGradeColor = (grade) => {
        switch (grade) {
            case 'A+':
                return '#4caf50';
            case 'A':
                return '#8bc34a';
            case 'B+':
                return '#ff9800';
            case 'B':
                return '#ff5722';
            case 'C':
                return '#f44336';
            default:
                return '#757575';
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'completed':
                return '#4caf50';
            case 'ongoing':
                return '#ff9800';
            case 'scheduled':
                return '#2196f3';
            default:
                return '#757575';
        }
    };

    const handleCreateExam = () => {
        setOpenDialog(true);
    };

    const TabPanel = ({ children, value, index }) => (
        <div hidden={value !== index}>
            {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
        </div>
    );

    return (
        <Box sx={{ p: 3 }}>
            {/* Header */}
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
            >
                <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                    📝 Examination Management
                </Typography>
                <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                    Manage exams, schedules, and results efficiently
                </Typography>
            </motion.div>

            {/* Stats Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.1 }}
                    >
                        <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography variant="h4" fontWeight="bold">
                                            {examStats.totalExams}
                                        </Typography>
                                        <Typography variant="body2">Total Exams</Typography>
                                    </Box>
                                    <ExamIcon sx={{ fontSize: 40, opacity: 0.8 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.2 }}
                    >
                        <Card sx={{ background: 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)', color: 'white' }}>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography variant="h4" fontWeight="bold">
                                            {examStats.completedExams}
                                        </Typography>
                                        <Typography variant="body2">Completed</Typography>
                                    </Box>
                                    <CompletedIcon sx={{ fontSize: 40, opacity: 0.8 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.3 }}
                    >
                        <Card sx={{ background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)', color: 'white' }}>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography variant="h4" fontWeight="bold">
                                            {examStats.ongoingExams}
                                        </Typography>
                                        <Typography variant="body2">Ongoing</Typography>
                                    </Box>
                                    <PendingIcon sx={{ fontSize: 40, opacity: 0.8 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.4 }}
                    >
                        <Card sx={{ background: 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)', color: 'white' }}>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography variant="h4" fontWeight="bold">
                                            {examStats.averagePerformance}%
                                        </Typography>
                                        <Typography variant="body2">Avg Performance</Typography>
                                    </Box>
                                    <TrendingUpIcon sx={{ fontSize: 40, opacity: 0.8 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>
            </Grid>

            {/* Tabs */}
            <Paper sx={{ mb: 3 }}>
                <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                    <Tab label="Exam Schedule" />
                    <Tab label="Results & Grades" />
                    <Tab label="Upcoming Exams" />
                </Tabs>

                {/* Tab 1: Exam Schedule */}
                <TabPanel value={tabValue} index={0}>
                    <Box sx={{ p: 3 }}>
                        <Grid container spacing={3} alignItems="center" sx={{ mb: 3 }}>
                            <Grid item xs={12} md={4}>
                                <FormControl fullWidth>
                                    <InputLabel>Select Class</InputLabel>
                                    <Select
                                        value={selectedClass}
                                        onChange={(e) => setSelectedClass(e.target.value)}
                                        label="Select Class"
                                    >
                                        {classes.map((cls) => (
                                            <MenuItem key={cls.id} value={cls.id}>
                                                {cls.name}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <FormControl fullWidth>
                                    <InputLabel>Select Exam</InputLabel>
                                    <Select
                                        value={selectedExam}
                                        onChange={(e) => setSelectedExam(e.target.value)}
                                        label="Select Exam"
                                    >
                                        {examTypes.map((exam) => (
                                            <MenuItem key={exam.id} value={exam.id}>
                                                {exam.name}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <Button
                                    variant="contained"
                                    startIcon={<AddIcon />}
                                    onClick={handleCreateExam}
                                    sx={{ height: 56 }}
                                >
                                    Create Exam
                                </Button>
                            </Grid>
                        </Grid>

                        <Grid container spacing={3}>
                            {examTypes.map((exam) => (
                                <Grid item xs={12} md={6} key={exam.id}>
                                    <Card>
                                        <CardContent>
                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                                                <Typography variant="h6">{exam.name}</Typography>
                                                <Chip
                                                    label={exam.status.toUpperCase()}
                                                    sx={{
                                                        backgroundColor: getStatusColor(exam.status),
                                                        color: 'white',
                                                        fontWeight: 'bold'
                                                    }}
                                                />
                                            </Box>
                                            <Typography variant="body2" color="text.secondary" gutterBottom>
                                                Type: {exam.type.toUpperCase()}
                                            </Typography>
                                            <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                                                <Button size="small" startIcon={<EditIcon />}>
                                                    Edit
                                                </Button>
                                                <Button size="small" startIcon={<ViewIcon />}>
                                                    View
                                                </Button>
                                            </Box>
                                        </CardContent>
                                    </Card>
                                </Grid>
                            ))}
                        </Grid>
                    </Box>
                </TabPanel>

                {/* Tab 2: Results & Grades */}
                <TabPanel value={tabValue} index={1}>
                    <Box sx={{ p: 3 }}>
                        {selectedClass && selectedExam && (
                            <>
                                <Typography variant="h6" gutterBottom>
                                    Results for {classes.find(c => c.id === selectedClass)?.name} - {examTypes.find(e => e.id === selectedExam)?.name}
                                </Typography>
                                
                                {loading ? (
                                    <LinearProgress sx={{ my: 2 }} />
                                ) : (
                                    <TableContainer>
                                        <Table>
                                            <TableHead>
                                                <TableRow>
                                                    <TableCell>Roll No</TableCell>
                                                    <TableCell>Student Name</TableCell>
                                                    <TableCell>Math</TableCell>
                                                    <TableCell>English</TableCell>
                                                    <TableCell>Science</TableCell>
                                                    <TableCell>Total</TableCell>
                                                    <TableCell>Percentage</TableCell>
                                                    <TableCell>Grade</TableCell>
                                                    <TableCell>Actions</TableCell>
                                                </TableRow>
                                            </TableHead>
                                            <TableBody>
                                                {examData.map((student) => (
                                                    <TableRow key={student.id}>
                                                        <TableCell>{student.rollNo}</TableCell>
                                                        <TableCell>
                                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                                                <Avatar sx={{ width: 32, height: 32 }}>
                                                                    {student.name.charAt(0)}
                                                                </Avatar>
                                                                {student.name}
                                                            </Box>
                                                        </TableCell>
                                                        <TableCell>{student.math}</TableCell>
                                                        <TableCell>{student.english}</TableCell>
                                                        <TableCell>{student.science}</TableCell>
                                                        <TableCell><strong>{student.total}</strong></TableCell>
                                                        <TableCell><strong>{student.percentage}%</strong></TableCell>
                                                        <TableCell>
                                                            <Chip
                                                                label={student.grade}
                                                                sx={{
                                                                    backgroundColor: getGradeColor(student.grade),
                                                                    color: 'white',
                                                                    fontWeight: 'bold'
                                                                }}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Tooltip title="Edit Marks">
                                                                <IconButton size="small">
                                                                    <EditIcon />
                                                                </IconButton>
                                                            </Tooltip>
                                                            <Tooltip title="View Details">
                                                                <IconButton size="small">
                                                                    <ViewIcon />
                                                                </IconButton>
                                                            </Tooltip>
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </TableContainer>
                                )}
                            </>
                        )}
                    </Box>
                </TabPanel>

                {/* Tab 3: Upcoming Exams */}
                <TabPanel value={tabValue} index={2}>
                    <Box sx={{ p: 3 }}>
                        <Typography variant="h6" gutterBottom>
                            Upcoming Examinations
                        </Typography>
                        <Grid container spacing={3}>
                            {upcomingExams.map((exam) => (
                                <Grid item xs={12} md={6} lg={4} key={exam.id}>
                                    <Card>
                                        <CardContent>
                                            <Typography variant="h6" gutterBottom>
                                                {exam.subject}
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary" gutterBottom>
                                                {exam.class}
                                            </Typography>
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                                <CalendarIcon fontSize="small" />
                                                <Typography variant="body2">{exam.date}</Typography>
                                            </Box>
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                                <ScheduleIcon fontSize="small" />
                                                <Typography variant="body2">{exam.time}</Typography>
                                            </Box>
                                            <Typography variant="body2" color="text.secondary">
                                                Duration: {exam.duration}
                                            </Typography>
                                        </CardContent>
                                    </Card>
                                </Grid>
                            ))}
                        </Grid>
                    </Box>
                </TabPanel>
            </Paper>

            {/* Floating Action Button */}
            <Fab
                color="primary"
                aria-label="add"
                sx={{ position: 'fixed', bottom: 16, right: 16 }}
                onClick={handleCreateExam}
            >
                <AddIcon />
            </Fab>

            {/* Create Exam Dialog */}
            <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
                <DialogTitle>Create New Exam</DialogTitle>
                <DialogContent>
                    <Typography>Exam creation interface will be implemented here.</Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
                    <Button variant="contained">Create Exam</Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default ExaminationManagement;
