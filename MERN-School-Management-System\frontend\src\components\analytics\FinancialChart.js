import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { Paper, Typography, Box, Grid } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  height: '400px',
  background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
  color: '#333',
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
}));

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const FinancialChart = ({ data }) => {
  // Sample data if none provided
  const defaultData = [
    { name: 'Tuition Fees', value: 65, amount: 65000 },
    { name: 'Lab Fees', value: 15, amount: 15000 },
    { name: 'Library Fees', value: 8, amount: 8000 },
    { name: 'Sports Fees', value: 7, amount: 7000 },
    { name: 'Other Fees', value: 5, amount: 5000 },
  ];

  const chartData = data || defaultData;
  const totalAmount = chartData.reduce((sum, item) => sum + item.amount, 0);

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <StyledPaper elevation={3}>
      <Box mb={2}>
        <Typography variant="h6" component="h3" fontWeight="bold">
          💰 Fee Collection Breakdown
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.7 }}>
          Total Collection: ${totalAmount.toLocaleString()}
        </Typography>
      </Box>
      <Grid container spacing={2} height="85%">
        <Grid item xs={12} md={8}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value, name, props) => [
                  `$${props.payload.amount.toLocaleString()}`, 
                  name
                ]}
                contentStyle={{
                  backgroundColor: 'rgba(0,0,0,0.8)',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        </Grid>
        <Grid item xs={12} md={4}>
          <Box mt={2}>
            {chartData.map((entry, index) => (
              <Box key={entry.name} display="flex" alignItems="center" mb={1}>
                <Box
                  width={12}
                  height={12}
                  borderRadius="50%"
                  bgcolor={COLORS[index % COLORS.length]}
                  mr={1}
                />
                <Typography variant="body2" fontSize={11}>
                  {entry.name}: ${entry.amount.toLocaleString()}
                </Typography>
              </Box>
            ))}
          </Box>
        </Grid>
      </Grid>
    </StyledPaper>
  );
};

export default FinancialChart;
