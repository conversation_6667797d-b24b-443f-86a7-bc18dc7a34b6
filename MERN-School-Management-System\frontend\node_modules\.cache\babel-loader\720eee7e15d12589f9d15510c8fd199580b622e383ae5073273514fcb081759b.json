{"ast": null, "code": "import axios from 'axios';\nimport { getRequest, getSuccess, getFailed, getError, postDone, doneSuccess } from './teacherSlice';\nconst REACT_APP_BASE_URL = \"http://localhost:5000\";\nexport const getAllTeachers = id => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${REACT_APP_BASE_URL}/Teachers/${id}`);\n    if (result.data.message) {\n      dispatch(getFailed(result.data.message));\n    } else {\n      dispatch(getSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const getTeacherDetails = id => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/Teacher/${id}`);\n    if (result.data) {\n      dispatch(doneSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const updateTeachSubject = (teacherId, teachSubject) => async dispatch => {\n  dispatch(getRequest());\n  try {\n    await axios.put(`${process.env.REACT_APP_BASE_URL}/TeacherSubject`, {\n      teacherId,\n      teachSubject\n    }, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    dispatch(postDone());\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};", "map": {"version": 3, "names": ["axios", "getRequest", "getSuccess", "getFailed", "getError", "postDone", "doneSuccess", "REACT_APP_BASE_URL", "getAllTeachers", "id", "dispatch", "result", "get", "data", "message", "error", "getTeacherDetails", "process", "env", "updateTeachSubject", "teacherId", "teachSubject", "put", "headers"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/redux/teacherRelated/teacherHandle.js"], "sourcesContent": ["import axios from 'axios';\r\nimport {\r\n    getRequest,\r\n    getSuccess,\r\n    getFailed,\r\n    getError,\r\n    postDone,\r\n    doneSuccess\r\n} from './teacherSlice';\r\n\r\nconst REACT_APP_BASE_URL = \"http://localhost:5000\";\r\n\r\nexport const getAllTeachers = (id) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${REACT_APP_BASE_URL}/Teachers/${id}`);\r\n        if (result.data.message) {\r\n            dispatch(getFailed(result.data.message));\r\n        } else {\r\n            dispatch(getSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const getTeacherDetails = (id) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/Teacher/${id}`);\r\n        if (result.data) {\r\n            dispatch(doneSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const updateTeachSubject = (teacherId, teachSubject) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        await axios.put(`${process.env.REACT_APP_BASE_URL}/TeacherSubject`, { teacherId, teachSubject }, {\r\n            headers: { 'Content-Type': 'application/json' },\r\n        });\r\n        dispatch(postDone());\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,UAAU,EACVC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,QAAQ,EACRC,WAAW,QACR,gBAAgB;AAEvB,MAAMC,kBAAkB,GAAG,uBAAuB;AAElD,OAAO,MAAMC,cAAc,GAAIC,EAAE,IAAK,MAAOC,QAAQ,IAAK;EACtDA,QAAQ,CAACT,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMU,MAAM,GAAG,MAAMX,KAAK,CAACY,GAAG,CAAE,GAAEL,kBAAmB,aAAYE,EAAG,EAAC,CAAC;IACtE,IAAIE,MAAM,CAACE,IAAI,CAACC,OAAO,EAAE;MACrBJ,QAAQ,CAACP,SAAS,CAACQ,MAAM,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC;IAC5C,CAAC,MAAM;MACHJ,QAAQ,CAACR,UAAU,CAACS,MAAM,CAACE,IAAI,CAAC,CAAC;IACrC;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZL,QAAQ,CAACN,QAAQ,CAACW,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAIP,EAAE,IAAK,MAAOC,QAAQ,IAAK;EACzDA,QAAQ,CAACT,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMU,MAAM,GAAG,MAAMX,KAAK,CAACY,GAAG,CAAE,GAAEK,OAAO,CAACC,GAAG,CAACX,kBAAmB,YAAWE,EAAG,EAAC,CAAC;IACjF,IAAIE,MAAM,CAACE,IAAI,EAAE;MACbH,QAAQ,CAACJ,WAAW,CAACK,MAAM,CAACE,IAAI,CAAC,CAAC;IACtC;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZL,QAAQ,CAACN,QAAQ,CAACW,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMI,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,YAAY,KAAK,MAAOX,QAAQ,IAAK;EAC/EA,QAAQ,CAACT,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMD,KAAK,CAACsB,GAAG,CAAE,GAAEL,OAAO,CAACC,GAAG,CAACX,kBAAmB,iBAAgB,EAAE;MAAEa,SAAS;MAAEC;IAAa,CAAC,EAAE;MAC7FE,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB;IAClD,CAAC,CAAC;IACFb,QAAQ,CAACL,QAAQ,EAAE,CAAC;EACxB,CAAC,CAAC,OAAOU,KAAK,EAAE;IACZL,QAAQ,CAACN,QAAQ,CAACW,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}