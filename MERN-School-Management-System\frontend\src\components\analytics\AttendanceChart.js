import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { Paper, Typography, Box } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  height: '400px',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
}));

const AttendanceChart = ({ data }) => {
  // Sample data if none provided
  const defaultData = [
    { month: 'Jan', attendance: 85, target: 90 },
    { month: 'Feb', attendance: 88, target: 90 },
    { month: 'Mar', attendance: 92, target: 90 },
    { month: 'Apr', attendance: 87, target: 90 },
    { month: 'May', attendance: 94, target: 90 },
    { month: 'Jun', attendance: 89, target: 90 },
  ];

  const chartData = data || defaultData;

  return (
    <StyledPaper elevation={3}>
      <Box mb={2}>
        <Typography variant="h6" component="h3" fontWeight="bold">
          📊 Attendance Trends
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.8 }}>
          Monthly attendance vs target
        </Typography>
      </Box>
      <ResponsiveContainer width="100%" height="85%">
        <LineChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.2)" />
          <XAxis 
            dataKey="month" 
            stroke="white"
            fontSize={12}
          />
          <YAxis 
            stroke="white"
            fontSize={12}
            domain={[70, 100]}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: 'rgba(0,0,0,0.8)',
              border: 'none',
              borderRadius: '8px',
              color: 'white'
            }}
          />
          <Legend />
          <Line 
            type="monotone" 
            dataKey="attendance" 
            stroke="#FFD700" 
            strokeWidth={3}
            dot={{ fill: '#FFD700', strokeWidth: 2, r: 6 }}
            name="Actual Attendance (%)"
          />
          <Line 
            type="monotone" 
            dataKey="target" 
            stroke="#FF6B6B" 
            strokeWidth={2}
            strokeDasharray="5 5"
            dot={{ fill: '#FF6B6B', strokeWidth: 2, r: 4 }}
            name="Target (%)"
          />
        </LineChart>
      </ResponsiveContainer>
    </StyledPaper>
  );
};

export default AttendanceChart;
