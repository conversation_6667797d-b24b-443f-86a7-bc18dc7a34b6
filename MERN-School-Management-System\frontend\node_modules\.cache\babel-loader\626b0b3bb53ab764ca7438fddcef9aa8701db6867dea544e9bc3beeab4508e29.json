{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\pages\\\\ChooseUser.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Grid, Paper, Box, Container } from '@mui/material';\nimport { AccountCircle, School, Group } from '@mui/icons-material';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChooseUser = () => {\n  _s();\n  const navigate = useNavigate();\n  const navigateHandler = user => {\n    if (user === \"Admin\") {\n      navigate('/Adminlogin');\n    } else if (user === \"Student\") {\n      navigate('/Studentlogin');\n    } else if (user === \"Teacher\") {\n      navigate('/Teacherlogin');\n    }\n  };\n  useEffect(() => {\n    if (status === 'success' || currentUser !== null) {\n      if (currentRole === 'Admin') {\n        navigate('/Admin/dashboard');\n      } else if (currentRole === 'Student') {\n        navigate('/Student/dashboard');\n      } else if (currentRole === 'Teacher') {\n        navigate('/Teacher/dashboard');\n      }\n    } else if (status === 'error') {\n      setLoader(false);\n      setMessage(\"Network Error\");\n      setShowPopup(true);\n    }\n  }, [status, currentRole, navigate, currentUser]);\n  return /*#__PURE__*/_jsxDEV(StyledContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        justifyContent: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => navigateHandler(\"Admin\"),\n            children: /*#__PURE__*/_jsxDEV(StyledPaper, {\n              elevation: 3,\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: /*#__PURE__*/_jsxDEV(AccountCircle, {\n                  fontSize: \"large\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTypography, {\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this), \"Login as an administrator to access the dashboard to manage app data.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(StyledPaper, {\n            elevation: 3,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => navigateHandler(\"Student\"),\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: /*#__PURE__*/_jsxDEV(School, {\n                  fontSize: \"large\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTypography, {\n                children: \"Student\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), \"Login as a student to explore course materials and assignments.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(StyledPaper, {\n            elevation: 3,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => navigateHandler(\"Teacher\"),\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: /*#__PURE__*/_jsxDEV(Group, {\n                  fontSize: \"large\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTypography, {\n                children: \"Teacher\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this), \"Login as a teacher to create courses, assignments, and track student progress.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Backdrop, {\n      sx: {\n        color: '#fff',\n        zIndex: theme => theme.zIndex.drawer + 1\n      },\n      open: loader,\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        color: \"inherit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), \"Please Wait\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Popup, {\n      message: message,\n      setShowPopup: setShowPopup,\n      showPopup: showPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(ChooseUser, \"0pNeyzXk/ByIxyERsdaIrG6js9s=\", false, function () {\n  return [useNavigate];\n});\n_c = ChooseUser;\nexport default ChooseUser;\nconst StyledContainer = styled.div`\n  background: linear-gradient(to bottom, #411d70, #19118b);\n  height: 120vh;\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n`;\n_c2 = StyledContainer;\nconst StyledPaper = styled(Paper)`\n  padding: 20px;\n  text-align: center;\n  background-color: #1f1f38;\n  color:rgba(255, 255, 255, 0.6);\n  cursor:pointer;\n\n  &:hover {\n    background-color: #2c2c6c;\n    color:white;\n  }\n`;\n_c3 = StyledPaper;\nconst StyledTypography = styled.h2`\n  margin-bottom: 10px;\n`;\n_c4 = StyledTypography;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ChooseUser\");\n$RefreshReg$(_c2, \"StyledContainer\");\n$RefreshReg$(_c3, \"StyledPaper\");\n$RefreshReg$(_c4, \"StyledTypography\");", "map": {"version": 3, "names": ["React", "useNavigate", "Grid", "Paper", "Box", "Container", "AccountCircle", "School", "Group", "styled", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "_s", "navigate", "<PERSON><PERSON><PERSON><PERSON>", "user", "useEffect", "status", "currentUser", "currentRole", "<PERSON><PERSON><PERSON><PERSON>", "setMessage", "setShowPopup", "StyledContainer", "children", "container", "spacing", "justifyContent", "item", "xs", "sm", "md", "onClick", "StyledPaper", "elevation", "mb", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "StyledTypography", "Backdrop", "sx", "color", "zIndex", "theme", "drawer", "open", "loader", "CircularProgress", "Popup", "message", "showPopup", "_c", "div", "_c2", "_c3", "h2", "_c4", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/pages/ChooseUser.js"], "sourcesContent": ["import React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport {\r\n  Grid,\r\n  Paper,\r\n  Box,\r\n  Container,\r\n} from '@mui/material';\r\nimport { AccountCircle, School, Group } from '@mui/icons-material';\r\nimport styled from 'styled-components';\r\n\r\nconst ChooseUser = () => {\r\n  const navigate = useNavigate();\r\n\r\n  const navigateHandler = (user) => {\r\n    if (user === \"Admin\") {\r\n      navigate('/Adminlogin');\r\n    }\r\n    else if (user === \"Student\") {\r\n      navigate('/Studentlogin');\r\n    }\r\n    else if (user === \"Teacher\") {\r\n      navigate('/Teacherlogin');\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (status === 'success' || currentUser !== null) {\r\n      if (currentRole === 'Admin') {\r\n        navigate('/Admin/dashboard');\r\n      }\r\n      else if (currentRole === 'Student') {\r\n        navigate('/Student/dashboard');\r\n      } else if (currentRole === 'Teacher') {\r\n        navigate('/Teacher/dashboard');\r\n      }\r\n    }\r\n    else if (status === 'error') {\r\n      setLoader(false)\r\n      setMessage(\"Network Error\")\r\n      setShowPopup(true)\r\n    }\r\n  }, [status, currentRole, navigate, currentUser]);\r\n\r\n  return (\r\n    <StyledContainer>\r\n      <Container>\r\n        <Grid container spacing={2} justifyContent=\"center\">\r\n          <Grid item xs={12} sm={6} md={4}>\r\n            <div onClick={() => navigateHandler(\"Admin\")}>\r\n              <StyledPaper elevation={3}>\r\n                <Box mb={2}>\r\n                  <AccountCircle fontSize=\"large\" />\r\n                </Box>\r\n                <StyledTypography>\r\n                  Admin\r\n                </StyledTypography>\r\n                Login as an administrator to access the dashboard to manage app data.\r\n              </StyledPaper>\r\n            </div>\r\n          </Grid>\r\n          <Grid item xs={12} sm={6} md={4}>\r\n            <StyledPaper elevation={3}>\r\n              <div onClick={() => navigateHandler(\"Student\")}>\r\n                <Box mb={2}>\r\n                  <School fontSize=\"large\" />\r\n                </Box>\r\n                <StyledTypography>\r\n                  Student\r\n                </StyledTypography>\r\n                Login as a student to explore course materials and assignments.\r\n              </div>\r\n            </StyledPaper>\r\n          </Grid>\r\n          <Grid item xs={12} sm={6} md={4}>\r\n            <StyledPaper elevation={3}>\r\n              <div onClick={() => navigateHandler(\"Teacher\")}>\r\n                <Box mb={2}>\r\n                  <Group fontSize=\"large\" />\r\n                </Box>\r\n                <StyledTypography>\r\n                  Teacher\r\n                </StyledTypography>\r\n                Login as a teacher to create courses, assignments, and track student progress.\r\n              </div>\r\n            </StyledPaper>\r\n          </Grid>\r\n        </Grid>\r\n      </Container>\r\n      <Backdrop\r\n        sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}\r\n        open={loader}\r\n      >\r\n        <CircularProgress color=\"inherit\" />\r\n        Please Wait\r\n      </Backdrop>\r\n      <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n    </StyledContainer>\r\n  );\r\n};\r\n\r\nexport default ChooseUser;\r\n\r\nconst StyledContainer = styled.div`\r\n  background: linear-gradient(to bottom, #411d70, #19118b);\r\n  height: 120vh;\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 2rem;\r\n`;\r\n\r\nconst StyledPaper = styled(Paper)`\r\n  padding: 20px;\r\n  text-align: center;\r\n  background-color: #1f1f38;\r\n  color:rgba(255, 255, 255, 0.6);\r\n  cursor:pointer;\r\n\r\n  &:hover {\r\n    background-color: #2c2c6c;\r\n    color:white;\r\n  }\r\n`;\r\n\r\nconst StyledTypography = styled.h2`\r\n  margin-bottom: 10px;\r\n`;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,IAAI,EACJC,KAAK,EACLC,GAAG,EACHC,SAAS,QACJ,eAAe;AACtB,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,qBAAqB;AAClE,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGb,WAAW,EAAE;EAE9B,MAAMc,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpBF,QAAQ,CAAC,aAAa,CAAC;IACzB,CAAC,MACI,IAAIE,IAAI,KAAK,SAAS,EAAE;MAC3BF,QAAQ,CAAC,eAAe,CAAC;IAC3B,CAAC,MACI,IAAIE,IAAI,KAAK,SAAS,EAAE;MAC3BF,QAAQ,CAAC,eAAe,CAAC;IAC3B;EACF,CAAC;EAEDG,SAAS,CAAC,MAAM;IACd,IAAIC,MAAM,KAAK,SAAS,IAAIC,WAAW,KAAK,IAAI,EAAE;MAChD,IAAIC,WAAW,KAAK,OAAO,EAAE;QAC3BN,QAAQ,CAAC,kBAAkB,CAAC;MAC9B,CAAC,MACI,IAAIM,WAAW,KAAK,SAAS,EAAE;QAClCN,QAAQ,CAAC,oBAAoB,CAAC;MAChC,CAAC,MAAM,IAAIM,WAAW,KAAK,SAAS,EAAE;QACpCN,QAAQ,CAAC,oBAAoB,CAAC;MAChC;IACF,CAAC,MACI,IAAII,MAAM,KAAK,OAAO,EAAE;MAC3BG,SAAS,CAAC,KAAK,CAAC;MAChBC,UAAU,CAAC,eAAe,CAAC;MAC3BC,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC,EAAE,CAACL,MAAM,EAAEE,WAAW,EAAEN,QAAQ,EAAEK,WAAW,CAAC,CAAC;EAEhD,oBACER,OAAA,CAACa,eAAe;IAAAC,QAAA,gBACdd,OAAA,CAACN,SAAS;MAAAoB,QAAA,eACRd,OAAA,CAACT,IAAI;QAACwB,SAAS;QAACC,OAAO,EAAE,CAAE;QAACC,cAAc,EAAC,QAAQ;QAAAH,QAAA,gBACjDd,OAAA,CAACT,IAAI;UAAC2B,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,eAC9Bd,OAAA;YAAKsB,OAAO,EAAEA,CAAA,KAAMlB,eAAe,CAAC,OAAO,CAAE;YAAAU,QAAA,eAC3Cd,OAAA,CAACuB,WAAW;cAACC,SAAS,EAAE,CAAE;cAAAV,QAAA,gBACxBd,OAAA,CAACP,GAAG;gBAACgC,EAAE,EAAE,CAAE;gBAAAX,QAAA,eACTd,OAAA,CAACL,aAAa;kBAAC+B,QAAQ,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAC9B,eACN9B,OAAA,CAAC+B,gBAAgB;gBAAAjB,QAAA,EAAC;cAElB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAmB,yEAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAc;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACV;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACD,eACP9B,OAAA,CAACT,IAAI;UAAC2B,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,eAC9Bd,OAAA,CAACuB,WAAW;YAACC,SAAS,EAAE,CAAE;YAAAV,QAAA,eACxBd,OAAA;cAAKsB,OAAO,EAAEA,CAAA,KAAMlB,eAAe,CAAC,SAAS,CAAE;cAAAU,QAAA,gBAC7Cd,OAAA,CAACP,GAAG;gBAACgC,EAAE,EAAE,CAAE;gBAAAX,QAAA,eACTd,OAAA,CAACJ,MAAM;kBAAC8B,QAAQ,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACvB,eACN9B,OAAA,CAAC+B,gBAAgB;gBAAAjB,QAAA,EAAC;cAElB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAmB,mEAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACT,eACP9B,OAAA,CAACT,IAAI;UAAC2B,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,eAC9Bd,OAAA,CAACuB,WAAW;YAACC,SAAS,EAAE,CAAE;YAAAV,QAAA,eACxBd,OAAA;cAAKsB,OAAO,EAAEA,CAAA,KAAMlB,eAAe,CAAC,SAAS,CAAE;cAAAU,QAAA,gBAC7Cd,OAAA,CAACP,GAAG;gBAACgC,EAAE,EAAE,CAAE;gBAAAX,QAAA,eACTd,OAAA,CAACH,KAAK;kBAAC6B,QAAQ,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACtB,eACN9B,OAAA,CAAC+B,gBAAgB;gBAAAjB,QAAA,EAAC;cAElB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAmB,kFAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACG,eACZ9B,OAAA,CAACgC,QAAQ;MACPC,EAAE,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACD,MAAM,CAACE,MAAM,GAAG;MAAE,CAAE;MAClEC,IAAI,EAAEC,MAAO;MAAAzB,QAAA,gBAEbd,OAAA,CAACwC,gBAAgB;QAACN,KAAK,EAAC;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAEtC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAW,eACX9B,OAAA,CAACyC,KAAK;MAACC,OAAO,EAAEA,OAAQ;MAAC9B,YAAY,EAAEA,YAAa;MAAC+B,SAAS,EAAEA;IAAU;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAC7D;AAEtB,CAAC;AAAC5B,EAAA,CAxFID,UAAU;EAAA,QACGX,WAAW;AAAA;AAAAsD,EAAA,GADxB3C,UAAU;AA0FhB,eAAeA,UAAU;AAEzB,MAAMY,eAAe,GAAGf,MAAM,CAAC+C,GAAI;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIjC,eAAe;AAQrB,MAAMU,WAAW,GAAGzB,MAAM,CAACN,KAAK,CAAE;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuD,GAAA,GAXIxB,WAAW;AAajB,MAAMQ,gBAAgB,GAAGjC,MAAM,CAACkD,EAAG;AACnC;AACA,CAAC;AAACC,GAAA,GAFIlB,gBAAgB;AAAA,IAAAa,EAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}