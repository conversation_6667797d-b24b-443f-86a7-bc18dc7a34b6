{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\components\\\\widgets\\\\EnhancedStatCard.js\";\nimport React from 'react';\nimport { Paper, Typography, Box, IconButton } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport CountUp from 'react-countup';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport TrendingDownIcon from '@mui/icons-material/TrendingDown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledPaper = styled(motion.div)(_ref => {\n  let {\n    theme,\n    gradient\n  } = _ref;\n  return {\n    padding: theme.spacing(2),\n    height: '140px',\n    background: gradient,\n    color: 'white',\n    borderRadius: '20px',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',\n    cursor: 'pointer',\n    position: 'relative',\n    overflow: 'hidden',\n    '&::before': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: 'rgba(255, 255, 255, 0.1)',\n      opacity: 0,\n      transition: 'opacity 0.3s ease'\n    },\n    '&:hover::before': {\n      opacity: 1\n    }\n  };\n});\n_c = StyledPaper;\nconst IconContainer = styled(Box)(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    position: 'absolute',\n    top: theme.spacing(2),\n    right: theme.spacing(2),\n    opacity: 0.3,\n    fontSize: '3rem'\n  };\n});\n_c2 = IconContainer;\nconst TrendContainer = styled(Box)(_ref3 => {\n  let {\n    theme\n  } = _ref3;\n  return {\n    display: 'flex',\n    alignItems: 'center',\n    marginTop: theme.spacing(1)\n  };\n});\n_c3 = TrendContainer;\nconst EnhancedStatCard = _ref4 => {\n  let {\n    title,\n    value,\n    icon,\n    gradient,\n    trend,\n    trendValue,\n    prefix = '',\n    suffix = '',\n    onClick\n  } = _ref4;\n  const getTrendIcon = () => {\n    if (trend === 'up') return /*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 32\n    }, this);\n    if (trend === 'down') return /*#__PURE__*/_jsxDEV(TrendingDownIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 34\n    }, this);\n    return null;\n  };\n  const getTrendColor = () => {\n    if (trend === 'up') return '#4CAF50';\n    if (trend === 'down') return '#F44336';\n    return '#FFC107';\n  };\n  return /*#__PURE__*/_jsxDEV(StyledPaper, {\n    gradient: gradient,\n    whileHover: {\n      scale: 1.05,\n      boxShadow: '0 15px 40px rgba(0, 0, 0, 0.2)'\n    },\n    whileTap: {\n      scale: 0.98\n    },\n    onClick: onClick,\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      duration: 0.5\n    },\n    children: /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        background: 'transparent',\n        color: 'inherit',\n        height: '100%',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"h3\",\n          fontWeight: \"600\",\n          sx: {\n            fontSize: '0.9rem',\n            opacity: 0.9,\n            mb: 0.5\n          },\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          component: \"div\",\n          fontWeight: \"bold\",\n          sx: {\n            fontSize: '2rem',\n            lineHeight: 1,\n            mb: 0.5\n          },\n          children: [prefix, /*#__PURE__*/_jsxDEV(CountUp, {\n            end: value,\n            duration: 2.5,\n            separator: \",\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), suffix]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), (trend || trendValue) && /*#__PURE__*/_jsxDEV(TrendContainer, {\n        children: [getTrendIcon(), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            ml: 0.5,\n            fontWeight: 500,\n            color: getTrendColor()\n          },\n          children: trendValue\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            ml: 1,\n            opacity: 0.8\n          },\n          children: \"vs last month\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(IconContainer, {\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_c4 = EnhancedStatCard;\nexport default EnhancedStatCard;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"StyledPaper\");\n$RefreshReg$(_c2, \"IconContainer\");\n$RefreshReg$(_c3, \"TrendContainer\");\n$RefreshReg$(_c4, \"EnhancedStatCard\");", "map": {"version": 3, "names": ["React", "Paper", "Typography", "Box", "IconButton", "styled", "motion", "CountUp", "TrendingUpIcon", "TrendingDownIcon", "jsxDEV", "_jsxDEV", "StyledPaper", "div", "_ref", "theme", "gradient", "padding", "spacing", "height", "background", "color", "borderRadius", "boxShadow", "cursor", "position", "overflow", "content", "top", "left", "right", "bottom", "opacity", "transition", "_c", "IconContainer", "_ref2", "fontSize", "_c2", "TrendContainer", "_ref3", "display", "alignItems", "marginTop", "_c3", "EnhancedStatCard", "_ref4", "title", "value", "icon", "trend", "trendValue", "prefix", "suffix", "onClick", "getTrendIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getTrendColor", "whileHover", "scale", "whileTap", "initial", "y", "animate", "duration", "children", "elevation", "sx", "flexDirection", "justifyContent", "variant", "component", "fontWeight", "mb", "lineHeight", "end", "separator", "ml", "_c4", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/components/widgets/EnhancedStatCard.js"], "sourcesContent": ["import React from 'react';\nimport { Paper, Typography, Box, IconButton } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport CountUp from 'react-countup';\nimport TrendingUpIcon from '@mui/icons-material/TrendingUp';\nimport TrendingDownIcon from '@mui/icons-material/TrendingDown';\n\nconst StyledPaper = styled(motion.div)(({ theme, gradient }) => ({\n  padding: theme.spacing(2),\n  height: '140px',\n  background: gradient,\n  color: 'white',\n  borderRadius: '20px',\n  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',\n  cursor: 'pointer',\n  position: 'relative',\n  overflow: 'hidden',\n  '&::before': {\n    content: '\"\"',\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: 'rgba(255, 255, 255, 0.1)',\n    opacity: 0,\n    transition: 'opacity 0.3s ease',\n  },\n  '&:hover::before': {\n    opacity: 1,\n  },\n}));\n\nconst IconContainer = styled(Box)(({ theme }) => ({\n  position: 'absolute',\n  top: theme.spacing(2),\n  right: theme.spacing(2),\n  opacity: 0.3,\n  fontSize: '3rem',\n}));\n\nconst TrendContainer = styled(Box)(({ theme }) => ({\n  display: 'flex',\n  alignItems: 'center',\n  marginTop: theme.spacing(1),\n}));\n\nconst EnhancedStatCard = ({ \n  title, \n  value, \n  icon, \n  gradient, \n  trend, \n  trendValue, \n  prefix = '', \n  suffix = '',\n  onClick \n}) => {\n  const getTrendIcon = () => {\n    if (trend === 'up') return <TrendingUpIcon fontSize=\"small\" />;\n    if (trend === 'down') return <TrendingDownIcon fontSize=\"small\" />;\n    return null;\n  };\n\n  const getTrendColor = () => {\n    if (trend === 'up') return '#4CAF50';\n    if (trend === 'down') return '#F44336';\n    return '#FFC107';\n  };\n\n  return (\n    <StyledPaper\n      gradient={gradient}\n      whileHover={{ \n        scale: 1.05,\n        boxShadow: '0 15px 40px rgba(0, 0, 0, 0.2)'\n      }}\n      whileTap={{ scale: 0.98 }}\n      onClick={onClick}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <Paper \n        elevation={0} \n        sx={{ \n          background: 'transparent', \n          color: 'inherit',\n          height: '100%',\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'space-between'\n        }}\n      >\n        <Box>\n          <Typography\n            variant=\"h6\"\n            component=\"h3\"\n            fontWeight=\"600\"\n            sx={{\n              fontSize: '0.9rem',\n              opacity: 0.9,\n              mb: 0.5\n            }}\n          >\n            {title}\n          </Typography>\n\n          <Typography\n            variant=\"h3\"\n            component=\"div\"\n            fontWeight=\"bold\"\n            sx={{\n              fontSize: '2rem',\n              lineHeight: 1,\n              mb: 0.5\n            }}\n          >\n            {prefix}\n            <CountUp \n              end={value} \n              duration={2.5} \n              separator=\",\" \n            />\n            {suffix}\n          </Typography>\n        </Box>\n\n        {(trend || trendValue) && (\n          <TrendContainer>\n            {getTrendIcon()}\n            <Typography \n              variant=\"body2\" \n              sx={{ \n                ml: 0.5,\n                fontWeight: 500,\n                color: getTrendColor()\n              }}\n            >\n              {trendValue}\n            </Typography>\n            <Typography \n              variant=\"caption\" \n              sx={{ \n                ml: 1,\n                opacity: 0.8\n              }}\n            >\n              vs last month\n            </Typography>\n          </TrendContainer>\n        )}\n\n        <IconContainer>\n          {icon}\n        </IconContainer>\n      </Paper>\n    </StyledPaper>\n  );\n};\n\nexport default EnhancedStatCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,UAAU,EAAEC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AAClE,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,gBAAgB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,WAAW,GAAGP,MAAM,CAACC,MAAM,CAACO,GAAG,CAAC,CAACC,IAAA;EAAA,IAAC;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAAA,OAAM;IAC/DG,OAAO,EAAEF,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;IACzBC,MAAM,EAAE,OAAO;IACfC,UAAU,EAAEJ,QAAQ;IACpBK,KAAK,EAAE,OAAO;IACdC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE,iCAAiC;IAC5CC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,QAAQ;IAClB,WAAW,EAAE;MACXC,OAAO,EAAE,IAAI;MACbF,QAAQ,EAAE,UAAU;MACpBG,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTX,UAAU,EAAE,0BAA0B;MACtCY,OAAO,EAAE,CAAC;MACVC,UAAU,EAAE;IACd,CAAC;IACD,iBAAiB,EAAE;MACjBD,OAAO,EAAE;IACX;EACF,CAAC;AAAA,CAAC,CAAC;AAACE,EAAA,GAxBEtB,WAAW;AA0BjB,MAAMuB,aAAa,GAAG9B,MAAM,CAACF,GAAG,CAAC,CAACiC,KAAA;EAAA,IAAC;IAAErB;EAAM,CAAC,GAAAqB,KAAA;EAAA,OAAM;IAChDX,QAAQ,EAAE,UAAU;IACpBG,GAAG,EAAEb,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;IACrBY,KAAK,EAAEf,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;IACvBc,OAAO,EAAE,GAAG;IACZK,QAAQ,EAAE;EACZ,CAAC;AAAA,CAAC,CAAC;AAACC,GAAA,GANEH,aAAa;AAQnB,MAAMI,cAAc,GAAGlC,MAAM,CAACF,GAAG,CAAC,CAACqC,KAAA;EAAA,IAAC;IAAEzB;EAAM,CAAC,GAAAyB,KAAA;EAAA,OAAM;IACjDC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,SAAS,EAAE5B,KAAK,CAACG,OAAO,CAAC,CAAC;EAC5B,CAAC;AAAA,CAAC,CAAC;AAAC0B,GAAA,GAJEL,cAAc;AAMpB,MAAMM,gBAAgB,GAAGC,KAAA,IAUnB;EAAA,IAVoB;IACxBC,KAAK;IACLC,KAAK;IACLC,IAAI;IACJjC,QAAQ;IACRkC,KAAK;IACLC,UAAU;IACVC,MAAM,GAAG,EAAE;IACXC,MAAM,GAAG,EAAE;IACXC;EACF,CAAC,GAAAR,KAAA;EACC,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIL,KAAK,KAAK,IAAI,EAAE,oBAAOvC,OAAA,CAACH,cAAc;MAAC6B,QAAQ,EAAC;IAAO;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC9D,IAAIT,KAAK,KAAK,MAAM,EAAE,oBAAOvC,OAAA,CAACF,gBAAgB;MAAC4B,QAAQ,EAAC;IAAO;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAClE,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIV,KAAK,KAAK,IAAI,EAAE,OAAO,SAAS;IACpC,IAAIA,KAAK,KAAK,MAAM,EAAE,OAAO,SAAS;IACtC,OAAO,SAAS;EAClB,CAAC;EAED,oBACEvC,OAAA,CAACC,WAAW;IACVI,QAAQ,EAAEA,QAAS;IACnB6C,UAAU,EAAE;MACVC,KAAK,EAAE,IAAI;MACXvC,SAAS,EAAE;IACb,CAAE;IACFwC,QAAQ,EAAE;MAAED,KAAK,EAAE;IAAK,CAAE;IAC1BR,OAAO,EAAEA,OAAQ;IACjBU,OAAO,EAAE;MAAEhC,OAAO,EAAE,CAAC;MAAEiC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAElC,OAAO,EAAE,CAAC;MAAEiC,CAAC,EAAE;IAAE,CAAE;IAC9BhC,UAAU,EAAE;MAAEkC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,eAE9BzD,OAAA,CAACV,KAAK;MACJoE,SAAS,EAAE,CAAE;MACbC,EAAE,EAAE;QACFlD,UAAU,EAAE,aAAa;QACzBC,KAAK,EAAE,SAAS;QAChBF,MAAM,EAAE,MAAM;QACdsB,OAAO,EAAE,MAAM;QACf8B,aAAa,EAAE,QAAQ;QACvBC,cAAc,EAAE;MAClB,CAAE;MAAAJ,QAAA,gBAEFzD,OAAA,CAACR,GAAG;QAAAiE,QAAA,gBACFzD,OAAA,CAACT,UAAU;UACTuE,OAAO,EAAC,IAAI;UACZC,SAAS,EAAC,IAAI;UACdC,UAAU,EAAC,KAAK;UAChBL,EAAE,EAAE;YACFjC,QAAQ,EAAE,QAAQ;YAClBL,OAAO,EAAE,GAAG;YACZ4C,EAAE,EAAE;UACN,CAAE;UAAAR,QAAA,EAEDrB;QAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACK,eAEbhD,OAAA,CAACT,UAAU;UACTuE,OAAO,EAAC,IAAI;UACZC,SAAS,EAAC,KAAK;UACfC,UAAU,EAAC,MAAM;UACjBL,EAAE,EAAE;YACFjC,QAAQ,EAAE,MAAM;YAChBwC,UAAU,EAAE,CAAC;YACbD,EAAE,EAAE;UACN,CAAE;UAAAR,QAAA,GAEDhB,MAAM,eACPzC,OAAA,CAACJ,OAAO;YACNuE,GAAG,EAAE9B,KAAM;YACXmB,QAAQ,EAAE,GAAI;YACdY,SAAS,EAAC;UAAG;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACb,EACDN,MAAM;QAAA;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACI;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACT,EAEL,CAACT,KAAK,IAAIC,UAAU,kBACnBxC,OAAA,CAAC4B,cAAc;QAAA6B,QAAA,GACZb,YAAY,EAAE,eACf5C,OAAA,CAACT,UAAU;UACTuE,OAAO,EAAC,OAAO;UACfH,EAAE,EAAE;YACFU,EAAE,EAAE,GAAG;YACPL,UAAU,EAAE,GAAG;YACftD,KAAK,EAAEuC,aAAa;UACtB,CAAE;UAAAQ,QAAA,EAEDjB;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACA,eACbhD,OAAA,CAACT,UAAU;UACTuE,OAAO,EAAC,SAAS;UACjBH,EAAE,EAAE;YACFU,EAAE,EAAE,CAAC;YACLhD,OAAO,EAAE;UACX,CAAE;UAAAoC,QAAA,EACH;QAED;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAEhB,eAEDhD,OAAA,CAACwB,aAAa;QAAAiC,QAAA,EACXnB;MAAI;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACS;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACV;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACI;AAElB,CAAC;AAACsB,GAAA,GAhHIpC,gBAAgB;AAkHtB,eAAeA,gBAAgB;AAAC,IAAAX,EAAA,EAAAI,GAAA,EAAAM,GAAA,EAAAqC,GAAA;AAAAC,YAAA,CAAAhD,EAAA;AAAAgD,YAAA,CAAA5C,GAAA;AAAA4C,YAAA,CAAAtC,GAAA;AAAAsC,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}