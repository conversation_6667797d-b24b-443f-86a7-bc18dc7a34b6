{"ast": null, "code": "import axios from 'axios';\nimport { authRequest, stuffAdded, authSuccess, authFailed, authError, authLogout, doneSuccess, getDeleteSuccess, getRequest, getFailed, getError } from './userSlice';\nconst REACT_APP_BASE_URL = \"http://localhost:5000\";\nexport const loginUser = (fields, role) => async dispatch => {\n  dispatch(authRequest());\n  try {\n    const result = await axios.post(`${REACT_APP_BASE_URL}/${role}Login`, fields, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    if (result.data.role) {\n      dispatch(authSuccess(result.data));\n    } else {\n      dispatch(authFailed(result.data.message));\n    }\n  } catch (error) {\n    dispatch(authError(error));\n  }\n};\nexport const registerUser = (fields, role) => async dispatch => {\n  dispatch(authRequest());\n  try {\n    const result = await axios.post(`${REACT_APP_BASE_URL}/${role}Reg`, fields, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    if (result.data.schoolName) {\n      dispatch(authSuccess(result.data));\n    } else if (result.data.school) {\n      dispatch(stuffAdded());\n    } else {\n      dispatch(authFailed(result.data.message));\n    }\n  } catch (error) {\n    dispatch(authError(error));\n  }\n};\nexport const logoutUser = () => dispatch => {\n  dispatch(authLogout());\n};\nexport const getUserDetails = (id, address) => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${REACT_APP_BASE_URL}/${address}/${id}`);\n    if (result.data) {\n      dispatch(doneSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\n\n// export const deleteUser = (id, address) => async (dispatch) => {\n//     dispatch(getRequest());\n\n//     try {\n//         const result = await axios.delete(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\n//         if (result.data.message) {\n//             dispatch(getFailed(result.data.message));\n//         } else {\n//             dispatch(getDeleteSuccess());\n//         }\n//     } catch (error) {\n//         dispatch(getError(error));\n//     }\n// }\n\nexport const deleteUser = (id, address) => async dispatch => {\n  dispatch(getRequest());\n  dispatch(getFailed(\"Sorry the delete function has been disabled for now.\"));\n};\nexport const updateUser = (fields, id, address) => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.put(`${REACT_APP_BASE_URL}/${address}/${id}`, fields, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    if (result.data.schoolName) {\n      dispatch(authSuccess(result.data));\n    } else {\n      dispatch(doneSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const addStuff = (fields, address) => async dispatch => {\n  dispatch(authRequest());\n  try {\n    const result = await axios.post(`${REACT_APP_BASE_URL}/${address}Create`, fields, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    if (result.data.message) {\n      dispatch(authFailed(result.data.message));\n    } else {\n      dispatch(stuffAdded(result.data));\n    }\n  } catch (error) {\n    dispatch(authError(error));\n  }\n};", "map": {"version": 3, "names": ["axios", "authRequest", "stuffAdded", "authSuccess", "authFailed", "authError", "authLogout", "doneSuccess", "getDeleteSuccess", "getRequest", "getFailed", "getError", "REACT_APP_BASE_URL", "loginUser", "fields", "role", "dispatch", "result", "post", "headers", "data", "message", "error", "registerUser", "schoolName", "school", "logoutUser", "getUserDetails", "id", "address", "get", "deleteUser", "updateUser", "put", "addStuff"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/redux/userRelated/userHandle.js"], "sourcesContent": ["import axios from 'axios';\r\nimport {\r\n    authRequest,\r\n    stuffAdded,\r\n    authSuccess,\r\n    authFailed,\r\n    authError,\r\n    authLogout,\r\n    doneSuccess,\r\n    getDeleteSuccess,\r\n    getRequest,\r\n    getFailed,\r\n    getError,\r\n} from './userSlice';\r\n\r\nconst REACT_APP_BASE_URL = \"http://localhost:5000\";\r\n\r\nexport const loginUser = (fields, role) => async (dispatch) => {\r\n    dispatch(authRequest());\r\n\r\n    try {\r\n        const result = await axios.post(`${REACT_APP_BASE_URL}/${role}Login`, fields, {\r\n            headers: { 'Content-Type': 'application/json' },\r\n        });\r\n        if (result.data.role) {\r\n            dispatch(authSuccess(result.data));\r\n        } else {\r\n            dispatch(authFailed(result.data.message));\r\n        }\r\n    } catch (error) {\r\n        dispatch(authError(error));\r\n    }\r\n};\r\n\r\nexport const registerUser = (fields, role) => async (dispatch) => {\r\n    dispatch(authRequest());\r\n\r\n    try {\r\n        const result = await axios.post(`${REACT_APP_BASE_URL}/${role}Reg`, fields, {\r\n            headers: { 'Content-Type': 'application/json' },\r\n        });\r\n        if (result.data.schoolName) {\r\n            dispatch(authSuccess(result.data));\r\n        }\r\n        else if (result.data.school) {\r\n            dispatch(stuffAdded());\r\n        }\r\n        else {\r\n            dispatch(authFailed(result.data.message));\r\n        }\r\n    } catch (error) {\r\n        dispatch(authError(error));\r\n    }\r\n};\r\n\r\nexport const logoutUser = () => (dispatch) => {\r\n    dispatch(authLogout());\r\n};\r\n\r\nexport const getUserDetails = (id, address) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${REACT_APP_BASE_URL}/${address}/${id}`);\r\n        if (result.data) {\r\n            dispatch(doneSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\n// export const deleteUser = (id, address) => async (dispatch) => {\r\n//     dispatch(getRequest());\r\n\r\n//     try {\r\n//         const result = await axios.delete(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\r\n//         if (result.data.message) {\r\n//             dispatch(getFailed(result.data.message));\r\n//         } else {\r\n//             dispatch(getDeleteSuccess());\r\n//         }\r\n//     } catch (error) {\r\n//         dispatch(getError(error));\r\n//     }\r\n// }\r\n\r\n\r\nexport const deleteUser = (id, address) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n    dispatch(getFailed(\"Sorry the delete function has been disabled for now.\"));\r\n}\r\n\r\nexport const updateUser = (fields, id, address) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.put(`${REACT_APP_BASE_URL}/${address}/${id}`, fields, {\r\n            headers: { 'Content-Type': 'application/json' },\r\n        });\r\n        if (result.data.schoolName) {\r\n            dispatch(authSuccess(result.data));\r\n        }\r\n        else {\r\n            dispatch(doneSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const addStuff = (fields, address) => async (dispatch) => {\r\n    dispatch(authRequest());\r\n\r\n    try {\r\n        const result = await axios.post(`${REACT_APP_BASE_URL}/${address}Create`, fields, {\r\n            headers: { 'Content-Type': 'application/json' },\r\n        });\r\n\r\n        if (result.data.message) {\r\n            dispatch(authFailed(result.data.message));\r\n        } else {\r\n            dispatch(stuffAdded(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(authError(error));\r\n    }\r\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,WAAW,EACXC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,WAAW,EACXC,gBAAgB,EAChBC,UAAU,EACVC,SAAS,EACTC,QAAQ,QACL,aAAa;AAEpB,MAAMC,kBAAkB,GAAG,uBAAuB;AAElD,OAAO,MAAMC,SAAS,GAAGA,CAACC,MAAM,EAAEC,IAAI,KAAK,MAAOC,QAAQ,IAAK;EAC3DA,QAAQ,CAACf,WAAW,EAAE,CAAC;EAEvB,IAAI;IACA,MAAMgB,MAAM,GAAG,MAAMjB,KAAK,CAACkB,IAAI,CAAE,GAAEN,kBAAmB,IAAGG,IAAK,OAAM,EAAED,MAAM,EAAE;MAC1EK,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB;IAClD,CAAC,CAAC;IACF,IAAIF,MAAM,CAACG,IAAI,CAACL,IAAI,EAAE;MAClBC,QAAQ,CAACb,WAAW,CAACc,MAAM,CAACG,IAAI,CAAC,CAAC;IACtC,CAAC,MAAM;MACHJ,QAAQ,CAACZ,UAAU,CAACa,MAAM,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC;IAC7C;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZN,QAAQ,CAACX,SAAS,CAACiB,KAAK,CAAC,CAAC;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMC,YAAY,GAAGA,CAACT,MAAM,EAAEC,IAAI,KAAK,MAAOC,QAAQ,IAAK;EAC9DA,QAAQ,CAACf,WAAW,EAAE,CAAC;EAEvB,IAAI;IACA,MAAMgB,MAAM,GAAG,MAAMjB,KAAK,CAACkB,IAAI,CAAE,GAAEN,kBAAmB,IAAGG,IAAK,KAAI,EAAED,MAAM,EAAE;MACxEK,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB;IAClD,CAAC,CAAC;IACF,IAAIF,MAAM,CAACG,IAAI,CAACI,UAAU,EAAE;MACxBR,QAAQ,CAACb,WAAW,CAACc,MAAM,CAACG,IAAI,CAAC,CAAC;IACtC,CAAC,MACI,IAAIH,MAAM,CAACG,IAAI,CAACK,MAAM,EAAE;MACzBT,QAAQ,CAACd,UAAU,EAAE,CAAC;IAC1B,CAAC,MACI;MACDc,QAAQ,CAACZ,UAAU,CAACa,MAAM,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC;IAC7C;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZN,QAAQ,CAACX,SAAS,CAACiB,KAAK,CAAC,CAAC;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMI,UAAU,GAAGA,CAAA,KAAOV,QAAQ,IAAK;EAC1CA,QAAQ,CAACV,UAAU,EAAE,CAAC;AAC1B,CAAC;AAED,OAAO,MAAMqB,cAAc,GAAGA,CAACC,EAAE,EAAEC,OAAO,KAAK,MAAOb,QAAQ,IAAK;EAC/DA,QAAQ,CAACP,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMQ,MAAM,GAAG,MAAMjB,KAAK,CAAC8B,GAAG,CAAE,GAAElB,kBAAmB,IAAGiB,OAAQ,IAAGD,EAAG,EAAC,CAAC;IACxE,IAAIX,MAAM,CAACG,IAAI,EAAE;MACbJ,QAAQ,CAACT,WAAW,CAACU,MAAM,CAACG,IAAI,CAAC,CAAC;IACtC;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZN,QAAQ,CAACL,QAAQ,CAACW,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;;AAED;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,OAAO,MAAMS,UAAU,GAAGA,CAACH,EAAE,EAAEC,OAAO,KAAK,MAAOb,QAAQ,IAAK;EAC3DA,QAAQ,CAACP,UAAU,EAAE,CAAC;EACtBO,QAAQ,CAACN,SAAS,CAAC,sDAAsD,CAAC,CAAC;AAC/E,CAAC;AAED,OAAO,MAAMsB,UAAU,GAAGA,CAAClB,MAAM,EAAEc,EAAE,EAAEC,OAAO,KAAK,MAAOb,QAAQ,IAAK;EACnEA,QAAQ,CAACP,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMQ,MAAM,GAAG,MAAMjB,KAAK,CAACiC,GAAG,CAAE,GAAErB,kBAAmB,IAAGiB,OAAQ,IAAGD,EAAG,EAAC,EAAEd,MAAM,EAAE;MAC7EK,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB;IAClD,CAAC,CAAC;IACF,IAAIF,MAAM,CAACG,IAAI,CAACI,UAAU,EAAE;MACxBR,QAAQ,CAACb,WAAW,CAACc,MAAM,CAACG,IAAI,CAAC,CAAC;IACtC,CAAC,MACI;MACDJ,QAAQ,CAACT,WAAW,CAACU,MAAM,CAACG,IAAI,CAAC,CAAC;IACtC;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZN,QAAQ,CAACL,QAAQ,CAACW,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMY,QAAQ,GAAGA,CAACpB,MAAM,EAAEe,OAAO,KAAK,MAAOb,QAAQ,IAAK;EAC7DA,QAAQ,CAACf,WAAW,EAAE,CAAC;EAEvB,IAAI;IACA,MAAMgB,MAAM,GAAG,MAAMjB,KAAK,CAACkB,IAAI,CAAE,GAAEN,kBAAmB,IAAGiB,OAAQ,QAAO,EAAEf,MAAM,EAAE;MAC9EK,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB;IAClD,CAAC,CAAC;IAEF,IAAIF,MAAM,CAACG,IAAI,CAACC,OAAO,EAAE;MACrBL,QAAQ,CAACZ,UAAU,CAACa,MAAM,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC;IAC7C,CAAC,MAAM;MACHL,QAAQ,CAACd,UAAU,CAACe,MAAM,CAACG,IAAI,CAAC,CAAC;IACrC;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZN,QAAQ,CAACX,SAAS,CAACiB,KAAK,CAAC,CAAC;EAC9B;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}