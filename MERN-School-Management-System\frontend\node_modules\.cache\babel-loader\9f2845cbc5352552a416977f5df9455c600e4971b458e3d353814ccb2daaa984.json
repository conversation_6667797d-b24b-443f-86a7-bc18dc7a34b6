{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\pages\\\\ChooseUser.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Grid, Paper, Box, Container } from '@mui/material';\nimport { AccountCircle, School, Group } from '@mui/icons-material';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChooseUser = () => {\n  _s();\n  const navigate = useNavigate();\n  const navigateHandler = user => {\n    if (user === \"Admin\") {\n      navigate('/Adminlogin');\n    } else if (user === \"Student\") {\n      navigate('/Studentlogin');\n    } else if (user === \"Teacher\") {\n      navigate('/Teacherlogin');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(StyledContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        justifyContent: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => navigateHandler(\"Admin\"),\n            children: /*#__PURE__*/_jsxDEV(StyledPaper, {\n              elevation: 3,\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: /*#__PURE__*/_jsxDEV(AccountCircle, {\n                  fontSize: \"large\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 37,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTypography, {\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this), \"Login as an administrator to access the dashboard to manage app data.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(StyledPaper, {\n            elevation: 3,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => navigateHandler(\"Student\"),\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: /*#__PURE__*/_jsxDEV(School, {\n                  fontSize: \"large\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTypography, {\n                children: \"Student\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this), \"Login as a student to explore course materials and assignments.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(StyledPaper, {\n            elevation: 3,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => navigateHandler(\"Teacher\"),\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: /*#__PURE__*/_jsxDEV(Group, {\n                  fontSize: \"large\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTypography, {\n                children: \"Teacher\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this), \"Login as a teacher to create courses, assignments, and track student progress.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Backdrop, {\n      sx: {\n        color: '#fff',\n        zIndex: theme => theme.zIndex.drawer + 1\n      },\n      open: loader,\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        color: \"inherit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), \"Please Wait\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Popup, {\n      message: message,\n      setShowPopup: setShowPopup,\n      showPopup: showPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(ChooseUser, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = ChooseUser;\nexport default ChooseUser;\nconst StyledContainer = styled.div`\n  background: linear-gradient(to bottom, #411d70, #19118b);\n  height: 120vh;\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n`;\n_c2 = StyledContainer;\nconst StyledPaper = styled(Paper)`\n  padding: 20px;\n  text-align: center;\n  background-color: #1f1f38;\n  color:rgba(255, 255, 255, 0.6);\n  cursor:pointer;\n\n  &:hover {\n    background-color: #2c2c6c;\n    color:white;\n  }\n`;\n_c3 = StyledPaper;\nconst StyledTypography = styled.h2`\n  margin-bottom: 10px;\n`;\n_c4 = StyledTypography;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ChooseUser\");\n$RefreshReg$(_c2, \"StyledContainer\");\n$RefreshReg$(_c3, \"StyledPaper\");\n$RefreshReg$(_c4, \"StyledTypography\");", "map": {"version": 3, "names": ["React", "useNavigate", "Grid", "Paper", "Box", "Container", "AccountCircle", "School", "Group", "styled", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "_s", "navigate", "<PERSON><PERSON><PERSON><PERSON>", "user", "StyledContainer", "children", "container", "spacing", "justifyContent", "item", "xs", "sm", "md", "onClick", "StyledPaper", "elevation", "mb", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "StyledTypography", "Backdrop", "sx", "color", "zIndex", "theme", "drawer", "open", "loader", "CircularProgress", "Popup", "message", "setShowPopup", "showPopup", "_c", "div", "_c2", "_c3", "h2", "_c4", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/pages/ChooseUser.js"], "sourcesContent": ["import React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport {\r\n  Grid,\r\n  Paper,\r\n  Box,\r\n  Container,\r\n} from '@mui/material';\r\nimport { AccountCircle, School, Group } from '@mui/icons-material';\r\nimport styled from 'styled-components';\r\n\r\nconst ChooseUser = () => {\r\n  const navigate = useNavigate();\r\n\r\n  const navigateHandler = (user) => {\r\n    if (user === \"Admin\") {\r\n      navigate('/Adminlogin');\r\n    }\r\n    else if (user === \"Student\") {\r\n      navigate('/Studentlogin');\r\n    }\r\n    else if (user === \"Teacher\") {\r\n      navigate('/Teacherlogin');\r\n    }\r\n  }\r\n\r\n\r\n\r\n  return (\r\n    <StyledContainer>\r\n      <Container>\r\n        <Grid container spacing={2} justifyContent=\"center\">\r\n          <Grid item xs={12} sm={6} md={4}>\r\n            <div onClick={() => navigateHandler(\"Admin\")}>\r\n              <StyledPaper elevation={3}>\r\n                <Box mb={2}>\r\n                  <AccountCircle fontSize=\"large\" />\r\n                </Box>\r\n                <StyledTypography>\r\n                  Admin\r\n                </StyledTypography>\r\n                Login as an administrator to access the dashboard to manage app data.\r\n              </StyledPaper>\r\n            </div>\r\n          </Grid>\r\n          <Grid item xs={12} sm={6} md={4}>\r\n            <StyledPaper elevation={3}>\r\n              <div onClick={() => navigateHandler(\"Student\")}>\r\n                <Box mb={2}>\r\n                  <School fontSize=\"large\" />\r\n                </Box>\r\n                <StyledTypography>\r\n                  Student\r\n                </StyledTypography>\r\n                Login as a student to explore course materials and assignments.\r\n              </div>\r\n            </StyledPaper>\r\n          </Grid>\r\n          <Grid item xs={12} sm={6} md={4}>\r\n            <StyledPaper elevation={3}>\r\n              <div onClick={() => navigateHandler(\"Teacher\")}>\r\n                <Box mb={2}>\r\n                  <Group fontSize=\"large\" />\r\n                </Box>\r\n                <StyledTypography>\r\n                  Teacher\r\n                </StyledTypography>\r\n                Login as a teacher to create courses, assignments, and track student progress.\r\n              </div>\r\n            </StyledPaper>\r\n          </Grid>\r\n        </Grid>\r\n      </Container>\r\n      <Backdrop\r\n        sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}\r\n        open={loader}\r\n      >\r\n        <CircularProgress color=\"inherit\" />\r\n        Please Wait\r\n      </Backdrop>\r\n      <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n    </StyledContainer>\r\n  );\r\n};\r\n\r\nexport default ChooseUser;\r\n\r\nconst StyledContainer = styled.div`\r\n  background: linear-gradient(to bottom, #411d70, #19118b);\r\n  height: 120vh;\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 2rem;\r\n`;\r\n\r\nconst StyledPaper = styled(Paper)`\r\n  padding: 20px;\r\n  text-align: center;\r\n  background-color: #1f1f38;\r\n  color:rgba(255, 255, 255, 0.6);\r\n  cursor:pointer;\r\n\r\n  &:hover {\r\n    background-color: #2c2c6c;\r\n    color:white;\r\n  }\r\n`;\r\n\r\nconst StyledTypography = styled.h2`\r\n  margin-bottom: 10px;\r\n`;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,IAAI,EACJC,KAAK,EACLC,GAAG,EACHC,SAAS,QACJ,eAAe;AACtB,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,qBAAqB;AAClE,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGb,WAAW,EAAE;EAE9B,MAAMc,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpBF,QAAQ,CAAC,aAAa,CAAC;IACzB,CAAC,MACI,IAAIE,IAAI,KAAK,SAAS,EAAE;MAC3BF,QAAQ,CAAC,eAAe,CAAC;IAC3B,CAAC,MACI,IAAIE,IAAI,KAAK,SAAS,EAAE;MAC3BF,QAAQ,CAAC,eAAe,CAAC;IAC3B;EACF,CAAC;EAID,oBACEH,OAAA,CAACM,eAAe;IAAAC,QAAA,gBACdP,OAAA,CAACN,SAAS;MAAAa,QAAA,eACRP,OAAA,CAACT,IAAI;QAACiB,SAAS;QAACC,OAAO,EAAE,CAAE;QAACC,cAAc,EAAC,QAAQ;QAAAH,QAAA,gBACjDP,OAAA,CAACT,IAAI;UAACoB,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,eAC9BP,OAAA;YAAKe,OAAO,EAAEA,CAAA,KAAMX,eAAe,CAAC,OAAO,CAAE;YAAAG,QAAA,eAC3CP,OAAA,CAACgB,WAAW;cAACC,SAAS,EAAE,CAAE;cAAAV,QAAA,gBACxBP,OAAA,CAACP,GAAG;gBAACyB,EAAE,EAAE,CAAE;gBAAAX,QAAA,eACTP,OAAA,CAACL,aAAa;kBAACwB,QAAQ,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAC9B,eACNvB,OAAA,CAACwB,gBAAgB;gBAAAjB,QAAA,EAAC;cAElB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAmB,yEAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAc;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACV;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACD,eACPvB,OAAA,CAACT,IAAI;UAACoB,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,eAC9BP,OAAA,CAACgB,WAAW;YAACC,SAAS,EAAE,CAAE;YAAAV,QAAA,eACxBP,OAAA;cAAKe,OAAO,EAAEA,CAAA,KAAMX,eAAe,CAAC,SAAS,CAAE;cAAAG,QAAA,gBAC7CP,OAAA,CAACP,GAAG;gBAACyB,EAAE,EAAE,CAAE;gBAAAX,QAAA,eACTP,OAAA,CAACJ,MAAM;kBAACuB,QAAQ,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACvB,eACNvB,OAAA,CAACwB,gBAAgB;gBAAAjB,QAAA,EAAC;cAElB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAmB,mEAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACT,eACPvB,OAAA,CAACT,IAAI;UAACoB,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,eAC9BP,OAAA,CAACgB,WAAW;YAACC,SAAS,EAAE,CAAE;YAAAV,QAAA,eACxBP,OAAA;cAAKe,OAAO,EAAEA,CAAA,KAAMX,eAAe,CAAC,SAAS,CAAE;cAAAG,QAAA,gBAC7CP,OAAA,CAACP,GAAG;gBAACyB,EAAE,EAAE,CAAE;gBAAAX,QAAA,eACTP,OAAA,CAACH,KAAK;kBAACsB,QAAQ,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACtB,eACNvB,OAAA,CAACwB,gBAAgB;gBAAAjB,QAAA,EAAC;cAElB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAmB,kFAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACG,eACZvB,OAAA,CAACyB,QAAQ;MACPC,EAAE,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACD,MAAM,CAACE,MAAM,GAAG;MAAE,CAAE;MAClEC,IAAI,EAAEC,MAAO;MAAAzB,QAAA,gBAEbP,OAAA,CAACiC,gBAAgB;QAACN,KAAK,EAAC;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAEtC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAW,eACXvB,OAAA,CAACkC,KAAK;MAACC,OAAO,EAAEA,OAAQ;MAACC,YAAY,EAAEA,YAAa;MAACC,SAAS,EAAEA;IAAU;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAC7D;AAEtB,CAAC;AAACrB,EAAA,CAxEID,UAAU;EAAA,QACGX,WAAW;AAAA;AAAAgD,EAAA,GADxBrC,UAAU;AA0EhB,eAAeA,UAAU;AAEzB,MAAMK,eAAe,GAAGR,MAAM,CAACyC,GAAI;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIlC,eAAe;AAQrB,MAAMU,WAAW,GAAGlB,MAAM,CAACN,KAAK,CAAE;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiD,GAAA,GAXIzB,WAAW;AAajB,MAAMQ,gBAAgB,GAAG1B,MAAM,CAAC4C,EAAG;AACnC;AACA,CAAC;AAACC,GAAA,GAFInB,gBAAgB;AAAA,IAAAc,EAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}