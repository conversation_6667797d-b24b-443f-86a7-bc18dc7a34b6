{"ast": null, "code": "/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */\nconst isNodeOrChild = (parent, child) => {\n  if (!child) {\n    return false;\n  } else if (parent === child) {\n    return true;\n  } else {\n    return isNodeOrChild(parent, child.parentElement);\n  }\n};\nexport { isNodeOrChild };", "map": {"version": 3, "names": ["isNodeOrChild", "parent", "child", "parentElement"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs"], "sourcesContent": ["/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */\nconst isNodeOrChild = (parent, child) => {\n    if (!child) {\n        return false;\n    }\n    else if (parent === child) {\n        return true;\n    }\n    else {\n        return isNodeOrChild(parent, child.parentElement);\n    }\n};\n\nexport { isNodeOrChild };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,aAAa,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACrC,IAAI,CAACA,KAAK,EAAE;IACR,OAAO,KAAK;EAChB,CAAC,MACI,IAAID,MAAM,KAAKC,KAAK,EAAE;IACvB,OAAO,IAAI;EACf,CAAC,MACI;IACD,OAAOF,aAAa,CAACC,MAAM,EAAEC,KAAK,CAACC,aAAa,CAAC;EACrD;AACJ,CAAC;AAED,SAASH,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}