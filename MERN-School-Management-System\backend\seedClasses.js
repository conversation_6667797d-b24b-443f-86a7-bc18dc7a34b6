const mongoose = require('mongoose');
const Admin = require('./models/adminSchema.js');
const Sclass = require('./models/sclassSchema.js');
const Subject = require('./models/subjectSchema.js');
require('dotenv').config();

const seedClasses = async () => {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URL, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('Connected to MongoDB');

        // Find the admin user
        const admin = await Admin.findOne({ email: '<EMAIL>' });
        if (!admin) {
            console.log('Admin user not found. Please run seedAdmin.js first.');
            process.exit(1);
        }

        // Check if classes already exist
        const existingClasses = await Sclass.find({ school: admin._id });
        if (existingClasses.length > 0) {
            console.log('Classes already exist');
            process.exit(0);
        }

        // Create sample classes
        const classesData = [
            { sclassName: 'Class 1', school: admin._id },
            { sclassName: 'Class 2', school: admin._id },
            { sclassName: 'Class 3', school: admin._id },
            { sclassName: 'Class 4', school: admin._id },
            { sclassName: 'Class 5', school: admin._id },
            { sclassName: 'Class 6', school: admin._id },
            { sclassName: 'Class 7', school: admin._id },
            { sclassName: 'Class 8', school: admin._id },
            { sclassName: 'Class 9', school: admin._id },
            { sclassName: 'Class 10', school: admin._id }
        ];

        const createdClasses = await Sclass.insertMany(classesData);
        console.log('Sample classes created successfully!');

        // Create sample subjects for each class
        const subjectsData = [];
        
        // Common subjects for all classes
        const commonSubjects = [
            { name: 'Mathematics', code: 'MATH', sessions: '40' },
            { name: 'English', code: 'ENG', sessions: '35' },
            { name: 'Science', code: 'SCI', sessions: '45' },
            { name: 'Social Studies', code: 'SS', sessions: '30' },
            { name: 'Hindi', code: 'HIN', sessions: '30' }
        ];

        // Additional subjects for higher classes (6-10)
        const higherClassSubjects = [
            { name: 'Computer Science', code: 'CS', sessions: '25' },
            { name: 'Physical Education', code: 'PE', sessions: '20' }
        ];

        // Create subjects for each class
        for (const sclass of createdClasses) {
            // Add common subjects
            for (const subject of commonSubjects) {
                subjectsData.push({
                    subName: subject.name,
                    subCode: subject.code,
                    sessions: subject.sessions,
                    sclassName: sclass._id,
                    school: admin._id
                });
            }

            // Add additional subjects for classes 6-10
            const classNumber = parseInt(sclass.sclassName.split(' ')[1]);
            if (classNumber >= 6) {
                for (const subject of higherClassSubjects) {
                    subjectsData.push({
                        subName: subject.name,
                        subCode: subject.code,
                        sessions: subject.sessions,
                        sclassName: sclass._id,
                        school: admin._id
                    });
                }
            }
        }

        await Subject.insertMany(subjectsData);
        console.log('Sample subjects created successfully!');

        console.log('\n=== SAMPLE DATA CREATED ===');
        console.log('Classes: 10 classes (Class 1 to Class 10)');
        console.log('Subjects: 5-7 subjects per class');
        console.log('You can now view and manage classes in the admin panel!');
        
        process.exit(0);
    } catch (error) {
        console.error('Error creating sample data:', error);
        process.exit(1);
    }
};

seedClasses();
