import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIcon,
  ExpandMore as ExpandMoreIcon,
  Image as ImageIcon,
  TextFields as TextIcon,
  ViewModule as ComponentIcon,
  Code as CodeIcon,
  Preview as PreviewIcon,
  Save as SaveIcon,
} from '@mui/icons-material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
}));

const ComponentCard = styled(Card)(({ theme }) => ({
  borderRadius: '12px',
  border: '2px dashed #e0e0e0',
  minHeight: '100px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    borderColor: theme.palette.primary.main,
    backgroundColor: 'rgba(102, 126, 234, 0.05)',
  },
}));

const PageBuilder = () => {
  const [pages, setPages] = useState([
    { id: 1, name: 'Login Page', path: '/login', status: 'active' },
    { id: 2, name: 'Registration Page', path: '/register', status: 'draft' },
    { id: 3, name: 'Forgot Password', path: '/forgot-password', status: 'active' },
  ]);

  const [selectedPage, setSelectedPage] = useState(null);
  const [editDialog, setEditDialog] = useState(false);
  const [newPageDialog, setNewPageDialog] = useState(false);
  const [pageComponents, setPageComponents] = useState([]);

  const componentTypes = [
    { type: 'header', name: 'Header', icon: <TextIcon />, description: 'Page title and subtitle' },
    { type: 'form', name: 'Login Form', icon: <ComponentIcon />, description: 'Login form with fields' },
    { type: 'image', name: 'Image', icon: <ImageIcon />, description: 'Background or logo image' },
    { type: 'text', name: 'Text Block', icon: <TextIcon />, description: 'Custom text content' },
    { type: 'custom', name: 'Custom HTML', icon: <CodeIcon />, description: 'Custom HTML/CSS code' },
  ];

  const [newPage, setNewPage] = useState({
    name: '',
    path: '',
    template: 'blank',
    status: 'draft',
  });

  const handleCreatePage = () => {
    const page = {
      id: pages.length + 1,
      ...newPage,
    };
    setPages([...pages, page]);
    setNewPageDialog(false);
    setNewPage({ name: '', path: '', template: 'blank', status: 'draft' });
  };

  const handleDeletePage = (pageId) => {
    setPages(pages.filter(page => page.id !== pageId));
  };

  const handleEditPage = (page) => {
    setSelectedPage(page);
    setEditDialog(true);
  };

  const addComponent = (componentType) => {
    const newComponent = {
      id: Date.now(),
      type: componentType,
      props: getDefaultProps(componentType),
    };
    setPageComponents([...pageComponents, newComponent]);
  };

  const getDefaultProps = (type) => {
    switch (type) {
      case 'header':
        return { title: 'Page Title', subtitle: 'Page subtitle' };
      case 'form':
        return { fields: ['email', 'password'], submitText: 'Submit' };
      case 'image':
        return { src: '', alt: 'Image', width: '100%' };
      case 'text':
        return { content: 'Your text content here' };
      case 'custom':
        return { html: '<div>Custom HTML content</div>' };
      default:
        return {};
    }
  };

  const removeComponent = (componentId) => {
    setPageComponents(pageComponents.filter(comp => comp.id !== componentId));
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <StyledPaper sx={{ mb: 4 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                🏗️ Page Builder
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                Create and customize pages for your school management system
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setNewPageDialog(true)}
              sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.3)',
                },
              }}
            >
              New Page
            </Button>
          </Box>
        </StyledPaper>
      </motion.div>

      <Grid container spacing={3}>
        {/* Left Panel - Page List */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Pages
              </Typography>
              <List>
                {pages.map((page) => (
                  <ListItem key={page.id} divider>
                    <ListItemIcon>
                      <ComponentIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={page.name}
                      secondary={
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="caption">{page.path}</Typography>
                          <Chip
                            label={page.status}
                            size="small"
                            color={page.status === 'active' ? 'success' : 'default'}
                          />
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        size="small"
                        onClick={() => handleEditPage(page)}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDeletePage(page.id)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>

          {/* Component Library */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Components
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Drag and drop components to build your page
              </Typography>
              
              {componentTypes.map((component) => (
                <Card
                  key={component.type}
                  sx={{
                    mb: 2,
                    cursor: 'pointer',
                    border: '1px solid #e0e0e0',
                    '&:hover': {
                      borderColor: 'primary.main',
                      backgroundColor: 'rgba(102, 126, 234, 0.05)',
                    },
                  }}
                  onClick={() => addComponent(component.type)}
                >
                  <CardContent sx={{ p: 2 }}>
                    <Box display="flex" alignItems="center" gap={2}>
                      {component.icon}
                      <Box>
                        <Typography variant="subtitle2">
                          {component.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {component.description}
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Center Panel - Page Builder */}
        <Grid item xs={12} md={5}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Page Canvas
                </Typography>
                <Box>
                  <IconButton>
                    <PreviewIcon />
                  </IconButton>
                  <IconButton>
                    <SaveIcon />
                  </IconButton>
                </Box>
              </Box>

              <Box
                sx={{
                  minHeight: '600px',
                  border: '2px dashed #e0e0e0',
                  borderRadius: '8px',
                  p: 2,
                  backgroundColor: '#fafafa',
                }}
              >
                {pageComponents.length === 0 ? (
                  <ComponentCard>
                    <Box textAlign="center">
                      <ComponentIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="h6" color="text.secondary">
                        Drop components here
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Start building your page by adding components from the left panel
                      </Typography>
                    </Box>
                  </ComponentCard>
                ) : (
                  pageComponents.map((component, index) => (
                    <Card key={component.id} sx={{ mb: 2, position: 'relative' }}>
                      <CardContent>
                        <Box display="flex" justifyContent="between" alignItems="center" mb={1}>
                          <Box display="flex" alignItems="center" gap={1}>
                            <DragIcon sx={{ cursor: 'grab' }} />
                            <Typography variant="subtitle2">
                              {componentTypes.find(t => t.type === component.type)?.name}
                            </Typography>
                          </Box>
                          <IconButton
                            size="small"
                            onClick={() => removeComponent(component.id)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                        
                        {/* Component Preview */}
                        <Box
                          sx={{
                            p: 2,
                            border: '1px solid #e0e0e0',
                            borderRadius: '4px',
                            backgroundColor: 'white',
                          }}
                        >
                          {component.type === 'header' && (
                            <Box>
                              <Typography variant="h4">{component.props.title}</Typography>
                              <Typography variant="subtitle1">{component.props.subtitle}</Typography>
                            </Box>
                          )}
                          {component.type === 'form' && (
                            <Box>
                              <TextField fullWidth label="Email" sx={{ mb: 2 }} />
                              <TextField fullWidth label="Password" type="password" sx={{ mb: 2 }} />
                              <Button variant="contained" fullWidth>
                                {component.props.submitText}
                              </Button>
                            </Box>
                          )}
                          {component.type === 'text' && (
                            <Typography>{component.props.content}</Typography>
                          )}
                          {component.type === 'image' && (
                            <Box
                              sx={{
                                height: '200px',
                                backgroundColor: '#f0f0f0',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '4px',
                              }}
                            >
                              <ImageIcon sx={{ fontSize: 48, color: 'text.secondary' }} />
                            </Box>
                          )}
                          {component.type === 'custom' && (
                            <Box
                              sx={{
                                p: 2,
                                backgroundColor: '#f5f5f5',
                                fontFamily: 'monospace',
                                fontSize: '0.8rem',
                              }}
                            >
                              {component.props.html}
                            </Box>
                          )}
                        </Box>
                      </CardContent>
                    </Card>
                  ))
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Right Panel - Properties */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Properties
              </Typography>
              
              {pageComponents.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  Select a component to edit its properties
                </Typography>
              ) : (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Page Settings
                  </Typography>
                  
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>Layout</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                        <InputLabel>Container Width</InputLabel>
                        <Select defaultValue="full">
                          <MenuItem value="full">Full Width</MenuItem>
                          <MenuItem value="container">Container</MenuItem>
                          <MenuItem value="narrow">Narrow</MenuItem>
                        </Select>
                      </FormControl>
                      
                      <TextField
                        fullWidth
                        size="small"
                        label="Background Color"
                        type="color"
                        defaultValue="#ffffff"
                        sx={{ mb: 2 }}
                      />
                    </AccordionDetails>
                  </Accordion>

                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>SEO</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <TextField
                        fullWidth
                        size="small"
                        label="Page Title"
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        size="small"
                        label="Meta Description"
                        multiline
                        rows={3}
                      />
                    </AccordionDetails>
                  </Accordion>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* New Page Dialog */}
      <Dialog open={newPageDialog} onClose={() => setNewPageDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Page</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Page Name"
            value={newPage.name}
            onChange={(e) => setNewPage({ ...newPage, name: e.target.value })}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Page Path"
            value={newPage.path}
            onChange={(e) => setNewPage({ ...newPage, path: e.target.value })}
            margin="normal"
            placeholder="/my-page"
          />
          <FormControl fullWidth margin="normal">
            <InputLabel>Template</InputLabel>
            <Select
              value={newPage.template}
              onChange={(e) => setNewPage({ ...newPage, template: e.target.value })}
            >
              <MenuItem value="blank">Blank Page</MenuItem>
              <MenuItem value="login">Login Template</MenuItem>
              <MenuItem value="form">Form Template</MenuItem>
            </Select>
          </FormControl>
          <FormControl fullWidth margin="normal">
            <InputLabel>Status</InputLabel>
            <Select
              value={newPage.status}
              onChange={(e) => setNewPage({ ...newPage, status: e.target.value })}
            >
              <MenuItem value="draft">Draft</MenuItem>
              <MenuItem value="active">Active</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewPageDialog(false)}>Cancel</Button>
          <Button onClick={handleCreatePage} variant="contained">
            Create Page
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PageBuilder;
