{"ast": null, "code": "const generateLinearEasing = function (easing, duration) {\n  let resolution = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 10;\n  let points = \"\";\n  const numPoints = Math.max(Math.round(duration / resolution), 2);\n  for (let i = 0; i < numPoints; i++) {\n    points += Math.round(easing(i / (numPoints - 1)) * 10000) / 10000 + \", \";\n  }\n  return `linear(${points.substring(0, points.length - 2)})`;\n};\nexport { generateLinearEasing };", "map": {"version": 3, "names": ["generateLinearEasing", "easing", "duration", "resolution", "arguments", "length", "undefined", "points", "numPoints", "Math", "max", "round", "i", "substring"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs"], "sourcesContent": ["const generateLinearEasing = (easing, duration, // as milliseconds\nresolution = 10 // as milliseconds\n) => {\n    let points = \"\";\n    const numPoints = Math.max(Math.round(duration / resolution), 2);\n    for (let i = 0; i < numPoints; i++) {\n        points += Math.round(easing(i / (numPoints - 1)) * 10000) / 10000 + \", \";\n    }\n    return `linear(${points.substring(0, points.length - 2)})`;\n};\n\nexport { generateLinearEasing };\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG,SAAAA,CAACC,MAAM,EAAEC,QAAQ,EAEzC;EAAA,IADLC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAEX,IAAIG,MAAM,GAAG,EAAE;EACf,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACT,QAAQ,GAAGC,UAAU,CAAC,EAAE,CAAC,CAAC;EAChE,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,EAAEI,CAAC,EAAE,EAAE;IAChCL,MAAM,IAAIE,IAAI,CAACE,KAAK,CAACV,MAAM,CAACW,CAAC,IAAIJ,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,<PERSON>AAK,<PERSON>G,IAAI;EAC5E;EACA,OAAQ,UAASD,MAAM,CAACM,SAAS,CAAC,CAAC,EAAEN,MAAM,CAACF,MAAM,GAAG,CAAC,CAAE,GAAE;AAC9D,CAAC;AAED,SAASL,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}