{"ast": null, "code": "import { noop } from 'motion-utils';\nimport { attachToAnimation } from './attach-animation.mjs';\nimport { attachToFunction } from './attach-function.mjs';\nfunction scroll(onScroll) {\n  let {\n    axis = \"y\",\n    container = document.scrollingElement,\n    ...options\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (!container) return noop;\n  const optionsWithDefaults = {\n    axis,\n    container,\n    ...options\n  };\n  return typeof onScroll === \"function\" ? attachToFunction(onScroll, optionsWithDefaults) : attachToAnimation(onScroll, optionsWithDefaults);\n}\nexport { scroll };", "map": {"version": 3, "names": ["noop", "attachToAnimation", "attachToFunction", "scroll", "onScroll", "axis", "container", "document", "scrollingElement", "options", "arguments", "length", "undefined", "optionsWithDefaults"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs"], "sourcesContent": ["import { noop } from 'motion-utils';\nimport { attachToAnimation } from './attach-animation.mjs';\nimport { attachToFunction } from './attach-function.mjs';\n\nfunction scroll(onScroll, { axis = \"y\", container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return noop;\n    const optionsWithDefaults = { axis, container, ...options };\n    return typeof onScroll === \"function\"\n        ? attachToFunction(onScroll, optionsWithDefaults)\n        : attachToAnimation(onScroll, optionsWithDefaults);\n}\n\nexport { scroll };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,cAAc;AACnC,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,gBAAgB,QAAQ,uBAAuB;AAExD,SAASC,MAAMA,CAACC,QAAQ,EAA0E;EAAA,IAAxE;IAAEC,IAAI,GAAG,GAAG;IAAEC,SAAS,GAAGC,QAAQ,CAACC,gBAAgB;IAAE,GAAGC;EAAQ,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC5F,IAAI,CAACJ,SAAS,EACV,OAAON,IAAI;EACf,MAAMa,mBAAmB,GAAG;IAAER,IAAI;IAAEC,SAAS;IAAE,GAAGG;EAAQ,CAAC;EAC3D,OAAO,OAAOL,QAAQ,KAAK,UAAU,GAC/BF,gBAAgB,CAACE,QAAQ,EAAES,mBAAmB,CAAC,GAC/CZ,iBAAiB,CAACG,QAAQ,EAAES,mBAAmB,CAAC;AAC1D;AAEA,SAASV,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}