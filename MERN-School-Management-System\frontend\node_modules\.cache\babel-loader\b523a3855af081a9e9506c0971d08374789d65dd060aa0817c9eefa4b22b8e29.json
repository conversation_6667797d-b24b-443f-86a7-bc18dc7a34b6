{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\components\\\\layout\\\\ResponsiveSidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Drawer, List, ListItemButton, ListItemIcon, ListItemText, Divider, Box, Typography, useTheme, useMediaQuery, Avatar, Chip, Collapse } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Home as HomeIcon, Class as ClassIcon, Assignment as AssignmentIcon, SupervisorAccount as SupervisorAccountIcon, PersonOutline as PersonOutlineIcon, Announcement as AnnouncementIcon, Report as ReportIcon, AccountCircleOutlined as AccountCircleOutlinedIcon, ExitToApp as ExitToAppIcon, School as SchoolIcon,\n// New icons for additional modules\nInfo as InfoIcon, Payment as PaymentIcon, Description as DescriptionIcon, DirectionsBus as DirectionsBusIcon, Inventory as InventoryIcon, Web as WebIcon, People as PeopleIcon, PersonAdd as PersonAddIcon, EventAvailable as EventAvailableIcon, Quiz as QuizIcon, MenuBook as MenuBookIcon, NotificationsActive as NotificationsActiveIcon, Hotel as HotelIcon, Business as BusinessIcon, ExpandLess, ExpandMore,\n// Attendance and Examination icons\nCheckCircle as AttendanceIcon, Assessment as ExaminationIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledDrawer = styled(Drawer)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    '& .MuiDrawer-paper': {\n      background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%)',\n      color: 'white',\n      borderRight: 'none',\n      boxShadow: '8px 0 32px rgba(0, 0, 0, 0.15)',\n      backdropFilter: 'blur(10px)',\n      position: 'fixed',\n      top: theme.mixins.toolbar.minHeight || '64px',\n      // Use theme toolbar height\n      height: `calc(100vh - ${theme.mixins.toolbar.minHeight || '64px'}px)`,\n      // Full height minus AppBar\n      zIndex: theme.zIndex.drawer,\n      [theme.breakpoints.up('sm')]: {\n        top: '64px',\n        height: 'calc(100vh - 64px)'\n      },\n      [theme.breakpoints.up('md')]: {\n        top: '64px',\n        height: 'calc(100vh - 64px)'\n      },\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n        pointerEvents: 'none'\n      }\n    }\n  };\n});\n_c = StyledDrawer;\nconst SidebarHeader = styled(Box)(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    padding: theme.spacing(2, 2),\n    display: 'flex',\n    alignItems: 'center',\n    gap: theme.spacing(2),\n    borderBottom: '2px solid rgba(255, 255, 255, 0.15)',\n    background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n    backdropFilter: 'blur(10px)',\n    position: 'relative',\n    '&::after': {\n      content: '\"\"',\n      position: 'absolute',\n      bottom: 0,\n      left: '50%',\n      transform: 'translateX(-50%)',\n      width: '60%',\n      height: '2px',\n      background: 'linear-gradient(90deg, transparent, #FFD700, transparent)'\n    }\n  };\n});\n_c2 = SidebarHeader;\nconst StyledListItemButton = styled(ListItemButton)(_ref3 => {\n  let {\n    theme,\n    active\n  } = _ref3;\n  return {\n    margin: theme.spacing(0.5, 1.5),\n    borderRadius: '16px',\n    padding: theme.spacing(1.5, 2),\n    backgroundColor: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',\n    backdropFilter: active ? 'blur(15px)' : 'none',\n    border: active ? '1px solid rgba(255, 255, 255, 0.3)' : '1px solid transparent',\n    boxShadow: active ? '0 8px 32px rgba(0, 0, 0, 0.1)' : 'none',\n    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n    position: 'relative',\n    overflow: 'hidden',\n    '&:hover': {\n      backgroundColor: 'rgba(255, 255, 255, 0.15)',\n      transform: 'translateX(8px) scale(1.02)',\n      boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)'\n    },\n    '&:before': {\n      content: '\"\"',\n      position: 'absolute',\n      left: 0,\n      top: '50%',\n      transform: 'translateY(-50%)',\n      width: active ? '5px' : '0px',\n      height: '70%',\n      background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n      borderRadius: '0 8px 8px 0',\n      transition: 'width 0.3s ease',\n      boxShadow: active ? '0 0 20px rgba(255, 215, 0, 0.5)' : 'none'\n    },\n    '&:after': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: active ? 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))' : 'transparent',\n      pointerEvents: 'none',\n      borderRadius: '16px'\n    }\n  };\n});\n_c3 = StyledListItemButton;\nconst MenuSection = styled(Box)(_ref4 => {\n  let {\n    theme\n  } = _ref4;\n  return {\n    padding: theme.spacing(1, 1.5),\n    marginTop: theme.spacing(1.5)\n  };\n});\n_c4 = MenuSection;\nconst SectionTitle = styled(Typography)(_ref5 => {\n  let {\n    theme\n  } = _ref5;\n  return {\n    fontSize: '0.8rem',\n    fontWeight: 700,\n    textTransform: 'uppercase',\n    letterSpacing: '1px',\n    opacity: 0.9,\n    marginBottom: theme.spacing(1.5),\n    marginLeft: theme.spacing(1),\n    background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n    textShadow: '0 2px 4px rgba(0,0,0,0.1)'\n  };\n});\n_c5 = SectionTitle;\nconst ResponsiveSidebar = _ref6 => {\n  _s();\n  let {\n    open,\n    onClose,\n    variant = 'permanent'\n  } = _ref6;\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const location = useLocation();\n  // Simplified menu without collapsible sections\n\n  // Simplified main menu items\n  const mainMenuItems = [{\n    text: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/dashboard',\n    badge: null\n  }, {\n    text: 'Student Info',\n    icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/student-info',\n    badge: null\n  }, {\n    text: 'Students',\n    icon: /*#__PURE__*/_jsxDEV(PersonOutlineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/students',\n    badge: null\n  }, {\n    text: 'Classes',\n    icon: /*#__PURE__*/_jsxDEV(ClassIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/classes',\n    badge: null\n  }, {\n    text: 'Teachers',\n    icon: /*#__PURE__*/_jsxDEV(SupervisorAccountIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/teachers',\n    badge: null\n  }, {\n    text: 'Subjects',\n    icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/subjects',\n    badge: null\n  }, {\n    text: 'Fee Management',\n    icon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/fee-management',\n    badge: '12'\n  }, {\n    text: 'Transport',\n    icon: /*#__PURE__*/_jsxDEV(DirectionsBusIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/transport',\n    badge: null\n  }, {\n    text: 'Inventory',\n    icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/inventory',\n    badge: '5'\n  }, {\n    text: 'Documents',\n    icon: /*#__PURE__*/_jsxDEV(DescriptionIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/documents',\n    badge: null\n  }, {\n    text: 'CMS',\n    icon: /*#__PURE__*/_jsxDEV(WebIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/cms',\n    badge: 'New'\n  }, {\n    text: 'Notifications',\n    icon: /*#__PURE__*/_jsxDEV(NotificationsActiveIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/notifications',\n    badge: '15'\n  }, {\n    text: 'Notices',\n    icon: /*#__PURE__*/_jsxDEV(AnnouncementIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/notices',\n    badge: '3'\n  }];\n\n  // User account menu items\n  const userMenuItems = [{\n    text: 'Profile',\n    icon: /*#__PURE__*/_jsxDEV(AccountCircleOutlinedIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/profile',\n    badge: null\n  }, {\n    text: 'Logout',\n    icon: /*#__PURE__*/_jsxDEV(ExitToAppIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 13\n    }, this),\n    path: '/logout',\n    badge: null\n  }];\n  const isActive = path => {\n    if (path === '/Admin/dashboard') {\n      return location.pathname === '/' || location.pathname === '/Admin/dashboard';\n    }\n    return location.pathname.startsWith(path);\n  };\n  const renderMenuItem = (item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      x: -20\n    },\n    animate: {\n      opacity: 1,\n      x: 0\n    },\n    transition: {\n      delay: index * 0.05\n    },\n    children: /*#__PURE__*/_jsxDEV(StyledListItemButton, {\n      component: Link,\n      to: item.path,\n      active: isActive(item.path),\n      onClick: isMobile ? onClose : undefined,\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        sx: {\n          color: 'inherit',\n          minWidth: 40\n        },\n        children: item.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: item.text,\n        primaryTypographyProps: {\n          fontSize: '0.9rem',\n          fontWeight: isActive(item.path) ? 600 : 400\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), item.badge && /*#__PURE__*/_jsxDEV(Chip, {\n        label: item.badge,\n        size: \"small\",\n        sx: {\n          bgcolor: '#FFD700',\n          color: '#333',\n          fontSize: '0.7rem',\n          height: 20,\n          minWidth: 20,\n          fontWeight: 600\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this)\n  }, item.text, false, {\n    fileName: _jsxFileName,\n    lineNumber: 280,\n    columnNumber: 5\n  }, this);\n  const drawerContent = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(SidebarHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        sx: {\n          bgcolor: 'rgba(255, 255, 255, 0.3)',\n          width: 56,\n          height: 56,\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(SchoolIcon, {\n          sx: {\n            fontSize: 32\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"bold\",\n          sx: {\n            fontSize: '1.1rem'\n          },\n          children: \"\\uD83C\\uDF93 School Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            opacity: 0.9,\n            fontSize: '0.8rem'\n          },\n          children: \"Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        overflowY: 'auto',\n        py: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(MenuSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"\\uD83D\\uDCCB Main Menu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          sx: {\n            padding: 0\n          },\n          children: mainMenuItems.map((item, index) => renderMenuItem(item, index))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mx: 3,\n          my: 2,\n          bgcolor: 'rgba(255, 255, 255, 0.2)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"\\uD83D\\uDC64 Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          sx: {\n            padding: 0\n          },\n          children: userMenuItems.map((item, index) => renderMenuItem(item, index))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 321,\n    columnNumber: 5\n  }, this);\n  if (isMobile) {\n    return /*#__PURE__*/_jsxDEV(StyledDrawer, {\n      variant: \"temporary\",\n      open: open,\n      onClose: onClose,\n      ModalProps: {\n        keepMounted: true // Better open performance on mobile.\n      },\n\n      sx: {\n        '& .MuiDrawer-paper': {\n          width: 280,\n          top: {\n            xs: '72px',\n            sm: '80px',\n            md: '88px'\n          },\n          height: {\n            xs: 'calc(100vh - 72px)',\n            sm: 'calc(100vh - 80px)',\n            md: 'calc(100vh - 88px)'\n          }\n        }\n      },\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(StyledDrawer, {\n    variant: variant,\n    open: open,\n    sx: {\n      '& .MuiDrawer-paper': {\n        width: open ? 280 : 70,\n        transition: theme.transitions.create('width', {\n          easing: theme.transitions.easing.sharp,\n          duration: theme.transitions.duration.enteringScreen\n        }),\n        overflowX: 'hidden',\n        top: {\n          xs: '72px',\n          sm: '80px',\n          md: '88px'\n        },\n        height: {\n          xs: 'calc(100vh - 72px)',\n          sm: 'calc(100vh - 80px)',\n          md: 'calc(100vh - 88px)'\n        }\n      }\n    },\n    children: drawerContent\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 388,\n    columnNumber: 5\n  }, this);\n};\n_s(ResponsiveSidebar, \"EDcwBIz4vaW9vHEUm4iGTJ9ZhSk=\", false, function () {\n  return [useTheme, useMediaQuery, useLocation];\n});\n_c6 = ResponsiveSidebar;\nexport default ResponsiveSidebar;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"StyledDrawer\");\n$RefreshReg$(_c2, \"SidebarHeader\");\n$RefreshReg$(_c3, \"StyledListItemButton\");\n$RefreshReg$(_c4, \"MenuSection\");\n$RefreshReg$(_c5, \"SectionTitle\");\n$RefreshReg$(_c6, \"ResponsiveSidebar\");", "map": {"version": 3, "names": ["React", "Drawer", "List", "ListItemButton", "ListItemIcon", "ListItemText", "Divider", "Box", "Typography", "useTheme", "useMediaQuery", "Avatar", "Chip", "Collapse", "styled", "motion", "Link", "useLocation", "Home", "HomeIcon", "Class", "ClassIcon", "Assignment", "AssignmentIcon", "SupervisorAccount", "SupervisorAccountIcon", "PersonOutline", "PersonOutlineIcon", "Announcement", "AnnouncementIcon", "Report", "ReportIcon", "AccountCircleOutlined", "AccountCircleOutlinedIcon", "ExitToApp", "ExitToAppIcon", "School", "SchoolIcon", "Info", "InfoIcon", "Payment", "PaymentIcon", "Description", "DescriptionIcon", "DirectionsBus", "DirectionsBusIcon", "Inventory", "InventoryIcon", "Web", "WebIcon", "People", "PeopleIcon", "PersonAdd", "PersonAddIcon", "EventAvailable", "EventAvailableIcon", "Quiz", "QuizIcon", "MenuBook", "MenuBookIcon", "NotificationsActive", "NotificationsActiveIcon", "Hotel", "HotelIcon", "Business", "BusinessIcon", "ExpandLess", "ExpandMore", "CheckCircle", "AttendanceIcon", "Assessment", "ExaminationIcon", "jsxDEV", "_jsxDEV", "StyledDrawer", "_ref", "theme", "background", "color", "borderRight", "boxShadow", "<PERSON><PERSON>ilter", "position", "top", "mixins", "toolbar", "minHeight", "height", "zIndex", "drawer", "breakpoints", "up", "content", "left", "right", "bottom", "pointerEvents", "_c", "SidebarHeader", "_ref2", "padding", "spacing", "display", "alignItems", "gap", "borderBottom", "transform", "width", "_c2", "StyledListItemButton", "_ref3", "active", "margin", "borderRadius", "backgroundColor", "border", "transition", "overflow", "_c3", "MenuSection", "_ref4", "marginTop", "_c4", "SectionTitle", "_ref5", "fontSize", "fontWeight", "textTransform", "letterSpacing", "opacity", "marginBottom", "marginLeft", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "_c5", "ResponsiveSidebar", "_ref6", "_s", "open", "onClose", "variant", "isMobile", "down", "location", "mainMenuItems", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "badge", "userMenuItems", "isActive", "pathname", "startsWith", "renderMenuItem", "item", "index", "div", "initial", "x", "animate", "delay", "children", "component", "to", "onClick", "undefined", "sx", "min<PERSON><PERSON><PERSON>", "primary", "primaryTypographyProps", "label", "size", "bgcolor", "drawerContent", "flexDirection", "flex", "overflowY", "py", "map", "mx", "my", "ModalProps", "keepMounted", "xs", "sm", "md", "transitions", "create", "easing", "sharp", "duration", "enteringScreen", "overflowX", "_c6", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/components/layout/ResponsiveSidebar.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Drawer,\n  List,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Box,\n  Typography,\n  useTheme,\n  useMediaQuery,\n  Avatar,\n  Chip,\n  Collapse\n} from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport {\n  Home as HomeIcon,\n  Class as ClassIcon,\n  Assignment as AssignmentIcon,\n  SupervisorAccount as SupervisorAccountIcon,\n  PersonOutline as PersonOutlineIcon,\n  Announcement as AnnouncementIcon,\n  Report as ReportIcon,\n  AccountCircleOutlined as AccountCircleOutlinedIcon,\n  ExitToApp as ExitToAppIcon,\n  School as SchoolIcon,\n  // New icons for additional modules\n  Info as InfoIcon,\n  Payment as PaymentIcon,\n  Description as DescriptionIcon,\n  DirectionsBus as DirectionsBusIcon,\n  Inventory as InventoryIcon,\n  Web as WebIcon,\n  People as PeopleIcon,\n  PersonAdd as PersonAddIcon,\n  EventAvailable as EventAvailableIcon,\n  Quiz as QuizIcon,\n  MenuBook as MenuBookIcon,\n  NotificationsActive as NotificationsActiveIcon,\n  Hotel as HotelIcon,\n  Business as BusinessIcon,\n  ExpandLess,\n  ExpandMore,\n  // Attendance and Examination icons\n  CheckCircle as AttendanceIcon,\n  Assessment as ExaminationIcon,\n} from '@mui/icons-material';\n\nconst StyledDrawer = styled(Drawer)(({ theme }) => ({\n  '& .MuiDrawer-paper': {\n    background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%)',\n    color: 'white',\n    borderRight: 'none',\n    boxShadow: '8px 0 32px rgba(0, 0, 0, 0.15)',\n    backdropFilter: 'blur(10px)',\n    position: 'fixed',\n    top: theme.mixins.toolbar.minHeight || '64px', // Use theme toolbar height\n    height: `calc(100vh - ${theme.mixins.toolbar.minHeight || '64px'}px)`, // Full height minus AppBar\n    zIndex: theme.zIndex.drawer,\n    [theme.breakpoints.up('sm')]: {\n      top: '64px',\n      height: 'calc(100vh - 64px)',\n    },\n    [theme.breakpoints.up('md')]: {\n      top: '64px',\n      height: 'calc(100vh - 64px)',\n    },\n    '&::before': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n      pointerEvents: 'none',\n    }\n  },\n}));\n\nconst SidebarHeader = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(2, 2),\n  display: 'flex',\n  alignItems: 'center',\n  gap: theme.spacing(2),\n  borderBottom: '2px solid rgba(255, 255, 255, 0.15)',\n  background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n  backdropFilter: 'blur(10px)',\n  position: 'relative',\n  '&::after': {\n    content: '\"\"',\n    position: 'absolute',\n    bottom: 0,\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '60%',\n    height: '2px',\n    background: 'linear-gradient(90deg, transparent, #FFD700, transparent)',\n  }\n}));\n\nconst StyledListItemButton = styled(ListItemButton)(({ theme, active }) => ({\n  margin: theme.spacing(0.5, 1.5),\n  borderRadius: '16px',\n  padding: theme.spacing(1.5, 2),\n  backgroundColor: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',\n  backdropFilter: active ? 'blur(15px)' : 'none',\n  border: active ? '1px solid rgba(255, 255, 255, 0.3)' : '1px solid transparent',\n  boxShadow: active ? '0 8px 32px rgba(0, 0, 0, 0.1)' : 'none',\n  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n  position: 'relative',\n  overflow: 'hidden',\n  '&:hover': {\n    backgroundColor: 'rgba(255, 255, 255, 0.15)',\n    transform: 'translateX(8px) scale(1.02)',\n    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',\n  },\n  '&:before': {\n    content: '\"\"',\n    position: 'absolute',\n    left: 0,\n    top: '50%',\n    transform: 'translateY(-50%)',\n    width: active ? '5px' : '0px',\n    height: '70%',\n    background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n    borderRadius: '0 8px 8px 0',\n    transition: 'width 0.3s ease',\n    boxShadow: active ? '0 0 20px rgba(255, 215, 0, 0.5)' : 'none',\n  },\n  '&:after': {\n    content: '\"\"',\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: active ? 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))' : 'transparent',\n    pointerEvents: 'none',\n    borderRadius: '16px',\n  },\n}));\n\nconst MenuSection = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(1, 1.5),\n  marginTop: theme.spacing(1.5),\n}));\n\nconst SectionTitle = styled(Typography)(({ theme }) => ({\n  fontSize: '0.8rem',\n  fontWeight: 700,\n  textTransform: 'uppercase',\n  letterSpacing: '1px',\n  opacity: 0.9,\n  marginBottom: theme.spacing(1.5),\n  marginLeft: theme.spacing(1),\n  background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n  backgroundClip: 'text',\n  WebkitBackgroundClip: 'text',\n  WebkitTextFillColor: 'transparent',\n  textShadow: '0 2px 4px rgba(0,0,0,0.1)',\n}));\n\nconst ResponsiveSidebar = ({ open, onClose, variant = 'permanent' }) => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const location = useLocation();\n  // Simplified menu without collapsible sections\n\n  // Simplified main menu items\n  const mainMenuItems = [\n    {\n      text: 'Dashboard',\n      icon: <HomeIcon />,\n      path: '/Admin/dashboard',\n      badge: null\n    },\n    {\n      text: 'Student Info',\n      icon: <InfoIcon />,\n      path: '/Admin/student-info',\n      badge: null\n    },\n    {\n      text: 'Students',\n      icon: <PersonOutlineIcon />,\n      path: '/Admin/students',\n      badge: null\n    },\n    {\n      text: 'Classes',\n      icon: <ClassIcon />,\n      path: '/Admin/classes',\n      badge: null\n    },\n    {\n      text: 'Teachers',\n      icon: <SupervisorAccountIcon />,\n      path: '/Admin/teachers',\n      badge: null\n    },\n    {\n      text: 'Subjects',\n      icon: <AssignmentIcon />,\n      path: '/Admin/subjects',\n      badge: null\n    },\n    {\n      text: 'Fee Management',\n      icon: <PaymentIcon />,\n      path: '/Admin/fee-management',\n      badge: '12'\n    },\n    {\n      text: 'Transport',\n      icon: <DirectionsBusIcon />,\n      path: '/Admin/transport',\n      badge: null\n    },\n    {\n      text: 'Inventory',\n      icon: <InventoryIcon />,\n      path: '/Admin/inventory',\n      badge: '5'\n    },\n    {\n      text: 'Documents',\n      icon: <DescriptionIcon />,\n      path: '/Admin/documents',\n      badge: null\n    },\n    {\n      text: 'CMS',\n      icon: <WebIcon />,\n      path: '/Admin/cms',\n      badge: 'New'\n    },\n    {\n      text: 'Notifications',\n      icon: <NotificationsActiveIcon />,\n      path: '/Admin/notifications',\n      badge: '15'\n    },\n    {\n      text: 'Notices',\n      icon: <AnnouncementIcon />,\n      path: '/Admin/notices',\n      badge: '3'\n    },\n  ];\n\n  // User account menu items\n  const userMenuItems = [\n    {\n      text: 'Profile',\n      icon: <AccountCircleOutlinedIcon />,\n      path: '/Admin/profile',\n      badge: null\n    },\n    {\n      text: 'Logout',\n      icon: <ExitToAppIcon />,\n      path: '/logout',\n      badge: null\n    },\n  ];\n\n  const isActive = (path) => {\n    if (path === '/Admin/dashboard') {\n      return location.pathname === '/' || location.pathname === '/Admin/dashboard';\n    }\n    return location.pathname.startsWith(path);\n  };\n\n  const renderMenuItem = (item, index) => (\n    <motion.div\n      key={item.text}\n      initial={{ opacity: 0, x: -20 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ delay: index * 0.05 }}\n    >\n      <StyledListItemButton\n        component={Link}\n        to={item.path}\n        active={isActive(item.path)}\n        onClick={isMobile ? onClose : undefined}\n      >\n        <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>\n          {item.icon}\n        </ListItemIcon>\n        <ListItemText\n          primary={item.text}\n          primaryTypographyProps={{\n            fontSize: '0.9rem',\n            fontWeight: isActive(item.path) ? 600 : 400,\n          }}\n        />\n        {item.badge && (\n          <Chip\n            label={item.badge}\n            size=\"small\"\n            sx={{\n              bgcolor: '#FFD700',\n              color: '#333',\n              fontSize: '0.7rem',\n              height: 20,\n              minWidth: 20,\n              fontWeight: 600,\n            }}\n          />\n        )}\n      </StyledListItemButton>\n    </motion.div>\n  );\n\n  const drawerContent = (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <SidebarHeader>\n        <Avatar\n          sx={{\n            bgcolor: 'rgba(255, 255, 255, 0.3)',\n            width: 56,\n            height: 56,\n            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n          }}\n        >\n          <SchoolIcon sx={{ fontSize: 32 }} />\n        </Avatar>\n        <Box>\n          <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ fontSize: '1.1rem' }}>\n            🎓 School Admin\n          </Typography>\n          <Typography variant=\"caption\" sx={{ opacity: 0.9, fontSize: '0.8rem' }}>\n            Management System\n          </Typography>\n        </Box>\n      </SidebarHeader>\n\n      <Box sx={{ flex: 1, overflowY: 'auto', py: 2 }}>\n        {/* Main Menu */}\n        <MenuSection>\n          <SectionTitle>📋 Main Menu</SectionTitle>\n          <List component=\"nav\" sx={{ padding: 0 }}>\n            {mainMenuItems.map((item, index) => renderMenuItem(item, index))}\n          </List>\n        </MenuSection>\n\n        <Divider sx={{ mx: 3, my: 2, bgcolor: 'rgba(255, 255, 255, 0.2)' }} />\n\n        {/* User Account */}\n        <MenuSection>\n          <SectionTitle>👤 Account</SectionTitle>\n          <List component=\"nav\" sx={{ padding: 0 }}>\n            {userMenuItems.map((item, index) => renderMenuItem(item, index))}\n          </List>\n        </MenuSection>\n      </Box>\n    </Box>\n  );\n\n  if (isMobile) {\n    return (\n      <StyledDrawer\n        variant=\"temporary\"\n        open={open}\n        onClose={onClose}\n        ModalProps={{\n          keepMounted: true, // Better open performance on mobile.\n        }}\n        sx={{\n          '& .MuiDrawer-paper': {\n            width: 280,\n            top: { xs: '72px', sm: '80px', md: '88px' },\n            height: { xs: 'calc(100vh - 72px)', sm: 'calc(100vh - 80px)', md: 'calc(100vh - 88px)' },\n          },\n        }}\n      >\n        {drawerContent}\n      </StyledDrawer>\n    );\n  }\n\n  return (\n    <StyledDrawer\n      variant={variant}\n      open={open}\n      sx={{\n        '& .MuiDrawer-paper': {\n          width: open ? 280 : 70,\n          transition: theme.transitions.create('width', {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.enteringScreen,\n          }),\n          overflowX: 'hidden',\n          top: { xs: '72px', sm: '80px', md: '88px' },\n          height: { xs: 'calc(100vh - 72px)', sm: 'calc(100vh - 80px)', md: 'calc(100vh - 88px)' },\n        },\n      }}\n    >\n      {drawerContent}\n    </StyledDrawer>\n  );\n};\n\nexport default ResponsiveSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,IAAI,EACJC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,aAAa,EACbC,MAAM,EACNC,IAAI,EACJC,QAAQ,QACH,eAAe;AACtB,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SACEC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,iBAAiB,IAAIC,qBAAqB,EAC1CC,aAAa,IAAIC,iBAAiB,EAClCC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,qBAAqB,IAAIC,yBAAyB,EAClDC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU;AACpB;AACAC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,eAAe,EAC9BC,aAAa,IAAIC,iBAAiB,EAClCC,SAAS,IAAIC,aAAa,EAC1BC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,cAAc,IAAIC,kBAAkB,EACpCC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,mBAAmB,IAAIC,uBAAuB,EAC9CC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,EACVC,UAAU;AACV;AACAC,WAAW,IAAIC,cAAc,EAC7BC,UAAU,IAAIC,eAAe,QACxB,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,YAAY,GAAG5D,MAAM,CAACb,MAAM,CAAC,CAAC0E,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IAClD,oBAAoB,EAAE;MACpBE,UAAU,EAAE,gEAAgE;MAC5EC,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE,gCAAgC;MAC3CC,cAAc,EAAE,YAAY;MAC5BC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAEP,KAAK,CAACQ,MAAM,CAACC,OAAO,CAACC,SAAS,IAAI,MAAM;MAAE;MAC/CC,MAAM,EAAG,gBAAeX,KAAK,CAACQ,MAAM,CAACC,OAAO,CAACC,SAAS,IAAI,MAAO,KAAI;MAAE;MACvEE,MAAM,EAAEZ,KAAK,CAACY,MAAM,CAACC,MAAM;MAC3B,CAACb,KAAK,CAACc,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;QAC5BR,GAAG,EAAE,MAAM;QACXI,MAAM,EAAE;MACV,CAAC;MACD,CAACX,KAAK,CAACc,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;QAC5BR,GAAG,EAAE,MAAM;QACXI,MAAM,EAAE;MACV,CAAC;MACD,WAAW,EAAE;QACXK,OAAO,EAAE,IAAI;QACbV,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,CAAC;QACNU,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTlB,UAAU,EAAE,gFAAgF;QAC5FmB,aAAa,EAAE;MACjB;IACF;EACF,CAAC;AAAA,CAAC,CAAC;AAACC,EAAA,GA9BEvB,YAAY;AAgClB,MAAMwB,aAAa,GAAGpF,MAAM,CAACP,GAAG,CAAC,CAAC4F,KAAA;EAAA,IAAC;IAAEvB;EAAM,CAAC,GAAAuB,KAAA;EAAA,OAAM;IAChDC,OAAO,EAAExB,KAAK,CAACyB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5BC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAE5B,KAAK,CAACyB,OAAO,CAAC,CAAC,CAAC;IACrBI,YAAY,EAAE,qCAAqC;IACnD5B,UAAU,EAAE,gFAAgF;IAC5FI,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,UAAU;IACpB,UAAU,EAAE;MACVU,OAAO,EAAE,IAAI;MACbV,QAAQ,EAAE,UAAU;MACpBa,MAAM,EAAE,CAAC;MACTF,IAAI,EAAE,KAAK;MACXa,SAAS,EAAE,kBAAkB;MAC7BC,KAAK,EAAE,KAAK;MACZpB,MAAM,EAAE,KAAK;MACbV,UAAU,EAAE;IACd;EACF,CAAC;AAAA,CAAC,CAAC;AAAC+B,GAAA,GAnBEV,aAAa;AAqBnB,MAAMW,oBAAoB,GAAG/F,MAAM,CAACX,cAAc,CAAC,CAAC2G,KAAA;EAAA,IAAC;IAAElC,KAAK;IAAEmC;EAAO,CAAC,GAAAD,KAAA;EAAA,OAAM;IAC1EE,MAAM,EAAEpC,KAAK,CAACyB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAC/BY,YAAY,EAAE,MAAM;IACpBb,OAAO,EAAExB,KAAK,CAACyB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IAC9Ba,eAAe,EAAEH,MAAM,GAAG,0BAA0B,GAAG,aAAa;IACpE9B,cAAc,EAAE8B,MAAM,GAAG,YAAY,GAAG,MAAM;IAC9CI,MAAM,EAAEJ,MAAM,GAAG,oCAAoC,GAAG,uBAAuB;IAC/E/B,SAAS,EAAE+B,MAAM,GAAG,+BAA+B,GAAG,MAAM;IAC5DK,UAAU,EAAE,uCAAuC;IACnDlC,QAAQ,EAAE,UAAU;IACpBmC,QAAQ,EAAE,QAAQ;IAClB,SAAS,EAAE;MACTH,eAAe,EAAE,2BAA2B;MAC5CR,SAAS,EAAE,6BAA6B;MACxC1B,SAAS,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACVY,OAAO,EAAE,IAAI;MACbV,QAAQ,EAAE,UAAU;MACpBW,IAAI,EAAE,CAAC;MACPV,GAAG,EAAE,KAAK;MACVuB,SAAS,EAAE,kBAAkB;MAC7BC,KAAK,EAAEI,MAAM,GAAG,KAAK,GAAG,KAAK;MAC7BxB,MAAM,EAAE,KAAK;MACbV,UAAU,EAAE,2CAA2C;MACvDoC,YAAY,EAAE,aAAa;MAC3BG,UAAU,EAAE,iBAAiB;MAC7BpC,SAAS,EAAE+B,MAAM,GAAG,iCAAiC,GAAG;IAC1D,CAAC;IACD,SAAS,EAAE;MACTnB,OAAO,EAAE,IAAI;MACbV,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,CAAC;MACNU,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTlB,UAAU,EAAEkC,MAAM,GAAG,wEAAwE,GAAG,aAAa;MAC7Gf,aAAa,EAAE,MAAM;MACrBiB,YAAY,EAAE;IAChB;EACF,CAAC;AAAA,CAAC,CAAC;AAACK,GAAA,GAxCET,oBAAoB;AA0C1B,MAAMU,WAAW,GAAGzG,MAAM,CAACP,GAAG,CAAC,CAACiH,KAAA;EAAA,IAAC;IAAE5C;EAAM,CAAC,GAAA4C,KAAA;EAAA,OAAM;IAC9CpB,OAAO,EAAExB,KAAK,CAACyB,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;IAC9BoB,SAAS,EAAE7C,KAAK,CAACyB,OAAO,CAAC,GAAG;EAC9B,CAAC;AAAA,CAAC,CAAC;AAACqB,GAAA,GAHEH,WAAW;AAKjB,MAAMI,YAAY,GAAG7G,MAAM,CAACN,UAAU,CAAC,CAACoH,KAAA;EAAA,IAAC;IAAEhD;EAAM,CAAC,GAAAgD,KAAA;EAAA,OAAM;IACtDC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,GAAG;IACfC,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE,KAAK;IACpBC,OAAO,EAAE,GAAG;IACZC,YAAY,EAAEtD,KAAK,CAACyB,OAAO,CAAC,GAAG,CAAC;IAChC8B,UAAU,EAAEvD,KAAK,CAACyB,OAAO,CAAC,CAAC,CAAC;IAC5BxB,UAAU,EAAE,2CAA2C;IACvDuD,cAAc,EAAE,MAAM;IACtBC,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE,aAAa;IAClCC,UAAU,EAAE;EACd,CAAC;AAAA,CAAC,CAAC;AAACC,GAAA,GAbEb,YAAY;AAelB,MAAMc,iBAAiB,GAAGC,KAAA,IAA8C;EAAAC,EAAA;EAAA,IAA7C;IAAEC,IAAI;IAAEC,OAAO;IAAEC,OAAO,GAAG;EAAY,CAAC,GAAAJ,KAAA;EACjE,MAAM9D,KAAK,GAAGnE,QAAQ,EAAE;EACxB,MAAMsI,QAAQ,GAAGrI,aAAa,CAACkE,KAAK,CAACc,WAAW,CAACsD,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAMC,QAAQ,GAAGhI,WAAW,EAAE;EAC9B;;EAEA;EACA,MAAMiI,aAAa,GAAG,CACpB;IACEC,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAE3E,OAAA,CAACtD,QAAQ;MAAAkI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAClBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,eAAE3E,OAAA,CAAClC,QAAQ;MAAA8G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAClBC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAE3E,OAAA,CAAC9C,iBAAiB;MAAA0H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC3BC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,eAAE3E,OAAA,CAACpD,SAAS;MAAAgI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnBC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAE3E,OAAA,CAAChD,qBAAqB;MAAA4H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC/BC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAE3E,OAAA,CAAClD,cAAc;MAAA8H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACxBC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,gBAAgB;IACtBC,IAAI,eAAE3E,OAAA,CAAChC,WAAW;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACrBC,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAE3E,OAAA,CAAC5B,iBAAiB;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC3BC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAE3E,OAAA,CAAC1B,aAAa;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACvBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAE3E,OAAA,CAAC9B,eAAe;MAAA0G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,KAAK;IACXC,IAAI,eAAE3E,OAAA,CAACxB,OAAO;MAAAoG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACjBC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,eAAe;IACrBC,IAAI,eAAE3E,OAAA,CAACZ,uBAAuB;MAAAwF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACjCC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,eAAE3E,OAAA,CAAC5C,gBAAgB;MAAAwH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC1BC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IACER,IAAI,EAAE,SAAS;IACfC,IAAI,eAAE3E,OAAA,CAACxC,yBAAyB;MAAAoH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnCC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdC,IAAI,eAAE3E,OAAA,CAACtC,aAAa;MAAAkH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAME,QAAQ,GAAIH,IAAI,IAAK;IACzB,IAAIA,IAAI,KAAK,kBAAkB,EAAE;MAC/B,OAAOR,QAAQ,CAACY,QAAQ,KAAK,GAAG,IAAIZ,QAAQ,CAACY,QAAQ,KAAK,kBAAkB;IAC9E;IACA,OAAOZ,QAAQ,CAACY,QAAQ,CAACC,UAAU,CAACL,IAAI,CAAC;EAC3C,CAAC;EAED,MAAMM,cAAc,GAAGA,CAACC,IAAI,EAAEC,KAAK,kBACjCxF,OAAA,CAAC1D,MAAM,CAACmJ,GAAG;IAETC,OAAO,EAAE;MAAElC,OAAO,EAAE,CAAC;MAAEmC,CAAC,EAAE,CAAC;IAAG,CAAE;IAChCC,OAAO,EAAE;MAAEpC,OAAO,EAAE,CAAC;MAAEmC,CAAC,EAAE;IAAE,CAAE;IAC9BhD,UAAU,EAAE;MAAEkD,KAAK,EAAEL,KAAK,GAAG;IAAK,CAAE;IAAAM,QAAA,eAEpC9F,OAAA,CAACoC,oBAAoB;MACnB2D,SAAS,EAAExJ,IAAK;MAChByJ,EAAE,EAAET,IAAI,CAACP,IAAK;MACd1C,MAAM,EAAE6C,QAAQ,CAACI,IAAI,CAACP,IAAI,CAAE;MAC5BiB,OAAO,EAAE3B,QAAQ,GAAGF,OAAO,GAAG8B,SAAU;MAAAJ,QAAA,gBAExC9F,OAAA,CAACrE,YAAY;QAACwK,EAAE,EAAE;UAAE9F,KAAK,EAAE,SAAS;UAAE+F,QAAQ,EAAE;QAAG,CAAE;QAAAN,QAAA,EAClDP,IAAI,CAACZ;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACG,eACf/E,OAAA,CAACpE,YAAY;QACXyK,OAAO,EAAEd,IAAI,CAACb,IAAK;QACnB4B,sBAAsB,EAAE;UACtBlD,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE8B,QAAQ,CAACI,IAAI,CAACP,IAAI,CAAC,GAAG,GAAG,GAAG;QAC1C;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACF,EACDQ,IAAI,CAACN,KAAK,iBACTjF,OAAA,CAAC7D,IAAI;QACHoK,KAAK,EAAEhB,IAAI,CAACN,KAAM;QAClBuB,IAAI,EAAC,OAAO;QACZL,EAAE,EAAE;UACFM,OAAO,EAAE,SAAS;UAClBpG,KAAK,EAAE,MAAM;UACb+C,QAAQ,EAAE,QAAQ;UAClBtC,MAAM,EAAE,EAAE;UACVsF,QAAQ,EAAE,EAAE;UACZ/C,UAAU,EAAE;QACd;MAAE;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAEL;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACoB,GAnClBQ,IAAI,CAACb,IAAI;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAqCjB;EAED,MAAM2B,aAAa,gBACjB1G,OAAA,CAAClE,GAAG;IAACqK,EAAE,EAAE;MAAErF,MAAM,EAAE,MAAM;MAAEe,OAAO,EAAE,MAAM;MAAE8E,aAAa,EAAE;IAAS,CAAE;IAAAb,QAAA,gBACpE9F,OAAA,CAACyB,aAAa;MAAAqE,QAAA,gBACZ9F,OAAA,CAAC9D,MAAM;QACLiK,EAAE,EAAE;UACFM,OAAO,EAAE,0BAA0B;UACnCvE,KAAK,EAAE,EAAE;UACTpB,MAAM,EAAE,EAAE;UACVP,SAAS,EAAE;QACb,CAAE;QAAAuF,QAAA,eAEF9F,OAAA,CAACpC,UAAU;UAACuI,EAAE,EAAE;YAAE/C,QAAQ,EAAE;UAAG;QAAE;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC7B,eACT/E,OAAA,CAAClE,GAAG;QAAAgK,QAAA,gBACF9F,OAAA,CAACjE,UAAU;UAACsI,OAAO,EAAC,IAAI;UAAChB,UAAU,EAAC,MAAM;UAAC8C,EAAE,EAAE;YAAE/C,QAAQ,EAAE;UAAS,CAAE;UAAA0C,QAAA,EAAC;QAEvE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACb/E,OAAA,CAACjE,UAAU;UAACsI,OAAO,EAAC,SAAS;UAAC8B,EAAE,EAAE;YAAE3C,OAAO,EAAE,GAAG;YAAEJ,QAAQ,EAAE;UAAS,CAAE;UAAA0C,QAAA,EAAC;QAExE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACQ,eAEhB/E,OAAA,CAAClE,GAAG;MAACqK,EAAE,EAAE;QAAES,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE,MAAM;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAhB,QAAA,gBAE7C9F,OAAA,CAAC8C,WAAW;QAAAgD,QAAA,gBACV9F,OAAA,CAACkD,YAAY;UAAA4C,QAAA,EAAC;QAAY;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAe,eACzC/E,OAAA,CAACvE,IAAI;UAACsK,SAAS,EAAC,KAAK;UAACI,EAAE,EAAE;YAAExE,OAAO,EAAE;UAAE,CAAE;UAAAmE,QAAA,EACtCrB,aAAa,CAACsC,GAAG,CAAC,CAACxB,IAAI,EAAEC,KAAK,KAAKF,cAAc,CAACC,IAAI,EAAEC,KAAK,CAAC;QAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAC3D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACK,eAEd/E,OAAA,CAACnE,OAAO;QAACsK,EAAE,EAAE;UAAEa,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAER,OAAO,EAAE;QAA2B;MAAE;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAGtE/E,OAAA,CAAC8C,WAAW;QAAAgD,QAAA,gBACV9F,OAAA,CAACkD,YAAY;UAAA4C,QAAA,EAAC;QAAU;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAe,eACvC/E,OAAA,CAACvE,IAAI;UAACsK,SAAS,EAAC,KAAK;UAACI,EAAE,EAAE;YAAExE,OAAO,EAAE;UAAE,CAAE;UAAAmE,QAAA,EACtCZ,aAAa,CAAC6B,GAAG,CAAC,CAACxB,IAAI,EAAEC,KAAK,KAAKF,cAAc,CAACC,IAAI,EAAEC,KAAK,CAAC;QAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAC3D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACK;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACV;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAET;EAED,IAAIT,QAAQ,EAAE;IACZ,oBACEtE,OAAA,CAACC,YAAY;MACXoE,OAAO,EAAC,WAAW;MACnBF,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAEA,OAAQ;MACjB8C,UAAU,EAAE;QACVC,WAAW,EAAE,IAAI,CAAE;MACrB,CAAE;;MACFhB,EAAE,EAAE;QACF,oBAAoB,EAAE;UACpBjE,KAAK,EAAE,GAAG;UACVxB,GAAG,EAAE;YAAE0G,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAO,CAAC;UAC3CxG,MAAM,EAAE;YAAEsG,EAAE,EAAE,oBAAoB;YAAEC,EAAE,EAAE,oBAAoB;YAAEC,EAAE,EAAE;UAAqB;QACzF;MACF,CAAE;MAAAxB,QAAA,EAEDY;IAAa;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD;EAEnB;EAEA,oBACE/E,OAAA,CAACC,YAAY;IACXoE,OAAO,EAAEA,OAAQ;IACjBF,IAAI,EAAEA,IAAK;IACXgC,EAAE,EAAE;MACF,oBAAoB,EAAE;QACpBjE,KAAK,EAAEiC,IAAI,GAAG,GAAG,GAAG,EAAE;QACtBxB,UAAU,EAAExC,KAAK,CAACoH,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;UAC5CC,MAAM,EAAEtH,KAAK,CAACoH,WAAW,CAACE,MAAM,CAACC,KAAK;UACtCC,QAAQ,EAAExH,KAAK,CAACoH,WAAW,CAACI,QAAQ,CAACC;QACvC,CAAC,CAAC;QACFC,SAAS,EAAE,QAAQ;QACnBnH,GAAG,EAAE;UAAE0G,EAAE,EAAE,MAAM;UAAEC,EAAE,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAO,CAAC;QAC3CxG,MAAM,EAAE;UAAEsG,EAAE,EAAE,oBAAoB;UAAEC,EAAE,EAAE,oBAAoB;UAAEC,EAAE,EAAE;QAAqB;MACzF;IACF,CAAE;IAAAxB,QAAA,EAEDY;EAAa;IAAA9B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACD;AAEnB,CAAC;AAACb,EAAA,CA/OIF,iBAAiB;EAAA,QACPhI,QAAQ,EACLC,aAAa,EACbO,WAAW;AAAA;AAAAsL,GAAA,GAHxB9D,iBAAiB;AAiPvB,eAAeA,iBAAiB;AAAC,IAAAxC,EAAA,EAAAW,GAAA,EAAAU,GAAA,EAAAI,GAAA,EAAAc,GAAA,EAAA+D,GAAA;AAAAC,YAAA,CAAAvG,EAAA;AAAAuG,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}