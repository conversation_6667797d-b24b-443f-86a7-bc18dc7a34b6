{"ast": null, "code": "import { isHTMLElement } from '../../utils/is-html-element.mjs';\nimport { isDragActive } from '../drag/state/is-active.mjs';\nimport { isNodeOrChild } from '../utils/is-node-or-child.mjs';\nimport { isPrimaryPointer } from '../utils/is-primary-pointer.mjs';\nimport { setupGesture } from '../utils/setup.mjs';\nimport { isElementKeyboardAccessible } from './utils/is-keyboard-accessible.mjs';\nimport { enableKeyboardPress } from './utils/keyboard.mjs';\nimport { isPressing } from './utils/state.mjs';\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n  return isPrimaryPointer(event) && !isDragActive();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const [targets, eventOptions, cancelEvents] = setupGesture(targetOrSelector, options);\n  const startPress = startEvent => {\n    const target = startEvent.currentTarget;\n    if (!isValidPressEvent(startEvent)) return;\n    isPressing.add(target);\n    const onPressEnd = onPressStart(target, startEvent);\n    const onPointerEnd = (endEvent, success) => {\n      window.removeEventListener(\"pointerup\", onPointerUp);\n      window.removeEventListener(\"pointercancel\", onPointerCancel);\n      if (isPressing.has(target)) {\n        isPressing.delete(target);\n      }\n      if (!isValidPressEvent(endEvent)) {\n        return;\n      }\n      if (typeof onPressEnd === \"function\") {\n        onPressEnd(endEvent, {\n          success\n        });\n      }\n    };\n    const onPointerUp = upEvent => {\n      onPointerEnd(upEvent, target === window || target === document || options.useGlobalTarget || isNodeOrChild(target, upEvent.target));\n    };\n    const onPointerCancel = cancelEvent => {\n      onPointerEnd(cancelEvent, false);\n    };\n    window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n    window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n  };\n  targets.forEach(target => {\n    const pointerDownTarget = options.useGlobalTarget ? window : target;\n    pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n    if (isHTMLElement(target)) {\n      target.addEventListener(\"focus\", event => enableKeyboardPress(event, eventOptions));\n      if (!isElementKeyboardAccessible(target) && !target.hasAttribute(\"tabindex\")) {\n        target.tabIndex = 0;\n      }\n    }\n  });\n  return cancelEvents;\n}\nexport { press };", "map": {"version": 3, "names": ["isHTMLElement", "isDragActive", "isNodeOrChild", "isPrimaryPointer", "setupGesture", "isElementKeyboardAccessible", "enableKeyboardPress", "isPressing", "isValidPressEvent", "event", "press", "targetOrSelector", "onPressStart", "options", "arguments", "length", "undefined", "targets", "eventOptions", "cancelEvents", "startPress", "startEvent", "target", "currentTarget", "add", "onPressEnd", "onPointerEnd", "endEvent", "success", "window", "removeEventListener", "onPointerUp", "onPointerCancel", "has", "delete", "upEvent", "document", "useGlobalTarget", "cancelEvent", "addEventListener", "for<PERSON>ach", "pointerDownTarget", "hasAttribute", "tabIndex"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/gestures/press/index.mjs"], "sourcesContent": ["import { isHTMLElement } from '../../utils/is-html-element.mjs';\nimport { isDragActive } from '../drag/state/is-active.mjs';\nimport { isNodeOrChild } from '../utils/is-node-or-child.mjs';\nimport { isPrimaryPointer } from '../utils/is-primary-pointer.mjs';\nimport { setupGesture } from '../utils/setup.mjs';\nimport { isElementKeyboardAccessible } from './utils/is-keyboard-accessible.mjs';\nimport { enableKeyboardPress } from './utils/keyboard.mjs';\nimport { isPressing } from './utils/state.mjs';\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n    return isPrimaryPointer(event) && !isDragActive();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart, options = {}) {\n    const [targets, eventOptions, cancelEvents] = setupGesture(targetOrSelector, options);\n    const startPress = (startEvent) => {\n        const target = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent))\n            return;\n        isPressing.add(target);\n        const onPressEnd = onPressStart(target, startEvent);\n        const onPointerEnd = (endEvent, success) => {\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (isPressing.has(target)) {\n                isPressing.delete(target);\n            }\n            if (!isValidPressEvent(endEvent)) {\n                return;\n            }\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, { success });\n            }\n        };\n        const onPointerUp = (upEvent) => {\n            onPointerEnd(upEvent, target === window ||\n                target === document ||\n                options.useGlobalTarget ||\n                isNodeOrChild(target, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent) => {\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    targets.forEach((target) => {\n        const pointerDownTarget = options.useGlobalTarget ? window : target;\n        pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n        if (isHTMLElement(target)) {\n            target.addEventListener(\"focus\", (event) => enableKeyboardPress(event, eventOptions));\n            if (!isElementKeyboardAccessible(target) &&\n                !target.hasAttribute(\"tabindex\")) {\n                target.tabIndex = 0;\n            }\n        }\n    });\n    return cancelEvents;\n}\n\nexport { press };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,2BAA2B,QAAQ,oCAAoC;AAChF,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,UAAU,QAAQ,mBAAmB;;AAE9C;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAC9B,OAAON,gBAAgB,CAACM,KAAK,CAAC,IAAI,CAACR,YAAY,EAAE;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,KAAKA,CAACC,gBAAgB,EAAEC,YAAY,EAAgB;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACvD,MAAM,CAACG,OAAO,EAAEC,YAAY,EAAEC,YAAY,CAAC,GAAGf,YAAY,CAACO,gBAAgB,EAAEE,OAAO,CAAC;EACrF,MAAMO,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,MAAM,GAAGD,UAAU,CAACE,aAAa;IACvC,IAAI,CAACf,iBAAiB,CAACa,UAAU,CAAC,EAC9B;IACJd,UAAU,CAACiB,GAAG,CAACF,MAAM,CAAC;IACtB,MAAMG,UAAU,GAAGb,YAAY,CAACU,MAAM,EAAED,UAAU,CAAC;IACnD,MAAMK,YAAY,GAAGA,CAACC,QAAQ,EAAEC,OAAO,KAAK;MACxCC,MAAM,CAACC,mBAAmB,CAAC,WAAW,EAAEC,WAAW,CAAC;MACpDF,MAAM,CAACC,mBAAmB,CAAC,eAAe,EAAEE,eAAe,CAAC;MAC5D,IAAIzB,UAAU,CAAC0B,GAAG,CAACX,MAAM,CAAC,EAAE;QACxBf,UAAU,CAAC2B,MAAM,CAACZ,MAAM,CAAC;MAC7B;MACA,IAAI,CAACd,iBAAiB,CAACmB,QAAQ,CAAC,EAAE;QAC9B;MACJ;MACA,IAAI,OAAOF,UAAU,KAAK,UAAU,EAAE;QAClCA,UAAU,CAACE,QAAQ,EAAE;UAAEC;QAAQ,CAAC,CAAC;MACrC;IACJ,CAAC;IACD,MAAMG,WAAW,GAAII,OAAO,IAAK;MAC7BT,YAAY,CAACS,OAAO,EAAEb,MAAM,KAAKO,MAAM,IACnCP,MAAM,KAAKc,QAAQ,IACnBvB,OAAO,CAACwB,eAAe,IACvBnC,aAAa,CAACoB,MAAM,EAAEa,OAAO,CAACb,MAAM,CAAC,CAAC;IAC9C,CAAC;IACD,MAAMU,eAAe,GAAIM,WAAW,IAAK;MACrCZ,YAAY,CAACY,WAAW,EAAE,KAAK,CAAC;IACpC,CAAC;IACDT,MAAM,CAACU,gBAAgB,CAAC,WAAW,EAAER,WAAW,EAAEb,YAAY,CAAC;IAC/DW,MAAM,CAACU,gBAAgB,CAAC,eAAe,EAAEP,eAAe,EAAEd,YAAY,CAAC;EAC3E,CAAC;EACDD,OAAO,CAACuB,OAAO,CAAElB,MAAM,IAAK;IACxB,MAAMmB,iBAAiB,GAAG5B,OAAO,CAACwB,eAAe,GAAGR,MAAM,GAAGP,MAAM;IACnEmB,iBAAiB,CAACF,gBAAgB,CAAC,aAAa,EAAEnB,UAAU,EAAEF,YAAY,CAAC;IAC3E,IAAIlB,aAAa,CAACsB,MAAM,CAAC,EAAE;MACvBA,MAAM,CAACiB,gBAAgB,CAAC,OAAO,EAAG9B,KAAK,IAAKH,mBAAmB,CAACG,KAAK,EAAES,YAAY,CAAC,CAAC;MACrF,IAAI,CAACb,2BAA2B,CAACiB,MAAM,CAAC,IACpC,CAACA,MAAM,CAACoB,YAAY,CAAC,UAAU,CAAC,EAAE;QAClCpB,MAAM,CAACqB,QAAQ,GAAG,CAAC;MACvB;IACJ;EACJ,CAAC,CAAC;EACF,OAAOxB,YAAY;AACvB;AAEA,SAAST,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}