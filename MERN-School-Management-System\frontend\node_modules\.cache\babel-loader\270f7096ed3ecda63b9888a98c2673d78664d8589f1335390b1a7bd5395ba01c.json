{"ast": null, "code": "import { resize, frame, cancelFrame, frameData } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = element => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll) {\n  let {\n    container = document.scrollingElement,\n    ...options\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (!container) return noop;\n  let containerHandlers = onScrollHandlers.get(container);\n  /**\n   * Get the onScroll handlers for this container.\n   * If one isn't found, create a new one.\n   */\n  if (!containerHandlers) {\n    containerHandlers = new Set();\n    onScrollHandlers.set(container, containerHandlers);\n  }\n  /**\n   * Create a new onScroll handler for the provided callback.\n   */\n  const info = createScrollInfo();\n  const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n  containerHandlers.add(containerHandler);\n  /**\n   * Check if there's a scroll event listener for this container.\n   * If not, create one.\n   */\n  if (!scrollListeners.has(container)) {\n    const measureAll = () => {\n      for (const handler of containerHandlers) {\n        handler.measure(frameData.timestamp);\n      }\n      frame.preUpdate(notifyAll);\n    };\n    const notifyAll = () => {\n      for (const handler of containerHandlers) {\n        handler.notify();\n      }\n    };\n    const listener = () => frame.read(measureAll);\n    scrollListeners.set(container, listener);\n    const target = getEventTarget(container);\n    window.addEventListener(\"resize\", listener, {\n      passive: true\n    });\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, resize(container, listener));\n    }\n    target.addEventListener(\"scroll\", listener, {\n      passive: true\n    });\n    listener();\n  }\n  const listener = scrollListeners.get(container);\n  frame.read(listener, false, true);\n  return () => {\n    cancelFrame(listener);\n    /**\n     * Check if we even have any handlers for this container.\n     */\n    const currentHandlers = onScrollHandlers.get(container);\n    if (!currentHandlers) return;\n    currentHandlers.delete(containerHandler);\n    if (currentHandlers.size) return;\n    /**\n     * If no more handlers, remove the scroll listener too.\n     */\n    const scrollListener = scrollListeners.get(container);\n    scrollListeners.delete(container);\n    if (scrollListener) {\n      getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n      resizeListeners.get(container)?.();\n      window.removeEventListener(\"resize\", scrollListener);\n    }\n  };\n}\nexport { scrollInfo };", "map": {"version": 3, "names": ["resize", "frame", "cancelFrame", "frameData", "noop", "createScrollInfo", "createOnScrollHandler", "scrollListeners", "WeakMap", "resizeListeners", "onScrollHandlers", "getEventTarget", "element", "document", "scrollingElement", "window", "scrollInfo", "onScroll", "container", "options", "arguments", "length", "undefined", "containerHandlers", "get", "Set", "set", "info", "containerHandler", "add", "has", "measureAll", "handler", "measure", "timestamp", "preUpdate", "notifyAll", "notify", "listener", "read", "target", "addEventListener", "passive", "documentElement", "currentHandlers", "delete", "size", "scrollListener", "removeEventListener"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs"], "sourcesContent": ["import { resize, frame, cancelFrame, frameData } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return noop;\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = createScrollInfo();\n    const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers) {\n                handler.measure(frameData.timestamp);\n            }\n            frame.preUpdate(notifyAll);\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers) {\n                handler.notify();\n            }\n        };\n        const listener = () => frame.read(measureAll);\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, resize(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n        listener();\n    }\n    const listener = scrollListeners.get(container);\n    frame.read(listener, false, true);\n    return () => {\n        cancelFrame(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            resizeListeners.get(container)?.();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\nexport { scrollInfo };\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,YAAY;AAClE,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,qBAAqB,QAAQ,yBAAyB;AAE/D,MAAMC,eAAe,GAAG,IAAIC,OAAO,EAAE;AACrC,MAAMC,eAAe,GAAG,IAAID,OAAO,EAAE;AACrC,MAAME,gBAAgB,GAAG,IAAIF,OAAO,EAAE;AACtC,MAAMG,cAAc,GAAIC,OAAO,IAAKA,OAAO,KAAKC,QAAQ,CAACC,gBAAgB,GAAGC,MAAM,GAAGH,OAAO;AAC5F,SAASI,UAAUA,CAACC,QAAQ,EAA8D;EAAA,IAA5D;IAAEC,SAAS,GAAGL,QAAQ,CAACC,gBAAgB;IAAE,GAAGK;EAAQ,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACpF,IAAI,CAACF,SAAS,EACV,OAAOd,IAAI;EACf,IAAImB,iBAAiB,GAAGb,gBAAgB,CAACc,GAAG,CAACN,SAAS,CAAC;EACvD;AACJ;AACA;AACA;EACI,IAAI,CAACK,iBAAiB,EAAE;IACpBA,iBAAiB,GAAG,IAAIE,GAAG,EAAE;IAC7Bf,gBAAgB,CAACgB,GAAG,CAACR,SAAS,EAAEK,iBAAiB,CAAC;EACtD;EACA;AACJ;AACA;EACI,MAAMI,IAAI,GAAGtB,gBAAgB,EAAE;EAC/B,MAAMuB,gBAAgB,GAAGtB,qBAAqB,CAACY,SAAS,EAAED,QAAQ,EAAEU,IAAI,EAAER,OAAO,CAAC;EAClFI,iBAAiB,CAACM,GAAG,CAACD,gBAAgB,CAAC;EACvC;AACJ;AACA;AACA;EACI,IAAI,CAACrB,eAAe,CAACuB,GAAG,CAACZ,SAAS,CAAC,EAAE;IACjC,MAAMa,UAAU,GAAGA,CAAA,KAAM;MACrB,KAAK,MAAMC,OAAO,IAAIT,iBAAiB,EAAE;QACrCS,OAAO,CAACC,OAAO,CAAC9B,SAAS,CAAC+B,SAAS,CAAC;MACxC;MACAjC,KAAK,CAACkC,SAAS,CAACC,SAAS,CAAC;IAC9B,CAAC;IACD,MAAMA,SAAS,GAAGA,CAAA,KAAM;MACpB,KAAK,MAAMJ,OAAO,IAAIT,iBAAiB,EAAE;QACrCS,OAAO,CAACK,MAAM,EAAE;MACpB;IACJ,CAAC;IACD,MAAMC,QAAQ,GAAGA,CAAA,KAAMrC,KAAK,CAACsC,IAAI,CAACR,UAAU,CAAC;IAC7CxB,eAAe,CAACmB,GAAG,CAACR,SAAS,EAAEoB,QAAQ,CAAC;IACxC,MAAME,MAAM,GAAG7B,cAAc,CAACO,SAAS,CAAC;IACxCH,MAAM,CAAC0B,gBAAgB,CAAC,QAAQ,EAAEH,QAAQ,EAAE;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;IAC9D,IAAIxB,SAAS,KAAKL,QAAQ,CAAC8B,eAAe,EAAE;MACxClC,eAAe,CAACiB,GAAG,CAACR,SAAS,EAAElB,MAAM,CAACkB,SAAS,EAAEoB,QAAQ,CAAC,CAAC;IAC/D;IACAE,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,QAAQ,EAAE;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;IAC9DJ,QAAQ,EAAE;EACd;EACA,MAAMA,QAAQ,GAAG/B,eAAe,CAACiB,GAAG,CAACN,SAAS,CAAC;EAC/CjB,KAAK,CAACsC,IAAI,CAACD,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC;EACjC,OAAO,MAAM;IACTpC,WAAW,CAACoC,QAAQ,CAAC;IACrB;AACR;AACA;IACQ,MAAMM,eAAe,GAAGlC,gBAAgB,CAACc,GAAG,CAACN,SAAS,CAAC;IACvD,IAAI,CAAC0B,eAAe,EAChB;IACJA,eAAe,CAACC,MAAM,CAACjB,gBAAgB,CAAC;IACxC,IAAIgB,eAAe,CAACE,IAAI,EACpB;IACJ;AACR;AACA;IACQ,MAAMC,cAAc,GAAGxC,eAAe,CAACiB,GAAG,CAACN,SAAS,CAAC;IACrDX,eAAe,CAACsC,MAAM,CAAC3B,SAAS,CAAC;IACjC,IAAI6B,cAAc,EAAE;MAChBpC,cAAc,CAACO,SAAS,CAAC,CAAC8B,mBAAmB,CAAC,QAAQ,EAAED,cAAc,CAAC;MACvEtC,eAAe,CAACe,GAAG,CAACN,SAAS,CAAC,IAAI;MAClCH,MAAM,CAACiC,mBAAmB,CAAC,QAAQ,EAAED,cAAc,CAAC;IACxD;EACJ,CAAC;AACL;AAEA,SAAS/B,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}