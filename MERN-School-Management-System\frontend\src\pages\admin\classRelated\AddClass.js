import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  CircularProgress,
  Stack,
  TextField,
  Typography,
  Paper,
  Container,
  Card,
  CardContent,
  Grid,
  Chip,
  Avatar
} from "@mui/material";
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { addStuff } from '../../../redux/userRelated/userHandle';
import { underControl } from '../../../redux/userRelated/userSlice';
import Popup from "../../../components/Popup";
import Classroom from "../../../assets/classroom.png";
import styled from "styled-components";
import ClassIcon from '@mui/icons-material/Class';
import SchoolIcon from '@mui/icons-material/School';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

const AddClass = () => {
    const [sclassName, setSclassName] = useState("");

    const dispatch = useDispatch()
    const navigate = useNavigate()

    const userState = useSelector(state => state.user);
    const { status, currentUser, response, error, tempDetails } = userState;

    const adminID = currentUser._id
    const address = "Sclass"

    const [loader, setLoader] = useState(false)
    const [message, setMessage] = useState("");
    const [showPopup, setShowPopup] = useState(false);

    const fields = {
        sclassName,
        adminID,
    };

    const submitHandler = (event) => {
        event.preventDefault()
        setLoader(true)
        dispatch(addStuff(fields, address))
    };

    useEffect(() => {
        if (status === 'added' && tempDetails) {
            navigate("/Admin/classes/class/" + tempDetails._id)
            dispatch(underControl())
            setLoader(false)
        }
        else if (status === 'failed') {
            setMessage(response)
            setShowPopup(true)
            setLoader(false)
        }
        else if (status === 'error') {
            setMessage("Network Error")
            setShowPopup(true)
            setLoader(false)
        }
    }, [status, navigate, error, response, dispatch, tempDetails]);
    return (
        <Container maxWidth="lg" sx={{ py: 4 }}>
            {/* Header */}
            <Paper elevation={3} sx={{ p: 3, mb: 4, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Button
                        startIcon={<ArrowBackIcon />}
                        onClick={() => navigate(-1)}
                        sx={{ color: 'white', '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' } }}
                    >
                        Back to Classes
                    </Button>
                    <Box sx={{ flexGrow: 1, textAlign: 'center' }}>
                        <Typography variant="h4" component="h1" fontWeight="bold">
                            Create New Class
                        </Typography>
                        <Typography variant="h6" sx={{ opacity: 0.9 }}>
                            Add a new class to organize students and subjects
                        </Typography>
                    </Box>
                </Box>
            </Paper>

            <Grid container spacing={4}>
                {/* Form Section */}
                <Grid item xs={12} md={6}>
                    <Card elevation={3} sx={{ height: '100%' }}>
                        <CardContent sx={{ p: 4 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                <Avatar sx={{ bgcolor: '#667eea', mr: 2, width: 56, height: 56 }}>
                                    <ClassIcon sx={{ fontSize: 30 }} />
                                </Avatar>
                                <Box>
                                    <Typography variant="h5" fontWeight="bold">
                                        Class Information
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Enter the details for the new class
                                    </Typography>
                                </Box>
                            </Box>

                            <form onSubmit={submitHandler}>
                                <Stack spacing={3}>
                                    <TextField
                                        label="Class Name"
                                        placeholder="e.g., Class 1, Grade 5, 10th Standard"
                                        variant="outlined"
                                        value={sclassName}
                                        onChange={(event) => {
                                            setSclassName(event.target.value);
                                        }}
                                        required
                                        fullWidth
                                        sx={{
                                            '& .MuiOutlinedInput-root': {
                                                '&:hover fieldset': {
                                                    borderColor: '#667eea',
                                                },
                                                '&.Mui-focused fieldset': {
                                                    borderColor: '#667eea',
                                                },
                                            },
                                        }}
                                    />

                                    <Box sx={{ display: 'flex', gap: 2 }}>
                                        <Button
                                            fullWidth
                                            size="large"
                                            variant="contained"
                                            type="submit"
                                            disabled={loader}
                                            sx={{
                                                bgcolor: '#667eea',
                                                '&:hover': { bgcolor: '#5a67d8' },
                                                py: 1.5
                                            }}
                                        >
                                            {loader ? <CircularProgress size={24} color="inherit" /> : "Create Class"}
                                        </Button>
                                        <Button
                                            variant="outlined"
                                            onClick={() => navigate(-1)}
                                            size="large"
                                            sx={{ py: 1.5 }}
                                        >
                                            Cancel
                                        </Button>
                                    </Box>
                                </Stack>
                            </form>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Info Section */}
                <Grid item xs={12} md={6}>
                    <Stack spacing={3}>
                        {/* Illustration */}
                        <Card elevation={2}>
                            <CardContent sx={{ textAlign: 'center', p: 4 }}>
                                <img
                                    src={Classroom}
                                    alt="classroom"
                                    style={{ width: '100%', maxWidth: '300px', height: 'auto' }}
                                />
                            </CardContent>
                        </Card>

                        {/* Tips */}
                        <Card elevation={2}>
                            <CardContent>
                                <Typography variant="h6" fontWeight="bold" gutterBottom>
                                    💡 Tips for Creating Classes
                                </Typography>
                                <Stack spacing={2}>
                                    <Box>
                                        <Typography variant="body2" fontWeight="bold">
                                            • Use clear naming conventions
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Examples: "Class 1", "Grade 5", "10th Standard"
                                        </Typography>
                                    </Box>
                                    <Box>
                                        <Typography variant="body2" fontWeight="bold">
                                            • Consider academic levels
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Group students by age and academic progress
                                        </Typography>
                                    </Box>
                                    <Box>
                                        <Typography variant="body2" fontWeight="bold">
                                            • Plan for growth
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Create classes that can accommodate future students
                                        </Typography>
                                    </Box>
                                </Stack>
                            </CardContent>
                        </Card>

                        {/* Quick Stats */}
                        <Card elevation={2}>
                            <CardContent>
                                <Typography variant="h6" fontWeight="bold" gutterBottom>
                                    📊 After Creating This Class
                                </Typography>
                                <Stack direction="row" spacing={2}>
                                    <Chip
                                        icon={<SchoolIcon />}
                                        label="Add Students"
                                        variant="outlined"
                                        color="primary"
                                    />
                                    <Chip
                                        icon={<ClassIcon />}
                                        label="Add Subjects"
                                        variant="outlined"
                                        color="secondary"
                                    />
                                </Stack>
                                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                                    You'll be able to add students and subjects to this class immediately after creation.
                                </Typography>
                            </CardContent>
                        </Card>
                    </Stack>
                </Grid>
            </Grid>

            <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />
        </Container>
    )
}

export default AddClass