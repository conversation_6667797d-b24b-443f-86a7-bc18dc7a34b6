import React, { useState, useEffect } from 'react';
import {
    Box,
    Paper,
    Typography,
    Grid,
    Card,
    CardContent,
    Button,
    IconButton,
    Chip,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Avatar,
    LinearProgress,
    Tooltip,
    Fab,
} from '@mui/material';
import {
    CheckCircle as PresentIcon,
    Cancel as AbsentIcon,
    Schedule as LateIcon,
    Add as AddIcon,
    Edit as EditIcon,
    Visibility as ViewIcon,
    CalendarToday as CalendarIcon,
    TrendingUp as TrendingUpIcon,
    Group as GroupIcon,
    Person as PersonIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

const AttendanceManagement = () => {
    const [selectedClass, setSelectedClass] = useState('');
    const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
    const [attendanceData, setAttendanceData] = useState([]);
    const [openDialog, setOpenDialog] = useState(false);
    const [loading, setLoading] = useState(false);

    // Sample data - replace with actual API calls
    const classes = [
        { id: 1, name: 'Class 1', students: 25 },
        { id: 2, name: 'Class 2', students: 28 },
        { id: 3, name: 'Class 3', students: 30 },
        { id: 4, name: 'Class 4', students: 27 },
        { id: 5, name: 'Class 5', students: 32 },
    ];

    const sampleAttendance = [
        { id: 1, name: 'John Doe', rollNo: '001', status: 'present', time: '09:00 AM' },
        { id: 2, name: 'Jane Smith', rollNo: '002', status: 'absent', time: '-' },
        { id: 3, name: 'Mike Johnson', rollNo: '003', status: 'late', time: '09:15 AM' },
        { id: 4, name: 'Sarah Wilson', rollNo: '004', status: 'present', time: '08:55 AM' },
        { id: 5, name: 'David Brown', rollNo: '005', status: 'present', time: '09:02 AM' },
    ];

    const attendanceStats = {
        totalStudents: 150,
        presentToday: 142,
        absentToday: 8,
        attendanceRate: 94.7
    };

    useEffect(() => {
        if (selectedClass) {
            setLoading(true);
            // Simulate API call
            setTimeout(() => {
                setAttendanceData(sampleAttendance);
                setLoading(false);
            }, 1000);
        }
    }, [selectedClass, selectedDate]);

    const getStatusIcon = (status) => {
        switch (status) {
            case 'present':
                return <PresentIcon sx={{ color: '#4caf50' }} />;
            case 'absent':
                return <AbsentIcon sx={{ color: '#f44336' }} />;
            case 'late':
                return <LateIcon sx={{ color: '#ff9800' }} />;
            default:
                return null;
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'present':
                return '#4caf50';
            case 'absent':
                return '#f44336';
            case 'late':
                return '#ff9800';
            default:
                return '#757575';
        }
    };

    const handleMarkAttendance = () => {
        setOpenDialog(true);
    };

    return (
        <Box sx={{ p: 3 }}>
            {/* Header */}
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
            >
                <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                    📊 Attendance Management
                </Typography>
                <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                    Track and manage student attendance efficiently
                </Typography>
            </motion.div>

            {/* Stats Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.1 }}
                    >
                        <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography variant="h4" fontWeight="bold">
                                            {attendanceStats.totalStudents}
                                        </Typography>
                                        <Typography variant="body2">Total Students</Typography>
                                    </Box>
                                    <GroupIcon sx={{ fontSize: 40, opacity: 0.8 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.2 }}
                    >
                        <Card sx={{ background: 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)', color: 'white' }}>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography variant="h4" fontWeight="bold">
                                            {attendanceStats.presentToday}
                                        </Typography>
                                        <Typography variant="body2">Present Today</Typography>
                                    </Box>
                                    <PresentIcon sx={{ fontSize: 40, opacity: 0.8 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.3 }}
                    >
                        <Card sx={{ background: 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)', color: 'white' }}>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography variant="h4" fontWeight="bold">
                                            {attendanceStats.absentToday}
                                        </Typography>
                                        <Typography variant="body2">Absent Today</Typography>
                                    </Box>
                                    <AbsentIcon sx={{ fontSize: 40, opacity: 0.8 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.4 }}
                    >
                        <Card sx={{ background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)', color: 'white' }}>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Box>
                                        <Typography variant="h4" fontWeight="bold">
                                            {attendanceStats.attendanceRate}%
                                        </Typography>
                                        <Typography variant="body2">Attendance Rate</Typography>
                                    </Box>
                                    <TrendingUpIcon sx={{ fontSize: 40, opacity: 0.8 }} />
                                </Box>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>
            </Grid>

            {/* Filters */}
            <Paper sx={{ p: 3, mb: 3 }}>
                <Grid container spacing={3} alignItems="center">
                    <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                            <InputLabel>Select Class</InputLabel>
                            <Select
                                value={selectedClass}
                                onChange={(e) => setSelectedClass(e.target.value)}
                                label="Select Class"
                            >
                                {classes.map((cls) => (
                                    <MenuItem key={cls.id} value={cls.id}>
                                        {cls.name} ({cls.students} students)
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <TextField
                            fullWidth
                            type="date"
                            label="Date"
                            value={selectedDate}
                            onChange={(e) => setSelectedDate(e.target.value)}
                            InputLabelProps={{ shrink: true }}
                        />
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <Button
                            variant="contained"
                            startIcon={<AddIcon />}
                            onClick={handleMarkAttendance}
                            disabled={!selectedClass}
                            sx={{ height: 56 }}
                        >
                            Mark Attendance
                        </Button>
                    </Grid>
                </Grid>
            </Paper>

            {/* Attendance Table */}
            {selectedClass && (
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                >
                    <Paper sx={{ p: 3 }}>
                        <Typography variant="h6" gutterBottom>
                            Attendance for {classes.find(c => c.id === selectedClass)?.name} - {selectedDate}
                        </Typography>
                        
                        {loading ? (
                            <LinearProgress sx={{ my: 2 }} />
                        ) : (
                            <TableContainer>
                                <Table>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell>Roll No</TableCell>
                                            <TableCell>Student Name</TableCell>
                                            <TableCell>Status</TableCell>
                                            <TableCell>Time</TableCell>
                                            <TableCell>Actions</TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {attendanceData.map((student) => (
                                            <TableRow key={student.id}>
                                                <TableCell>{student.rollNo}</TableCell>
                                                <TableCell>
                                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                                        <Avatar sx={{ width: 32, height: 32 }}>
                                                            {student.name.charAt(0)}
                                                        </Avatar>
                                                        {student.name}
                                                    </Box>
                                                </TableCell>
                                                <TableCell>
                                                    <Chip
                                                        icon={getStatusIcon(student.status)}
                                                        label={student.status.toUpperCase()}
                                                        sx={{
                                                            backgroundColor: getStatusColor(student.status),
                                                            color: 'white',
                                                            fontWeight: 'bold'
                                                        }}
                                                    />
                                                </TableCell>
                                                <TableCell>{student.time}</TableCell>
                                                <TableCell>
                                                    <Tooltip title="Edit">
                                                        <IconButton size="small">
                                                            <EditIcon />
                                                        </IconButton>
                                                    </Tooltip>
                                                    <Tooltip title="View Details">
                                                        <IconButton size="small">
                                                            <ViewIcon />
                                                        </IconButton>
                                                    </Tooltip>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        )}
                    </Paper>
                </motion.div>
            )}

            {/* Floating Action Button */}
            <Fab
                color="primary"
                aria-label="add"
                sx={{ position: 'fixed', bottom: 16, right: 16 }}
                onClick={handleMarkAttendance}
            >
                <CalendarIcon />
            </Fab>

            {/* Mark Attendance Dialog */}
            <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
                <DialogTitle>Mark Attendance</DialogTitle>
                <DialogContent>
                    <Typography>Attendance marking interface will be implemented here.</Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
                    <Button variant="contained">Save Attendance</Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default AttendanceManagement;
