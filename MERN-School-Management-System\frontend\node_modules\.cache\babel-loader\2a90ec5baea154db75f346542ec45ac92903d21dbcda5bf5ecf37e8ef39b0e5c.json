{"ast": null, "code": "const animationMaps = new WeakMap();\nconst animationMapKey = function (name) {\n  let pseudoElement = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n  return `${name}:${pseudoElement}`;\n};\nfunction getAnimationMap(element) {\n  const map = animationMaps.get(element) || new Map();\n  animationMaps.set(element, map);\n  return map;\n}\nexport { animationMapKey, getAnimationMap };", "map": {"version": 3, "names": ["animationMaps", "WeakMap", "animationMapKey", "name", "pseudoElement", "arguments", "length", "undefined", "getAnimationMap", "element", "map", "get", "Map", "set"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/animation/utils/active-animations.mjs"], "sourcesContent": ["const animationMaps = new WeakMap();\nconst animationMapKey = (name, pseudoElement = \"\") => `${name}:${pseudoElement}`;\nfunction getAnimationMap(element) {\n    const map = animationMaps.get(element) || new Map();\n    animationMaps.set(element, map);\n    return map;\n}\n\nexport { animationMapKey, getAnimationMap };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAG,IAAIC,OAAO,EAAE;AACnC,MAAMC,eAAe,GAAG,SAAAA,CAACC,IAAI;EAAA,IAAEC,aAAa,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,OAAM,GAAEF,IAAK,IAAGC,aAAc,EAAC;AAAA;AAChF,SAASI,eAAeA,CAACC,OAAO,EAAE;EAC9B,MAAMC,GAAG,GAAGV,aAAa,CAACW,GAAG,CAACF,OAAO,CAAC,IAAI,IAAIG,GAAG,EAAE;EACnDZ,aAAa,CAACa,GAAG,CAACJ,OAAO,EAAEC,GAAG,CAAC;EAC/B,OAAOA,GAAG;AACd;AAEA,SAASR,eAAe,EAAEM,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}