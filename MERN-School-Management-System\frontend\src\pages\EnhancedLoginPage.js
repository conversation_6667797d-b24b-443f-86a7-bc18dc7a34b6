import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Container,
  Grid,
  Typography,
  TextField,
  Button,
  Paper,
  Avatar,
  Rating,
  Card,
  CardContent,
  CardMedia,
  IconButton,
  AppBar,
  Toolbar,
  Fab,
  Checkbox,
  FormControlLabel,
  InputAdornment,
  CircularProgress,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Facebook,
  Instagram,
  YouTube,
  LinkedIn,
  Phone,
  Email,
  LocationOn,
  ArrowUpward,
  AccountCircle,
  School,
  Group
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { loginUser } from '../redux/userRelated/userHandle';
import Popup from '../components/Popup';

const EnhancedLoginPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { status, currentUser, response, error, currentRole } = useSelector(state => state.user);

  // Login form states
  const [selectedRole, setSelectedRole] = useState('Admin');
  const [showPassword, setShowPassword] = useState(false);
  const [loader, setLoader] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [message, setMessage] = useState("");
  const [emailError, setEmailError] = useState(false);
  const [passwordError, setPasswordError] = useState(false);
  const [rollNumberError, setRollNumberError] = useState(false);
  const [studentNameError, setStudentNameError] = useState(false);

  // CMS Content states
  const [cmsContent, setCmsContent] = useState({
    heroTitle: 'Empowering Minds, Shaping Future Leaders',
    heroSubtitle: 'Discover a dynamic educational experience with interactive visuals, engaging content, and personalized learning paths designed to inspire and empower students.',
    schoolName: 'Amar Vidya Mandir',
    contactInfo: {
      address: 'Partaj, Anantapuram, AP, India',
      phone: '7799505005',
      alternatePhone: '9866358067',
      email: '<EMAIL>'
    },
    testimonials: [
      {
        name: 'Aisha Khan',
        role: 'Student',
        message: "Amar Vidya Mandir's interactive platform transformed my learning experience. The engaging visuals and personalized content made complex topics easy to understand. I highly recommend it to any student seeking academic success.",
        rating: 5,
        image: '/api/placeholder/60/60'
      }
    ],
    gallery: [
      { title: 'The Future of Education', image: '/api/placeholder/300/200', category: 'education' },
      { title: 'Tips for Parents', image: '/api/placeholder/300/200', category: 'tips' },
      { title: 'Effective Study Strategies', image: '/api/placeholder/300/200', category: 'education' }
    ],
    blogPosts: [
      {
        title: 'The Future of Education',
        excerpt: 'Exploring innovative teaching methods...',
        date: '25 October 2024',
        author: 'admin',
        image: '/api/placeholder/300/200'
      },
      {
        title: 'Tips for Parents',
        excerpt: 'How to support your child\'s learning...',
        date: '25 October 2024',
        author: 'admin',
        image: '/api/placeholder/300/200'
      },
      {
        title: 'Effective Study Strategies',
        excerpt: 'Proven methods for academic success...',
        date: '26 October 2024',
        author: 'admin',
        image: '/api/placeholder/300/200'
      }
    ]
  });

  const handleSubmit = (event) => {
    event.preventDefault();

    if (selectedRole === "Student") {
      const rollNum = event.target.rollNumber.value;
      const studentName = event.target.studentName.value;
      const password = event.target.password.value;

      if (!rollNum || !studentName || !password) {
        if (!rollNum) setRollNumberError(true);
        if (!studentName) setStudentNameError(true);
        if (!password) setPasswordError(true);
        return;
      }
      const fields = { rollNum, studentName, password };
      setLoader(true);
      dispatch(loginUser(fields, selectedRole));
    } else {
      const email = event.target.email.value;
      const password = event.target.password.value;

      if (!email || !password) {
        if (!email) setEmailError(true);
        if (!password) setPasswordError(true);
        return;
      }

      const fields = { email, password };
      setLoader(true);
      dispatch(loginUser(fields, selectedRole));
    }
  };

  const handleInputChange = (event) => {
    const { name } = event.target;
    if (name === 'email') setEmailError(false);
    if (name === 'password') setPasswordError(false);
    if (name === 'rollNumber') setRollNumberError(false);
    if (name === 'studentName') setStudentNameError(false);
  };

  useEffect(() => {
    if (status === 'success' || currentUser !== null) {
      if (currentRole === 'Admin') {
        navigate('/Admin/dashboard');
      } else if (currentRole === 'Student') {
        navigate('/Student/dashboard');
      } else if (currentRole === 'Teacher') {
        navigate('/Teacher/dashboard');
      }
    } else if (status === 'failed') {
      setMessage(response);
      setShowPopup(true);
      setLoader(false);
    } else if (status === 'error') {
      setMessage("Network Error");
      setShowPopup(true);
      setLoader(false);
    }
  }, [status, currentRole, navigate, error, response, currentUser]);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#0a0e27' }}>
      {/* Navigation Bar */}
      <AppBar position="static" sx={{ bgcolor: 'transparent', boxShadow: 'none' }}>
        <Toolbar sx={{ justifyContent: 'space-between', py: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box
              component="img"
              src="/api/placeholder/40/40"
              alt="Logo"
              sx={{ width: 40, height: 40 }}
            />
            <Typography variant="h6" sx={{ color: '#ff6b35', fontWeight: 'bold' }}>
              {cmsContent.schoolName}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 3 }}>
            <Button sx={{ color: 'white' }}>HOME</Button>
            <Button sx={{ color: 'white' }}>ABOUT</Button>
            <Button sx={{ color: 'white' }}>SERVICE</Button>
            <Button
              variant="contained"
              sx={{
                bgcolor: '#4f46e5',
                '&:hover': { bgcolor: '#4338ca' }
              }}
            >
              CONTACT US
            </Button>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Hero Section */}
      <Container maxWidth="xl" sx={{ py: 8 }}>
        <Grid container spacing={4} alignItems="center">
          {/* Left Side - Content */}
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Typography
                variant="h2"
                sx={{
                  color: 'white',
                  fontWeight: 'bold',
                  mb: 3,
                  fontSize: { xs: '2rem', md: '3.5rem' }
                }}
              >
                {cmsContent.heroTitle}
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: '#94a3b8',
                  mb: 4,
                  lineHeight: 1.6
                }}
              >
                {cmsContent.heroSubtitle}
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  sx={{
                    bgcolor: '#4f46e5',
                    px: 4,
                    py: 1.5,
                    '&:hover': { bgcolor: '#4338ca' }
                  }}
                >
                  Join
                </Button>
                <Button
                  variant="outlined"
                  sx={{
                    borderColor: 'white',
                    color: 'white',
                    px: 4,
                    py: 1.5,
                    '&:hover': { borderColor: '#4f46e5', color: '#4f46e5' }
                  }}
                >
                  Learn
                </Button>
              </Box>
            </motion.div>
          </Grid>

          {/* Right Side - Login Form + Illustration */}
          <Grid item xs={12} md={6}>
            <Box sx={{ position: 'relative' }}>
              {/* Background Illustration */}
              <Box
                sx={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  borderRadius: '20px',
                  p: 4,
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                {/* Login Form */}
                <Paper
                  elevation={24}
                  sx={{
                    p: 4,
                    borderRadius: '16px',
                    bgcolor: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    position: 'relative',
                    zIndex: 2
                  }}
                >
                  <Typography
                    variant="h4"
                    sx={{
                      mb: 2,
                      color: '#2c2143',
                      textAlign: 'center',
                      fontWeight: 'bold'
                    }}
                  >
                    Login to Your Account
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      mb: 3,
                      color: '#64748b',
                      textAlign: 'center'
                    }}
                  >
                    Welcome back! Please select your role and enter your details
                  </Typography>

                  {/* Role Selection */}
                  <Box sx={{ mb: 3 }}>
                    <Typography
                      variant="h6"
                      sx={{
                        mb: 2,
                        color: '#2c2143',
                        textAlign: 'center',
                        fontWeight: 'bold'
                      }}
                    >
                      Select Your Role
                    </Typography>
                    <Grid container spacing={2} justifyContent="center">
                      <Grid item xs={4}>
                        <Paper
                          elevation={selectedRole === 'Admin' ? 8 : 2}
                          onClick={() => setSelectedRole('Admin')}
                          sx={{
                            p: 2,
                            textAlign: 'center',
                            cursor: 'pointer',
                            bgcolor: selectedRole === 'Admin' ? '#7f56da' : 'white',
                            color: selectedRole === 'Admin' ? 'white' : '#2c2143',
                            transition: 'all 0.3s ease',
                            border: selectedRole === 'Admin' ? '2px solid #7f56da' : '2px solid transparent',
                            '&:hover': {
                              transform: 'translateY(-2px)',
                              boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'
                            }
                          }}
                        >
                          <AccountCircle sx={{ fontSize: 40, mb: 1 }} />
                          <Typography variant="body2" fontWeight="bold">
                            Admin
                          </Typography>
                        </Paper>
                      </Grid>
                      <Grid item xs={4}>
                        <Paper
                          elevation={selectedRole === 'Student' ? 8 : 2}
                          onClick={() => setSelectedRole('Student')}
                          sx={{
                            p: 2,
                            textAlign: 'center',
                            cursor: 'pointer',
                            bgcolor: selectedRole === 'Student' ? '#7f56da' : 'white',
                            color: selectedRole === 'Student' ? 'white' : '#2c2143',
                            transition: 'all 0.3s ease',
                            border: selectedRole === 'Student' ? '2px solid #7f56da' : '2px solid transparent',
                            '&:hover': {
                              transform: 'translateY(-2px)',
                              boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'
                            }
                          }}
                        >
                          <School sx={{ fontSize: 40, mb: 1 }} />
                          <Typography variant="body2" fontWeight="bold">
                            Student
                          </Typography>
                        </Paper>
                      </Grid>
                      <Grid item xs={4}>
                        <Paper
                          elevation={selectedRole === 'Teacher' ? 8 : 2}
                          onClick={() => setSelectedRole('Teacher')}
                          sx={{
                            p: 2,
                            textAlign: 'center',
                            cursor: 'pointer',
                            bgcolor: selectedRole === 'Teacher' ? '#7f56da' : 'white',
                            color: selectedRole === 'Teacher' ? 'white' : '#2c2143',
                            transition: 'all 0.3s ease',
                            border: selectedRole === 'Teacher' ? '2px solid #7f56da' : '2px solid transparent',
                            '&:hover': {
                              transform: 'translateY(-2px)',
                              boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'
                            }
                          }}
                        >
                          <Group sx={{ fontSize: 40, mb: 1 }} />
                          <Typography variant="body2" fontWeight="bold">
                            Teacher
                          </Typography>
                        </Paper>
                      </Grid>
                    </Grid>
                  </Box>

                  <Box component="form" onSubmit={handleSubmit}>
                    {selectedRole === "Student" ? (
                      <>
                        <TextField
                          margin="normal"
                          required
                          fullWidth
                          id="rollNumber"
                          label="Enter your Roll Number"
                          name="rollNumber"
                          type="number"
                          error={rollNumberError}
                          helperText={rollNumberError && 'Roll Number is required'}
                          onChange={handleInputChange}
                          sx={{ mb: 2 }}
                        />
                        <TextField
                          margin="normal"
                          required
                          fullWidth
                          id="studentName"
                          label="Enter your name"
                          name="studentName"
                          error={studentNameError}
                          helperText={studentNameError && 'Name is required'}
                          onChange={handleInputChange}
                          sx={{ mb: 2 }}
                        />
                      </>
                    ) : (
                      <TextField
                        margin="normal"
                        required
                        fullWidth
                        id="email"
                        label="Enter your email"
                        name="email"
                        autoComplete="email"
                        error={emailError}
                        helperText={emailError && 'Email is required'}
                        onChange={handleInputChange}
                        sx={{ mb: 2 }}
                      />
                    )}

                    <TextField
                      margin="normal"
                      required
                      fullWidth
                      name="password"
                      label="Password"
                      type={showPassword ? 'text' : 'password'}
                      id="password"
                      autoComplete="current-password"
                      error={passwordError}
                      helperText={passwordError && 'Password is required'}
                      onChange={handleInputChange}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowPassword(!showPassword)}
                              edge="end"
                            >
                              {showPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 2 }}
                    />

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                      <FormControlLabel
                        control={<Checkbox color="primary" />}
                        label="Remember me"
                      />
                      <Button variant="text" sx={{ color: '#7f56da' }}>
                        Forgot password?
                      </Button>
                    </Box>

                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      sx={{
                        py: 1.5,
                        bgcolor: '#7f56da',
                        '&:hover': { bgcolor: '#6d48c7' },
                        borderRadius: '8px',
                        fontWeight: 'bold'
                      }}
                    >
                      {loader ? (
                        <CircularProgress size={24} color="inherit" />
                      ) : (
                        "Login"
                      )}
                    </Button>
                  </Box>
                </Paper>

                {/* Decorative Elements */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: '20%',
                    right: '10%',
                    width: 60,
                    height: 60,
                    bgcolor: '#fbbf24',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 1
                  }}
                >
                  💡
                </Box>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>

      {/* Testimonials Section */}
      <Box sx={{ py: 8, bgcolor: '#0f172a' }}>
        <Container maxWidth="lg">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h4"
              sx={{
                color: '#4f46e5',
                textAlign: 'center',
                mb: 2,
                fontWeight: 'bold'
              }}
            >
              Our Students
            </Typography>
            <Typography
              variant="h3"
              sx={{
                color: 'white',
                textAlign: 'center',
                mb: 6,
                fontWeight: 'bold'
              }}
            >
              Testimonials Speak
            </Typography>

            <Grid container justifyContent="center">
              {cmsContent.testimonials.map((testimonial, index) => (
                <Grid item xs={12} md={8} key={index}>
                  <Card
                    sx={{
                      bgcolor: 'rgba(255, 255, 255, 0.05)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: '16px',
                      p: 4,
                      textAlign: 'center'
                    }}
                  >
                    <Avatar
                      src={testimonial.image}
                      sx={{
                        width: 80,
                        height: 80,
                        mx: 'auto',
                        mb: 3,
                        border: '3px solid #4f46e5'
                      }}
                    />
                    <Typography
                      variant="h5"
                      sx={{ color: 'white', mb: 1, fontWeight: 'bold' }}
                    >
                      {testimonial.name}
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{ color: '#94a3b8', mb: 3, lineHeight: 1.6 }}
                    >
                      {testimonial.message}
                    </Typography>
                    <Rating
                      value={testimonial.rating}
                      readOnly
                      sx={{
                        '& .MuiRating-iconFilled': {
                          color: '#fbbf24'
                        }
                      }}
                    />
                  </Card>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </Box>

      {/* Blog Section */}
      <Box sx={{ py: 8, bgcolor: '#0a0e27' }}>
        <Container maxWidth="lg">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Grid container spacing={4}>
              <Grid item xs={12} md={8}>
                <Grid container spacing={3}>
                  {cmsContent.blogPosts.map((post, index) => (
                    <Grid item xs={12} sm={6} key={index}>
                      <Card
                        sx={{
                          bgcolor: 'rgba(255, 255, 255, 0.05)',
                          backdropFilter: 'blur(10px)',
                          border: '1px solid rgba(255, 255, 255, 0.1)',
                          borderRadius: '16px',
                          overflow: 'hidden',
                          transition: 'transform 0.3s ease',
                          '&:hover': {
                            transform: 'translateY(-5px)'
                          }
                        }}
                      >
                        <CardMedia
                          component="img"
                          height="200"
                          image={post.image}
                          alt={post.title}
                        />
                        <CardContent sx={{ p: 3 }}>
                          <Typography
                            variant="h6"
                            sx={{ color: 'white', mb: 2, fontWeight: 'bold' }}
                          >
                            {post.title}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ color: '#94a3b8', mb: 2 }}
                          >
                            📅 {post.date} | by {post.author}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box
                  sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.05)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '16px',
                    p: 4,
                    textAlign: 'center'
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{ color: '#4f46e5', mb: 2, fontWeight: 'bold' }}
                  >
                    Our Insights
                  </Typography>
                  <Typography
                    variant="h4"
                    sx={{ color: 'white', mb: 3, fontWeight: 'bold' }}
                  >
                    Explore Our Blog
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{ color: '#94a3b8', mb: 4, lineHeight: 1.6 }}
                  >
                    Dive into our blog for insightful articles, expert tips, and the latest trends in education. Stay informed, inspired, and empowered on your journey to academic success and personal growth.
                  </Typography>
                  <Button
                    variant="contained"
                    sx={{
                      bgcolor: '#4f46e5',
                      px: 4,
                      py: 1.5,
                      '&:hover': { bgcolor: '#4338ca' }
                    }}
                  >
                    See
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </motion.div>
        </Container>
      </Box>

      {/* Contact Section */}
      <Box sx={{ py: 8, bgcolor: '#0f172a' }}>
        <Container maxWidth="lg">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h6"
              sx={{ color: '#4f46e5', textAlign: 'center', mb: 2 }}
            >
              Get In Touch
            </Typography>
            <Typography
              variant="h3"
              sx={{
                color: 'white',
                textAlign: 'center',
                mb: 6,
                fontWeight: 'bold'
              }}
            >
              Contact Us Today
            </Typography>

            <Grid container spacing={4}>
              <Grid item xs={12} md={6}>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="First name"
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          bgcolor: 'rgba(255, 255, 255, 0.05)',
                          '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.2)' },
                          '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.3)' },
                          '&.Mui-focused fieldset': { borderColor: '#4f46e5' }
                        },
                        '& .MuiInputLabel-root': { color: '#94a3b8' },
                        '& .MuiOutlinedInput-input': { color: 'white' }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Email"
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          bgcolor: 'rgba(255, 255, 255, 0.05)',
                          '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.2)' },
                          '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.3)' },
                          '&.Mui-focused fieldset': { borderColor: '#4f46e5' }
                        },
                        '& .MuiInputLabel-root': { color: '#94a3b8' },
                        '& .MuiOutlinedInput-input': { color: 'white' }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Phone"
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          bgcolor: 'rgba(255, 255, 255, 0.05)',
                          '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.2)' },
                          '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.3)' },
                          '&.Mui-focused fieldset': { borderColor: '#4f46e5' }
                        },
                        '& .MuiInputLabel-root': { color: '#94a3b8' },
                        '& .MuiOutlinedInput-input': { color: 'white' }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Subject"
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          bgcolor: 'rgba(255, 255, 255, 0.05)',
                          '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.2)' },
                          '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.3)' },
                          '&.Mui-focused fieldset': { borderColor: '#4f46e5' }
                        },
                        '& .MuiInputLabel-root': { color: '#94a3b8' },
                        '& .MuiOutlinedInput-input': { color: 'white' }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      label="Message"
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          bgcolor: 'rgba(255, 255, 255, 0.05)',
                          '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.2)' },
                          '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.3)' },
                          '&.Mui-focused fieldset': { borderColor: '#4f46e5' }
                        },
                        '& .MuiInputLabel-root': { color: '#94a3b8' },
                        '& .MuiOutlinedInput-input': { color: 'white' }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={<Checkbox sx={{ color: '#94a3b8' }} />}
                      label="You agree to our friendly policy"
                      sx={{ color: '#94a3b8' }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      fullWidth
                      variant="contained"
                      sx={{
                        bgcolor: '#4f46e5',
                        py: 1.5,
                        '&:hover': { bgcolor: '#4338ca' }
                      }}
                    >
                      Submit Form
                    </Button>
                  </Grid>
                </Grid>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box
                  sx={{
                    height: '100%',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    borderRadius: '16px',
                    p: 4,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center'
                  }}
                >
                  <Typography
                    variant="h4"
                    sx={{ color: 'white', mb: 4, fontWeight: 'bold' }}
                  >
                    Get in touch with us
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <LocationOn sx={{ color: 'white' }} />
                      <Typography sx={{ color: 'white' }}>
                        {cmsContent.contactInfo.address}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Phone sx={{ color: 'white' }} />
                      <Typography sx={{ color: 'white' }}>
                        {cmsContent.contactInfo.phone}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Phone sx={{ color: 'white' }} />
                      <Typography sx={{ color: 'white' }}>
                        {cmsContent.contactInfo.alternatePhone}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Email sx={{ color: 'white' }} />
                      <Typography sx={{ color: 'white' }}>
                        {cmsContent.contactInfo.email}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </motion.div>
        </Container>
      </Box>

      {/* Footer */}
      <Box sx={{ py: 6, bgcolor: '#0a0e27', borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <Box
                  component="img"
                  src="/api/placeholder/40/40"
                  alt="Logo"
                  sx={{ width: 40, height: 40 }}
                />
                <Typography variant="h6" sx={{ color: '#ff6b35', fontWeight: 'bold' }}>
                  {cmsContent.schoolName}
                </Typography>
              </Box>
              <Typography sx={{ color: '#94a3b8', mb: 3, lineHeight: 1.6 }}>
                Amar Vidya Mandir: Nurturing young minds, fostering growth, and inspiring a lifelong love for learning. Excellence in education.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <IconButton sx={{ color: '#94a3b8', '&:hover': { color: '#4f46e5' } }}>
                  <Facebook />
                </IconButton>
                <IconButton sx={{ color: '#94a3b8', '&:hover': { color: '#4f46e5' } }}>
                  <Instagram />
                </IconButton>
                <IconButton sx={{ color: '#94a3b8', '&:hover': { color: '#4f46e5' } }}>
                  <YouTube />
                </IconButton>
                <IconButton sx={{ color: '#94a3b8', '&:hover': { color: '#4f46e5' } }}>
                  <LinkedIn />
                </IconButton>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Typography variant="h6" sx={{ color: 'white', mb: 3, fontWeight: 'bold' }}>
                QUICK LINKS
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button sx={{ color: '#94a3b8', justifyContent: 'flex-start', '&:hover': { color: '#4f46e5' } }}>
                  ABOUT
                </Button>
                <Button sx={{ color: '#94a3b8', justifyContent: 'flex-start', '&:hover': { color: '#4f46e5' } }}>
                  SERVICE
                </Button>
                <Button sx={{ color: '#94a3b8', justifyContent: 'flex-start', '&:hover': { color: '#4f46e5' } }}>
                  CONTACT
                </Button>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Typography variant="h6" sx={{ color: 'white', mb: 3, fontWeight: 'bold' }}>
                CONTACT
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <LocationOn sx={{ color: '#4f46e5' }} />
                  <Typography sx={{ color: '#94a3b8' }}>
                    {cmsContent.contactInfo.address}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Phone sx={{ color: '#4f46e5' }} />
                  <Typography sx={{ color: '#94a3b8' }}>
                    {cmsContent.contactInfo.phone}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Phone sx={{ color: '#4f46e5' }} />
                  <Typography sx={{ color: '#94a3b8' }}>
                    {cmsContent.contactInfo.alternatePhone}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Email sx={{ color: '#4f46e5' }} />
                  <Typography sx={{ color: '#94a3b8' }}>
                    {cmsContent.contactInfo.email}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>

          <Box
            sx={{
              mt: 6,
              pt: 4,
              borderTop: '1px solid rgba(255, 255, 255, 0.1)',
              textAlign: 'center'
            }}
          >
            <Typography sx={{ color: '#94a3b8' }}>
              © 2024 {cmsContent.schoolName}. All rights reserved.
            </Typography>
          </Box>
        </Container>
      </Box>

      <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />

      {/* Scroll to Top Button */}
      <Fab
        color="primary"
        size="small"
        onClick={scrollToTop}
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          bgcolor: '#4f46e5',
          '&:hover': { bgcolor: '#4338ca' }
        }}
      >
        <ArrowUpward />
      </Fab>
    </Box>
  );
};

export default EnhancedLoginPage;
