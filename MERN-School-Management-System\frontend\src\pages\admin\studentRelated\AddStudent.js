import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { registerUser } from '../../../redux/userRelated/userHandle';
import Popup from '../../../components/Popup';
import { underControl } from '../../../redux/userRelated/userSlice';
import { getAllSclasses } from '../../../redux/sclassRelated/sclassHandle';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Card,
  CardContent,
  Divider,
  IconButton,
  Avatar,
  Badge
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  Person as PersonIcon,
  School as SchoolIcon,
  PhotoCamera as PhotoCameraIcon,
  CloudUpload as CloudUploadIcon,
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon
} from '@mui/icons-material';

// Styled components
const ProfilePictureUpload = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(2),
  padding: theme.spacing(3),
  border: `2px dashed ${theme.palette.primary.main}`,
  borderRadius: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  transition: 'all 0.3s ease',
  '&:hover': {
    borderColor: theme.palette.primary.dark,
    backgroundColor: theme.palette.action.hover,
  },
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
  borderRadius: '20px',
  boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
}));

const StyledCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  borderRadius: '15px',
  boxShadow: '0 8px 25px rgba(0, 0, 0, 0.08)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
}));

const AddStudent = ({ situation }) => {
    const dispatch = useDispatch()
    const navigate = useNavigate()
    const params = useParams()

    const userState = useSelector(state => state.user);
    const { status, currentUser, response, error } = userState;
    const { sclassesList } = useSelector((state) => state.sclass);

    // Basic Information
    const [name, setName] = useState('');
    const [rollNum, setRollNum] = useState('');
    const [password, setPassword] = useState('');
    const [className, setClassName] = useState('');
    const [sclassName, setSclassName] = useState('');

    // Additional Information
    const [email, setEmail] = useState('');
    const [phone, setPhone] = useState('');
    const [dateOfBirth, setDateOfBirth] = useState('');
    const [gender, setGender] = useState('');
    const [bloodGroup, setBloodGroup] = useState('');
    const [address, setAddress] = useState('');
    const [fatherName, setFatherName] = useState('');
    const [motherName, setMotherName] = useState('');
    const [guardianPhone, setGuardianPhone] = useState('');

    // Profile Picture
    const [profilePicture, setProfilePicture] = useState(null);
    const [profilePicturePreview, setProfilePicturePreview] = useState(null);

    const adminID = currentUser._id
    const role = "Student"
    const attendance = []

    useEffect(() => {
        if (situation === "Class") {
            setSclassName(params.id);
        }
    }, [params.id, situation]);

    const [showPopup, setShowPopup] = useState(false);
    const [message, setMessage] = useState("");
    const [loader, setLoader] = useState(false)

    useEffect(() => {
        dispatch(getAllSclasses(adminID, "Sclass"));
    }, [adminID, dispatch]);

    const changeHandler = (event) => {
        if (event.target.value === 'Select Class') {
            setClassName('Select Class');
            setSclassName('');
        } else {
            const selectedClass = sclassesList.find(
                (classItem) => classItem.sclassName === event.target.value
            );
            setClassName(selectedClass.sclassName);
            setSclassName(selectedClass._id);
        }
    }

    const handleProfilePictureChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            setProfilePicture(file);
            const reader = new FileReader();
            reader.onload = (e) => {
                setProfilePicturePreview(e.target.result);
            };
            reader.readAsDataURL(file);
        }
    };

    const fields = {
        name,
        rollNum,
        password,
        sclassName,
        adminID,
        role,
        attendance,
        email,
        phone,
        dateOfBirth,
        gender,
        bloodGroup,
        address,
        fatherName,
        motherName,
        guardianPhone,
        profilePicture: profilePicturePreview
    }

    const submitHandler = (event) => {
        event.preventDefault()
        if (sclassName === "") {
            setMessage("Please select a classname")
            setShowPopup(true)
        }
        else {
            setLoader(true)
            dispatch(registerUser(fields, role))
        }
    }

    useEffect(() => {
        if (status === 'added') {
            dispatch(underControl())
            navigate(-1)
        }
        else if (status === 'failed') {
            setMessage(response)
            setShowPopup(true)
            setLoader(false)
        }
        else if (status === 'error') {
            setMessage("Network Error")
            setShowPopup(true)
            setLoader(false)
        }
    }, [status, navigate, error, response, dispatch]);

    return (
        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
            >
                <StyledPaper>
                    {/* Header */}
                    <Box display="flex" alignItems="center" mb={4}>
                        <IconButton
                            onClick={() => navigate(-1)}
                            sx={{ mr: 2 }}
                            color="primary"
                        >
                            <ArrowBackIcon />
                        </IconButton>
                        <PersonIcon sx={{ fontSize: 40, mr: 2, color: 'primary.main' }} />
                        <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
                            Add New Student
                        </Typography>
                    </Box>

                    <form onSubmit={submitHandler}>
                        {/* Basic Information Card */}
                        <StyledCard>
                            <CardContent>
                                <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                    <SchoolIcon sx={{ mr: 1 }} />
                                    Basic Information
                                </Typography>

                                {/* Profile Picture Upload */}
                                <Grid container spacing={3}>
                                    <Grid item xs={12} md={4}>
                                        <ProfilePictureUpload>
                                            <Avatar
                                                src={profilePicturePreview}
                                                sx={{
                                                    width: 120,
                                                    height: 120,
                                                    border: '4px solid',
                                                    borderColor: 'primary.main'
                                                }}
                                            >
                                                <PersonIcon sx={{ fontSize: 60 }} />
                                            </Avatar>
                                            <input
                                                accept="image/*"
                                                style={{ display: 'none' }}
                                                id="profile-picture-upload"
                                                type="file"
                                                onChange={handleProfilePictureChange}
                                            />
                                            <label htmlFor="profile-picture-upload">
                                                <Button
                                                    variant="outlined"
                                                    component="span"
                                                    startIcon={<CloudUploadIcon />}
                                                    sx={{ mt: 1 }}
                                                >
                                                    Upload Photo
                                                </Button>
                                            </label>
                                            <Typography variant="caption" color="textSecondary" textAlign="center">
                                                Recommended: 300x300px, Max 5MB
                                            </Typography>
                                        </ProfilePictureUpload>
                                    </Grid>
                                    <Grid item xs={12} md={8}>
                                        <Grid container spacing={3}>
                                    <Grid item xs={12} md={6}>
                                        <TextField
                                            fullWidth
                                            label="Student Name"
                                            value={name}
                                            onChange={(e) => setName(e.target.value)}
                                            required
                                            variant="outlined"
                                            placeholder="Enter student's full name"
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        <TextField
                                            fullWidth
                                            label="Roll Number"
                                            type="number"
                                            value={rollNum}
                                            onChange={(e) => setRollNum(e.target.value)}
                                            required
                                            variant="outlined"
                                            placeholder="Enter roll number"
                                        />
                                    </Grid>
                                    {situation === "Student" && (
                                        <Grid item xs={12} md={6}>
                                            <FormControl fullWidth required>
                                                <InputLabel>Class</InputLabel>
                                                <Select
                                                    value={className}
                                                    onChange={changeHandler}
                                                    label="Class"
                                                >
                                                    <MenuItem value="Select Class">Select Class</MenuItem>
                                                    {sclassesList.map((classItem, index) => (
                                                        <MenuItem key={index} value={classItem.sclassName}>
                                                            {classItem.sclassName}
                                                        </MenuItem>
                                                    ))}
                                                </Select>
                                            </FormControl>
                                        </Grid>
                                    )}
                                    <Grid item xs={12} md={6}>
                                        <TextField
                                            fullWidth
                                            label="Password"
                                            type="password"
                                            value={password}
                                            onChange={(e) => setPassword(e.target.value)}
                                            required
                                            variant="outlined"
                                            placeholder="Enter student password"
                                            autoComplete="new-password"
                                        />
                                    </Grid>
                                        </Grid>
                                    </Grid>
                                </Grid>
                            </CardContent>
                        </StyledCard>

                        {/* Personal Information Card */}
                        <StyledCard>
                            <CardContent>
                                <Typography variant="h6" gutterBottom color="primary" sx={{ mb: 3 }}>
                                    Personal Information
                                </Typography>
                                <Grid container spacing={3}>
                                    <Grid item xs={12} md={6}>
                                        <TextField
                                            fullWidth
                                            label="Email Address"
                                            type="email"
                                            value={email}
                                            onChange={(e) => setEmail(e.target.value)}
                                            variant="outlined"
                                            placeholder="<EMAIL>"
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        <TextField
                                            fullWidth
                                            label="Phone Number"
                                            value={phone}
                                            onChange={(e) => setPhone(e.target.value)}
                                            variant="outlined"
                                            placeholder="+1234567890"
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        <TextField
                                            fullWidth
                                            label="Date of Birth"
                                            type="date"
                                            value={dateOfBirth}
                                            onChange={(e) => setDateOfBirth(e.target.value)}
                                            variant="outlined"
                                            InputLabelProps={{ shrink: true }}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        <FormControl fullWidth>
                                            <InputLabel>Gender</InputLabel>
                                            <Select
                                                value={gender}
                                                onChange={(e) => setGender(e.target.value)}
                                                label="Gender"
                                            >
                                                <MenuItem value="">Select Gender</MenuItem>
                                                <MenuItem value="Male">Male</MenuItem>
                                                <MenuItem value="Female">Female</MenuItem>
                                                <MenuItem value="Other">Other</MenuItem>
                                            </Select>
                                        </FormControl>
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        <FormControl fullWidth>
                                            <InputLabel>Blood Group</InputLabel>
                                            <Select
                                                value={bloodGroup}
                                                onChange={(e) => setBloodGroup(e.target.value)}
                                                label="Blood Group"
                                            >
                                                <MenuItem value="">Select Blood Group</MenuItem>
                                                <MenuItem value="A+">A+</MenuItem>
                                                <MenuItem value="A-">A-</MenuItem>
                                                <MenuItem value="B+">B+</MenuItem>
                                                <MenuItem value="B-">B-</MenuItem>
                                                <MenuItem value="AB+">AB+</MenuItem>
                                                <MenuItem value="AB-">AB-</MenuItem>
                                                <MenuItem value="O+">O+</MenuItem>
                                                <MenuItem value="O-">O-</MenuItem>
                                            </Select>
                                        </FormControl>
                                    </Grid>
                                    <Grid item xs={12}>
                                        <TextField
                                            fullWidth
                                            label="Address"
                                            multiline
                                            rows={3}
                                            value={address}
                                            onChange={(e) => setAddress(e.target.value)}
                                            variant="outlined"
                                            placeholder="Enter complete address"
                                        />
                                    </Grid>
                                </Grid>
                            </CardContent>
                        </StyledCard>

                        {/* Family Information Card */}
                        <StyledCard>
                            <CardContent>
                                <Typography variant="h6" gutterBottom color="primary" sx={{ mb: 3 }}>
                                    Family Information
                                </Typography>
                                <Grid container spacing={3}>
                                    <Grid item xs={12} md={6}>
                                        <TextField
                                            fullWidth
                                            label="Father's Name"
                                            value={fatherName}
                                            onChange={(e) => setFatherName(e.target.value)}
                                            variant="outlined"
                                            placeholder="Enter father's name"
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        <TextField
                                            fullWidth
                                            label="Mother's Name"
                                            value={motherName}
                                            onChange={(e) => setMotherName(e.target.value)}
                                            variant="outlined"
                                            placeholder="Enter mother's name"
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        <TextField
                                            fullWidth
                                            label="Guardian Phone"
                                            value={guardianPhone}
                                            onChange={(e) => setGuardianPhone(e.target.value)}
                                            variant="outlined"
                                            placeholder="+1234567890"
                                        />
                                    </Grid>
                                </Grid>
                            </CardContent>
                        </StyledCard>

                        {/* Submit Button */}
                        <Box display="flex" justifyContent="center" mt={4}>
                            <Button
                                type="submit"
                                variant="contained"
                                size="large"
                                disabled={loader}
                                startIcon={loader ? <CircularProgress size={20} /> : <SaveIcon />}
                                sx={{
                                    px: 6,
                                    py: 2,
                                    borderRadius: '25px',
                                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                                    boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
                                    '&:hover': {
                                        background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',
                                    }
                                }}
                            >
                                {loader ? 'Adding Student...' : 'Add Student'}
                            </Button>
                        </Box>
                    </form>
                </StyledPaper>
            </motion.div>
            <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />
        </Container>
    )
}

export default AddStudent