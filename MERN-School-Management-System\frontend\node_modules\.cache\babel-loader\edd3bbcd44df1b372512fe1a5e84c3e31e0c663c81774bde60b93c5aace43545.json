{"ast": null, "code": "import { motionValue } from 'motion-dom';\nimport { warning } from 'motion-utils';\nimport { useEffect } from 'react';\nimport { scroll } from '../render/dom/scroll/index.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nfunction refWarning(name, ref) {\n  warning(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n  scrollX: motionValue(0),\n  scrollY: motionValue(0),\n  scrollXProgress: motionValue(0),\n  scrollYProgress: motionValue(0)\n});\nfunction useScroll() {\n  let {\n    container,\n    target,\n    layoutEffect = true,\n    ...options\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const values = useConstant(createScrollMotionValues);\n  const useLifecycleEffect = layoutEffect ? useIsomorphicLayoutEffect : useEffect;\n  useLifecycleEffect(() => {\n    refWarning(\"target\", target);\n    refWarning(\"container\", container);\n    return scroll((_progress, _ref) => {\n      let {\n        x,\n        y\n      } = _ref;\n      values.scrollX.set(x.current);\n      values.scrollXProgress.set(x.progress);\n      values.scrollY.set(y.current);\n      values.scrollYProgress.set(y.progress);\n    }, {\n      ...options,\n      container: container?.current || undefined,\n      target: target?.current || undefined\n    });\n  }, [container, target, JSON.stringify(options.offset)]);\n  return values;\n}\nexport { useScroll };", "map": {"version": 3, "names": ["motionValue", "warning", "useEffect", "scroll", "useConstant", "useIsomorphicLayoutEffect", "refWarning", "name", "ref", "Boolean", "current", "createScrollMotionValues", "scrollX", "scrollY", "scrollXProgress", "scrollYProgress", "useScroll", "container", "target", "layoutEffect", "options", "arguments", "length", "undefined", "values", "useLifecycleEffect", "_progress", "_ref", "x", "y", "set", "progress", "JSON", "stringify", "offset"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/value/use-scroll.mjs"], "sourcesContent": ["import { motionValue } from 'motion-dom';\nimport { warning } from 'motion-utils';\nimport { useEffect } from 'react';\nimport { scroll } from '../render/dom/scroll/index.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\n\nfunction refWarning(name, ref) {\n    warning(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: motionValue(0),\n    scrollY: motionValue(0),\n    scrollXProgress: motionValue(0),\n    scrollYProgress: motionValue(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = useConstant(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? useIsomorphicLayoutEffect\n        : useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return scroll((_progress, { x, y, }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: container?.current || undefined,\n            target: target?.current || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\nexport { useScroll };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,YAAY;AACxC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,MAAM,QAAQ,gCAAgC;AACvD,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,yBAAyB,QAAQ,oCAAoC;AAE9E,SAASC,UAAUA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC3BP,OAAO,CAACQ,OAAO,CAAC,CAACD,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,EAAG,sBAAqBH,IAAK,+MAA8M,CAAC;AACpR;AACA,MAAMI,wBAAwB,GAAGA,CAAA,MAAO;EACpCC,OAAO,EAAEZ,WAAW,CAAC,CAAC,CAAC;EACvBa,OAAO,EAAEb,WAAW,CAAC,CAAC,CAAC;EACvBc,eAAe,EAAEd,WAAW,CAAC,CAAC,CAAC;EAC/Be,eAAe,EAAEf,WAAW,CAAC,CAAC;AAClC,CAAC,CAAC;AACF,SAASgB,SAASA,CAAA,EAA8D;EAAA,IAA7D;IAAEC,SAAS;IAAEC,MAAM;IAAEC,YAAY,GAAG,IAAI;IAAE,GAAGC;EAAQ,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC1E,MAAMG,MAAM,GAAGpB,WAAW,CAACO,wBAAwB,CAAC;EACpD,MAAMc,kBAAkB,GAAGN,YAAY,GACjCd,yBAAyB,GACzBH,SAAS;EACfuB,kBAAkB,CAAC,MAAM;IACrBnB,UAAU,CAAC,QAAQ,EAAEY,MAAM,CAAC;IAC5BZ,UAAU,CAAC,WAAW,EAAEW,SAAS,CAAC;IAClC,OAAOd,MAAM,CAAC,CAACuB,SAAS,EAAAC,IAAA,KAAgB;MAAA,IAAd;QAAEC,CAAC;QAAEC;MAAG,CAAC,GAAAF,IAAA;MAC/BH,MAAM,CAACZ,OAAO,CAACkB,GAAG,CAACF,CAAC,CAAClB,OAAO,CAAC;MAC7Bc,MAAM,CAACV,eAAe,CAACgB,GAAG,CAACF,CAAC,CAACG,QAAQ,CAAC;MACtCP,MAAM,CAACX,OAAO,CAACiB,GAAG,CAACD,CAAC,CAACnB,OAAO,CAAC;MAC7Bc,MAAM,CAACT,eAAe,CAACe,GAAG,CAACD,CAAC,CAACE,QAAQ,CAAC;IAC1C,CAAC,EAAE;MACC,GAAGX,OAAO;MACVH,SAAS,EAAEA,SAAS,EAAEP,OAAO,IAAIa,SAAS;MAC1CL,MAAM,EAAEA,MAAM,EAAER,OAAO,IAAIa;IAC/B,CAAC,CAAC;EACN,CAAC,EAAE,CAACN,SAAS,EAAEC,MAAM,EAAEc,IAAI,CAACC,SAAS,CAACb,OAAO,CAACc,MAAM,CAAC,CAAC,CAAC;EACvD,OAAOV,MAAM;AACjB;AAEA,SAASR,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}