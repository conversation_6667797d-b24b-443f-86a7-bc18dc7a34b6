import React from 'react';
import { Container, Grid, Paper, Box, Typography, useTheme, useMediaQuery } from '@mui/material';
import { motion } from 'framer-motion';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// Import existing components
import SeeNotice from '../../components/SeeNotice';

// Import new enhanced components
import EnhancedStatCard from '../../components/widgets/EnhancedStatCard';
import AttendanceChart from '../../components/analytics/AttendanceChart';
import PerformanceChart from '../../components/analytics/PerformanceChart';
import FinancialChart from '../../components/analytics/FinancialChart';
import QuickActions from '../../components/widgets/QuickActions';
import RecentActivity from '../../components/widgets/RecentActivity';
import CalendarWidget from '../../components/widgets/CalendarWidget';

// Import Redux actions
import { getAllSclasses } from '../../redux/sclassRelated/sclassHandle';
import { getAllStudents } from '../../redux/studentRelated/studentHandle';
import { getAllTeachers } from '../../redux/teacherRelated/teacherHandle';

// Import icons
import {
  School as SchoolIcon,
  Class as ClassIcon,
  SupervisorAccount as SupervisorAccountIcon,
  AttachMoney as AttachMoneyIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
} from '@mui/icons-material';

const AdminHomePage = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));

    const { studentsList } = useSelector((state) => state.student);
    const { sclassesList } = useSelector((state) => state.sclass);
    const { teachersList } = useSelector((state) => state.teacher);
    const { currentUser } = useSelector(state => state.user);

    const adminID = currentUser._id;

    useEffect(() => {
        dispatch(getAllStudents(adminID));
        dispatch(getAllSclasses(adminID, "Sclass"));
        dispatch(getAllTeachers(adminID));
    }, [adminID, dispatch]);

    const numberOfStudents = studentsList && studentsList.length;
    const numberOfClasses = sclassesList && sclassesList.length;
    const numberOfTeachers = teachersList && teachersList.length;

    // Calculate some additional metrics
    const totalRevenue = 125000; // This would come from your backend
    const attendanceRate = 92; // This would come from your backend
    const averageGrade = 85; // This would come from your backend

    return (
        <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
            {/* Welcome Section */}
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
            >
                <Box mb={4}>
                    <Typography
                        variant="h4"
                        fontWeight="bold"
                        color="primary"
                        gutterBottom
                    >
                        Welcome back, {currentUser?.name || 'Admin'}! 👋
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                        Here's what's happening at your school today
                    </Typography>
                </Box>
            </motion.div>

            <Grid container spacing={3}>
                {/* Enhanced Stat Cards */}
                <Grid item xs={12} sm={6} md={3}>
                    <EnhancedStatCard
                        title="Total Students"
                        value={numberOfStudents || 0}
                        icon={<SchoolIcon sx={{ fontSize: '3rem' }} />}
                        gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
                        trend="up"
                        trendValue="+12%"
                        onClick={() => navigate('/Admin/students')}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <EnhancedStatCard
                        title="Total Classes"
                        value={numberOfClasses || 0}
                        icon={<ClassIcon sx={{ fontSize: '3rem' }} />}
                        gradient="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
                        trend="up"
                        trendValue="+3%"
                        onClick={() => navigate('/Admin/classes')}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <EnhancedStatCard
                        title="Total Teachers"
                        value={numberOfTeachers || 0}
                        icon={<SupervisorAccountIcon sx={{ fontSize: '3rem' }} />}
                        gradient="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
                        trend="up"
                        trendValue="+8%"
                        onClick={() => navigate('/Admin/teachers')}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <EnhancedStatCard
                        title="Revenue"
                        value={totalRevenue}
                        prefix="$"
                        icon={<AttachMoneyIcon sx={{ fontSize: '3rem' }} />}
                        gradient="linear-gradient(135deg, #fa709a 0%, #fee140 100%)"
                        trend="up"
                        trendValue="+15%"
                    />
                </Grid>

                {/* Quick Actions */}
                <Grid item xs={12}>
                    <QuickActions />
                </Grid>

                {/* Analytics Charts Row 1 */}
                <Grid item xs={12} md={6}>
                    <AttendanceChart />
                </Grid>
                <Grid item xs={12} md={6}>
                    <PerformanceChart />
                </Grid>

                {/* Analytics Charts Row 2 */}
                <Grid item xs={12} md={4}>
                    <FinancialChart />
                </Grid>
                <Grid item xs={12} md={4}>
                    <RecentActivity />
                </Grid>
                <Grid item xs={12} md={4}>
                    <CalendarWidget />
                </Grid>

                {/* Enhanced Notice Section */}
                <Grid item xs={12}>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.4 }}
                    >
                        <Paper
                            sx={{
                                p: 3,
                                borderRadius: '16px',
                                background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                            }}
                        >
                            <SeeNotice />
                        </Paper>
                    </motion.div>
                </Grid>
            </Grid>
        </Container>
    );
};

export default AdminHomePage;