{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\components\\\\layout\\\\ResponsiveSidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Drawer, List, ListItemButton, ListItemIcon, ListItemText, Divider, Box, Typography, useTheme, useMediaQuery, Avatar, Chip, Collapse } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Home as HomeIcon, Class as ClassIcon, Assignment as AssignmentIcon, SupervisorAccount as SupervisorAccountIcon, PersonOutline as PersonOutlineIcon, Announcement as AnnouncementIcon, Report as ReportIcon, AccountCircleOutlined as AccountCircleOutlinedIcon, ExitToApp as ExitToAppIcon, School as SchoolIcon,\n// New icons for additional modules\nInfo as InfoIcon, Payment as PaymentIcon, Description as DescriptionIcon, DirectionsBus as DirectionsBusIcon, People as PeopleIcon, PersonAdd as PersonAddIcon, EventAvailable as EventAvailableIcon, Quiz as QuizIcon, MenuBook as MenuBookIcon, NotificationsActive as NotificationsActiveIcon, Hotel as HotelIcon, Web as WebIcon, Business as BusinessIcon, ExpandLess, ExpandMore } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledDrawer = styled(Drawer)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    '& .MuiDrawer-paper': {\n      background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%)',\n      color: 'white',\n      borderRight: 'none',\n      boxShadow: '8px 0 32px rgba(0, 0, 0, 0.15)',\n      backdropFilter: 'blur(10px)',\n      position: 'relative',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n        pointerEvents: 'none'\n      }\n    }\n  };\n});\n_c = StyledDrawer;\nconst SidebarHeader = styled(Box)(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    padding: theme.spacing(3, 2),\n    display: 'flex',\n    alignItems: 'center',\n    gap: theme.spacing(2),\n    borderBottom: '1px solid rgba(255, 255, 255, 0.1)'\n  };\n});\n_c2 = SidebarHeader;\nconst StyledListItemButton = styled(ListItemButton)(_ref3 => {\n  let {\n    theme,\n    active\n  } = _ref3;\n  return {\n    margin: theme.spacing(0.5, 1),\n    borderRadius: '12px',\n    padding: theme.spacing(1.5, 2),\n    backgroundColor: active ? 'rgba(255, 255, 255, 0.15)' : 'transparent',\n    backdropFilter: active ? 'blur(10px)' : 'none',\n    border: active ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid transparent',\n    transition: 'all 0.3s ease',\n    '&:hover': {\n      backgroundColor: 'rgba(255, 255, 255, 0.1)',\n      transform: 'translateX(4px)'\n    },\n    '&:before': {\n      content: '\"\"',\n      position: 'absolute',\n      left: 0,\n      top: '50%',\n      transform: 'translateY(-50%)',\n      width: active ? '4px' : '0px',\n      height: '60%',\n      backgroundColor: '#FFD700',\n      borderRadius: '0 4px 4px 0',\n      transition: 'width 0.3s ease'\n    }\n  };\n});\n_c3 = StyledListItemButton;\nconst MenuSection = styled(Box)(_ref4 => {\n  let {\n    theme\n  } = _ref4;\n  return {\n    padding: theme.spacing(1, 2),\n    marginTop: theme.spacing(2)\n  };\n});\n_c4 = MenuSection;\nconst SectionTitle = styled(Typography)(_ref5 => {\n  let {\n    theme\n  } = _ref5;\n  return {\n    fontSize: '0.75rem',\n    fontWeight: 600,\n    textTransform: 'uppercase',\n    letterSpacing: '0.5px',\n    opacity: 0.7,\n    marginBottom: theme.spacing(1)\n  };\n});\n_c5 = SectionTitle;\nconst ResponsiveSidebar = _ref6 => {\n  _s();\n  let {\n    open,\n    onClose,\n    variant = 'permanent'\n  } = _ref6;\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const location = useLocation();\n  const [expandedSections, setExpandedSections] = React.useState({});\n  const handleSectionToggle = section => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [section]: !prev[section]\n    }));\n  };\n\n  // Main Dashboard\n  const dashboardItems = [{\n    text: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/dashboard',\n    badge: null\n  }];\n\n  // Student Management\n  const studentManagementItems = [{\n    text: 'Student Info',\n    icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/student-info',\n    badge: null\n  }, {\n    text: 'Students',\n    icon: /*#__PURE__*/_jsxDEV(PersonOutlineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/students',\n    badge: null\n  }, {\n    text: 'Admission',\n    icon: /*#__PURE__*/_jsxDEV(PersonAddIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/admission',\n    badge: '5'\n  }];\n\n  // Academic Management\n  const academicItems = [{\n    text: 'Classes',\n    icon: /*#__PURE__*/_jsxDEV(ClassIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/classes',\n    badge: null\n  }, {\n    text: 'Subjects',\n    icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/subjects',\n    badge: null\n  }, {\n    text: 'Academics',\n    icon: /*#__PURE__*/_jsxDEV(MenuBookIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/academics',\n    badge: null\n  }, {\n    text: 'Examination',\n    icon: /*#__PURE__*/_jsxDEV(QuizIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/examination',\n    badge: '2'\n  }];\n\n  // Staff Management\n  const staffItems = [{\n    text: 'Teachers',\n    icon: /*#__PURE__*/_jsxDEV(SupervisorAccountIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/teachers',\n    badge: null\n  }, {\n    text: 'Attendance',\n    icon: /*#__PURE__*/_jsxDEV(EventAvailableIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/attendance',\n    badge: null\n  }];\n\n  // Financial Management\n  const financialItems = [{\n    text: 'Fee Management',\n    icon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/fee-management',\n    badge: '12'\n  }, {\n    text: 'Fee Due',\n    icon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/fee-due',\n    badge: '8'\n  }];\n\n  // Operations\n  const operationsItems = [{\n    text: 'Transport',\n    icon: /*#__PURE__*/_jsxDEV(DirectionsBusIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/transport',\n    badge: null\n  }, {\n    text: 'Hostel',\n    icon: /*#__PURE__*/_jsxDEV(HotelIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/hostel',\n    badge: '3'\n  }, {\n    text: 'Front Office',\n    icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/front-office',\n    badge: null\n  }];\n\n  // Communication\n  const communicationItems = [{\n    text: 'Notifications',\n    icon: /*#__PURE__*/_jsxDEV(NotificationsActiveIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/notifications',\n    badge: '15'\n  }, {\n    text: 'Notices',\n    icon: /*#__PURE__*/_jsxDEV(AnnouncementIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/notices',\n    badge: '3'\n  }, {\n    text: 'CMS',\n    icon: /*#__PURE__*/_jsxDEV(WebIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/cms',\n    badge: null\n  }];\n\n  // Documents & Reports\n  const documentsItems = [{\n    text: 'Documents',\n    icon: /*#__PURE__*/_jsxDEV(DescriptionIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/documents',\n    badge: null\n  }, {\n    text: 'Complaints',\n    icon: /*#__PURE__*/_jsxDEV(ReportIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/complains',\n    badge: '2'\n  }];\n  const userMenuItems = [{\n    text: 'Profile',\n    icon: /*#__PURE__*/_jsxDEV(AccountCircleOutlinedIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/profile',\n    badge: null\n  }, {\n    text: 'Logout',\n    icon: /*#__PURE__*/_jsxDEV(ExitToAppIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 13\n    }, this),\n    path: '/logout',\n    badge: null\n  }];\n  const isActive = path => {\n    if (path === '/Admin/dashboard') {\n      return location.pathname === '/' || location.pathname === '/Admin/dashboard';\n    }\n    return location.pathname.startsWith(path);\n  };\n  const renderMenuSection = (title, items, sectionKey) => /*#__PURE__*/_jsxDEV(MenuSection, {\n    children: [/*#__PURE__*/_jsxDEV(StyledListItemButton, {\n      onClick: () => handleSectionToggle(sectionKey),\n      sx: {\n        justifyContent: 'space-between',\n        backgroundColor: 'rgba(255, 255, 255, 0.05)',\n        mb: 1,\n        '&:hover': {\n          backgroundColor: 'rgba(255, 255, 255, 0.1)'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), expandedSections[sectionKey] ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 41\n      }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 58\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: expandedSections[sectionKey],\n      timeout: \"auto\",\n      unmountOnExit: true,\n      children: /*#__PURE__*/_jsxDEV(List, {\n        component: \"nav\",\n        sx: {\n          padding: 0,\n          pl: 1\n        },\n        children: items.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: index * 0.05\n          },\n          children: /*#__PURE__*/_jsxDEV(StyledListItemButton, {\n            component: Link,\n            to: item.path,\n            active: isActive(item.path),\n            onClick: isMobile ? onClose : undefined,\n            sx: {\n              pl: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              sx: {\n                color: 'inherit',\n                minWidth: 40\n              },\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: item.text,\n              primaryTypographyProps: {\n                fontSize: '0.85rem',\n                fontWeight: isActive(item.path) ? 600 : 400\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this), item.badge && /*#__PURE__*/_jsxDEV(Chip, {\n              label: item.badge,\n              size: \"small\",\n              sx: {\n                bgcolor: '#FFD700',\n                color: '#333',\n                fontSize: '0.7rem',\n                height: 18,\n                minWidth: 18\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this)\n        }, item.text, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 7\n    }, this)]\n  }, sectionKey, true, {\n    fileName: _jsxFileName,\n    lineNumber: 306,\n    columnNumber: 5\n  }, this);\n  const drawerContent = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(SidebarHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        sx: {\n          bgcolor: 'rgba(255, 255, 255, 0.2)',\n          width: 48,\n          height: 48\n        },\n        children: /*#__PURE__*/_jsxDEV(SchoolIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"bold\",\n          children: \"School Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            opacity: 0.8\n          },\n          children: \"Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        overflowY: 'auto',\n        py: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(MenuSection, {\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          sx: {\n            padding: 0\n          },\n          children: dashboardItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(StyledListItemButton, {\n              component: Link,\n              to: item.path,\n              active: isActive(item.path),\n              onClick: isMobile ? onClose : undefined,\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                sx: {\n                  color: 'inherit',\n                  minWidth: 40\n                },\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: item.text,\n                primaryTypographyProps: {\n                  fontSize: '0.9rem',\n                  fontWeight: isActive(item.path) ? 600 : 400\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this)\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mx: 2,\n          my: 1,\n          bgcolor: 'rgba(255, 255, 255, 0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this), renderMenuSection('👥 Student Management', studentManagementItems, 'students'), renderMenuSection('📚 Academic Management', academicItems, 'academics'), renderMenuSection('👨‍🏫 Staff Management', staffItems, 'staff'), renderMenuSection('💰 Financial Management', financialItems, 'finance'), renderMenuSection('🚌 Operations', operationsItems, 'operations'), renderMenuSection('📢 Communication', communicationItems, 'communication'), renderMenuSection('📄 Documents & Reports', documentsItems, 'documents'), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mx: 2,\n          my: 1,\n          bgcolor: 'rgba(255, 255, 255, 0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"\\uD83D\\uDC64 Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          sx: {\n            padding: 0\n          },\n          children: userMenuItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(StyledListItemButton, {\n              component: Link,\n              to: item.path,\n              active: isActive(item.path),\n              onClick: isMobile ? onClose : undefined,\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                sx: {\n                  color: 'inherit',\n                  minWidth: 40\n                },\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: item.text,\n                primaryTypographyProps: {\n                  fontSize: '0.9rem',\n                  fontWeight: isActive(item.path) ? 600 : 400\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this)\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 370,\n    columnNumber: 5\n  }, this);\n  if (isMobile) {\n    return /*#__PURE__*/_jsxDEV(StyledDrawer, {\n      variant: \"temporary\",\n      open: open,\n      onClose: onClose,\n      ModalProps: {\n        keepMounted: true // Better open performance on mobile.\n      },\n\n      sx: {\n        '& .MuiDrawer-paper': {\n          width: 280\n        }\n      },\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(StyledDrawer, {\n    variant: variant,\n    open: open,\n    sx: {\n      '& .MuiDrawer-paper': {\n        width: open ? 280 : 70,\n        transition: theme.transitions.create('width', {\n          easing: theme.transitions.easing.sharp,\n          duration: theme.transitions.duration.enteringScreen\n        }),\n        overflowX: 'hidden'\n      }\n    },\n    children: drawerContent\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 506,\n    columnNumber: 5\n  }, this);\n};\n_s(ResponsiveSidebar, \"U90N/3nl5x6OagxkeqRFUyhKzn0=\", false, function () {\n  return [useTheme, useMediaQuery, useLocation];\n});\n_c6 = ResponsiveSidebar;\nexport default ResponsiveSidebar;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"StyledDrawer\");\n$RefreshReg$(_c2, \"SidebarHeader\");\n$RefreshReg$(_c3, \"StyledListItemButton\");\n$RefreshReg$(_c4, \"MenuSection\");\n$RefreshReg$(_c5, \"SectionTitle\");\n$RefreshReg$(_c6, \"ResponsiveSidebar\");", "map": {"version": 3, "names": ["React", "Drawer", "List", "ListItemButton", "ListItemIcon", "ListItemText", "Divider", "Box", "Typography", "useTheme", "useMediaQuery", "Avatar", "Chip", "Collapse", "styled", "motion", "Link", "useLocation", "Home", "HomeIcon", "Class", "ClassIcon", "Assignment", "AssignmentIcon", "SupervisorAccount", "SupervisorAccountIcon", "PersonOutline", "PersonOutlineIcon", "Announcement", "AnnouncementIcon", "Report", "ReportIcon", "AccountCircleOutlined", "AccountCircleOutlinedIcon", "ExitToApp", "ExitToAppIcon", "School", "SchoolIcon", "Info", "InfoIcon", "Payment", "PaymentIcon", "Description", "DescriptionIcon", "DirectionsBus", "DirectionsBusIcon", "People", "PeopleIcon", "PersonAdd", "PersonAddIcon", "EventAvailable", "EventAvailableIcon", "Quiz", "QuizIcon", "MenuBook", "MenuBookIcon", "NotificationsActive", "NotificationsActiveIcon", "Hotel", "HotelIcon", "Web", "WebIcon", "Business", "BusinessIcon", "ExpandLess", "ExpandMore", "jsxDEV", "_jsxDEV", "StyledDrawer", "_ref", "theme", "background", "color", "borderRight", "boxShadow", "<PERSON><PERSON>ilter", "position", "content", "top", "left", "right", "bottom", "pointerEvents", "_c", "SidebarHeader", "_ref2", "padding", "spacing", "display", "alignItems", "gap", "borderBottom", "_c2", "StyledListItemButton", "_ref3", "active", "margin", "borderRadius", "backgroundColor", "border", "transition", "transform", "width", "height", "_c3", "MenuSection", "_ref4", "marginTop", "_c4", "SectionTitle", "_ref5", "fontSize", "fontWeight", "textTransform", "letterSpacing", "opacity", "marginBottom", "_c5", "ResponsiveSidebar", "_ref6", "_s", "open", "onClose", "variant", "isMobile", "breakpoints", "down", "location", "expandedSections", "setExpandedSections", "useState", "handleSectionToggle", "section", "prev", "dashboardItems", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "badge", "studentManagementItems", "academicItems", "staffItems", "financialItems", "operationsItems", "communicationItems", "documentsItems", "userMenuItems", "isActive", "pathname", "startsWith", "renderMenuSection", "title", "items", "sectionKey", "children", "onClick", "sx", "justifyContent", "mb", "in", "timeout", "unmountOnExit", "component", "pl", "map", "item", "index", "div", "initial", "x", "animate", "delay", "to", "undefined", "min<PERSON><PERSON><PERSON>", "primary", "primaryTypographyProps", "label", "size", "bgcolor", "drawerContent", "flexDirection", "flex", "overflowY", "py", "mx", "my", "ModalProps", "keepMounted", "transitions", "create", "easing", "sharp", "duration", "enteringScreen", "overflowX", "_c6", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/components/layout/ResponsiveSidebar.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Drawer,\n  List,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Box,\n  Typography,\n  useTheme,\n  useMediaQuery,\n  Avatar,\n  Chip,\n  Collapse\n} from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport {\n  Home as HomeIcon,\n  Class as ClassIcon,\n  Assignment as AssignmentIcon,\n  SupervisorAccount as SupervisorAccountIcon,\n  PersonOutline as PersonOutlineIcon,\n  Announcement as AnnouncementIcon,\n  Report as ReportIcon,\n  AccountCircleOutlined as AccountCircleOutlinedIcon,\n  ExitToApp as ExitToAppIcon,\n  School as SchoolIcon,\n  // New icons for additional modules\n  Info as InfoIcon,\n  Payment as PaymentIcon,\n  Description as DescriptionIcon,\n  DirectionsBus as DirectionsBusIcon,\n  People as PeopleIcon,\n  PersonAdd as PersonAddIcon,\n  EventAvailable as EventAvailableIcon,\n  Quiz as QuizIcon,\n  MenuBook as MenuBookIcon,\n  NotificationsActive as NotificationsActiveIcon,\n  Hotel as HotelIcon,\n  Web as WebIcon,\n  Business as BusinessIcon,\n  ExpandLess,\n  ExpandMore,\n} from '@mui/icons-material';\n\nconst StyledDrawer = styled(Drawer)(({ theme }) => ({\n  '& .MuiDrawer-paper': {\n    background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%)',\n    color: 'white',\n    borderRight: 'none',\n    boxShadow: '8px 0 32px rgba(0, 0, 0, 0.15)',\n    backdropFilter: 'blur(10px)',\n    position: 'relative',\n    '&::before': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n      pointerEvents: 'none',\n    }\n  },\n}));\n\nconst SidebarHeader = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(3, 2),\n  display: 'flex',\n  alignItems: 'center',\n  gap: theme.spacing(2),\n  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\n}));\n\nconst StyledListItemButton = styled(ListItemButton)(({ theme, active }) => ({\n  margin: theme.spacing(0.5, 1),\n  borderRadius: '12px',\n  padding: theme.spacing(1.5, 2),\n  backgroundColor: active ? 'rgba(255, 255, 255, 0.15)' : 'transparent',\n  backdropFilter: active ? 'blur(10px)' : 'none',\n  border: active ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid transparent',\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    backgroundColor: 'rgba(255, 255, 255, 0.1)',\n    transform: 'translateX(4px)',\n  },\n  '&:before': {\n    content: '\"\"',\n    position: 'absolute',\n    left: 0,\n    top: '50%',\n    transform: 'translateY(-50%)',\n    width: active ? '4px' : '0px',\n    height: '60%',\n    backgroundColor: '#FFD700',\n    borderRadius: '0 4px 4px 0',\n    transition: 'width 0.3s ease',\n  },\n}));\n\nconst MenuSection = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(1, 2),\n  marginTop: theme.spacing(2),\n}));\n\nconst SectionTitle = styled(Typography)(({ theme }) => ({\n  fontSize: '0.75rem',\n  fontWeight: 600,\n  textTransform: 'uppercase',\n  letterSpacing: '0.5px',\n  opacity: 0.7,\n  marginBottom: theme.spacing(1),\n}));\n\nconst ResponsiveSidebar = ({ open, onClose, variant = 'permanent' }) => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const location = useLocation();\n  const [expandedSections, setExpandedSections] = React.useState({});\n\n  const handleSectionToggle = (section) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [section]: !prev[section]\n    }));\n  };\n\n  // Main Dashboard\n  const dashboardItems = [\n    {\n      text: 'Dashboard',\n      icon: <HomeIcon />,\n      path: '/Admin/dashboard',\n      badge: null\n    },\n  ];\n\n  // Student Management\n  const studentManagementItems = [\n    {\n      text: 'Student Info',\n      icon: <InfoIcon />,\n      path: '/Admin/student-info',\n      badge: null\n    },\n    {\n      text: 'Students',\n      icon: <PersonOutlineIcon />,\n      path: '/Admin/students',\n      badge: null\n    },\n    {\n      text: 'Admission',\n      icon: <PersonAddIcon />,\n      path: '/Admin/admission',\n      badge: '5'\n    },\n  ];\n\n  // Academic Management\n  const academicItems = [\n    {\n      text: 'Classes',\n      icon: <ClassIcon />,\n      path: '/Admin/classes',\n      badge: null\n    },\n    {\n      text: 'Subjects',\n      icon: <AssignmentIcon />,\n      path: '/Admin/subjects',\n      badge: null\n    },\n    {\n      text: 'Academics',\n      icon: <MenuBookIcon />,\n      path: '/Admin/academics',\n      badge: null\n    },\n    {\n      text: 'Examination',\n      icon: <QuizIcon />,\n      path: '/Admin/examination',\n      badge: '2'\n    },\n  ];\n\n  // Staff Management\n  const staffItems = [\n    {\n      text: 'Teachers',\n      icon: <SupervisorAccountIcon />,\n      path: '/Admin/teachers',\n      badge: null\n    },\n    {\n      text: 'Attendance',\n      icon: <EventAvailableIcon />,\n      path: '/Admin/attendance',\n      badge: null\n    },\n  ];\n\n  // Financial Management\n  const financialItems = [\n    {\n      text: 'Fee Management',\n      icon: <PaymentIcon />,\n      path: '/Admin/fee-management',\n      badge: '12'\n    },\n    {\n      text: 'Fee Due',\n      icon: <PaymentIcon />,\n      path: '/Admin/fee-due',\n      badge: '8'\n    },\n  ];\n\n  // Operations\n  const operationsItems = [\n    {\n      text: 'Transport',\n      icon: <DirectionsBusIcon />,\n      path: '/Admin/transport',\n      badge: null\n    },\n    {\n      text: 'Hostel',\n      icon: <HotelIcon />,\n      path: '/Admin/hostel',\n      badge: '3'\n    },\n    {\n      text: 'Front Office',\n      icon: <BusinessIcon />,\n      path: '/Admin/front-office',\n      badge: null\n    },\n  ];\n\n  // Communication\n  const communicationItems = [\n    {\n      text: 'Notifications',\n      icon: <NotificationsActiveIcon />,\n      path: '/Admin/notifications',\n      badge: '15'\n    },\n    {\n      text: 'Notices',\n      icon: <AnnouncementIcon />,\n      path: '/Admin/notices',\n      badge: '3'\n    },\n    {\n      text: 'CMS',\n      icon: <WebIcon />,\n      path: '/Admin/cms',\n      badge: null\n    },\n  ];\n\n  // Documents & Reports\n  const documentsItems = [\n    {\n      text: 'Documents',\n      icon: <DescriptionIcon />,\n      path: '/Admin/documents',\n      badge: null\n    },\n    {\n      text: 'Complaints',\n      icon: <ReportIcon />,\n      path: '/Admin/complains',\n      badge: '2'\n    },\n  ];\n\n  const userMenuItems = [\n    {\n      text: 'Profile',\n      icon: <AccountCircleOutlinedIcon />,\n      path: '/Admin/profile',\n      badge: null\n    },\n    {\n      text: 'Logout',\n      icon: <ExitToAppIcon />,\n      path: '/logout',\n      badge: null\n    },\n  ];\n\n  const isActive = (path) => {\n    if (path === '/Admin/dashboard') {\n      return location.pathname === '/' || location.pathname === '/Admin/dashboard';\n    }\n    return location.pathname.startsWith(path);\n  };\n\n  const renderMenuSection = (title, items, sectionKey) => (\n    <MenuSection key={sectionKey}>\n      <StyledListItemButton\n        onClick={() => handleSectionToggle(sectionKey)}\n        sx={{\n          justifyContent: 'space-between',\n          backgroundColor: 'rgba(255, 255, 255, 0.05)',\n          mb: 1,\n          '&:hover': {\n            backgroundColor: 'rgba(255, 255, 255, 0.1)',\n          }\n        }}\n      >\n        <SectionTitle>{title}</SectionTitle>\n        {expandedSections[sectionKey] ? <ExpandLess /> : <ExpandMore />}\n      </StyledListItemButton>\n\n      <Collapse in={expandedSections[sectionKey]} timeout=\"auto\" unmountOnExit>\n        <List component=\"nav\" sx={{ padding: 0, pl: 1 }}>\n          {items.map((item, index) => (\n            <motion.div\n              key={item.text}\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: index * 0.05 }}\n            >\n              <StyledListItemButton\n                component={Link}\n                to={item.path}\n                active={isActive(item.path)}\n                onClick={isMobile ? onClose : undefined}\n                sx={{ pl: 2 }}\n              >\n                <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>\n                  {item.icon}\n                </ListItemIcon>\n                <ListItemText\n                  primary={item.text}\n                  primaryTypographyProps={{\n                    fontSize: '0.85rem',\n                    fontWeight: isActive(item.path) ? 600 : 400,\n                  }}\n                />\n                {item.badge && (\n                  <Chip\n                    label={item.badge}\n                    size=\"small\"\n                    sx={{\n                      bgcolor: '#FFD700',\n                      color: '#333',\n                      fontSize: '0.7rem',\n                      height: 18,\n                      minWidth: 18,\n                    }}\n                  />\n                )}\n              </StyledListItemButton>\n            </motion.div>\n          ))}\n        </List>\n      </Collapse>\n    </MenuSection>\n  );\n\n  const drawerContent = (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <SidebarHeader>\n        <Avatar\n          sx={{\n            bgcolor: 'rgba(255, 255, 255, 0.2)',\n            width: 48,\n            height: 48,\n          }}\n        >\n          <SchoolIcon />\n        </Avatar>\n        <Box>\n          <Typography variant=\"h6\" fontWeight=\"bold\">\n            School Admin\n          </Typography>\n          <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n            Management System\n          </Typography>\n        </Box>\n      </SidebarHeader>\n\n      <Box sx={{ flex: 1, overflowY: 'auto', py: 1 }}>\n        {/* Dashboard */}\n        <MenuSection>\n          <List component=\"nav\" sx={{ padding: 0 }}>\n            {dashboardItems.map((item, index) => (\n              <motion.div\n                key={item.text}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.1 }}\n              >\n                <StyledListItemButton\n                  component={Link}\n                  to={item.path}\n                  active={isActive(item.path)}\n                  onClick={isMobile ? onClose : undefined}\n                >\n                  <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>\n                    {item.icon}\n                  </ListItemIcon>\n                  <ListItemText\n                    primary={item.text}\n                    primaryTypographyProps={{\n                      fontSize: '0.9rem',\n                      fontWeight: isActive(item.path) ? 600 : 400,\n                    }}\n                  />\n                </StyledListItemButton>\n              </motion.div>\n            ))}\n          </List>\n        </MenuSection>\n\n        <Divider sx={{ mx: 2, my: 1, bgcolor: 'rgba(255, 255, 255, 0.1)' }} />\n\n        {/* Student Management */}\n        {renderMenuSection('👥 Student Management', studentManagementItems, 'students')}\n\n        {/* Academic Management */}\n        {renderMenuSection('📚 Academic Management', academicItems, 'academics')}\n\n        {/* Staff Management */}\n        {renderMenuSection('👨‍🏫 Staff Management', staffItems, 'staff')}\n\n        {/* Financial Management */}\n        {renderMenuSection('💰 Financial Management', financialItems, 'finance')}\n\n        {/* Operations */}\n        {renderMenuSection('🚌 Operations', operationsItems, 'operations')}\n\n        {/* Communication */}\n        {renderMenuSection('📢 Communication', communicationItems, 'communication')}\n\n        {/* Documents & Reports */}\n        {renderMenuSection('📄 Documents & Reports', documentsItems, 'documents')}\n\n        <Divider sx={{ mx: 2, my: 1, bgcolor: 'rgba(255, 255, 255, 0.1)' }} />\n\n        {/* User Account */}\n        <MenuSection>\n          <SectionTitle>👤 Account</SectionTitle>\n          <List component=\"nav\" sx={{ padding: 0 }}>\n            {userMenuItems.map((item, index) => (\n              <motion.div\n                key={item.text}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.1 }}\n              >\n                <StyledListItemButton\n                  component={Link}\n                  to={item.path}\n                  active={isActive(item.path)}\n                  onClick={isMobile ? onClose : undefined}\n                >\n                  <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>\n                    {item.icon}\n                  </ListItemIcon>\n                  <ListItemText\n                    primary={item.text}\n                    primaryTypographyProps={{\n                      fontSize: '0.9rem',\n                      fontWeight: isActive(item.path) ? 600 : 400,\n                    }}\n                  />\n                </StyledListItemButton>\n              </motion.div>\n            ))}\n          </List>\n        </MenuSection>\n      </Box>\n    </Box>\n  );\n\n  if (isMobile) {\n    return (\n      <StyledDrawer\n        variant=\"temporary\"\n        open={open}\n        onClose={onClose}\n        ModalProps={{\n          keepMounted: true, // Better open performance on mobile.\n        }}\n        sx={{\n          '& .MuiDrawer-paper': {\n            width: 280,\n          },\n        }}\n      >\n        {drawerContent}\n      </StyledDrawer>\n    );\n  }\n\n  return (\n    <StyledDrawer\n      variant={variant}\n      open={open}\n      sx={{\n        '& .MuiDrawer-paper': {\n          width: open ? 280 : 70,\n          transition: theme.transitions.create('width', {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.enteringScreen,\n          }),\n          overflowX: 'hidden',\n        },\n      }}\n    >\n      {drawerContent}\n    </StyledDrawer>\n  );\n};\n\nexport default ResponsiveSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,IAAI,EACJC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,aAAa,EACbC,MAAM,EACNC,IAAI,EACJC,QAAQ,QACH,eAAe;AACtB,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SACEC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,iBAAiB,IAAIC,qBAAqB,EAC1CC,aAAa,IAAIC,iBAAiB,EAClCC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,qBAAqB,IAAIC,yBAAyB,EAClDC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU;AACpB;AACAC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,eAAe,EAC9BC,aAAa,IAAIC,iBAAiB,EAClCC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,cAAc,IAAIC,kBAAkB,EACpCC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,mBAAmB,IAAIC,uBAAuB,EAC9CC,KAAK,IAAIC,SAAS,EAClBC,GAAG,IAAIC,OAAO,EACdC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,EACVC,UAAU,QACL,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,YAAY,GAAGtD,MAAM,CAACb,MAAM,CAAC,CAACoE,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IAClD,oBAAoB,EAAE;MACpBE,UAAU,EAAE,gEAAgE;MAC5EC,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE,gCAAgC;MAC3CC,cAAc,EAAE,YAAY;MAC5BC,QAAQ,EAAE,UAAU;MACpB,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbD,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTV,UAAU,EAAE,gFAAgF;QAC5FW,aAAa,EAAE;MACjB;IACF;EACF,CAAC;AAAA,CAAC,CAAC;AAACC,EAAA,GAnBEf,YAAY;AAqBlB,MAAMgB,aAAa,GAAGtE,MAAM,CAACP,GAAG,CAAC,CAAC8E,KAAA;EAAA,IAAC;IAAEf;EAAM,CAAC,GAAAe,KAAA;EAAA,OAAM;IAChDC,OAAO,EAAEhB,KAAK,CAACiB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5BC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAEpB,KAAK,CAACiB,OAAO,CAAC,CAAC,CAAC;IACrBI,YAAY,EAAE;EAChB,CAAC;AAAA,CAAC,CAAC;AAACC,GAAA,GANER,aAAa;AAQnB,MAAMS,oBAAoB,GAAG/E,MAAM,CAACX,cAAc,CAAC,CAAC2F,KAAA;EAAA,IAAC;IAAExB,KAAK;IAAEyB;EAAO,CAAC,GAAAD,KAAA;EAAA,OAAM;IAC1EE,MAAM,EAAE1B,KAAK,CAACiB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IAC7BU,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAEhB,KAAK,CAACiB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IAC9BW,eAAe,EAAEH,MAAM,GAAG,2BAA2B,GAAG,aAAa;IACrEpB,cAAc,EAAEoB,MAAM,GAAG,YAAY,GAAG,MAAM;IAC9CI,MAAM,EAAEJ,MAAM,GAAG,oCAAoC,GAAG,uBAAuB;IAC/EK,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTF,eAAe,EAAE,0BAA0B;MAC3CG,SAAS,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACVxB,OAAO,EAAE,IAAI;MACbD,QAAQ,EAAE,UAAU;MACpBG,IAAI,EAAE,CAAC;MACPD,GAAG,EAAE,KAAK;MACVuB,SAAS,EAAE,kBAAkB;MAC7BC,KAAK,EAAEP,MAAM,GAAG,KAAK,GAAG,KAAK;MAC7BQ,MAAM,EAAE,KAAK;MACbL,eAAe,EAAE,SAAS;MAC1BD,YAAY,EAAE,aAAa;MAC3BG,UAAU,EAAE;IACd;EACF,CAAC;AAAA,CAAC,CAAC;AAACI,GAAA,GAxBEX,oBAAoB;AA0B1B,MAAMY,WAAW,GAAG3F,MAAM,CAACP,GAAG,CAAC,CAACmG,KAAA;EAAA,IAAC;IAAEpC;EAAM,CAAC,GAAAoC,KAAA;EAAA,OAAM;IAC9CpB,OAAO,EAAEhB,KAAK,CAACiB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5BoB,SAAS,EAAErC,KAAK,CAACiB,OAAO,CAAC,CAAC;EAC5B,CAAC;AAAA,CAAC,CAAC;AAACqB,GAAA,GAHEH,WAAW;AAKjB,MAAMI,YAAY,GAAG/F,MAAM,CAACN,UAAU,CAAC,CAACsG,KAAA;EAAA,IAAC;IAAExC;EAAM,CAAC,GAAAwC,KAAA;EAAA,OAAM;IACtDC,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfC,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE,OAAO;IACtBC,OAAO,EAAE,GAAG;IACZC,YAAY,EAAE9C,KAAK,CAACiB,OAAO,CAAC,CAAC;EAC/B,CAAC;AAAA,CAAC,CAAC;AAAC8B,GAAA,GAPER,YAAY;AASlB,MAAMS,iBAAiB,GAAGC,KAAA,IAA8C;EAAAC,EAAA;EAAA,IAA7C;IAAEC,IAAI;IAAEC,OAAO;IAAEC,OAAO,GAAG;EAAY,CAAC,GAAAJ,KAAA;EACjE,MAAMjD,KAAK,GAAG7D,QAAQ,EAAE;EACxB,MAAMmH,QAAQ,GAAGlH,aAAa,CAAC4D,KAAK,CAACuD,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAMC,QAAQ,GAAG9G,WAAW,EAAE;EAC9B,MAAM,CAAC+G,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjI,KAAK,CAACkI,QAAQ,CAAC,CAAC,CAAC,CAAC;EAElE,MAAMC,mBAAmB,GAAIC,OAAO,IAAK;IACvCH,mBAAmB,CAACI,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAACD,OAAO,GAAG,CAACC,IAAI,CAACD,OAAO;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,cAAc,GAAG,CACrB;IACEC,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAErE,OAAA,CAAChD,QAAQ;MAAAsH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAClBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMC,sBAAsB,GAAG,CAC7B;IACER,IAAI,EAAE,cAAc;IACpBC,IAAI,eAAErE,OAAA,CAAC5B,QAAQ;MAAAkG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAClBC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAErE,OAAA,CAACxC,iBAAiB;MAAA8G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC3BC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAErE,OAAA,CAAClB,aAAa;MAAAwF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACvBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAME,aAAa,GAAG,CACpB;IACET,IAAI,EAAE,SAAS;IACfC,IAAI,eAAErE,OAAA,CAAC9C,SAAS;MAAAoH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnBC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAErE,OAAA,CAAC5C,cAAc;MAAAkH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACxBC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAErE,OAAA,CAACZ,YAAY;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACtBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,aAAa;IACnBC,IAAI,eAAErE,OAAA,CAACd,QAAQ;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAClBC,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMG,UAAU,GAAG,CACjB;IACEV,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAErE,OAAA,CAAC1C,qBAAqB;MAAAgH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC/BC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,eAAErE,OAAA,CAAChB,kBAAkB;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC5BC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMI,cAAc,GAAG,CACrB;IACEX,IAAI,EAAE,gBAAgB;IACtBC,IAAI,eAAErE,OAAA,CAAC1B,WAAW;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACrBC,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,eAAErE,OAAA,CAAC1B,WAAW;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACrBC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMK,eAAe,GAAG,CACtB;IACEZ,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAErE,OAAA,CAACtB,iBAAiB;MAAA4F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC3BC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdC,IAAI,eAAErE,OAAA,CAACR,SAAS;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnBC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,eAAErE,OAAA,CAACJ,YAAY;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACtBC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMM,kBAAkB,GAAG,CACzB;IACEb,IAAI,EAAE,eAAe;IACrBC,IAAI,eAAErE,OAAA,CAACV,uBAAuB;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACjCC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,eAAErE,OAAA,CAACtC,gBAAgB;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC1BC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,KAAK;IACXC,IAAI,eAAErE,OAAA,CAACN,OAAO;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACjBC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMO,cAAc,GAAG,CACrB;IACEd,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAErE,OAAA,CAACxB,eAAe;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,eAAErE,OAAA,CAACpC,UAAU;MAAA0G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACpBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMQ,aAAa,GAAG,CACpB;IACEf,IAAI,EAAE,SAAS;IACfC,IAAI,eAAErE,OAAA,CAAClC,yBAAyB;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnCC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdC,IAAI,eAAErE,OAAA,CAAChC,aAAa;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMS,QAAQ,GAAIV,IAAI,IAAK;IACzB,IAAIA,IAAI,KAAK,kBAAkB,EAAE;MAC/B,OAAOd,QAAQ,CAACyB,QAAQ,KAAK,GAAG,IAAIzB,QAAQ,CAACyB,QAAQ,KAAK,kBAAkB;IAC9E;IACA,OAAOzB,QAAQ,CAACyB,QAAQ,CAACC,UAAU,CAACZ,IAAI,CAAC;EAC3C,CAAC;EAED,MAAMa,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,UAAU,kBACjD1F,OAAA,CAACsC,WAAW;IAAAqD,QAAA,gBACV3F,OAAA,CAAC0B,oBAAoB;MACnBkE,OAAO,EAAEA,CAAA,KAAM5B,mBAAmB,CAAC0B,UAAU,CAAE;MAC/CG,EAAE,EAAE;QACFC,cAAc,EAAE,eAAe;QAC/B/D,eAAe,EAAE,2BAA2B;QAC5CgE,EAAE,EAAE,CAAC;QACL,SAAS,EAAE;UACThE,eAAe,EAAE;QACnB;MACF,CAAE;MAAA4D,QAAA,gBAEF3F,OAAA,CAAC0C,YAAY;QAAAiD,QAAA,EAAEH;MAAK;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAgB,EACnCZ,gBAAgB,CAAC6B,UAAU,CAAC,gBAAG1F,OAAA,CAACH,UAAU;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,gBAAGzE,OAAA,CAACF,UAAU;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC1C,eAEvBzE,OAAA,CAACtD,QAAQ;MAACsJ,EAAE,EAAEnC,gBAAgB,CAAC6B,UAAU,CAAE;MAACO,OAAO,EAAC,MAAM;MAACC,aAAa;MAAAP,QAAA,eACtE3F,OAAA,CAACjE,IAAI;QAACoK,SAAS,EAAC,KAAK;QAACN,EAAE,EAAE;UAAE1E,OAAO,EAAE,CAAC;UAAEiF,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,EAC7CF,KAAK,CAACY,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBvG,OAAA,CAACpD,MAAM,CAAC4J,GAAG;UAETC,OAAO,EAAE;YAAEzD,OAAO,EAAE,CAAC;YAAE0D,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAE3D,OAAO,EAAE,CAAC;YAAE0D,CAAC,EAAE;UAAE,CAAE;UAC9BzE,UAAU,EAAE;YAAE2E,KAAK,EAAEL,KAAK,GAAG;UAAK,CAAE;UAAAZ,QAAA,eAEpC3F,OAAA,CAAC0B,oBAAoB;YACnByE,SAAS,EAAEtJ,IAAK;YAChBgK,EAAE,EAAEP,IAAI,CAAC5B,IAAK;YACd9C,MAAM,EAAEwD,QAAQ,CAACkB,IAAI,CAAC5B,IAAI,CAAE;YAC5BkB,OAAO,EAAEnC,QAAQ,GAAGF,OAAO,GAAGuD,SAAU;YACxCjB,EAAE,EAAE;cAAEO,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,gBAEd3F,OAAA,CAAC/D,YAAY;cAAC4J,EAAE,EAAE;gBAAExF,KAAK,EAAE,SAAS;gBAAE0G,QAAQ,EAAE;cAAG,CAAE;cAAApB,QAAA,EAClDW,IAAI,CAACjC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACG,eACfzE,OAAA,CAAC9D,YAAY;cACX8K,OAAO,EAAEV,IAAI,CAAClC,IAAK;cACnB6C,sBAAsB,EAAE;gBACtBrE,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAEuC,QAAQ,CAACkB,IAAI,CAAC5B,IAAI,CAAC,GAAG,GAAG,GAAG;cAC1C;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACF,EACD6B,IAAI,CAAC3B,KAAK,iBACT3E,OAAA,CAACvD,IAAI;cACHyK,KAAK,EAAEZ,IAAI,CAAC3B,KAAM;cAClBwC,IAAI,EAAC,OAAO;cACZtB,EAAE,EAAE;gBACFuB,OAAO,EAAE,SAAS;gBAClB/G,KAAK,EAAE,MAAM;gBACbuC,QAAQ,EAAE,QAAQ;gBAClBR,MAAM,EAAE,EAAE;gBACV2E,QAAQ,EAAE;cACZ;YAAE;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAEL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACoB,GAnClB6B,IAAI,CAAClC,IAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAqCjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACG;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACE;EAAA,GA3DKiB,UAAU;IAAApB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QA6D7B;EAED,MAAM4C,aAAa,gBACjBrH,OAAA,CAAC5D,GAAG;IAACyJ,EAAE,EAAE;MAAEzD,MAAM,EAAE,MAAM;MAAEf,OAAO,EAAE,MAAM;MAAEiG,aAAa,EAAE;IAAS,CAAE;IAAA3B,QAAA,gBACpE3F,OAAA,CAACiB,aAAa;MAAA0E,QAAA,gBACZ3F,OAAA,CAACxD,MAAM;QACLqJ,EAAE,EAAE;UACFuB,OAAO,EAAE,0BAA0B;UACnCjF,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE;QACV,CAAE;QAAAuD,QAAA,eAEF3F,OAAA,CAAC9B,UAAU;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACP,eACTzE,OAAA,CAAC5D,GAAG;QAAAuJ,QAAA,gBACF3F,OAAA,CAAC3D,UAAU;UAACmH,OAAO,EAAC,IAAI;UAACX,UAAU,EAAC,MAAM;UAAA8C,QAAA,EAAC;QAE3C;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACbzE,OAAA,CAAC3D,UAAU;UAACmH,OAAO,EAAC,SAAS;UAACqC,EAAE,EAAE;YAAE7C,OAAO,EAAE;UAAI,CAAE;UAAA2C,QAAA,EAAC;QAEpD;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACQ,eAEhBzE,OAAA,CAAC5D,GAAG;MAACyJ,EAAE,EAAE;QAAE0B,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE,MAAM;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA9B,QAAA,gBAE7C3F,OAAA,CAACsC,WAAW;QAAAqD,QAAA,eACV3F,OAAA,CAACjE,IAAI;UAACoK,SAAS,EAAC,KAAK;UAACN,EAAE,EAAE;YAAE1E,OAAO,EAAE;UAAE,CAAE;UAAAwE,QAAA,EACtCxB,cAAc,CAACkC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC9BvG,OAAA,CAACpD,MAAM,CAAC4J,GAAG;YAETC,OAAO,EAAE;cAAEzD,OAAO,EAAE,CAAC;cAAE0D,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAE3D,OAAO,EAAE,CAAC;cAAE0D,CAAC,EAAE;YAAE,CAAE;YAC9BzE,UAAU,EAAE;cAAE2E,KAAK,EAAEL,KAAK,GAAG;YAAI,CAAE;YAAAZ,QAAA,eAEnC3F,OAAA,CAAC0B,oBAAoB;cACnByE,SAAS,EAAEtJ,IAAK;cAChBgK,EAAE,EAAEP,IAAI,CAAC5B,IAAK;cACd9C,MAAM,EAAEwD,QAAQ,CAACkB,IAAI,CAAC5B,IAAI,CAAE;cAC5BkB,OAAO,EAAEnC,QAAQ,GAAGF,OAAO,GAAGuD,SAAU;cAAAnB,QAAA,gBAExC3F,OAAA,CAAC/D,YAAY;gBAAC4J,EAAE,EAAE;kBAAExF,KAAK,EAAE,SAAS;kBAAE0G,QAAQ,EAAE;gBAAG,CAAE;gBAAApB,QAAA,EAClDW,IAAI,CAACjC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACG,eACfzE,OAAA,CAAC9D,YAAY;gBACX8K,OAAO,EAAEV,IAAI,CAAClC,IAAK;gBACnB6C,sBAAsB,EAAE;kBACtBrE,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAEuC,QAAQ,CAACkB,IAAI,CAAC5B,IAAI,CAAC,GAAG,GAAG,GAAG;gBAC1C;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACmB,GArBlB6B,IAAI,CAAClC,IAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAuBjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACK,eAEdzE,OAAA,CAAC7D,OAAO;QAAC0J,EAAE,EAAE;UAAE6B,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEP,OAAO,EAAE;QAA2B;MAAE;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,EAGrEc,iBAAiB,CAAC,uBAAuB,EAAEX,sBAAsB,EAAE,UAAU,CAAC,EAG9EW,iBAAiB,CAAC,wBAAwB,EAAEV,aAAa,EAAE,WAAW,CAAC,EAGvEU,iBAAiB,CAAC,wBAAwB,EAAET,UAAU,EAAE,OAAO,CAAC,EAGhES,iBAAiB,CAAC,yBAAyB,EAAER,cAAc,EAAE,SAAS,CAAC,EAGvEQ,iBAAiB,CAAC,eAAe,EAAEP,eAAe,EAAE,YAAY,CAAC,EAGjEO,iBAAiB,CAAC,kBAAkB,EAAEN,kBAAkB,EAAE,eAAe,CAAC,EAG1EM,iBAAiB,CAAC,wBAAwB,EAAEL,cAAc,EAAE,WAAW,CAAC,eAEzElF,OAAA,CAAC7D,OAAO;QAAC0J,EAAE,EAAE;UAAE6B,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEP,OAAO,EAAE;QAA2B;MAAE;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAGtEzE,OAAA,CAACsC,WAAW;QAAAqD,QAAA,gBACV3F,OAAA,CAAC0C,YAAY;UAAAiD,QAAA,EAAC;QAAU;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAe,eACvCzE,OAAA,CAACjE,IAAI;UAACoK,SAAS,EAAC,KAAK;UAACN,EAAE,EAAE;YAAE1E,OAAO,EAAE;UAAE,CAAE;UAAAwE,QAAA,EACtCR,aAAa,CAACkB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7BvG,OAAA,CAACpD,MAAM,CAAC4J,GAAG;YAETC,OAAO,EAAE;cAAEzD,OAAO,EAAE,CAAC;cAAE0D,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAE3D,OAAO,EAAE,CAAC;cAAE0D,CAAC,EAAE;YAAE,CAAE;YAC9BzE,UAAU,EAAE;cAAE2E,KAAK,EAAEL,KAAK,GAAG;YAAI,CAAE;YAAAZ,QAAA,eAEnC3F,OAAA,CAAC0B,oBAAoB;cACnByE,SAAS,EAAEtJ,IAAK;cAChBgK,EAAE,EAAEP,IAAI,CAAC5B,IAAK;cACd9C,MAAM,EAAEwD,QAAQ,CAACkB,IAAI,CAAC5B,IAAI,CAAE;cAC5BkB,OAAO,EAAEnC,QAAQ,GAAGF,OAAO,GAAGuD,SAAU;cAAAnB,QAAA,gBAExC3F,OAAA,CAAC/D,YAAY;gBAAC4J,EAAE,EAAE;kBAAExF,KAAK,EAAE,SAAS;kBAAE0G,QAAQ,EAAE;gBAAG,CAAE;gBAAApB,QAAA,EAClDW,IAAI,CAACjC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACG,eACfzE,OAAA,CAAC9D,YAAY;gBACX8K,OAAO,EAAEV,IAAI,CAAClC,IAAK;gBACnB6C,sBAAsB,EAAE;kBACtBrE,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAEuC,QAAQ,CAACkB,IAAI,CAAC5B,IAAI,CAAC,GAAG,GAAG,GAAG;gBAC1C;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACmB,GArBlB6B,IAAI,CAAClC,IAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAuBjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACK;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACV;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAET;EAED,IAAIhB,QAAQ,EAAE;IACZ,oBACEzD,OAAA,CAACC,YAAY;MACXuD,OAAO,EAAC,WAAW;MACnBF,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAEA,OAAQ;MACjBqE,UAAU,EAAE;QACVC,WAAW,EAAE,IAAI,CAAE;MACrB,CAAE;;MACFhC,EAAE,EAAE;QACF,oBAAoB,EAAE;UACpB1D,KAAK,EAAE;QACT;MACF,CAAE;MAAAwD,QAAA,EAED0B;IAAa;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD;EAEnB;EAEA,oBACEzE,OAAA,CAACC,YAAY;IACXuD,OAAO,EAAEA,OAAQ;IACjBF,IAAI,EAAEA,IAAK;IACXuC,EAAE,EAAE;MACF,oBAAoB,EAAE;QACpB1D,KAAK,EAAEmB,IAAI,GAAG,GAAG,GAAG,EAAE;QACtBrB,UAAU,EAAE9B,KAAK,CAAC2H,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;UAC5CC,MAAM,EAAE7H,KAAK,CAAC2H,WAAW,CAACE,MAAM,CAACC,KAAK;UACtCC,QAAQ,EAAE/H,KAAK,CAAC2H,WAAW,CAACI,QAAQ,CAACC;QACvC,CAAC,CAAC;QACFC,SAAS,EAAE;MACb;IACF,CAAE;IAAAzC,QAAA,EAED0B;EAAa;IAAA/C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACD;AAEnB,CAAC;AAACpB,EAAA,CArZIF,iBAAiB;EAAA,QACP7G,QAAQ,EACLC,aAAa,EACbO,WAAW;AAAA;AAAAuL,GAAA,GAHxBlF,iBAAiB;AAuZvB,eAAeA,iBAAiB;AAAC,IAAAnC,EAAA,EAAAS,GAAA,EAAAY,GAAA,EAAAI,GAAA,EAAAS,GAAA,EAAAmF,GAAA;AAAAC,YAAA,CAAAtH,EAAA;AAAAsH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}