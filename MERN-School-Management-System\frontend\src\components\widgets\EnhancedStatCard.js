import React from 'react';
import { Paper, Typography, Box, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import CountUp from 'react-countup';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';

const StyledPaper = styled(motion.div)(({ theme, gradient }) => ({
  padding: theme.spacing(2),
  height: '140px',
  background: gradient,
  color: 'white',
  borderRadius: '20px',
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
  cursor: 'pointer',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'rgba(255, 255, 255, 0.1)',
    opacity: 0,
    transition: 'opacity 0.3s ease',
  },
  '&:hover::before': {
    opacity: 1,
  },
}));

const IconContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(1.5),
  right: theme.spacing(1.5),
  opacity: 0.3,
  fontSize: '2.5rem',
}));

const TrendContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginTop: theme.spacing(1),
}));

const EnhancedStatCard = ({ 
  title, 
  value, 
  icon, 
  gradient, 
  trend, 
  trendValue, 
  prefix = '', 
  suffix = '',
  onClick 
}) => {
  const getTrendIcon = () => {
    if (trend === 'up') return <TrendingUpIcon fontSize="small" />;
    if (trend === 'down') return <TrendingDownIcon fontSize="small" />;
    return null;
  };

  const getTrendColor = () => {
    if (trend === 'up') return '#4CAF50';
    if (trend === 'down') return '#F44336';
    return '#FFC107';
  };

  return (
    <StyledPaper
      gradient={gradient}
      whileHover={{ 
        scale: 1.05,
        boxShadow: '0 15px 40px rgba(0, 0, 0, 0.2)'
      }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Paper 
        elevation={0} 
        sx={{ 
          background: 'transparent', 
          color: 'inherit',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between'
        }}
      >
        <Box>
          <Typography
            variant="h6"
            component="h3"
            fontWeight="600"
            sx={{
              fontSize: '0.9rem',
              opacity: 0.9,
              mb: 0.5
            }}
          >
            {title}
          </Typography>

          <Typography
            variant="h3"
            component="div"
            fontWeight="bold"
            sx={{
              fontSize: '2rem',
              lineHeight: 1,
              mb: 0.5
            }}
          >
            {prefix}
            <CountUp 
              end={value} 
              duration={2.5} 
              separator="," 
            />
            {suffix}
          </Typography>
        </Box>

        {(trend || trendValue) && (
          <TrendContainer>
            {getTrendIcon()}
            <Typography 
              variant="body2" 
              sx={{ 
                ml: 0.5,
                fontWeight: 500,
                color: getTrendColor()
              }}
            >
              {trendValue}
            </Typography>
            <Typography 
              variant="caption" 
              sx={{ 
                ml: 1,
                opacity: 0.8
              }}
            >
              vs last month
            </Typography>
          </TrendContainer>
        )}

        <IconContainer>
          {icon}
        </IconContainer>
      </Paper>
    </StyledPaper>
  );
};

export default EnhancedStatCard;
