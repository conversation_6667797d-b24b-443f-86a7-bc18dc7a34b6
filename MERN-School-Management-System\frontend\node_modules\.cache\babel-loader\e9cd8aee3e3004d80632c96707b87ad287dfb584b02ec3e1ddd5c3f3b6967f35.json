{"ast": null, "code": "import { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { isGenerator } from '../../generators/utils/is-generator.mjs';\nfunction applyGeneratorOptions(_ref) {\n  let {\n    type,\n    ...options\n  } = _ref;\n  if (isGenerator(type) && supportsLinearEasing()) {\n    return type.applyToOptions(options);\n  } else {\n    options.duration ?? (options.duration = 300);\n    options.ease ?? (options.ease = \"easeOut\");\n  }\n  return options;\n}\nexport { applyGeneratorOptions };", "map": {"version": 3, "names": ["supportsLinearEasing", "isGenerator", "applyGeneratorOptions", "_ref", "type", "options", "applyToOptions", "duration", "ease"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs"], "sourcesContent": ["import { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { isGenerator } from '../../generators/utils/is-generator.mjs';\n\nfunction applyGeneratorOptions({ type, ...options }) {\n    if (isGenerator(type) && supportsLinearEasing()) {\n        return type.applyToOptions(options);\n    }\n    else {\n        options.duration ?? (options.duration = 300);\n        options.ease ?? (options.ease = \"easeOut\");\n    }\n    return options;\n}\n\nexport { applyGeneratorOptions };\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,WAAW,QAAQ,yCAAyC;AAErE,SAASC,qBAAqBA,CAAAC,IAAA,EAAuB;EAAA,IAAtB;IAAEC,IAAI;IAAE,GAAGC;EAAQ,CAAC,GAAAF,IAAA;EAC/C,IAAIF,WAAW,CAACG,IAAI,CAAC,IAAIJ,oBAAoB,EAAE,EAAE;IAC7C,OAAOI,IAAI,CAACE,cAAc,CAACD,OAAO,CAAC;EACvC,CAAC,MACI;IACDA,OAAO,CAACE,QAAQ,KAAKF,OAAO,CAACE,QAAQ,GAAG,GAAG,CAAC;IAC5CF,OAAO,CAACG,IAAI,KAAKH,OAAO,CAACG,IAAI,GAAG,SAAS,CAAC;EAC9C;EACA,OAAOH,OAAO;AAClB;AAEA,SAASH,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}