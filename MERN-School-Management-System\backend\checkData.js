const mongoose = require('mongoose');
const Admin = require('./models/adminSchema.js');
const Sclass = require('./models/sclassSchema.js');
require('dotenv').config();

const checkData = async () => {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URL, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('Connected to MongoDB');

        // Find the admin user
        const admin = await Admin.findOne({ email: '<EMAIL>' });
        if (!admin) {
            console.log('❌ Admin user not found');
            process.exit(1);
        }

        console.log('✅ Admin found:');
        console.log('   ID:', admin._id.toString());
        console.log('   Email:', admin.email);
        console.log('   School:', admin.schoolName);
        console.log('');

        // Check classes for this admin
        const classes = await Sclass.find({ school: admin._id });
        console.log(`📚 Classes for admin (${classes.length} found):`);
        
        if (classes.length === 0) {
            console.log('   ❌ No classes found for this admin');
        } else {
            classes.forEach((cls, index) => {
                console.log(`   ${index + 1}. ${cls.sclassName} (ID: ${cls._id.toString()})`);
            });
        }

        console.log('');
        console.log('🔍 API Endpoint to test:');
        console.log(`   GET http://localhost:5000/SclassList/${admin._id.toString()}`);
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    }
};

checkData();
