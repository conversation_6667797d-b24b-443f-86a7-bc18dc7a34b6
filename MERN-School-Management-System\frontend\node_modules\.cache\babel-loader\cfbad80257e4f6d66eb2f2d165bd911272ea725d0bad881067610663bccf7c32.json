{"ast": null, "code": "const radToDeg = rad => rad * 180 / Math.PI;\nconst rotate = v => {\n  const angle = radToDeg(Math.atan2(v[1], v[0]));\n  return rebaseAngle(angle);\n};\nconst matrix2dParsers = {\n  x: 4,\n  y: 5,\n  translateX: 4,\n  translateY: 5,\n  scaleX: 0,\n  scaleY: 3,\n  scale: v => (Math.abs(v[0]) + Math.abs(v[3])) / 2,\n  rotate,\n  rotateZ: rotate,\n  skewX: v => radToDeg(Math.atan(v[1])),\n  skewY: v => radToDeg(Math.atan(v[2])),\n  skew: v => (Math.abs(v[1]) + Math.abs(v[2])) / 2\n};\nconst rebaseAngle = angle => {\n  angle = angle % 360;\n  if (angle < 0) angle += 360;\n  return angle;\n};\nconst rotateZ = rotate;\nconst scaleX = v => Math.sqrt(v[0] * v[0] + v[1] * v[1]);\nconst scaleY = v => Math.sqrt(v[4] * v[4] + v[5] * v[5]);\nconst matrix3dParsers = {\n  x: 12,\n  y: 13,\n  z: 14,\n  translateX: 12,\n  translateY: 13,\n  translateZ: 14,\n  scaleX,\n  scaleY,\n  scale: v => (scaleX(v) + scaleY(v)) / 2,\n  rotateX: v => rebaseAngle(radToDeg(Math.atan2(v[6], v[5]))),\n  rotateY: v => rebaseAngle(radToDeg(Math.atan2(-v[2], v[0]))),\n  rotateZ,\n  rotate: rotateZ,\n  skewX: v => radToDeg(Math.atan(v[4])),\n  skewY: v => radToDeg(Math.atan(v[1])),\n  skew: v => (Math.abs(v[1]) + Math.abs(v[4])) / 2\n};\nfunction defaultTransformValue(name) {\n  return name.includes(\"scale\") ? 1 : 0;\n}\nfunction parseValueFromTransform(transform, name) {\n  if (!transform || transform === \"none\") {\n    return defaultTransformValue(name);\n  }\n  const matrix3dMatch = transform.match(/^matrix3d\\(([-\\d.e\\s,]+)\\)$/u);\n  let parsers;\n  let match;\n  if (matrix3dMatch) {\n    parsers = matrix3dParsers;\n    match = matrix3dMatch;\n  } else {\n    const matrix2dMatch = transform.match(/^matrix\\(([-\\d.e\\s,]+)\\)$/u);\n    parsers = matrix2dParsers;\n    match = matrix2dMatch;\n  }\n  if (!match) {\n    return defaultTransformValue(name);\n  }\n  const valueParser = parsers[name];\n  const values = match[1].split(\",\").map(convertTransformToNumber);\n  return typeof valueParser === \"function\" ? valueParser(values) : values[valueParser];\n}\nconst readTransformValue = (instance, name) => {\n  const {\n    transform = \"none\"\n  } = getComputedStyle(instance);\n  return parseValueFromTransform(transform, name);\n};\nfunction convertTransformToNumber(value) {\n  return parseFloat(value.trim());\n}\nexport { defaultTransformValue, parseValueFromTransform, readTransformValue };", "map": {"version": 3, "names": ["radToDeg", "rad", "Math", "PI", "rotate", "v", "angle", "atan2", "rebaseAngle", "matrix2dParsers", "x", "y", "translateX", "translateY", "scaleX", "scaleY", "scale", "abs", "rotateZ", "skewX", "atan", "skewY", "skew", "sqrt", "matrix3dParsers", "z", "translateZ", "rotateX", "rotateY", "defaultTransformValue", "name", "includes", "parseValueFromTransform", "transform", "matrix3dMatch", "match", "parsers", "matrix2dMatch", "valueParser", "values", "split", "map", "convertTransformToNumber", "readTransformValue", "instance", "getComputedStyle", "value", "parseFloat", "trim"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/render/dom/parse-transform.mjs"], "sourcesContent": ["const radToDeg = (rad) => (rad * 180) / Math.PI;\nconst rotate = (v) => {\n    const angle = radToDeg(Math.atan2(v[1], v[0]));\n    return rebaseAngle(angle);\n};\nconst matrix2dParsers = {\n    x: 4,\n    y: 5,\n    translateX: 4,\n    translateY: 5,\n    scaleX: 0,\n    scaleY: 3,\n    scale: (v) => (Math.abs(v[0]) + Math.abs(v[3])) / 2,\n    rotate,\n    rotateZ: rotate,\n    skewX: (v) => radToDeg(Math.atan(v[1])),\n    skewY: (v) => radToDeg(Math.atan(v[2])),\n    skew: (v) => (Math.abs(v[1]) + Math.abs(v[2])) / 2,\n};\nconst rebaseAngle = (angle) => {\n    angle = angle % 360;\n    if (angle < 0)\n        angle += 360;\n    return angle;\n};\nconst rotateZ = rotate;\nconst scaleX = (v) => Math.sqrt(v[0] * v[0] + v[1] * v[1]);\nconst scaleY = (v) => Math.sqrt(v[4] * v[4] + v[5] * v[5]);\nconst matrix3dParsers = {\n    x: 12,\n    y: 13,\n    z: 14,\n    translateX: 12,\n    translateY: 13,\n    translateZ: 14,\n    scaleX,\n    scaleY,\n    scale: (v) => (scaleX(v) + scaleY(v)) / 2,\n    rotateX: (v) => rebaseAngle(radToDeg(Math.atan2(v[6], v[5]))),\n    rotateY: (v) => rebaseAngle(radToDeg(Math.atan2(-v[2], v[0]))),\n    rotateZ,\n    rotate: rotateZ,\n    skewX: (v) => radToDeg(Math.atan(v[4])),\n    skewY: (v) => radToDeg(Math.atan(v[1])),\n    skew: (v) => (Math.abs(v[1]) + Math.abs(v[4])) / 2,\n};\nfunction defaultTransformValue(name) {\n    return name.includes(\"scale\") ? 1 : 0;\n}\nfunction parseValueFromTransform(transform, name) {\n    if (!transform || transform === \"none\") {\n        return defaultTransformValue(name);\n    }\n    const matrix3dMatch = transform.match(/^matrix3d\\(([-\\d.e\\s,]+)\\)$/u);\n    let parsers;\n    let match;\n    if (matrix3dMatch) {\n        parsers = matrix3dParsers;\n        match = matrix3dMatch;\n    }\n    else {\n        const matrix2dMatch = transform.match(/^matrix\\(([-\\d.e\\s,]+)\\)$/u);\n        parsers = matrix2dParsers;\n        match = matrix2dMatch;\n    }\n    if (!match) {\n        return defaultTransformValue(name);\n    }\n    const valueParser = parsers[name];\n    const values = match[1].split(\",\").map(convertTransformToNumber);\n    return typeof valueParser === \"function\"\n        ? valueParser(values)\n        : values[valueParser];\n}\nconst readTransformValue = (instance, name) => {\n    const { transform = \"none\" } = getComputedStyle(instance);\n    return parseValueFromTransform(transform, name);\n};\nfunction convertTransformToNumber(value) {\n    return parseFloat(value.trim());\n}\n\nexport { defaultTransformValue, parseValueFromTransform, readTransformValue };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAIC,GAAG,IAAMA,GAAG,GAAG,GAAG,GAAIC,IAAI,CAACC,EAAE;AAC/C,MAAMC,MAAM,GAAIC,CAAC,IAAK;EAClB,MAAMC,KAAK,GAAGN,QAAQ,CAACE,IAAI,CAACK,KAAK,CAACF,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,OAAOG,WAAW,CAACF,KAAK,CAAC;AAC7B,CAAC;AACD,MAAMG,eAAe,GAAG;EACpBC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAGX,CAAC,IAAK,CAACH,IAAI,CAACe,GAAG,CAACZ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGH,IAAI,CAACe,GAAG,CAACZ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EACnDD,MAAM;EACNc,OAAO,EAAEd,MAAM;EACfe,KAAK,EAAGd,CAAC,IAAKL,QAAQ,CAACE,IAAI,CAACkB,IAAI,CAACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvCgB,KAAK,EAAGhB,CAAC,IAAKL,QAAQ,CAACE,IAAI,CAACkB,IAAI,CAACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvCiB,IAAI,EAAGjB,CAAC,IAAK,CAACH,IAAI,CAACe,GAAG,CAACZ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGH,IAAI,CAACe,GAAG,CAACZ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;AACrD,CAAC;AACD,MAAMG,WAAW,GAAIF,KAAK,IAAK;EAC3BA,KAAK,GAAGA,KAAK,GAAG,GAAG;EACnB,IAAIA,KAAK,GAAG,CAAC,EACTA,KAAK,IAAI,GAAG;EAChB,OAAOA,KAAK;AAChB,CAAC;AACD,MAAMY,OAAO,GAAGd,MAAM;AACtB,MAAMU,MAAM,GAAIT,CAAC,IAAKH,IAAI,CAACqB,IAAI,CAAClB,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,MAAMU,MAAM,GAAIV,CAAC,IAAKH,IAAI,CAACqB,IAAI,CAAClB,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,MAAMmB,eAAe,GAAG;EACpBd,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLc,CAAC,EAAE,EAAE;EACLb,UAAU,EAAE,EAAE;EACdC,UAAU,EAAE,EAAE;EACda,UAAU,EAAE,EAAE;EACdZ,MAAM;EACNC,MAAM;EACNC,KAAK,EAAGX,CAAC,IAAK,CAACS,MAAM,CAACT,CAAC,CAAC,GAAGU,MAAM,CAACV,CAAC,CAAC,IAAI,CAAC;EACzCsB,OAAO,EAAGtB,CAAC,IAAKG,WAAW,CAACR,QAAQ,CAACE,IAAI,CAACK,KAAK,CAACF,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7DuB,OAAO,EAAGvB,CAAC,IAAKG,WAAW,CAACR,QAAQ,CAACE,IAAI,CAACK,KAAK,CAAC,CAACF,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9Da,OAAO;EACPd,MAAM,EAAEc,OAAO;EACfC,KAAK,EAAGd,CAAC,IAAKL,QAAQ,CAACE,IAAI,CAACkB,IAAI,CAACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvCgB,KAAK,EAAGhB,CAAC,IAAKL,QAAQ,CAACE,IAAI,CAACkB,IAAI,CAACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvCiB,IAAI,EAAGjB,CAAC,IAAK,CAACH,IAAI,CAACe,GAAG,CAACZ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGH,IAAI,CAACe,GAAG,CAACZ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;AACrD,CAAC;AACD,SAASwB,qBAAqBA,CAACC,IAAI,EAAE;EACjC,OAAOA,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;AACzC;AACA,SAASC,uBAAuBA,CAACC,SAAS,EAAEH,IAAI,EAAE;EAC9C,IAAI,CAACG,SAAS,IAAIA,SAAS,KAAK,MAAM,EAAE;IACpC,OAAOJ,qBAAqB,CAACC,IAAI,CAAC;EACtC;EACA,MAAMI,aAAa,GAAGD,SAAS,CAACE,KAAK,CAAC,8BAA8B,CAAC;EACrE,IAAIC,OAAO;EACX,IAAID,KAAK;EACT,IAAID,aAAa,EAAE;IACfE,OAAO,GAAGZ,eAAe;IACzBW,KAAK,GAAGD,aAAa;EACzB,CAAC,MACI;IACD,MAAMG,aAAa,GAAGJ,SAAS,CAACE,KAAK,CAAC,4BAA4B,CAAC;IACnEC,OAAO,GAAG3B,eAAe;IACzB0B,KAAK,GAAGE,aAAa;EACzB;EACA,IAAI,CAACF,KAAK,EAAE;IACR,OAAON,qBAAqB,CAACC,IAAI,CAAC;EACtC;EACA,MAAMQ,WAAW,GAAGF,OAAO,CAACN,IAAI,CAAC;EACjC,MAAMS,MAAM,GAAGJ,KAAK,CAAC,CAAC,CAAC,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,wBAAwB,CAAC;EAChE,OAAO,OAAOJ,WAAW,KAAK,UAAU,GAClCA,WAAW,CAACC,MAAM,CAAC,GACnBA,MAAM,CAACD,WAAW,CAAC;AAC7B;AACA,MAAMK,kBAAkB,GAAGA,CAACC,QAAQ,EAAEd,IAAI,KAAK;EAC3C,MAAM;IAAEG,SAAS,GAAG;EAAO,CAAC,GAAGY,gBAAgB,CAACD,QAAQ,CAAC;EACzD,OAAOZ,uBAAuB,CAACC,SAAS,EAAEH,IAAI,CAAC;AACnD,CAAC;AACD,SAASY,wBAAwBA,CAACI,KAAK,EAAE;EACrC,OAAOC,UAAU,CAACD,KAAK,CAACE,IAAI,EAAE,CAAC;AACnC;AAEA,SAASnB,qBAAqB,EAAEG,uBAAuB,EAAEW,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}