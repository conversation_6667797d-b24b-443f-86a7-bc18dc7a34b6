{"ast": null, "code": "import { time, frame, cancelFrame } from 'motion-dom';\nimport { secondsToMilliseconds } from 'motion-utils';\n\n/**\n * Timeout defined in ms\n */\nfunction delay(callback, timeout) {\n  const start = time.now();\n  const checkElapsed = _ref => {\n    let {\n      timestamp\n    } = _ref;\n    const elapsed = timestamp - start;\n    if (elapsed >= timeout) {\n      cancelFrame(checkElapsed);\n      callback(elapsed - timeout);\n    }\n  };\n  frame.setup(checkElapsed, true);\n  return () => cancelFrame(checkElapsed);\n}\nfunction delayInSeconds(callback, timeout) {\n  return delay(callback, secondsToMilliseconds(timeout));\n}\nexport { delay, delayInSeconds };", "map": {"version": 3, "names": ["time", "frame", "cancelFrame", "secondsToMilliseconds", "delay", "callback", "timeout", "start", "now", "checkElapsed", "_ref", "timestamp", "elapsed", "setup", "delayInSeconds"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/utils/delay.mjs"], "sourcesContent": ["import { time, frame, cancelFrame } from 'motion-dom';\nimport { secondsToMilliseconds } from 'motion-utils';\n\n/**\n * Timeout defined in ms\n */\nfunction delay(callback, timeout) {\n    const start = time.now();\n    const checkElapsed = ({ timestamp }) => {\n        const elapsed = timestamp - start;\n        if (elapsed >= timeout) {\n            cancelFrame(checkElapsed);\n            callback(elapsed - timeout);\n        }\n    };\n    frame.setup(checkElapsed, true);\n    return () => cancelFrame(checkElapsed);\n}\nfunction delayInSeconds(callback, timeout) {\n    return delay(callback, secondsToMilliseconds(timeout));\n}\n\nexport { delay, delayInSeconds };\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,KAAK,EAAEC,WAAW,QAAQ,YAAY;AACrD,SAASC,qBAAqB,QAAQ,cAAc;;AAEpD;AACA;AACA;AACA,SAASC,KAAKA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC9B,MAAMC,KAAK,GAAGP,IAAI,CAACQ,GAAG,EAAE;EACxB,MAAMC,YAAY,GAAGC,IAAA,IAAmB;IAAA,IAAlB;MAAEC;IAAU,CAAC,GAAAD,IAAA;IAC/B,MAAME,OAAO,GAAGD,SAAS,GAAGJ,KAAK;IACjC,IAAIK,OAAO,IAAIN,OAAO,EAAE;MACpBJ,WAAW,CAACO,YAAY,CAAC;MACzBJ,QAAQ,CAACO,OAAO,GAAGN,OAAO,CAAC;IAC/B;EACJ,CAAC;EACDL,KAAK,CAACY,KAAK,CAACJ,YAAY,EAAE,IAAI,CAAC;EAC/B,OAAO,MAAMP,WAAW,CAACO,YAAY,CAAC;AAC1C;AACA,SAASK,cAAcA,CAACT,QAAQ,EAAEC,OAAO,EAAE;EACvC,OAAOF,KAAK,CAACC,QAAQ,EAAEF,qBAAqB,CAACG,OAAO,CAAC,CAAC;AAC1D;AAEA,SAASF,KAAK,EAAEU,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}