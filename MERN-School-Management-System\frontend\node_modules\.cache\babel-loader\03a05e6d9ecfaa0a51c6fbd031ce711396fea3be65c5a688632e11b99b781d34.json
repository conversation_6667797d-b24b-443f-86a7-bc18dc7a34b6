{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\components\\\\widgets\\\\QuickActions.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Paper, Typography, Box, Grid, IconButton, Tooltip } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { PersonAdd as PersonAddIcon, School as SchoolIcon, Assignment as AssignmentIcon, Announcement as AnnouncementIcon, Class as ClassIcon, SupervisorAccount as SupervisorAccountIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledPaper = styled(Paper)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    padding: theme.spacing(2),\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    color: 'white',\n    borderRadius: '16px',\n    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'\n  };\n});\n_c = StyledPaper;\nconst ActionButton = styled(motion.div)(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    padding: theme.spacing(1.5),\n    borderRadius: '12px',\n    background: 'rgba(255, 255, 255, 0.1)',\n    backdropFilter: 'blur(10px)',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    '&:hover': {\n      background: 'rgba(255, 255, 255, 0.2)',\n      transform: 'translateY(-2px)'\n    }\n  };\n});\n_c2 = ActionButton;\nconst QuickActions = () => {\n  _s();\n  const navigate = useNavigate();\n  const actions = [{\n    icon: /*#__PURE__*/_jsxDEV(PersonAddIcon, {\n      fontSize: \"large\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this),\n    label: 'Add Student',\n    path: '/Admin/addstudents',\n    color: '#4ECDC4'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(SupervisorAccountIcon, {\n      fontSize: \"large\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this),\n    label: 'Add Teacher',\n    path: '/Admin/teachers/chooseclass',\n    color: '#45B7D1'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(ClassIcon, {\n      fontSize: \"large\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this),\n    label: 'Add Class',\n    path: '/Admin/addclass',\n    color: '#96CEB4'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {\n      fontSize: \"large\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 13\n    }, this),\n    label: 'Add Subject',\n    path: '/Admin/subjects/chooseclass',\n    color: '#FFEAA7'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(AnnouncementIcon, {\n      fontSize: \"large\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this),\n    label: 'Add Notice',\n    path: '/Admin/addnotice',\n    color: '#DDA0DD'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(SchoolIcon, {\n      fontSize: \"large\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 13\n    }, this),\n    label: 'View Reports',\n    path: '/Admin/students',\n    color: '#98D8C8'\n  }];\n  const handleActionClick = path => {\n    navigate(path);\n  };\n  return /*#__PURE__*/_jsxDEV(StyledPaper, {\n    elevation: 3,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        component: \"h3\",\n        fontWeight: \"bold\",\n        children: \"\\u26A1 Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          opacity: 0.8\n        },\n        children: \"Frequently used actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: actions.map((action, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        sm: 4,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: action.label,\n          arrow: true,\n          children: /*#__PURE__*/_jsxDEV(ActionButton, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: () => handleActionClick(action.path),\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              sx: {\n                color: action.color,\n                mb: 1,\n                '&:hover': {\n                  backgroundColor: 'transparent'\n                }\n              },\n              children: action.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              align: \"center\",\n              sx: {\n                fontSize: '0.75rem',\n                fontWeight: 500,\n                lineHeight: 1.2\n              },\n              children: action.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickActions, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c3 = QuickActions;\nexport default QuickActions;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"StyledPaper\");\n$RefreshReg$(_c2, \"ActionButton\");\n$RefreshReg$(_c3, \"QuickActions\");", "map": {"version": 3, "names": ["React", "Paper", "Typography", "Box", "Grid", "IconButton", "<PERSON><PERSON><PERSON>", "styled", "motion", "PersonAdd", "PersonAddIcon", "School", "SchoolIcon", "Assignment", "AssignmentIcon", "Announcement", "AnnouncementIcon", "Class", "ClassIcon", "SupervisorAccount", "SupervisorAccountIcon", "useNavigate", "jsxDEV", "_jsxDEV", "StyledPaper", "_ref", "theme", "padding", "spacing", "background", "color", "borderRadius", "boxShadow", "_c", "ActionButton", "div", "_ref2", "display", "flexDirection", "alignItems", "<PERSON><PERSON>ilter", "cursor", "transition", "transform", "_c2", "QuickActions", "_s", "navigate", "actions", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "path", "handleActionClick", "elevation", "children", "mb", "variant", "component", "fontWeight", "sx", "opacity", "container", "map", "action", "index", "item", "xs", "sm", "md", "title", "arrow", "whileHover", "scale", "whileTap", "onClick", "backgroundColor", "align", "lineHeight", "_c3", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/components/widgets/QuickActions.js"], "sourcesContent": ["import React from 'react';\nimport { \n  Paper, \n  Typography, \n  Box, \n  Grid, \n  IconButton, \n  Tooltip \n} from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport {\n  PersonAdd as PersonAddIcon,\n  School as SchoolIcon,\n  Assignment as AssignmentIcon,\n  Announcement as AnnouncementIcon,\n  Class as ClassIcon,\n  SupervisorAccount as SupervisorAccountIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\n\nconst StyledPaper = styled(Paper)(({ theme }) => ({\n  padding: theme.spacing(2),\n  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n  color: 'white',\n  borderRadius: '16px',\n  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n}));\n\nconst ActionButton = styled(motion.div)(({ theme }) => ({\n  display: 'flex',\n  flexDirection: 'column',\n  alignItems: 'center',\n  padding: theme.spacing(1.5),\n  borderRadius: '12px',\n  background: 'rgba(255, 255, 255, 0.1)',\n  backdropFilter: 'blur(10px)',\n  cursor: 'pointer',\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    background: 'rgba(255, 255, 255, 0.2)',\n    transform: 'translateY(-2px)',\n  },\n}));\n\nconst QuickActions = () => {\n  const navigate = useNavigate();\n\n  const actions = [\n    {\n      icon: <PersonAddIcon fontSize=\"large\" />,\n      label: 'Add Student',\n      path: '/Admin/addstudents',\n      color: '#4ECDC4'\n    },\n    {\n      icon: <SupervisorAccountIcon fontSize=\"large\" />,\n      label: 'Add Teacher',\n      path: '/Admin/teachers/chooseclass',\n      color: '#45B7D1'\n    },\n    {\n      icon: <ClassIcon fontSize=\"large\" />,\n      label: 'Add Class',\n      path: '/Admin/addclass',\n      color: '#96CEB4'\n    },\n    {\n      icon: <AssignmentIcon fontSize=\"large\" />,\n      label: 'Add Subject',\n      path: '/Admin/subjects/chooseclass',\n      color: '#FFEAA7'\n    },\n    {\n      icon: <AnnouncementIcon fontSize=\"large\" />,\n      label: 'Add Notice',\n      path: '/Admin/addnotice',\n      color: '#DDA0DD'\n    },\n    {\n      icon: <SchoolIcon fontSize=\"large\" />,\n      label: 'View Reports',\n      path: '/Admin/students',\n      color: '#98D8C8'\n    },\n  ];\n\n  const handleActionClick = (path) => {\n    navigate(path);\n  };\n\n  return (\n    <StyledPaper elevation={3}>\n      <Box mb={3}>\n        <Typography variant=\"h6\" component=\"h3\" fontWeight=\"bold\">\n          ⚡ Quick Actions\n        </Typography>\n        <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n          Frequently used actions\n        </Typography>\n      </Box>\n      <Grid container spacing={2}>\n        {actions.map((action, index) => (\n          <Grid item xs={6} sm={4} md={2} key={index}>\n            <Tooltip title={action.label} arrow>\n              <ActionButton\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => handleActionClick(action.path)}\n              >\n                <IconButton \n                  sx={{ \n                    color: action.color, \n                    mb: 1,\n                    '&:hover': { backgroundColor: 'transparent' }\n                  }}\n                >\n                  {action.icon}\n                </IconButton>\n                <Typography \n                  variant=\"caption\" \n                  align=\"center\" \n                  sx={{ \n                    fontSize: '0.75rem',\n                    fontWeight: 500,\n                    lineHeight: 1.2\n                  }}\n                >\n                  {action.label}\n                </Typography>\n              </ActionButton>\n            </Tooltip>\n          </Grid>\n        ))}\n      </Grid>\n    </StyledPaper>\n  );\n};\n\nexport default QuickActions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,iBAAiB,IAAIC,qBAAqB,QACrC,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,WAAW,GAAGjB,MAAM,CAACN,KAAK,CAAC,CAACwB,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IAChDE,OAAO,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;IACzBC,UAAU,EAAE,mDAAmD;IAC/DC,KAAK,EAAE,OAAO;IACdC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE;EACb,CAAC;AAAA,CAAC,CAAC;AAACC,EAAA,GANET,WAAW;AAQjB,MAAMU,YAAY,GAAG3B,MAAM,CAACC,MAAM,CAAC2B,GAAG,CAAC,CAACC,KAAA;EAAA,IAAC;IAAEV;EAAM,CAAC,GAAAU,KAAA;EAAA,OAAM;IACtDC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,QAAQ;IACpBZ,OAAO,EAAED,KAAK,CAACE,OAAO,CAAC,GAAG,CAAC;IAC3BG,YAAY,EAAE,MAAM;IACpBF,UAAU,EAAE,0BAA0B;IACtCW,cAAc,EAAE,YAAY;IAC5BC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTb,UAAU,EAAE,0BAA0B;MACtCc,SAAS,EAAE;IACb;EACF,CAAC;AAAA,CAAC,CAAC;AAACC,GAAA,GAdEV,YAAY;AAgBlB,MAAMW,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAG1B,WAAW,EAAE;EAE9B,MAAM2B,OAAO,GAAG,CACd;IACEC,IAAI,eAAE1B,OAAA,CAACb,aAAa;MAACwC,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACxCC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,oBAAoB;IAC1B1B,KAAK,EAAE;EACT,CAAC,EACD;IACEmB,IAAI,eAAE1B,OAAA,CAACH,qBAAqB;MAAC8B,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAChDC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,6BAA6B;IACnC1B,KAAK,EAAE;EACT,CAAC,EACD;IACEmB,IAAI,eAAE1B,OAAA,CAACL,SAAS;MAACgC,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACpCC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,iBAAiB;IACvB1B,KAAK,EAAE;EACT,CAAC,EACD;IACEmB,IAAI,eAAE1B,OAAA,CAACT,cAAc;MAACoC,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACzCC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,6BAA6B;IACnC1B,KAAK,EAAE;EACT,CAAC,EACD;IACEmB,IAAI,eAAE1B,OAAA,CAACP,gBAAgB;MAACkC,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC3CC,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE,kBAAkB;IACxB1B,KAAK,EAAE;EACT,CAAC,EACD;IACEmB,IAAI,eAAE1B,OAAA,CAACX,UAAU;MAACsC,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACrCC,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE,iBAAiB;IACvB1B,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAM2B,iBAAiB,GAAID,IAAI,IAAK;IAClCT,QAAQ,CAACS,IAAI,CAAC;EAChB,CAAC;EAED,oBACEjC,OAAA,CAACC,WAAW;IAACkC,SAAS,EAAE,CAAE;IAAAC,QAAA,gBACxBpC,OAAA,CAACpB,GAAG;MAACyD,EAAE,EAAE,CAAE;MAAAD,QAAA,gBACTpC,OAAA,CAACrB,UAAU;QAAC2D,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,UAAU,EAAC,MAAM;QAAAJ,QAAA,EAAC;MAE1D;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAa,eACb/B,OAAA,CAACrB,UAAU;QAAC2D,OAAO,EAAC,OAAO;QAACG,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAI,CAAE;QAAAN,QAAA,EAAC;MAElD;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAa;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACT,eACN/B,OAAA,CAACnB,IAAI;MAAC8D,SAAS;MAACtC,OAAO,EAAE,CAAE;MAAA+B,QAAA,EACxBX,OAAO,CAACmB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzB9C,OAAA,CAACnB,IAAI;QAACkE,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAd,QAAA,eAC7BpC,OAAA,CAACjB,OAAO;UAACoE,KAAK,EAAEN,MAAM,CAACb,KAAM;UAACoB,KAAK;UAAAhB,QAAA,eACjCpC,OAAA,CAACW,YAAY;YACX0C,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1BE,OAAO,EAAEA,CAAA,KAAMtB,iBAAiB,CAACW,MAAM,CAACZ,IAAI,CAAE;YAAAG,QAAA,gBAE9CpC,OAAA,CAAClB,UAAU;cACT2D,EAAE,EAAE;gBACFlC,KAAK,EAAEsC,MAAM,CAACtC,KAAK;gBACnB8B,EAAE,EAAE,CAAC;gBACL,SAAS,EAAE;kBAAEoB,eAAe,EAAE;gBAAc;cAC9C,CAAE;cAAArB,QAAA,EAEDS,MAAM,CAACnB;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACD,eACb/B,OAAA,CAACrB,UAAU;cACT2D,OAAO,EAAC,SAAS;cACjBoB,KAAK,EAAC,QAAQ;cACdjB,EAAE,EAAE;gBACFd,QAAQ,EAAE,SAAS;gBACnBa,UAAU,EAAE,GAAG;gBACfmB,UAAU,EAAE;cACd,CAAE;cAAAvB,QAAA,EAEDS,MAAM,CAACb;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACP,GA5ByBe,KAAK;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QA8B3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACG;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACK;AAElB,CAAC;AAACR,EAAA,CA5FID,YAAY;EAAA,QACCxB,WAAW;AAAA;AAAA8D,GAAA,GADxBtC,YAAY;AA8FlB,eAAeA,YAAY;AAAC,IAAAZ,EAAA,EAAAW,GAAA,EAAAuC,GAAA;AAAAC,YAAA,CAAAnD,EAAA;AAAAmD,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}