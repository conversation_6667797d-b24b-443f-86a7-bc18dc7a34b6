import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Avatar,
  Chip,
  Button,
  TextField,
  Tab,
  Tabs,
  IconButton,
  Divider,
  Alert,
  CircularProgress
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  Person as PersonIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  ArrowBack as ArrowBackIcon,
  School as SchoolIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Home as HomeIcon,
  Cake as CakeIcon,
  Bloodtype as BloodtypeIcon,
  PhotoCamera as PhotoCameraIcon
} from '@mui/icons-material';
import { getUserDetails, updateUser } from '../../../redux/userRelated/userHandle';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
}));

const ProfileCard = styled(Card)(({ theme }) => ({
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  marginBottom: theme.spacing(3),
  borderRadius: theme.spacing(2),
}));

const InfoCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  borderRadius: theme.spacing(1.5),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
  },
}));

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`student-tabpanel-${index}`}
      aria-labelledby={`student-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const EnhancedViewStudent = () => {
  const navigate = useNavigate();
  const params = useParams();
  const dispatch = useDispatch();
  
  const { userDetails, response, loading, error } = useSelector((state) => state.user);
  
  const studentID = params.id;
  const address = "Student";
  
  const [tabValue, setTabValue] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertSeverity, setAlertSeverity] = useState('success');
  
  // Form states
  const [formData, setFormData] = useState({
    name: '',
    rollNum: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    bloodGroup: '',
    address: '',
    fatherName: '',
    motherName: '',
    guardianPhone: '',
    profilePicture: null
  });

  useEffect(() => {
    dispatch(getUserDetails(studentID, address));
  }, [dispatch, studentID]);

  useEffect(() => {
    if (userDetails) {
      setFormData({
        name: userDetails.name || '',
        rollNum: userDetails.rollNum || '',
        email: userDetails.email || '',
        phone: userDetails.phone || '',
        dateOfBirth: userDetails.dateOfBirth ? userDetails.dateOfBirth.split('T')[0] : '',
        gender: userDetails.gender || '',
        bloodGroup: userDetails.bloodGroup || '',
        address: userDetails.address || '',
        fatherName: userDetails.fatherName || '',
        motherName: userDetails.motherName || '',
        guardianPhone: userDetails.guardianPhone || '',
        profilePicture: userDetails.profilePicture || null
      });
    }
  }, [userDetails]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleInputChange = (field) => (event) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleProfilePictureChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setFormData(prev => ({
          ...prev,
          profilePicture: e.target.result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = () => {
    dispatch(updateUser(formData, studentID, address))
      .then(() => {
        setIsEditing(false);
        setAlertMessage('Student information updated successfully!');
        setAlertSeverity('success');
        setShowAlert(true);
        dispatch(getUserDetails(studentID, address));
        setTimeout(() => setShowAlert(false), 3000);
      })
      .catch((error) => {
        setAlertMessage('Failed to update student information. Please try again.');
        setAlertSeverity('error');
        setShowAlert(true);
        setTimeout(() => setShowAlert(false), 3000);
      });
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset form data to original values
    if (userDetails) {
      setFormData({
        name: userDetails.name || '',
        rollNum: userDetails.rollNum || '',
        email: userDetails.email || '',
        phone: userDetails.phone || '',
        dateOfBirth: userDetails.dateOfBirth ? userDetails.dateOfBirth.split('T')[0] : '',
        gender: userDetails.gender || '',
        bloodGroup: userDetails.bloodGroup || '',
        address: userDetails.address || '',
        fatherName: userDetails.fatherName || '',
        motherName: userDetails.motherName || '',
        guardianPhone: userDetails.guardianPhone || '',
        profilePicture: userDetails.profilePicture || null
      });
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error">
          Error loading student details: {error}
        </Alert>
      </Container>
    );
  }

  if (!userDetails) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="warning">
          Student not found.
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 2, mb: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* Header */}
        <Box display="flex" alignItems="center" mb={4}>
          <IconButton
            onClick={() => navigate(-1)}
            sx={{ mr: 2 }}
            color="primary"
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
            Student Profile
          </Typography>
        </Box>

        {showAlert && (
          <Alert severity={alertSeverity} sx={{ mb: 3 }}>
            {alertMessage}
          </Alert>
        )}

        {/* Profile Card */}
        <ProfileCard>
          <CardContent>
            <Box display="flex" alignItems="center" gap={3}>
              <Box position="relative">
                <Avatar
                  src={formData.profilePicture}
                  sx={{ width: 120, height: 120 }}
                >
                  {!formData.profilePicture && <PersonIcon sx={{ fontSize: 60 }} />}
                </Avatar>
                {isEditing && (
                  <Box
                    position="absolute"
                    bottom={0}
                    right={0}
                    sx={{
                      backgroundColor: 'rgba(255,255,255,0.9)',
                      borderRadius: '50%',
                      p: 1
                    }}
                  >
                    <input
                      accept="image/*"
                      style={{ display: 'none' }}
                      id="profile-picture-upload"
                      type="file"
                      onChange={handleProfilePictureChange}
                    />
                    <label htmlFor="profile-picture-upload">
                      <IconButton component="span" size="small">
                        <PhotoCameraIcon />
                      </IconButton>
                    </label>
                  </Box>
                )}
              </Box>
              <Box flex={1}>
                <Typography variant="h4" fontWeight="bold">
                  {userDetails.name}
                </Typography>
                <Typography variant="h6" sx={{ opacity: 0.9 }}>
                  Roll Number: {userDetails.rollNum}
                </Typography>
                <Typography variant="body1" sx={{ opacity: 0.8 }}>
                  Class: {userDetails.sclassName?.sclassName || 'N/A'}
                </Typography>
                <Chip 
                  label="Active Student" 
                  sx={{ 
                    mt: 1, 
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    color: 'white'
                  }} 
                />
              </Box>
              <Box>
                {!isEditing ? (
                  <Button
                    variant="contained"
                    startIcon={<EditIcon />}
                    onClick={() => setIsEditing(true)}
                    sx={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      color: 'white',
                      '&:hover': {
                        backgroundColor: 'rgba(255,255,255,0.3)',
                      }
                    }}
                  >
                    Edit Profile
                  </Button>
                ) : (
                  <Box display="flex" gap={1}>
                    <Button
                      variant="contained"
                      startIcon={<SaveIcon />}
                      onClick={handleSave}
                      sx={{
                        backgroundColor: 'rgba(76, 175, 80, 0.8)',
                        color: 'white',
                        '&:hover': {
                          backgroundColor: 'rgba(76, 175, 80, 1)',
                        }
                      }}
                    >
                      Save
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<CancelIcon />}
                      onClick={handleCancel}
                      sx={{
                        borderColor: 'rgba(255,255,255,0.5)',
                        color: 'white',
                        '&:hover': {
                          borderColor: 'white',
                          backgroundColor: 'rgba(255,255,255,0.1)',
                        }
                      }}
                    >
                      Cancel
                    </Button>
                  </Box>
                )}
              </Box>
            </Box>
          </CardContent>
        </ProfileCard>

        {/* Tabs */}
        <StyledPaper>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}
          >
            <Tab icon={<PersonIcon />} label="Personal Info" />
            <Tab icon={<SchoolIcon />} label="Academic" />
            <Tab icon={<HomeIcon />} label="Family" />
          </Tabs>

          {/* Personal Information Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Full Name"
                  value={formData.name}
                  onChange={handleInputChange('name')}
                  disabled={!isEditing}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Roll Number"
                  value={formData.rollNum}
                  onChange={handleInputChange('rollNum')}
                  disabled={!isEditing}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange('email')}
                  disabled={!isEditing}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Phone"
                  value={formData.phone}
                  onChange={handleInputChange('phone')}
                  disabled={!isEditing}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Date of Birth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={handleInputChange('dateOfBirth')}
                  disabled={!isEditing}
                  margin="normal"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Gender"
                  value={formData.gender}
                  onChange={handleInputChange('gender')}
                  disabled={!isEditing}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Blood Group"
                  value={formData.bloodGroup}
                  onChange={handleInputChange('bloodGroup')}
                  disabled={!isEditing}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Address"
                  multiline
                  rows={3}
                  value={formData.address}
                  onChange={handleInputChange('address')}
                  disabled={!isEditing}
                  margin="normal"
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Academic Information Tab */}
          <TabPanel value={tabValue} index={1}>
            <InfoCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Academic Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box display="flex" alignItems="center" mb={2}>
                      <SchoolIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="body1">
                        <strong>Class:</strong> {userDetails.sclassName?.sclassName || 'N/A'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box display="flex" alignItems="center" mb={2}>
                      <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="body1">
                        <strong>Student ID:</strong> {userDetails._id}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </InfoCard>
          </TabPanel>

          {/* Family Information Tab */}
          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Father's Name"
                  value={formData.fatherName}
                  onChange={handleInputChange('fatherName')}
                  disabled={!isEditing}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Mother's Name"
                  value={formData.motherName}
                  onChange={handleInputChange('motherName')}
                  disabled={!isEditing}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Guardian Phone"
                  value={formData.guardianPhone}
                  onChange={handleInputChange('guardianPhone')}
                  disabled={!isEditing}
                  margin="normal"
                />
              </Grid>
            </Grid>
          </TabPanel>
        </StyledPaper>
      </motion.div>
    </Container>
  );
};

export default EnhancedViewStudent;
