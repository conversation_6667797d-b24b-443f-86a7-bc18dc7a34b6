{"ast": null, "code": "\"use client\";\n\nimport { jsx, Fragment } from 'react/jsx-runtime';\nimport { useMemo, useRef, useState, useContext } from 'react';\nimport { LayoutGroupContext } from '../../context/LayoutGroupContext.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nimport { PresenceChild } from './PresenceChild.mjs';\nimport { usePresence } from './use-presence.mjs';\nimport { onlyElements, getChildKey } from './utils.mjs';\n\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */\nconst AnimatePresence = _ref => {\n  let {\n    children,\n    custom,\n    initial = true,\n    onExitComplete,\n    presenceAffectsLayout = true,\n    mode = \"sync\",\n    propagate = false,\n    anchorX = \"left\"\n  } = _ref;\n  const [isParentPresent, safeToRemove] = usePresence(propagate);\n  /**\n   * Filter any children that aren't ReactElements. We can only track components\n   * between renders with a props.key.\n   */\n  const presentChildren = useMemo(() => onlyElements(children), [children]);\n  /**\n   * Track the keys of the currently rendered children. This is used to\n   * determine which children are exiting.\n   */\n  const presentKeys = propagate && !isParentPresent ? [] : presentChildren.map(getChildKey);\n  /**\n   * If `initial={false}` we only want to pass this to components in the first render.\n   */\n  const isInitialRender = useRef(true);\n  /**\n   * A ref containing the currently present children. When all exit animations\n   * are complete, we use this to re-render the component with the latest children\n   * *committed* rather than the latest children *rendered*.\n   */\n  const pendingPresentChildren = useRef(presentChildren);\n  /**\n   * Track which exiting children have finished animating out.\n   */\n  const exitComplete = useConstant(() => new Map());\n  /**\n   * Save children to render as React state. To ensure this component is concurrent-safe,\n   * we check for exiting children via an effect.\n   */\n  const [diffedChildren, setDiffedChildren] = useState(presentChildren);\n  const [renderedChildren, setRenderedChildren] = useState(presentChildren);\n  useIsomorphicLayoutEffect(() => {\n    isInitialRender.current = false;\n    pendingPresentChildren.current = presentChildren;\n    /**\n     * Update complete status of exiting children.\n     */\n    for (let i = 0; i < renderedChildren.length; i++) {\n      const key = getChildKey(renderedChildren[i]);\n      if (!presentKeys.includes(key)) {\n        if (exitComplete.get(key) !== true) {\n          exitComplete.set(key, false);\n        }\n      } else {\n        exitComplete.delete(key);\n      }\n    }\n  }, [renderedChildren, presentKeys.length, presentKeys.join(\"-\")]);\n  const exitingChildren = [];\n  if (presentChildren !== diffedChildren) {\n    let nextChildren = [...presentChildren];\n    /**\n     * Loop through all the currently rendered components and decide which\n     * are exiting.\n     */\n    for (let i = 0; i < renderedChildren.length; i++) {\n      const child = renderedChildren[i];\n      const key = getChildKey(child);\n      if (!presentKeys.includes(key)) {\n        nextChildren.splice(i, 0, child);\n        exitingChildren.push(child);\n      }\n    }\n    /**\n     * If we're in \"wait\" mode, and we have exiting children, we want to\n     * only render these until they've all exited.\n     */\n    if (mode === \"wait\" && exitingChildren.length) {\n      nextChildren = exitingChildren;\n    }\n    setRenderedChildren(onlyElements(nextChildren));\n    setDiffedChildren(presentChildren);\n    /**\n     * Early return to ensure once we've set state with the latest diffed\n     * children, we can immediately re-render.\n     */\n    return null;\n  }\n  if (process.env.NODE_ENV !== \"production\" && mode === \"wait\" && renderedChildren.length > 1) {\n    console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.`);\n  }\n  /**\n   * If we've been provided a forceRender function by the LayoutGroupContext,\n   * we can use it to force a re-render amongst all surrounding components once\n   * all components have finished animating out.\n   */\n  const {\n    forceRender\n  } = useContext(LayoutGroupContext);\n  return jsx(Fragment, {\n    children: renderedChildren.map(child => {\n      const key = getChildKey(child);\n      const isPresent = propagate && !isParentPresent ? false : presentChildren === renderedChildren || presentKeys.includes(key);\n      const onExit = () => {\n        if (exitComplete.has(key)) {\n          exitComplete.set(key, true);\n        } else {\n          return;\n        }\n        let isEveryExitComplete = true;\n        exitComplete.forEach(isExitComplete => {\n          if (!isExitComplete) isEveryExitComplete = false;\n        });\n        if (isEveryExitComplete) {\n          forceRender?.();\n          setRenderedChildren(pendingPresentChildren.current);\n          propagate && safeToRemove?.();\n          onExitComplete && onExitComplete();\n        }\n      };\n      return jsx(PresenceChild, {\n        isPresent: isPresent,\n        initial: !isInitialRender.current || initial ? undefined : false,\n        custom: custom,\n        presenceAffectsLayout: presenceAffectsLayout,\n        mode: mode,\n        onExitComplete: isPresent ? undefined : onExit,\n        anchorX: anchorX,\n        children: child\n      }, key);\n    })\n  });\n};\nexport { AnimatePresence };", "map": {"version": 3, "names": ["jsx", "Fragment", "useMemo", "useRef", "useState", "useContext", "LayoutGroupContext", "useConstant", "useIsomorphicLayoutEffect", "Presence<PERSON><PERSON><PERSON>", "usePresence", "onlyElements", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AnimatePresence", "_ref", "children", "custom", "initial", "onExitComplete", "presenceAffectsLayout", "mode", "propagate", "anchorX", "isParentPresent", "safeToRemove", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "present<PERSON><PERSON>s", "map", "isInitialRender", "pendingPresentChildren", "exitComplete", "Map", "diffed<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>iff<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "i", "length", "key", "includes", "get", "set", "delete", "join", "exiting<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "splice", "push", "process", "env", "NODE_ENV", "console", "warn", "forceRender", "isPresent", "onExit", "has", "isEveryExitComplete", "for<PERSON>ach", "isExitComplete", "undefined"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx, Fragment } from 'react/jsx-runtime';\nimport { useMemo, useRef, useState, useContext } from 'react';\nimport { LayoutGroupContext } from '../../context/LayoutGroupContext.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nimport { PresenceChild } from './PresenceChild.mjs';\nimport { usePresence } from './use-presence.mjs';\nimport { onlyElements, getChildKey } from './utils.mjs';\n\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */\nconst AnimatePresence = ({ children, custom, initial = true, onExitComplete, presenceAffectsLayout = true, mode = \"sync\", propagate = false, anchorX = \"left\", }) => {\n    const [isParentPresent, safeToRemove] = usePresence(propagate);\n    /**\n     * Filter any children that aren't ReactElements. We can only track components\n     * between renders with a props.key.\n     */\n    const presentChildren = useMemo(() => onlyElements(children), [children]);\n    /**\n     * Track the keys of the currently rendered children. This is used to\n     * determine which children are exiting.\n     */\n    const presentKeys = propagate && !isParentPresent ? [] : presentChildren.map(getChildKey);\n    /**\n     * If `initial={false}` we only want to pass this to components in the first render.\n     */\n    const isInitialRender = useRef(true);\n    /**\n     * A ref containing the currently present children. When all exit animations\n     * are complete, we use this to re-render the component with the latest children\n     * *committed* rather than the latest children *rendered*.\n     */\n    const pendingPresentChildren = useRef(presentChildren);\n    /**\n     * Track which exiting children have finished animating out.\n     */\n    const exitComplete = useConstant(() => new Map());\n    /**\n     * Save children to render as React state. To ensure this component is concurrent-safe,\n     * we check for exiting children via an effect.\n     */\n    const [diffedChildren, setDiffedChildren] = useState(presentChildren);\n    const [renderedChildren, setRenderedChildren] = useState(presentChildren);\n    useIsomorphicLayoutEffect(() => {\n        isInitialRender.current = false;\n        pendingPresentChildren.current = presentChildren;\n        /**\n         * Update complete status of exiting children.\n         */\n        for (let i = 0; i < renderedChildren.length; i++) {\n            const key = getChildKey(renderedChildren[i]);\n            if (!presentKeys.includes(key)) {\n                if (exitComplete.get(key) !== true) {\n                    exitComplete.set(key, false);\n                }\n            }\n            else {\n                exitComplete.delete(key);\n            }\n        }\n    }, [renderedChildren, presentKeys.length, presentKeys.join(\"-\")]);\n    const exitingChildren = [];\n    if (presentChildren !== diffedChildren) {\n        let nextChildren = [...presentChildren];\n        /**\n         * Loop through all the currently rendered components and decide which\n         * are exiting.\n         */\n        for (let i = 0; i < renderedChildren.length; i++) {\n            const child = renderedChildren[i];\n            const key = getChildKey(child);\n            if (!presentKeys.includes(key)) {\n                nextChildren.splice(i, 0, child);\n                exitingChildren.push(child);\n            }\n        }\n        /**\n         * If we're in \"wait\" mode, and we have exiting children, we want to\n         * only render these until they've all exited.\n         */\n        if (mode === \"wait\" && exitingChildren.length) {\n            nextChildren = exitingChildren;\n        }\n        setRenderedChildren(onlyElements(nextChildren));\n        setDiffedChildren(presentChildren);\n        /**\n         * Early return to ensure once we've set state with the latest diffed\n         * children, we can immediately re-render.\n         */\n        return null;\n    }\n    if (process.env.NODE_ENV !== \"production\" &&\n        mode === \"wait\" &&\n        renderedChildren.length > 1) {\n        console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.`);\n    }\n    /**\n     * If we've been provided a forceRender function by the LayoutGroupContext,\n     * we can use it to force a re-render amongst all surrounding components once\n     * all components have finished animating out.\n     */\n    const { forceRender } = useContext(LayoutGroupContext);\n    return (jsx(Fragment, { children: renderedChildren.map((child) => {\n            const key = getChildKey(child);\n            const isPresent = propagate && !isParentPresent\n                ? false\n                : presentChildren === renderedChildren ||\n                    presentKeys.includes(key);\n            const onExit = () => {\n                if (exitComplete.has(key)) {\n                    exitComplete.set(key, true);\n                }\n                else {\n                    return;\n                }\n                let isEveryExitComplete = true;\n                exitComplete.forEach((isExitComplete) => {\n                    if (!isExitComplete)\n                        isEveryExitComplete = false;\n                });\n                if (isEveryExitComplete) {\n                    forceRender?.();\n                    setRenderedChildren(pendingPresentChildren.current);\n                    propagate && safeToRemove?.();\n                    onExitComplete && onExitComplete();\n                }\n            };\n            return (jsx(PresenceChild, { isPresent: isPresent, initial: !isInitialRender.current || initial\n                    ? undefined\n                    : false, custom: custom, presenceAffectsLayout: presenceAffectsLayout, mode: mode, onExitComplete: isPresent ? undefined : onExit, anchorX: anchorX, children: child }, key));\n        }) }));\n};\n\nexport { AnimatePresence };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,GAAG,EAAEC,QAAQ,QAAQ,mBAAmB;AACjD,SAASC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AAC7D,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,yBAAyB,QAAQ,uCAAuC;AACjF,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,YAAY,EAAEC,WAAW,QAAQ,aAAa;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGC,IAAA,IAA6I;EAAA,IAA5I;IAAEC,QAAQ;IAAEC,MAAM;IAAEC,OAAO,GAAG,IAAI;IAAEC,cAAc;IAAEC,qBAAqB,GAAG,IAAI;IAAEC,IAAI,GAAG,MAAM;IAAEC,SAAS,GAAG,KAAK;IAAEC,OAAO,GAAG;EAAQ,CAAC,GAAAR,IAAA;EAC5J,MAAM,CAACS,eAAe,EAAEC,YAAY,CAAC,GAAGd,WAAW,CAACW,SAAS,CAAC;EAC9D;AACJ;AACA;AACA;EACI,MAAMI,eAAe,GAAGvB,OAAO,CAAC,MAAMS,YAAY,CAACI,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACzE;AACJ;AACA;AACA;EACI,MAAMW,WAAW,GAAGL,SAAS,IAAI,CAACE,eAAe,GAAG,EAAE,GAAGE,eAAe,CAACE,GAAG,CAACf,WAAW,CAAC;EACzF;AACJ;AACA;EACI,MAAMgB,eAAe,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACpC;AACJ;AACA;AACA;AACA;EACI,MAAM0B,sBAAsB,GAAG1B,MAAM,CAACsB,eAAe,CAAC;EACtD;AACJ;AACA;EACI,MAAMK,YAAY,GAAGvB,WAAW,CAAC,MAAM,IAAIwB,GAAG,EAAE,CAAC;EACjD;AACJ;AACA;AACA;EACI,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAACqB,eAAe,CAAC;EACrE,MAAM,CAACS,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAACqB,eAAe,CAAC;EACzEjB,yBAAyB,CAAC,MAAM;IAC5BoB,eAAe,CAACQ,OAAO,GAAG,KAAK;IAC/BP,sBAAsB,CAACO,OAAO,GAAGX,eAAe;IAChD;AACR;AACA;IACQ,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,gBAAgB,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9C,MAAME,GAAG,GAAG3B,WAAW,CAACsB,gBAAgB,CAACG,CAAC,CAAC,CAAC;MAC5C,IAAI,CAACX,WAAW,CAACc,QAAQ,CAACD,GAAG,CAAC,EAAE;QAC5B,IAAIT,YAAY,CAACW,GAAG,CAACF,GAAG,CAAC,KAAK,IAAI,EAAE;UAChCT,YAAY,CAACY,GAAG,CAACH,GAAG,EAAE,KAAK,CAAC;QAChC;MACJ,CAAC,MACI;QACDT,YAAY,CAACa,MAAM,CAACJ,GAAG,CAAC;MAC5B;IACJ;EACJ,CAAC,EAAE,CAACL,gBAAgB,EAAER,WAAW,CAACY,MAAM,EAAEZ,WAAW,CAACkB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EACjE,MAAMC,eAAe,GAAG,EAAE;EAC1B,IAAIpB,eAAe,KAAKO,cAAc,EAAE;IACpC,IAAIc,YAAY,GAAG,CAAC,GAAGrB,eAAe,CAAC;IACvC;AACR;AACA;AACA;IACQ,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,gBAAgB,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9C,MAAMU,KAAK,GAAGb,gBAAgB,CAACG,CAAC,CAAC;MACjC,MAAME,GAAG,GAAG3B,WAAW,CAACmC,KAAK,CAAC;MAC9B,IAAI,CAACrB,WAAW,CAACc,QAAQ,CAACD,GAAG,CAAC,EAAE;QAC5BO,YAAY,CAACE,MAAM,CAACX,CAAC,EAAE,CAAC,EAAEU,KAAK,CAAC;QAChCF,eAAe,CAACI,IAAI,CAACF,KAAK,CAAC;MAC/B;IACJ;IACA;AACR;AACA;AACA;IACQ,IAAI3B,IAAI,KAAK,MAAM,IAAIyB,eAAe,CAACP,MAAM,EAAE;MAC3CQ,YAAY,GAAGD,eAAe;IAClC;IACAV,mBAAmB,CAACxB,YAAY,CAACmC,YAAY,CAAC,CAAC;IAC/Cb,iBAAiB,CAACR,eAAe,CAAC;IAClC;AACR;AACA;AACA;IACQ,OAAO,IAAI;EACf;EACA,IAAIyB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrChC,IAAI,KAAK,MAAM,IACfc,gBAAgB,CAACI,MAAM,GAAG,CAAC,EAAE;IAC7Be,OAAO,CAACC,IAAI,CAAE,+IAA8I,CAAC;EACjK;EACA;AACJ;AACA;AACA;AACA;EACI,MAAM;IAAEC;EAAY,CAAC,GAAGlD,UAAU,CAACC,kBAAkB,CAAC;EACtD,OAAQN,GAAG,CAACC,QAAQ,EAAE;IAAEc,QAAQ,EAAEmB,gBAAgB,CAACP,GAAG,CAAEoB,KAAK,IAAK;MAC1D,MAAMR,GAAG,GAAG3B,WAAW,CAACmC,KAAK,CAAC;MAC9B,MAAMS,SAAS,GAAGnC,SAAS,IAAI,CAACE,eAAe,GACzC,KAAK,GACLE,eAAe,KAAKS,gBAAgB,IAClCR,WAAW,CAACc,QAAQ,CAACD,GAAG,CAAC;MACjC,MAAMkB,MAAM,GAAGA,CAAA,KAAM;QACjB,IAAI3B,YAAY,CAAC4B,GAAG,CAACnB,GAAG,CAAC,EAAE;UACvBT,YAAY,CAACY,GAAG,CAACH,GAAG,EAAE,IAAI,CAAC;QAC/B,CAAC,MACI;UACD;QACJ;QACA,IAAIoB,mBAAmB,GAAG,IAAI;QAC9B7B,YAAY,CAAC8B,OAAO,CAAEC,cAAc,IAAK;UACrC,IAAI,CAACA,cAAc,EACfF,mBAAmB,GAAG,KAAK;QACnC,CAAC,CAAC;QACF,IAAIA,mBAAmB,EAAE;UACrBJ,WAAW,IAAI;UACfpB,mBAAmB,CAACN,sBAAsB,CAACO,OAAO,CAAC;UACnDf,SAAS,IAAIG,YAAY,IAAI;UAC7BN,cAAc,IAAIA,cAAc,EAAE;QACtC;MACJ,CAAC;MACD,OAAQlB,GAAG,CAACS,aAAa,EAAE;QAAE+C,SAAS,EAAEA,SAAS;QAAEvC,OAAO,EAAE,CAACW,eAAe,CAACQ,OAAO,IAAInB,OAAO,GACrF6C,SAAS,GACT,KAAK;QAAE9C,MAAM,EAAEA,MAAM;QAAEG,qBAAqB,EAAEA,qBAAqB;QAAEC,IAAI,EAAEA,IAAI;QAAEF,cAAc,EAAEsC,SAAS,GAAGM,SAAS,GAAGL,MAAM;QAAEnC,OAAO,EAAEA,OAAO;QAAEP,QAAQ,EAAEgC;MAAM,CAAC,EAAER,GAAG,CAAC;IACxL,CAAC;EAAE,CAAC,CAAC;AACb,CAAC;AAED,SAAS1B,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}