{"ast": null, "code": "const distance = (a, b) => Math.abs(a - b);\nfunction distance2D(a, b) {\n  // Multi-dimensional\n  const xDelta = distance(a.x, b.x);\n  const yDelta = distance(a.y, b.y);\n  return Math.sqrt(xDelta ** 2 + yDelta ** 2);\n}\nexport { distance, distance2D };", "map": {"version": 3, "names": ["distance", "a", "b", "Math", "abs", "distance2D", "xDelta", "x", "y<PERSON><PERSON><PERSON>", "y", "sqrt"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/utils/distance.mjs"], "sourcesContent": ["const distance = (a, b) => Math.abs(a - b);\nfunction distance2D(a, b) {\n    // Multi-dimensional\n    const xDelta = distance(a.x, b.x);\n    const yDelta = distance(a.y, b.y);\n    return Math.sqrt(xDelta ** 2 + yDelta ** 2);\n}\n\nexport { distance, distance2D };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKC,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGC,CAAC,CAAC;AAC1C,SAASG,UAAUA,CAACJ,CAAC,EAAEC,CAAC,EAAE;EACtB;EACA,MAAMI,MAAM,GAAGN,QAAQ,CAACC,CAAC,CAACM,CAAC,EAAEL,CAAC,CAACK,CAAC,CAAC;EACjC,MAAMC,MAAM,GAAGR,QAAQ,CAACC,CAAC,CAACQ,CAAC,EAAEP,CAAC,CAACO,CAAC,CAAC;EACjC,OAAON,IAAI,CAACO,IAAI,CAACJ,MAAM,IAAI,CAAC,GAAGE,MAAM,IAAI,CAAC,CAAC;AAC/C;AAEA,SAASR,QAAQ,EAAEK,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}