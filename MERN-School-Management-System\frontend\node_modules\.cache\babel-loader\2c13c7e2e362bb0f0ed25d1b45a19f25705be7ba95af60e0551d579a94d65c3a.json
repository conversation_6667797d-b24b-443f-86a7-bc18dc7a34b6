{"ast": null, "code": "import { getValueTransition, frame, JSAnimation, AsyncMotionValueAnimation } from 'motion-dom';\nimport { secondsToMilliseconds, MotionGlobalConfig } from 'motion-utils';\nimport { getFinalKeyframe } from '../animators/waapi/utils/get-final-keyframe.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isTransitionDefined } from '../utils/is-transition-defined.mjs';\nconst animateMotionValue = function (name, value, target) {\n  let transition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  let element = arguments.length > 4 ? arguments[4] : undefined;\n  let isHandoff = arguments.length > 5 ? arguments[5] : undefined;\n  return onComplete => {\n    const valueTransition = getValueTransition(transition, name) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let {\n      elapsed = 0\n    } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    const options = {\n      keyframes: Array.isArray(target) ? target : [null, target],\n      ease: \"easeOut\",\n      velocity: value.getVelocity(),\n      ...valueTransition,\n      delay: -elapsed,\n      onUpdate: v => {\n        value.set(v);\n        valueTransition.onUpdate && valueTransition.onUpdate(v);\n      },\n      onComplete: () => {\n        onComplete();\n        valueTransition.onComplete && valueTransition.onComplete();\n      },\n      name,\n      motionValue: value,\n      element: isHandoff ? undefined : element\n    };\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unique transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n      Object.assign(options, getDefaultTransition(name, options));\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    options.duration && (options.duration = secondsToMilliseconds(options.duration));\n    options.repeatDelay && (options.repeatDelay = secondsToMilliseconds(options.repeatDelay));\n    /**\n     * Support deprecated way to set initial value. Prefer keyframe syntax.\n     */\n    if (options.from !== undefined) {\n      options.keyframes[0] = options.from;\n    }\n    let shouldSkip = false;\n    if (options.type === false || options.duration === 0 && !options.repeatDelay) {\n      options.duration = 0;\n      if (options.delay === 0) {\n        shouldSkip = true;\n      }\n    }\n    if (MotionGlobalConfig.instantAnimations || MotionGlobalConfig.skipAnimations) {\n      shouldSkip = true;\n      options.duration = 0;\n      options.delay = 0;\n    }\n    /**\n     * If the transition type or easing has been explicitly set by the user\n     * then we don't want to allow flattening the animation.\n     */\n    options.allowFlatten = !valueTransition.type && !valueTransition.ease;\n    /**\n     * If we can or must skip creating the animation, and apply only\n     * the final keyframe, do so. We also check once keyframes are resolved but\n     * this early check prevents the need to create an animation at all.\n     */\n    if (shouldSkip && !isHandoff && value.get() !== undefined) {\n      const finalKeyframe = getFinalKeyframe(options.keyframes, valueTransition);\n      if (finalKeyframe !== undefined) {\n        frame.update(() => {\n          options.onUpdate(finalKeyframe);\n          options.onComplete();\n        });\n        return;\n      }\n    }\n    return valueTransition.isSync ? new JSAnimation(options) : new AsyncMotionValueAnimation(options);\n  };\n};\nexport { animateMotionValue };", "map": {"version": 3, "names": ["getValueTransition", "frame", "JSAnimation", "AsyncMotionValueAnimation", "secondsToMilliseconds", "MotionGlobalConfig", "getFinalKeyframe", "getDefaultTransition", "isTransitionDefined", "animateMotionValue", "name", "value", "target", "transition", "arguments", "length", "undefined", "element", "<PERSON><PERSON><PERSON><PERSON>", "onComplete", "valueTransition", "delay", "elapsed", "options", "keyframes", "Array", "isArray", "ease", "velocity", "getVelocity", "onUpdate", "v", "set", "motionValue", "Object", "assign", "duration", "repeatDelay", "from", "shouldSkip", "type", "instantAnimations", "skipAnimations", "allowFlatten", "get", "finalKeyframe", "update", "isSync"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs"], "sourcesContent": ["import { getValueTransition, frame, JSAnimation, AsyncMotionValueAnimation } from 'motion-dom';\nimport { secondsToMilliseconds, MotionGlobalConfig } from 'motion-utils';\nimport { getFinalKeyframe } from '../animators/waapi/utils/get-final-keyframe.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isTransitionDefined } from '../utils/is-transition-defined.mjs';\n\nconst animateMotionValue = (name, value, target, transition = {}, element, isHandoff) => (onComplete) => {\n    const valueTransition = getValueTransition(transition, name) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let { elapsed = 0 } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    const options = {\n        keyframes: Array.isArray(target) ? target : [null, target],\n        ease: \"easeOut\",\n        velocity: value.getVelocity(),\n        ...valueTransition,\n        delay: -elapsed,\n        onUpdate: (v) => {\n            value.set(v);\n            valueTransition.onUpdate && valueTransition.onUpdate(v);\n        },\n        onComplete: () => {\n            onComplete();\n            valueTransition.onComplete && valueTransition.onComplete();\n        },\n        name,\n        motionValue: value,\n        element: isHandoff ? undefined : element,\n    };\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unique transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n        Object.assign(options, getDefaultTransition(name, options));\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    options.duration && (options.duration = secondsToMilliseconds(options.duration));\n    options.repeatDelay && (options.repeatDelay = secondsToMilliseconds(options.repeatDelay));\n    /**\n     * Support deprecated way to set initial value. Prefer keyframe syntax.\n     */\n    if (options.from !== undefined) {\n        options.keyframes[0] = options.from;\n    }\n    let shouldSkip = false;\n    if (options.type === false ||\n        (options.duration === 0 && !options.repeatDelay)) {\n        options.duration = 0;\n        if (options.delay === 0) {\n            shouldSkip = true;\n        }\n    }\n    if (MotionGlobalConfig.instantAnimations ||\n        MotionGlobalConfig.skipAnimations) {\n        shouldSkip = true;\n        options.duration = 0;\n        options.delay = 0;\n    }\n    /**\n     * If the transition type or easing has been explicitly set by the user\n     * then we don't want to allow flattening the animation.\n     */\n    options.allowFlatten = !valueTransition.type && !valueTransition.ease;\n    /**\n     * If we can or must skip creating the animation, and apply only\n     * the final keyframe, do so. We also check once keyframes are resolved but\n     * this early check prevents the need to create an animation at all.\n     */\n    if (shouldSkip && !isHandoff && value.get() !== undefined) {\n        const finalKeyframe = getFinalKeyframe(options.keyframes, valueTransition);\n        if (finalKeyframe !== undefined) {\n            frame.update(() => {\n                options.onUpdate(finalKeyframe);\n                options.onComplete();\n            });\n            return;\n        }\n    }\n    return valueTransition.isSync\n        ? new JSAnimation(options)\n        : new AsyncMotionValueAnimation(options);\n};\n\nexport { animateMotionValue };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,KAAK,EAAEC,WAAW,EAAEC,yBAAyB,QAAQ,YAAY;AAC9F,SAASC,qBAAqB,EAAEC,kBAAkB,QAAQ,cAAc;AACxE,SAASC,gBAAgB,QAAQ,iDAAiD;AAClF,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,mBAAmB,QAAQ,oCAAoC;AAExE,MAAMC,kBAAkB,GAAG,SAAAA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM;EAAA,IAAEC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEG,OAAO,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEE,SAAS,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,OAAMG,UAAU,IAAK;IACrG,MAAMC,eAAe,GAAGpB,kBAAkB,CAACa,UAAU,EAAEH,IAAI,CAAC,IAAI,CAAC,CAAC;IAClE;AACJ;AACA;AACA;AACA;IACI,MAAMW,KAAK,GAAGD,eAAe,CAACC,KAAK,IAAIR,UAAU,CAACQ,KAAK,IAAI,CAAC;IAC5D;AACJ;AACA;AACA;IACI,IAAI;MAAEC,OAAO,GAAG;IAAE,CAAC,GAAGT,UAAU;IAChCS,OAAO,GAAGA,OAAO,GAAGlB,qBAAqB,CAACiB,KAAK,CAAC;IAChD,MAAME,OAAO,GAAG;MACZC,SAAS,EAAEC,KAAK,CAACC,OAAO,CAACd,MAAM,CAAC,GAAGA,MAAM,GAAG,CAAC,IAAI,EAAEA,MAAM,CAAC;MAC1De,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAEjB,KAAK,CAACkB,WAAW,EAAE;MAC7B,GAAGT,eAAe;MAClBC,KAAK,EAAE,CAACC,OAAO;MACfQ,QAAQ,EAAGC,CAAC,IAAK;QACbpB,KAAK,CAACqB,GAAG,CAACD,CAAC,CAAC;QACZX,eAAe,CAACU,QAAQ,IAAIV,eAAe,CAACU,QAAQ,CAACC,CAAC,CAAC;MAC3D,CAAC;MACDZ,UAAU,EAAEA,CAAA,KAAM;QACdA,UAAU,EAAE;QACZC,eAAe,CAACD,UAAU,IAAIC,eAAe,CAACD,UAAU,EAAE;MAC9D,CAAC;MACDT,IAAI;MACJuB,WAAW,EAAEtB,KAAK;MAClBM,OAAO,EAAEC,SAAS,GAAGF,SAAS,GAAGC;IACrC,CAAC;IACD;AACJ;AACA;AACA;IACI,IAAI,CAACT,mBAAmB,CAACY,eAAe,CAAC,EAAE;MACvCc,MAAM,CAACC,MAAM,CAACZ,OAAO,EAAEhB,oBAAoB,CAACG,IAAI,EAAEa,OAAO,CAAC,CAAC;IAC/D;IACA;AACJ;AACA;AACA;AACA;IACIA,OAAO,CAACa,QAAQ,KAAKb,OAAO,CAACa,QAAQ,GAAGhC,qBAAqB,CAACmB,OAAO,CAACa,QAAQ,CAAC,CAAC;IAChFb,OAAO,CAACc,WAAW,KAAKd,OAAO,CAACc,WAAW,GAAGjC,qBAAqB,CAACmB,OAAO,CAACc,WAAW,CAAC,CAAC;IACzF;AACJ;AACA;IACI,IAAId,OAAO,CAACe,IAAI,KAAKtB,SAAS,EAAE;MAC5BO,OAAO,CAACC,SAAS,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACe,IAAI;IACvC;IACA,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIhB,OAAO,CAACiB,IAAI,KAAK,KAAK,IACrBjB,OAAO,CAACa,QAAQ,KAAK,CAAC,IAAI,CAACb,OAAO,CAACc,WAAY,EAAE;MAClDd,OAAO,CAACa,QAAQ,GAAG,CAAC;MACpB,IAAIb,OAAO,CAACF,KAAK,KAAK,CAAC,EAAE;QACrBkB,UAAU,GAAG,IAAI;MACrB;IACJ;IACA,IAAIlC,kBAAkB,CAACoC,iBAAiB,IACpCpC,kBAAkB,CAACqC,cAAc,EAAE;MACnCH,UAAU,GAAG,IAAI;MACjBhB,OAAO,CAACa,QAAQ,GAAG,CAAC;MACpBb,OAAO,CAACF,KAAK,GAAG,CAAC;IACrB;IACA;AACJ;AACA;AACA;IACIE,OAAO,CAACoB,YAAY,GAAG,CAACvB,eAAe,CAACoB,IAAI,IAAI,CAACpB,eAAe,CAACO,IAAI;IACrE;AACJ;AACA;AACA;AACA;IACI,IAAIY,UAAU,IAAI,CAACrB,SAAS,IAAIP,KAAK,CAACiC,GAAG,EAAE,KAAK5B,SAAS,EAAE;MACvD,MAAM6B,aAAa,GAAGvC,gBAAgB,CAACiB,OAAO,CAACC,SAAS,EAAEJ,eAAe,CAAC;MAC1E,IAAIyB,aAAa,KAAK7B,SAAS,EAAE;QAC7Bf,KAAK,CAAC6C,MAAM,CAAC,MAAM;UACfvB,OAAO,CAACO,QAAQ,CAACe,aAAa,CAAC;UAC/BtB,OAAO,CAACJ,UAAU,EAAE;QACxB,CAAC,CAAC;QACF;MACJ;IACJ;IACA,OAAOC,eAAe,CAAC2B,MAAM,GACvB,IAAI7C,WAAW,CAACqB,OAAO,CAAC,GACxB,IAAIpB,yBAAyB,CAACoB,OAAO,CAAC;EAChD,CAAC;AAAA;AAED,SAASd,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}