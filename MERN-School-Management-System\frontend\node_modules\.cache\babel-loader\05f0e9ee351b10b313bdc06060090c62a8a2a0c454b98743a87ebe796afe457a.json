{"ast": null, "code": "import { floatRegex } from '../utils/float-regex.mjs';\nimport { isNullish } from '../utils/is-nullish.mjs';\nimport { singleColorRegex } from '../utils/single-color-regex.mjs';\n\n/**\n * Returns true if the provided string is a color, ie rgba(0,0,0,0) or #000,\n * but false if a number or multiple colors\n */\nconst isColorString = (type, testProp) => v => {\n  return Boolean(typeof v === \"string\" && singleColorRegex.test(v) && v.startsWith(type) || testProp && !isNullish(v) && Object.prototype.hasOwnProperty.call(v, testProp));\n};\nconst splitColor = (aName, bName, cName) => v => {\n  if (typeof v !== \"string\") return v;\n  const [a, b, c, alpha] = v.match(floatRegex);\n  return {\n    [aName]: parseFloat(a),\n    [bName]: parseFloat(b),\n    [cName]: parseFloat(c),\n    alpha: alpha !== undefined ? parseFloat(alpha) : 1\n  };\n};\nexport { isColorString, splitColor };", "map": {"version": 3, "names": ["floatRegex", "<PERSON><PERSON><PERSON><PERSON>", "singleColorRegex", "isColorString", "type", "testProp", "v", "Boolean", "test", "startsWith", "Object", "prototype", "hasOwnProperty", "call", "splitColor", "aName", "bName", "cName", "a", "b", "c", "alpha", "match", "parseFloat", "undefined"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/value/types/color/utils.mjs"], "sourcesContent": ["import { floatRegex } from '../utils/float-regex.mjs';\nimport { isNullish } from '../utils/is-nullish.mjs';\nimport { singleColorRegex } from '../utils/single-color-regex.mjs';\n\n/**\n * Returns true if the provided string is a color, ie rgba(0,0,0,0) or #000,\n * but false if a number or multiple colors\n */\nconst isColorString = (type, testProp) => (v) => {\n    return Boolean((typeof v === \"string\" &&\n        singleColorRegex.test(v) &&\n        v.startsWith(type)) ||\n        (testProp &&\n            !isNullish(v) &&\n            Object.prototype.hasOwnProperty.call(v, testProp)));\n};\nconst splitColor = (aName, bName, cName) => (v) => {\n    if (typeof v !== \"string\")\n        return v;\n    const [a, b, c, alpha] = v.match(floatRegex);\n    return {\n        [aName]: parseFloat(a),\n        [bName]: parseFloat(b),\n        [cName]: parseFloat(c),\n        alpha: alpha !== undefined ? parseFloat(alpha) : 1,\n    };\n};\n\nexport { isColorString, splitColor };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,0BAA0B;AACrD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,gBAAgB,QAAQ,iCAAiC;;AAElE;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAGA,CAACC,IAAI,EAAEC,QAAQ,KAAMC,CAAC,IAAK;EAC7C,OAAOC,OAAO,CAAE,OAAOD,CAAC,KAAK,QAAQ,IACjCJ,gBAAgB,CAACM,IAAI,CAACF,CAAC,CAAC,IACxBA,CAAC,CAACG,UAAU,CAACL,IAAI,CAAC,IACjBC,QAAQ,IACL,CAACJ,SAAS,CAACK,CAAC,CAAC,IACbI,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAED,QAAQ,CAAE,CAAC;AAC/D,CAAC;AACD,MAAMS,UAAU,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAMX,CAAC,IAAK;EAC/C,IAAI,OAAOA,CAAC,KAAK,QAAQ,EACrB,OAAOA,CAAC;EACZ,MAAM,CAACY,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC,GAAGf,CAAC,CAACgB,KAAK,CAACtB,UAAU,CAAC;EAC5C,OAAO;IACH,CAACe,KAAK,GAAGQ,UAAU,CAACL,CAAC,CAAC;IACtB,CAACF,KAAK,GAAGO,UAAU,CAACJ,CAAC,CAAC;IACtB,CAACF,KAAK,GAAGM,UAAU,CAACH,CAAC,CAAC;IACtBC,KAAK,EAAEA,KAAK,KAAKG,SAAS,GAAGD,UAAU,CAACF,KAAK,CAAC,GAAG;EACrD,CAAC;AACL,CAAC;AAED,SAASlB,aAAa,EAAEW,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}