import React, { useState } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Button,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  Description as DescriptionIcon,
  Print as PrintIcon,
  Download as DownloadIcon,
  Preview as PreviewIcon,
  School as SchoolIcon,
  CardMembership as CardMembershipIcon,
  Assignment as AssignmentIcon,
  WorkspacePremium as CertificateIcon,
  Email as EmailIcon,
  Share as ShareIcon
} from '@mui/icons-material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
}));

const DocumentCard = styled(Card)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
  },
}));

const DocumentGeneration = () => {
  const [selectedStudent, setSelectedStudent] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [previewDialog, setPreviewDialog] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);

  // Sample data
  const students = [
    { id: 1, name: 'John Doe', rollNumber: 'ST001', class: '10A' },
    { id: 2, name: 'Alice Smith', rollNumber: 'ST002', class: '10A' },
    { id: 3, name: 'Bob Johnson', rollNumber: 'ST003', class: '9B' },
  ];

  const classes = ['10A', '10B', '9A', '9B', '8A', '8B'];

  const documentTypes = [
    {
      id: 1,
      title: 'Admission Letter',
      description: 'Official admission confirmation letter',
      icon: <SchoolIcon />,
      color: '#667eea',
      category: 'Admission'
    },
    {
      id: 2,
      title: 'Study Certificate',
      description: 'Academic study certificate',
      icon: <CertificateIcon />,
      color: '#f093fb',
      category: 'Academic'
    },
    {
      id: 3,
      title: 'Transfer Certificate',
      description: 'School transfer certificate',
      icon: <AssignmentIcon />,
      color: '#4facfe',
      category: 'Transfer'
    },
    {
      id: 4,
      title: 'Character Certificate',
      description: 'Student character certificate',
      icon: <CardMembershipIcon />,
      color: '#fa709a',
      category: 'Character'
    },
    {
      id: 5,
      title: 'Bonafide Certificate',
      description: 'Student bonafide certificate',
      icon: <DescriptionIcon />,
      color: '#a8edea',
      category: 'Bonafide'
    },
    {
      id: 6,
      title: 'ID Card',
      description: 'Student identification card',
      icon: <CardMembershipIcon />,
      color: '#ffeaa7',
      category: 'Identity'
    },
  ];

  const handleDocumentSelect = (document) => {
    setSelectedDocument(document);
    setPreviewDialog(true);
  };

  const handleGenerate = (format) => {
    console.log(`Generating ${selectedDocument?.title} in ${format} format`);
    // Implementation for document generation
    setPreviewDialog(false);
  };

  const renderDocumentCard = (document) => (
    <Grid item xs={12} sm={6} md={4} key={document.id}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <DocumentCard onClick={() => handleDocumentSelect(document)}>
          <CardContent>
            <Box display="flex" alignItems="center" mb={2}>
              <Box
                sx={{
                  width: 60,
                  height: 60,
                  borderRadius: '12px',
                  bgcolor: document.color,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  mr: 2
                }}
              >
                {React.cloneElement(document.icon, { fontSize: 'large' })}
              </Box>
              <Box flex={1}>
                <Typography variant="h6" fontWeight="bold">
                  {document.title}
                </Typography>
                <Chip
                  label={document.category}
                  size="small"
                  sx={{ 
                    bgcolor: document.color,
                    color: 'white',
                    mt: 0.5
                  }}
                />
              </Box>
            </Box>
            <Typography variant="body2" color="text.secondary" mb={2}>
              {document.description}
            </Typography>
            <Box display="flex" justifyContent="space-between">
              <Tooltip title="Preview">
                <IconButton size="small" color="primary">
                  <PreviewIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Print">
                <IconButton size="small" color="secondary">
                  <PrintIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Download">
                <IconButton size="small" color="success">
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Email">
                <IconButton size="small" color="info">
                  <EmailIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </CardContent>
        </DocumentCard>
      </motion.div>
    </Grid>
  );

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box mb={4}>
          <Typography variant="h4" fontWeight="bold" color="primary" gutterBottom>
            📄 Document Generation System
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Generate admission letters, certificates, and student documents
          </Typography>
        </Box>
      </motion.div>

      {/* Selection Panel */}
      <StyledPaper sx={{ mb: 3 }}>
        <Typography variant="h6" fontWeight="bold" mb={3}>
          Student Selection
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Select Class</InputLabel>
              <Select
                value={selectedClass}
                onChange={(e) => setSelectedClass(e.target.value)}
                label="Select Class"
              >
                {classes.map((cls) => (
                  <MenuItem key={cls} value={cls}>
                    Class {cls}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Select Student</InputLabel>
              <Select
                value={selectedStudent}
                onChange={(e) => setSelectedStudent(e.target.value)}
                label="Select Student"
              >
                {students
                  .filter(student => !selectedClass || student.class === selectedClass)
                  .map((student) => (
                    <MenuItem key={student.id} value={student.id}>
                      {student.name} ({student.rollNumber})
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box display="flex" gap={2} height="100%" alignItems="center">
              <Button variant="contained" color="primary" fullWidth>
                Generate for All Students
              </Button>
              <Button variant="outlined" color="secondary" fullWidth>
                Bulk Generation
              </Button>
            </Box>
          </Grid>
        </Grid>
      </StyledPaper>

      {/* Document Types */}
      <StyledPaper>
        <Typography variant="h6" fontWeight="bold" mb={3}>
          Available Documents
        </Typography>
        <Grid container spacing={3}>
          {documentTypes.map(renderDocumentCard)}
        </Grid>
      </StyledPaper>

      {/* Preview Dialog */}
      <Dialog 
        open={previewDialog} 
        onClose={() => setPreviewDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center">
            {selectedDocument?.icon}
            <Typography variant="h6" ml={1}>
              {selectedDocument?.title} - Preview & Generate
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box mb={3}>
            <Typography variant="body1" color="text.secondary" mb={2}>
              {selectedDocument?.description}
            </Typography>
            <Divider />
          </Box>

          <Typography variant="h6" gutterBottom>
            Generation Options
          </Typography>
          <List>
            <ListItem>
              <ListItemIcon>
                <PrintIcon color="primary" />
              </ListItemIcon>
              <ListItemText 
                primary="Print Document" 
                secondary="Print directly to connected printer"
              />
              <Button 
                variant="outlined" 
                color="primary"
                onClick={() => handleGenerate('print')}
              >
                Print
              </Button>
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <DownloadIcon color="secondary" />
              </ListItemIcon>
              <ListItemText 
                primary="Download PDF" 
                secondary="Download as PDF file"
              />
              <Button 
                variant="outlined" 
                color="secondary"
                onClick={() => handleGenerate('pdf')}
              >
                Download
              </Button>
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <EmailIcon color="info" />
              </ListItemIcon>
              <ListItemText 
                primary="Email Document" 
                secondary="Send via email to student/parent"
              />
              <Button 
                variant="outlined" 
                color="info"
                onClick={() => handleGenerate('email')}
              >
                Email
              </Button>
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <ShareIcon color="success" />
              </ListItemIcon>
              <ListItemText 
                primary="Share Link" 
                secondary="Generate shareable link"
              />
              <Button 
                variant="outlined" 
                color="success"
                onClick={() => handleGenerate('share')}
              >
                Share
              </Button>
            </ListItem>
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained" 
            color="primary"
            onClick={() => handleGenerate('preview')}
          >
            Preview Document
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default DocumentGeneration;
