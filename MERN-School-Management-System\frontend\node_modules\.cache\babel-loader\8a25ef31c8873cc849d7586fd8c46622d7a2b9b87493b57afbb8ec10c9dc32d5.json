{"ast": null, "code": "import { getValueTransition, frame, positionalKeys } from 'motion-dom';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { getOptimisedAppearId } from '../optimized-appear/get-appear-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation(_ref, key) {\n  let {\n    protectedKeys,\n    needsAnimating\n  } = _ref;\n  const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n  needsAnimating[key] = false;\n  return shouldBlock;\n}\nfunction animateTarget(visualElement, targetAndTransition) {\n  let {\n    delay = 0,\n    transitionOverride,\n    type\n  } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  let {\n    transition = visualElement.getDefaultTransition(),\n    transitionEnd,\n    ...target\n  } = targetAndTransition;\n  if (transitionOverride) transition = transitionOverride;\n  const animations = [];\n  const animationTypeState = type && visualElement.animationState && visualElement.animationState.getState()[type];\n  for (const key in target) {\n    const value = visualElement.getValue(key, visualElement.latestValues[key] ?? null);\n    const valueTarget = target[key];\n    if (valueTarget === undefined || animationTypeState && shouldBlockAnimation(animationTypeState, key)) {\n      continue;\n    }\n    const valueTransition = {\n      delay,\n      ...getValueTransition(transition || {}, key)\n    };\n    /**\n     * If the value is already at the defined target, skip the animation.\n     */\n    const currentValue = value.get();\n    if (currentValue !== undefined && !value.isAnimating && !Array.isArray(valueTarget) && valueTarget === currentValue && !valueTransition.velocity) {\n      continue;\n    }\n    /**\n     * If this is the first time a value is being animated, check\n     * to see if we're handling off from an existing animation.\n     */\n    let isHandoff = false;\n    if (window.MotionHandoffAnimation) {\n      const appearId = getOptimisedAppearId(visualElement);\n      if (appearId) {\n        const startTime = window.MotionHandoffAnimation(appearId, key, frame);\n        if (startTime !== null) {\n          valueTransition.startTime = startTime;\n          isHandoff = true;\n        }\n      }\n    }\n    addValueToWillChange(visualElement, key);\n    value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && positionalKeys.has(key) ? {\n      type: false\n    } : valueTransition, visualElement, isHandoff));\n    const animation = value.animation;\n    if (animation) {\n      animations.push(animation);\n    }\n  }\n  if (transitionEnd) {\n    Promise.all(animations).then(() => {\n      frame.update(() => {\n        transitionEnd && setTarget(visualElement, transitionEnd);\n      });\n    });\n  }\n  return animations;\n}\nexport { animateTarget };", "map": {"version": 3, "names": ["getValueTransition", "frame", "positional<PERSON>eys", "<PERSON><PERSON><PERSON><PERSON>", "addValueToWillChange", "getOptimisedAppearId", "animateMotionValue", "shouldBlockAnimation", "_ref", "key", "protected<PERSON><PERSON>s", "needsAnimating", "shouldBlock", "hasOwnProperty", "animate<PERSON>arget", "visualElement", "targetAndTransition", "delay", "transitionOverride", "type", "arguments", "length", "undefined", "transition", "getDefaultTransition", "transitionEnd", "target", "animations", "animationTypeState", "animationState", "getState", "value", "getValue", "latestValues", "valueTarget", "valueTransition", "currentValue", "get", "isAnimating", "Array", "isArray", "velocity", "<PERSON><PERSON><PERSON><PERSON>", "window", "MotionHandoffAnimation", "appearId", "startTime", "start", "shouldReduceMotion", "has", "animation", "push", "Promise", "all", "then", "update"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs"], "sourcesContent": ["import { getValueTransition, frame, positionalKeys } from 'motion-dom';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { getOptimisedAppearId } from '../optimized-appear/get-appear-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({ protectedKeys, needsAnimating }, key) {\n    const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n    needsAnimating[key] = false;\n    return shouldBlock;\n}\nfunction animateTarget(visualElement, targetAndTransition, { delay = 0, transitionOverride, type } = {}) {\n    let { transition = visualElement.getDefaultTransition(), transitionEnd, ...target } = targetAndTransition;\n    if (transitionOverride)\n        transition = transitionOverride;\n    const animations = [];\n    const animationTypeState = type &&\n        visualElement.animationState &&\n        visualElement.animationState.getState()[type];\n    for (const key in target) {\n        const value = visualElement.getValue(key, visualElement.latestValues[key] ?? null);\n        const valueTarget = target[key];\n        if (valueTarget === undefined ||\n            (animationTypeState &&\n                shouldBlockAnimation(animationTypeState, key))) {\n            continue;\n        }\n        const valueTransition = {\n            delay,\n            ...getValueTransition(transition || {}, key),\n        };\n        /**\n         * If the value is already at the defined target, skip the animation.\n         */\n        const currentValue = value.get();\n        if (currentValue !== undefined &&\n            !value.isAnimating &&\n            !Array.isArray(valueTarget) &&\n            valueTarget === currentValue &&\n            !valueTransition.velocity) {\n            continue;\n        }\n        /**\n         * If this is the first time a value is being animated, check\n         * to see if we're handling off from an existing animation.\n         */\n        let isHandoff = false;\n        if (window.MotionHandoffAnimation) {\n            const appearId = getOptimisedAppearId(visualElement);\n            if (appearId) {\n                const startTime = window.MotionHandoffAnimation(appearId, key, frame);\n                if (startTime !== null) {\n                    valueTransition.startTime = startTime;\n                    isHandoff = true;\n                }\n            }\n        }\n        addValueToWillChange(visualElement, key);\n        value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && positionalKeys.has(key)\n            ? { type: false }\n            : valueTransition, visualElement, isHandoff));\n        const animation = value.animation;\n        if (animation) {\n            animations.push(animation);\n        }\n    }\n    if (transitionEnd) {\n        Promise.all(animations).then(() => {\n            frame.update(() => {\n                transitionEnd && setTarget(visualElement, transitionEnd);\n            });\n        });\n    }\n    return animations;\n}\n\nexport { animateTarget };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,KAAK,EAAEC,cAAc,QAAQ,YAAY;AACtE,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,oBAAoB,QAAQ,iDAAiD;AACtF,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,kBAAkB,QAAQ,oBAAoB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAAC,IAAA,EAAoCC,GAAG,EAAE;EAAA,IAAxC;IAAEC,aAAa;IAAEC;EAAe,CAAC,GAAAH,IAAA;EAC3D,MAAMI,WAAW,GAAGF,aAAa,CAACG,cAAc,CAACJ,GAAG,CAAC,IAAIE,cAAc,CAACF,GAAG,CAAC,KAAK,IAAI;EACrFE,cAAc,CAACF,GAAG,CAAC,GAAG,KAAK;EAC3B,OAAOG,WAAW;AACtB;AACA,SAASE,aAAaA,CAACC,aAAa,EAAEC,mBAAmB,EAAgD;EAAA,IAA9C;IAAEC,KAAK,GAAG,CAAC;IAAEC,kBAAkB;IAAEC;EAAK,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACnG,IAAI;IAAEG,UAAU,GAAGR,aAAa,CAACS,oBAAoB,EAAE;IAAEC,aAAa;IAAE,GAAGC;EAAO,CAAC,GAAGV,mBAAmB;EACzG,IAAIE,kBAAkB,EAClBK,UAAU,GAAGL,kBAAkB;EACnC,MAAMS,UAAU,GAAG,EAAE;EACrB,MAAMC,kBAAkB,GAAGT,IAAI,IAC3BJ,aAAa,CAACc,cAAc,IAC5Bd,aAAa,CAACc,cAAc,CAACC,QAAQ,EAAE,CAACX,IAAI,CAAC;EACjD,KAAK,MAAMV,GAAG,IAAIiB,MAAM,EAAE;IACtB,MAAMK,KAAK,GAAGhB,aAAa,CAACiB,QAAQ,CAACvB,GAAG,EAAEM,aAAa,CAACkB,YAAY,CAACxB,GAAG,CAAC,IAAI,IAAI,CAAC;IAClF,MAAMyB,WAAW,GAAGR,MAAM,CAACjB,GAAG,CAAC;IAC/B,IAAIyB,WAAW,KAAKZ,SAAS,IACxBM,kBAAkB,IACfrB,oBAAoB,CAACqB,kBAAkB,EAAEnB,GAAG,CAAE,EAAE;MACpD;IACJ;IACA,MAAM0B,eAAe,GAAG;MACpBlB,KAAK;MACL,GAAGjB,kBAAkB,CAACuB,UAAU,IAAI,CAAC,CAAC,EAAEd,GAAG;IAC/C,CAAC;IACD;AACR;AACA;IACQ,MAAM2B,YAAY,GAAGL,KAAK,CAACM,GAAG,EAAE;IAChC,IAAID,YAAY,KAAKd,SAAS,IAC1B,CAACS,KAAK,CAACO,WAAW,IAClB,CAACC,KAAK,CAACC,OAAO,CAACN,WAAW,CAAC,IAC3BA,WAAW,KAAKE,YAAY,IAC5B,CAACD,eAAe,CAACM,QAAQ,EAAE;MAC3B;IACJ;IACA;AACR;AACA;AACA;IACQ,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,MAAM,CAACC,sBAAsB,EAAE;MAC/B,MAAMC,QAAQ,GAAGxC,oBAAoB,CAACU,aAAa,CAAC;MACpD,IAAI8B,QAAQ,EAAE;QACV,MAAMC,SAAS,GAAGH,MAAM,CAACC,sBAAsB,CAACC,QAAQ,EAAEpC,GAAG,EAAER,KAAK,CAAC;QACrE,IAAI6C,SAAS,KAAK,IAAI,EAAE;UACpBX,eAAe,CAACW,SAAS,GAAGA,SAAS;UACrCJ,SAAS,GAAG,IAAI;QACpB;MACJ;IACJ;IACAtC,oBAAoB,CAACW,aAAa,EAAEN,GAAG,CAAC;IACxCsB,KAAK,CAACgB,KAAK,CAACzC,kBAAkB,CAACG,GAAG,EAAEsB,KAAK,EAAEG,WAAW,EAAEnB,aAAa,CAACiC,kBAAkB,IAAI9C,cAAc,CAAC+C,GAAG,CAACxC,GAAG,CAAC,GAC7G;MAAEU,IAAI,EAAE;IAAM,CAAC,GACfgB,eAAe,EAAEpB,aAAa,EAAE2B,SAAS,CAAC,CAAC;IACjD,MAAMQ,SAAS,GAAGnB,KAAK,CAACmB,SAAS;IACjC,IAAIA,SAAS,EAAE;MACXvB,UAAU,CAACwB,IAAI,CAACD,SAAS,CAAC;IAC9B;EACJ;EACA,IAAIzB,aAAa,EAAE;IACf2B,OAAO,CAACC,GAAG,CAAC1B,UAAU,CAAC,CAAC2B,IAAI,CAAC,MAAM;MAC/BrD,KAAK,CAACsD,MAAM,CAAC,MAAM;QACf9B,aAAa,IAAItB,SAAS,CAACY,aAAa,EAAEU,aAAa,CAAC;MAC5D,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA,OAAOE,UAAU;AACrB;AAEA,SAASb,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}