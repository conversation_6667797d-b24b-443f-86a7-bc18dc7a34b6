{"ast": null, "code": "const isBezierDefinition = easing => Array.isArray(easing) && typeof easing[0] === \"number\";\nexport { isBezierDefinition };", "map": {"version": 3, "names": ["isBezierDefinition", "easing", "Array", "isArray"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-utils/dist/es/easing/utils/is-bezier-definition.mjs"], "sourcesContent": ["const isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\nexport { isBezierDefinition };\n"], "mappings": "AAAA,MAAMA,kBAAkB,GAAIC,MAAM,IAAKC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAI,OAAOA,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ;AAE7F,SAASD,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}