{"ast": null, "code": "import { warnOnce } from 'motion-utils';\nimport { useScroll } from '../use-scroll.mjs';\n\n/**\n * @deprecated useElementScroll is deprecated. Convert to useScroll({ container: ref })\n */\nfunction useElementScroll(ref) {\n  if (process.env.NODE_ENV === \"development\") {\n    warnOnce(false, \"useElementScroll is deprecated. Convert to useScroll({ container: ref }).\");\n  }\n  return useScroll({\n    container: ref\n  });\n}\nexport { useElementScroll };", "map": {"version": 3, "names": ["warnOnce", "useScroll", "useElementScroll", "ref", "process", "env", "NODE_ENV", "container"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/value/scroll/use-element-scroll.mjs"], "sourcesContent": ["import { warnOnce } from 'motion-utils';\nimport { useScroll } from '../use-scroll.mjs';\n\n/**\n * @deprecated useElementScroll is deprecated. Convert to useScroll({ container: ref })\n */\nfunction useElementScroll(ref) {\n    if (process.env.NODE_ENV === \"development\") {\n        warnOnce(false, \"useElementScroll is deprecated. Convert to useScroll({ container: ref }).\");\n    }\n    return useScroll({ container: ref });\n}\n\nexport { useElementScroll };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,SAAS,QAAQ,mBAAmB;;AAE7C;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IACxCN,QAAQ,CAAC,KAAK,EAAE,2EAA2E,CAAC;EAChG;EACA,OAAOC,SAAS,CAAC;IAAEM,SAAS,EAAEJ;EAAI,CAAC,CAAC;AACxC;AAEA,SAASD,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}