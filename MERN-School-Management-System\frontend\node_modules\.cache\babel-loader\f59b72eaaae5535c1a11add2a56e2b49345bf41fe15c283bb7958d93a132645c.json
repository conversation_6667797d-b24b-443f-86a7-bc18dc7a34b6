{"ast": null, "code": "import { resolveElements } from 'motion-dom';\nimport { isDOMKeyframes } from '../utils/is-dom-keyframes.mjs';\nfunction resolveSubjects(subject, keyframes, scope, selectorCache) {\n  if (typeof subject === \"string\" && isDOMKeyframes(keyframes)) {\n    return resolveElements(subject, scope, selectorCache);\n  } else if (subject instanceof NodeList) {\n    return Array.from(subject);\n  } else if (Array.isArray(subject)) {\n    return subject;\n  } else {\n    return [subject];\n  }\n}\nexport { resolveSubjects };", "map": {"version": 3, "names": ["resolveElements", "isDOMKeyframes", "resolveSubjects", "subject", "keyframes", "scope", "selectorCache", "NodeList", "Array", "from", "isArray"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/animation/animate/resolve-subjects.mjs"], "sourcesContent": ["import { resolveElements } from 'motion-dom';\nimport { isDOMKeyframes } from '../utils/is-dom-keyframes.mjs';\n\nfunction resolveSubjects(subject, keyframes, scope, selectorCache) {\n    if (typeof subject === \"string\" && isDOMKeyframes(keyframes)) {\n        return resolveElements(subject, scope, selectorCache);\n    }\n    else if (subject instanceof NodeList) {\n        return Array.from(subject);\n    }\n    else if (Array.isArray(subject)) {\n        return subject;\n    }\n    else {\n        return [subject];\n    }\n}\n\nexport { resolveSubjects };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,YAAY;AAC5C,SAASC,cAAc,QAAQ,+BAA+B;AAE9D,SAASC,eAAeA,CAACC,OAAO,EAAEC,SAAS,EAAEC,KAAK,EAAEC,aAAa,EAAE;EAC/D,IAAI,OAAOH,OAAO,KAAK,QAAQ,IAAIF,cAAc,CAACG,SAAS,CAAC,EAAE;IAC1D,OAAOJ,eAAe,CAACG,OAAO,EAAEE,KAAK,EAAEC,aAAa,CAAC;EACzD,CAAC,MACI,IAAIH,OAAO,YAAYI,QAAQ,EAAE;IAClC,OAAOC,KAAK,CAACC,IAAI,CAACN,OAAO,CAAC;EAC9B,CAAC,MACI,IAAIK,KAAK,CAACE,OAAO,CAACP,OAAO,CAAC,EAAE;IAC7B,OAAOA,OAAO;EAClB,CAAC,MACI;IACD,OAAO,CAACA,OAAO,CAAC;EACpB;AACJ;AAEA,SAASD,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}