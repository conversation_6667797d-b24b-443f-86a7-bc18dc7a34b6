{"ast": null, "code": "import { time } from '../../frameloop/sync-time.mjs';\nimport { frame, cancelFrame, frameData } from '../../frameloop/frame.mjs';\nconst frameloopDriver = update => {\n  const passTimestamp = _ref => {\n    let {\n      timestamp\n    } = _ref;\n    return update(timestamp);\n  };\n  return {\n    start: function () {\n      let keepAlive = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      return frame.update(passTimestamp, keepAlive);\n    },\n    stop: () => cancelFrame(passTimestamp),\n    /**\n     * If we're processing this frame we can use the\n     * framelocked timestamp to keep things in sync.\n     */\n    now: () => frameData.isProcessing ? frameData.timestamp : time.now()\n  };\n};\nexport { frameloopDriver };", "map": {"version": 3, "names": ["time", "frame", "cancelFrame", "frameData", "frameloopDriver", "update", "passTimestamp", "_ref", "timestamp", "start", "keepAlive", "arguments", "length", "undefined", "stop", "now", "isProcessing"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/animation/drivers/frame.mjs"], "sourcesContent": ["import { time } from '../../frameloop/sync-time.mjs';\nimport { frame, cancelFrame, frameData } from '../../frameloop/frame.mjs';\n\nconst frameloopDriver = (update) => {\n    const passTimestamp = ({ timestamp }) => update(timestamp);\n    return {\n        start: (keepAlive = true) => frame.update(passTimestamp, keepAlive),\n        stop: () => cancelFrame(passTimestamp),\n        /**\n         * If we're processing this frame we can use the\n         * framelocked timestamp to keep things in sync.\n         */\n        now: () => (frameData.isProcessing ? frameData.timestamp : time.now()),\n    };\n};\n\nexport { frameloopDriver };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,+BAA+B;AACpD,SAASC,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,2BAA2B;AAEzE,MAAMC,eAAe,GAAIC,MAAM,IAAK;EAChC,MAAMC,aAAa,GAAGC,IAAA;IAAA,IAAC;MAAEC;IAAU,CAAC,GAAAD,IAAA;IAAA,OAAKF,MAAM,CAACG,SAAS,CAAC;EAAA;EAC1D,OAAO;IACHC,KAAK,EAAE,SAAAA,CAAA;MAAA,IAACC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAAA,OAAKV,KAAK,CAACI,MAAM,CAACC,aAAa,EAAEI,SAAS,CAAC;IAAA;IACnEI,IAAI,EAAEA,CAAA,KAAMZ,WAAW,CAACI,aAAa,CAAC;IACtC;AACR;AACA;AACA;IACQS,GAAG,EAAEA,CAAA,KAAOZ,SAAS,CAACa,YAAY,GAAGb,SAAS,CAACK,SAAS,GAAGR,IAAI,CAACe,GAAG;EACvE,CAAC;AACL,CAAC;AAED,SAASX,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}