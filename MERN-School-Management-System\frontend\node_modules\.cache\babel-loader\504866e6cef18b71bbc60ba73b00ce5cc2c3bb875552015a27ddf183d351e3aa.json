{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\components\\\\layout\\\\ResponsiveSidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Drawer, List, ListItemButton, ListItemIcon, ListItemText, Divider, Box, Typography, useTheme, useMediaQuery, Avatar, Chip, Collapse } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Home as HomeIcon, Class as ClassIcon, Assignment as AssignmentIcon, SupervisorAccount as SupervisorAccountIcon, PersonOutline as PersonOutlineIcon, Announcement as AnnouncementIcon, Report as ReportIcon, AccountCircleOutlined as AccountCircleOutlinedIcon, ExitToApp as ExitToAppIcon, School as SchoolIcon,\n// New icons for additional modules\nInfo as InfoIcon, Payment as PaymentIcon, Description as DescriptionIcon, DirectionsBus as DirectionsBusIcon, People as PeopleIcon, PersonAdd as PersonAddIcon, EventAvailable as EventAvailableIcon, Quiz as QuizIcon, MenuBook as MenuBookIcon, NotificationsActive as NotificationsActiveIcon, Hotel as HotelIcon, Web as WebIcon, Business as BusinessIcon, ExpandLess, ExpandMore } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledDrawer = styled(Drawer)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    '& .MuiDrawer-paper': {\n      background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%)',\n      color: 'white',\n      borderRight: 'none',\n      boxShadow: '8px 0 32px rgba(0, 0, 0, 0.15)',\n      backdropFilter: 'blur(10px)',\n      position: 'fixed',\n      top: '64px',\n      // Position below the AppBar\n      height: 'calc(100vh - 64px)',\n      // Full height minus AppBar\n      zIndex: theme.zIndex.drawer,\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n        pointerEvents: 'none'\n      }\n    }\n  };\n});\n_c = StyledDrawer;\nconst SidebarHeader = styled(Box)(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    padding: theme.spacing(2, 2),\n    display: 'flex',\n    alignItems: 'center',\n    gap: theme.spacing(2),\n    borderBottom: '2px solid rgba(255, 255, 255, 0.15)',\n    background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n    backdropFilter: 'blur(10px)',\n    position: 'relative',\n    '&::after': {\n      content: '\"\"',\n      position: 'absolute',\n      bottom: 0,\n      left: '50%',\n      transform: 'translateX(-50%)',\n      width: '60%',\n      height: '2px',\n      background: 'linear-gradient(90deg, transparent, #FFD700, transparent)'\n    }\n  };\n});\n_c2 = SidebarHeader;\nconst StyledListItemButton = styled(ListItemButton)(_ref3 => {\n  let {\n    theme,\n    active\n  } = _ref3;\n  return {\n    margin: theme.spacing(0.5, 1.5),\n    borderRadius: '16px',\n    padding: theme.spacing(1.5, 2),\n    backgroundColor: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',\n    backdropFilter: active ? 'blur(15px)' : 'none',\n    border: active ? '1px solid rgba(255, 255, 255, 0.3)' : '1px solid transparent',\n    boxShadow: active ? '0 8px 32px rgba(0, 0, 0, 0.1)' : 'none',\n    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n    position: 'relative',\n    overflow: 'hidden',\n    '&:hover': {\n      backgroundColor: 'rgba(255, 255, 255, 0.15)',\n      transform: 'translateX(8px) scale(1.02)',\n      boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)'\n    },\n    '&:before': {\n      content: '\"\"',\n      position: 'absolute',\n      left: 0,\n      top: '50%',\n      transform: 'translateY(-50%)',\n      width: active ? '5px' : '0px',\n      height: '70%',\n      background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n      borderRadius: '0 8px 8px 0',\n      transition: 'width 0.3s ease',\n      boxShadow: active ? '0 0 20px rgba(255, 215, 0, 0.5)' : 'none'\n    },\n    '&:after': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: active ? 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))' : 'transparent',\n      pointerEvents: 'none',\n      borderRadius: '16px'\n    }\n  };\n});\n_c3 = StyledListItemButton;\nconst MenuSection = styled(Box)(_ref4 => {\n  let {\n    theme\n  } = _ref4;\n  return {\n    padding: theme.spacing(1, 1.5),\n    marginTop: theme.spacing(1.5)\n  };\n});\n_c4 = MenuSection;\nconst SectionTitle = styled(Typography)(_ref5 => {\n  let {\n    theme\n  } = _ref5;\n  return {\n    fontSize: '0.8rem',\n    fontWeight: 700,\n    textTransform: 'uppercase',\n    letterSpacing: '1px',\n    opacity: 0.9,\n    marginBottom: theme.spacing(1.5),\n    marginLeft: theme.spacing(1),\n    background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n    textShadow: '0 2px 4px rgba(0,0,0,0.1)'\n  };\n});\n_c5 = SectionTitle;\nconst ResponsiveSidebar = _ref6 => {\n  _s();\n  let {\n    open,\n    onClose,\n    variant = 'permanent'\n  } = _ref6;\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const location = useLocation();\n  // Simplified menu without collapsible sections\n\n  // Simplified main menu items\n  const mainMenuItems = [{\n    text: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/dashboard',\n    badge: null\n  }, {\n    text: 'Student Info',\n    icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/student-info',\n    badge: null\n  }, {\n    text: 'Students',\n    icon: /*#__PURE__*/_jsxDEV(PersonOutlineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/students',\n    badge: null\n  }, {\n    text: 'Classes',\n    icon: /*#__PURE__*/_jsxDEV(ClassIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/classes',\n    badge: null\n  }, {\n    text: 'Teachers',\n    icon: /*#__PURE__*/_jsxDEV(SupervisorAccountIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/teachers',\n    badge: null\n  }, {\n    text: 'Subjects',\n    icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/subjects',\n    badge: null\n  }, {\n    text: 'Fee Management',\n    icon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/fee-management',\n    badge: '12'\n  }, {\n    text: 'Transport',\n    icon: /*#__PURE__*/_jsxDEV(DirectionsBusIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/transport',\n    badge: null\n  }, {\n    text: 'Documents',\n    icon: /*#__PURE__*/_jsxDEV(DescriptionIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/documents',\n    badge: null\n  }, {\n    text: 'Notifications',\n    icon: /*#__PURE__*/_jsxDEV(NotificationsActiveIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/notifications',\n    badge: '15'\n  }, {\n    text: 'Notices',\n    icon: /*#__PURE__*/_jsxDEV(AnnouncementIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/notices',\n    badge: '3'\n  }];\n\n  // User account menu items\n  const userMenuItems = [{\n    text: 'Profile',\n    icon: /*#__PURE__*/_jsxDEV(AccountCircleOutlinedIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/profile',\n    badge: null\n  }, {\n    text: 'Logout',\n    icon: /*#__PURE__*/_jsxDEV(ExitToAppIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 13\n    }, this),\n    path: '/logout',\n    badge: null\n  }];\n  const isActive = path => {\n    if (path === '/Admin/dashboard') {\n      return location.pathname === '/' || location.pathname === '/Admin/dashboard';\n    }\n    return location.pathname.startsWith(path);\n  };\n  const renderMenuItem = (item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      x: -20\n    },\n    animate: {\n      opacity: 1,\n      x: 0\n    },\n    transition: {\n      delay: index * 0.05\n    },\n    children: /*#__PURE__*/_jsxDEV(StyledListItemButton, {\n      component: Link,\n      to: item.path,\n      active: isActive(item.path),\n      onClick: isMobile ? onClose : undefined,\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        sx: {\n          color: 'inherit',\n          minWidth: 40\n        },\n        children: item.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: item.text,\n        primaryTypographyProps: {\n          fontSize: '0.9rem',\n          fontWeight: isActive(item.path) ? 600 : 400\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), item.badge && /*#__PURE__*/_jsxDEV(Chip, {\n        label: item.badge,\n        size: \"small\",\n        sx: {\n          bgcolor: '#FFD700',\n          color: '#333',\n          fontSize: '0.7rem',\n          height: 20,\n          minWidth: 20,\n          fontWeight: 600\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)\n  }, item.text, false, {\n    fileName: _jsxFileName,\n    lineNumber: 256,\n    columnNumber: 5\n  }, this);\n  const drawerContent = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(SidebarHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        sx: {\n          bgcolor: 'rgba(255, 255, 255, 0.3)',\n          width: 56,\n          height: 56,\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(SchoolIcon, {\n          sx: {\n            fontSize: 32\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"bold\",\n          sx: {\n            fontSize: '1.1rem'\n          },\n          children: \"\\uD83C\\uDF93 School Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            opacity: 0.9,\n            fontSize: '0.8rem'\n          },\n          children: \"Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        overflowY: 'auto',\n        py: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(MenuSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"\\uD83D\\uDCCB Main Menu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          sx: {\n            padding: 0\n          },\n          children: mainMenuItems.map((item, index) => renderMenuItem(item, index))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mx: 3,\n          my: 2,\n          bgcolor: 'rgba(255, 255, 255, 0.2)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"\\uD83D\\uDC64 Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          sx: {\n            padding: 0\n          },\n          children: userMenuItems.map((item, index) => renderMenuItem(item, index))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 297,\n    columnNumber: 5\n  }, this);\n  if (isMobile) {\n    return /*#__PURE__*/_jsxDEV(StyledDrawer, {\n      variant: \"temporary\",\n      open: open,\n      onClose: onClose,\n      ModalProps: {\n        keepMounted: true // Better open performance on mobile.\n      },\n\n      sx: {\n        '& .MuiDrawer-paper': {\n          width: 280,\n          top: '64px',\n          height: 'calc(100vh - 64px)'\n        }\n      },\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(StyledDrawer, {\n    variant: variant,\n    open: open,\n    sx: {\n      '& .MuiDrawer-paper': {\n        width: open ? 280 : 70,\n        transition: theme.transitions.create('width', {\n          easing: theme.transitions.easing.sharp,\n          duration: theme.transitions.duration.enteringScreen\n        }),\n        overflowX: 'hidden',\n        top: '64px',\n        height: 'calc(100vh - 64px)'\n      }\n    },\n    children: drawerContent\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 364,\n    columnNumber: 5\n  }, this);\n};\n_s(ResponsiveSidebar, \"EDcwBIz4vaW9vHEUm4iGTJ9ZhSk=\", false, function () {\n  return [useTheme, useMediaQuery, useLocation];\n});\n_c6 = ResponsiveSidebar;\nexport default ResponsiveSidebar;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"StyledDrawer\");\n$RefreshReg$(_c2, \"SidebarHeader\");\n$RefreshReg$(_c3, \"StyledListItemButton\");\n$RefreshReg$(_c4, \"MenuSection\");\n$RefreshReg$(_c5, \"SectionTitle\");\n$RefreshReg$(_c6, \"ResponsiveSidebar\");", "map": {"version": 3, "names": ["React", "Drawer", "List", "ListItemButton", "ListItemIcon", "ListItemText", "Divider", "Box", "Typography", "useTheme", "useMediaQuery", "Avatar", "Chip", "Collapse", "styled", "motion", "Link", "useLocation", "Home", "HomeIcon", "Class", "ClassIcon", "Assignment", "AssignmentIcon", "SupervisorAccount", "SupervisorAccountIcon", "PersonOutline", "PersonOutlineIcon", "Announcement", "AnnouncementIcon", "Report", "ReportIcon", "AccountCircleOutlined", "AccountCircleOutlinedIcon", "ExitToApp", "ExitToAppIcon", "School", "SchoolIcon", "Info", "InfoIcon", "Payment", "PaymentIcon", "Description", "DescriptionIcon", "DirectionsBus", "DirectionsBusIcon", "People", "PeopleIcon", "PersonAdd", "PersonAddIcon", "EventAvailable", "EventAvailableIcon", "Quiz", "QuizIcon", "MenuBook", "MenuBookIcon", "NotificationsActive", "NotificationsActiveIcon", "Hotel", "HotelIcon", "Web", "WebIcon", "Business", "BusinessIcon", "ExpandLess", "ExpandMore", "jsxDEV", "_jsxDEV", "StyledDrawer", "_ref", "theme", "background", "color", "borderRight", "boxShadow", "<PERSON><PERSON>ilter", "position", "top", "height", "zIndex", "drawer", "content", "left", "right", "bottom", "pointerEvents", "_c", "SidebarHeader", "_ref2", "padding", "spacing", "display", "alignItems", "gap", "borderBottom", "transform", "width", "_c2", "StyledListItemButton", "_ref3", "active", "margin", "borderRadius", "backgroundColor", "border", "transition", "overflow", "_c3", "MenuSection", "_ref4", "marginTop", "_c4", "SectionTitle", "_ref5", "fontSize", "fontWeight", "textTransform", "letterSpacing", "opacity", "marginBottom", "marginLeft", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "_c5", "ResponsiveSidebar", "_ref6", "_s", "open", "onClose", "variant", "isMobile", "breakpoints", "down", "location", "mainMenuItems", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "badge", "userMenuItems", "isActive", "pathname", "startsWith", "renderMenuItem", "item", "index", "div", "initial", "x", "animate", "delay", "children", "component", "to", "onClick", "undefined", "sx", "min<PERSON><PERSON><PERSON>", "primary", "primaryTypographyProps", "label", "size", "bgcolor", "drawerContent", "flexDirection", "flex", "overflowY", "py", "map", "mx", "my", "ModalProps", "keepMounted", "transitions", "create", "easing", "sharp", "duration", "enteringScreen", "overflowX", "_c6", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/components/layout/ResponsiveSidebar.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Drawer,\n  List,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Box,\n  Typography,\n  useTheme,\n  useMediaQuery,\n  Avatar,\n  Chip,\n  Collapse\n} from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport {\n  Home as HomeIcon,\n  Class as ClassIcon,\n  Assignment as AssignmentIcon,\n  SupervisorAccount as SupervisorAccountIcon,\n  PersonOutline as PersonOutlineIcon,\n  Announcement as AnnouncementIcon,\n  Report as ReportIcon,\n  AccountCircleOutlined as AccountCircleOutlinedIcon,\n  ExitToApp as ExitToAppIcon,\n  School as SchoolIcon,\n  // New icons for additional modules\n  Info as InfoIcon,\n  Payment as PaymentIcon,\n  Description as DescriptionIcon,\n  DirectionsBus as DirectionsBusIcon,\n  People as PeopleIcon,\n  PersonAdd as PersonAddIcon,\n  EventAvailable as EventAvailableIcon,\n  Quiz as QuizIcon,\n  MenuBook as MenuBookIcon,\n  NotificationsActive as NotificationsActiveIcon,\n  Hotel as HotelIcon,\n  Web as WebIcon,\n  Business as BusinessIcon,\n  ExpandLess,\n  ExpandMore,\n} from '@mui/icons-material';\n\nconst StyledDrawer = styled(Drawer)(({ theme }) => ({\n  '& .MuiDrawer-paper': {\n    background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%)',\n    color: 'white',\n    borderRight: 'none',\n    boxShadow: '8px 0 32px rgba(0, 0, 0, 0.15)',\n    backdropFilter: 'blur(10px)',\n    position: 'fixed',\n    top: '64px', // Position below the AppBar\n    height: 'calc(100vh - 64px)', // Full height minus AppBar\n    zIndex: theme.zIndex.drawer,\n    '&::before': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n      pointerEvents: 'none',\n    }\n  },\n}));\n\nconst SidebarHeader = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(2, 2),\n  display: 'flex',\n  alignItems: 'center',\n  gap: theme.spacing(2),\n  borderBottom: '2px solid rgba(255, 255, 255, 0.15)',\n  background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n  backdropFilter: 'blur(10px)',\n  position: 'relative',\n  '&::after': {\n    content: '\"\"',\n    position: 'absolute',\n    bottom: 0,\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '60%',\n    height: '2px',\n    background: 'linear-gradient(90deg, transparent, #FFD700, transparent)',\n  }\n}));\n\nconst StyledListItemButton = styled(ListItemButton)(({ theme, active }) => ({\n  margin: theme.spacing(0.5, 1.5),\n  borderRadius: '16px',\n  padding: theme.spacing(1.5, 2),\n  backgroundColor: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',\n  backdropFilter: active ? 'blur(15px)' : 'none',\n  border: active ? '1px solid rgba(255, 255, 255, 0.3)' : '1px solid transparent',\n  boxShadow: active ? '0 8px 32px rgba(0, 0, 0, 0.1)' : 'none',\n  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n  position: 'relative',\n  overflow: 'hidden',\n  '&:hover': {\n    backgroundColor: 'rgba(255, 255, 255, 0.15)',\n    transform: 'translateX(8px) scale(1.02)',\n    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',\n  },\n  '&:before': {\n    content: '\"\"',\n    position: 'absolute',\n    left: 0,\n    top: '50%',\n    transform: 'translateY(-50%)',\n    width: active ? '5px' : '0px',\n    height: '70%',\n    background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n    borderRadius: '0 8px 8px 0',\n    transition: 'width 0.3s ease',\n    boxShadow: active ? '0 0 20px rgba(255, 215, 0, 0.5)' : 'none',\n  },\n  '&:after': {\n    content: '\"\"',\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: active ? 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))' : 'transparent',\n    pointerEvents: 'none',\n    borderRadius: '16px',\n  },\n}));\n\nconst MenuSection = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(1, 1.5),\n  marginTop: theme.spacing(1.5),\n}));\n\nconst SectionTitle = styled(Typography)(({ theme }) => ({\n  fontSize: '0.8rem',\n  fontWeight: 700,\n  textTransform: 'uppercase',\n  letterSpacing: '1px',\n  opacity: 0.9,\n  marginBottom: theme.spacing(1.5),\n  marginLeft: theme.spacing(1),\n  background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n  backgroundClip: 'text',\n  WebkitBackgroundClip: 'text',\n  WebkitTextFillColor: 'transparent',\n  textShadow: '0 2px 4px rgba(0,0,0,0.1)',\n}));\n\nconst ResponsiveSidebar = ({ open, onClose, variant = 'permanent' }) => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const location = useLocation();\n  // Simplified menu without collapsible sections\n\n  // Simplified main menu items\n  const mainMenuItems = [\n    {\n      text: 'Dashboard',\n      icon: <HomeIcon />,\n      path: '/Admin/dashboard',\n      badge: null\n    },\n    {\n      text: 'Student Info',\n      icon: <InfoIcon />,\n      path: '/Admin/student-info',\n      badge: null\n    },\n    {\n      text: 'Students',\n      icon: <PersonOutlineIcon />,\n      path: '/Admin/students',\n      badge: null\n    },\n    {\n      text: 'Classes',\n      icon: <ClassIcon />,\n      path: '/Admin/classes',\n      badge: null\n    },\n    {\n      text: 'Teachers',\n      icon: <SupervisorAccountIcon />,\n      path: '/Admin/teachers',\n      badge: null\n    },\n    {\n      text: 'Subjects',\n      icon: <AssignmentIcon />,\n      path: '/Admin/subjects',\n      badge: null\n    },\n    {\n      text: 'Fee Management',\n      icon: <PaymentIcon />,\n      path: '/Admin/fee-management',\n      badge: '12'\n    },\n    {\n      text: 'Transport',\n      icon: <DirectionsBusIcon />,\n      path: '/Admin/transport',\n      badge: null\n    },\n    {\n      text: 'Documents',\n      icon: <DescriptionIcon />,\n      path: '/Admin/documents',\n      badge: null\n    },\n    {\n      text: 'Notifications',\n      icon: <NotificationsActiveIcon />,\n      path: '/Admin/notifications',\n      badge: '15'\n    },\n    {\n      text: 'Notices',\n      icon: <AnnouncementIcon />,\n      path: '/Admin/notices',\n      badge: '3'\n    },\n  ];\n\n  // User account menu items\n  const userMenuItems = [\n    {\n      text: 'Profile',\n      icon: <AccountCircleOutlinedIcon />,\n      path: '/Admin/profile',\n      badge: null\n    },\n    {\n      text: 'Logout',\n      icon: <ExitToAppIcon />,\n      path: '/logout',\n      badge: null\n    },\n  ];\n\n  const isActive = (path) => {\n    if (path === '/Admin/dashboard') {\n      return location.pathname === '/' || location.pathname === '/Admin/dashboard';\n    }\n    return location.pathname.startsWith(path);\n  };\n\n  const renderMenuItem = (item, index) => (\n    <motion.div\n      key={item.text}\n      initial={{ opacity: 0, x: -20 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ delay: index * 0.05 }}\n    >\n      <StyledListItemButton\n        component={Link}\n        to={item.path}\n        active={isActive(item.path)}\n        onClick={isMobile ? onClose : undefined}\n      >\n        <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>\n          {item.icon}\n        </ListItemIcon>\n        <ListItemText\n          primary={item.text}\n          primaryTypographyProps={{\n            fontSize: '0.9rem',\n            fontWeight: isActive(item.path) ? 600 : 400,\n          }}\n        />\n        {item.badge && (\n          <Chip\n            label={item.badge}\n            size=\"small\"\n            sx={{\n              bgcolor: '#FFD700',\n              color: '#333',\n              fontSize: '0.7rem',\n              height: 20,\n              minWidth: 20,\n              fontWeight: 600,\n            }}\n          />\n        )}\n      </StyledListItemButton>\n    </motion.div>\n  );\n\n  const drawerContent = (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <SidebarHeader>\n        <Avatar\n          sx={{\n            bgcolor: 'rgba(255, 255, 255, 0.3)',\n            width: 56,\n            height: 56,\n            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n          }}\n        >\n          <SchoolIcon sx={{ fontSize: 32 }} />\n        </Avatar>\n        <Box>\n          <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ fontSize: '1.1rem' }}>\n            🎓 School Admin\n          </Typography>\n          <Typography variant=\"caption\" sx={{ opacity: 0.9, fontSize: '0.8rem' }}>\n            Management System\n          </Typography>\n        </Box>\n      </SidebarHeader>\n\n      <Box sx={{ flex: 1, overflowY: 'auto', py: 2 }}>\n        {/* Main Menu */}\n        <MenuSection>\n          <SectionTitle>📋 Main Menu</SectionTitle>\n          <List component=\"nav\" sx={{ padding: 0 }}>\n            {mainMenuItems.map((item, index) => renderMenuItem(item, index))}\n          </List>\n        </MenuSection>\n\n        <Divider sx={{ mx: 3, my: 2, bgcolor: 'rgba(255, 255, 255, 0.2)' }} />\n\n        {/* User Account */}\n        <MenuSection>\n          <SectionTitle>👤 Account</SectionTitle>\n          <List component=\"nav\" sx={{ padding: 0 }}>\n            {userMenuItems.map((item, index) => renderMenuItem(item, index))}\n          </List>\n        </MenuSection>\n      </Box>\n    </Box>\n  );\n\n  if (isMobile) {\n    return (\n      <StyledDrawer\n        variant=\"temporary\"\n        open={open}\n        onClose={onClose}\n        ModalProps={{\n          keepMounted: true, // Better open performance on mobile.\n        }}\n        sx={{\n          '& .MuiDrawer-paper': {\n            width: 280,\n            top: '64px',\n            height: 'calc(100vh - 64px)',\n          },\n        }}\n      >\n        {drawerContent}\n      </StyledDrawer>\n    );\n  }\n\n  return (\n    <StyledDrawer\n      variant={variant}\n      open={open}\n      sx={{\n        '& .MuiDrawer-paper': {\n          width: open ? 280 : 70,\n          transition: theme.transitions.create('width', {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.enteringScreen,\n          }),\n          overflowX: 'hidden',\n          top: '64px',\n          height: 'calc(100vh - 64px)',\n        },\n      }}\n    >\n      {drawerContent}\n    </StyledDrawer>\n  );\n};\n\nexport default ResponsiveSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,IAAI,EACJC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,aAAa,EACbC,MAAM,EACNC,IAAI,EACJC,QAAQ,QACH,eAAe;AACtB,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SACEC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,iBAAiB,IAAIC,qBAAqB,EAC1CC,aAAa,IAAIC,iBAAiB,EAClCC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,qBAAqB,IAAIC,yBAAyB,EAClDC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU;AACpB;AACAC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,eAAe,EAC9BC,aAAa,IAAIC,iBAAiB,EAClCC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,cAAc,IAAIC,kBAAkB,EACpCC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,mBAAmB,IAAIC,uBAAuB,EAC9CC,KAAK,IAAIC,SAAS,EAClBC,GAAG,IAAIC,OAAO,EACdC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,EACVC,UAAU,QACL,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,YAAY,GAAGtD,MAAM,CAACb,MAAM,CAAC,CAACoE,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IAClD,oBAAoB,EAAE;MACpBE,UAAU,EAAE,gEAAgE;MAC5EC,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE,gCAAgC;MAC3CC,cAAc,EAAE,YAAY;MAC5BC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,MAAM;MAAE;MACbC,MAAM,EAAE,oBAAoB;MAAE;MAC9BC,MAAM,EAAET,KAAK,CAACS,MAAM,CAACC,MAAM;MAC3B,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbL,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,CAAC;QACNK,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTb,UAAU,EAAE,gFAAgF;QAC5Fc,aAAa,EAAE;MACjB;IACF;EACF,CAAC;AAAA,CAAC,CAAC;AAACC,EAAA,GAtBElB,YAAY;AAwBlB,MAAMmB,aAAa,GAAGzE,MAAM,CAACP,GAAG,CAAC,CAACiF,KAAA;EAAA,IAAC;IAAElB;EAAM,CAAC,GAAAkB,KAAA;EAAA,OAAM;IAChDC,OAAO,EAAEnB,KAAK,CAACoB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5BC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAEvB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;IACrBI,YAAY,EAAE,qCAAqC;IACnDvB,UAAU,EAAE,gFAAgF;IAC5FI,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,UAAU;IACpB,UAAU,EAAE;MACVK,OAAO,EAAE,IAAI;MACbL,QAAQ,EAAE,UAAU;MACpBQ,MAAM,EAAE,CAAC;MACTF,IAAI,EAAE,KAAK;MACXa,SAAS,EAAE,kBAAkB;MAC7BC,KAAK,EAAE,KAAK;MACZlB,MAAM,EAAE,KAAK;MACbP,UAAU,EAAE;IACd;EACF,CAAC;AAAA,CAAC,CAAC;AAAC0B,GAAA,GAnBEV,aAAa;AAqBnB,MAAMW,oBAAoB,GAAGpF,MAAM,CAACX,cAAc,CAAC,CAACgG,KAAA;EAAA,IAAC;IAAE7B,KAAK;IAAE8B;EAAO,CAAC,GAAAD,KAAA;EAAA,OAAM;IAC1EE,MAAM,EAAE/B,KAAK,CAACoB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAC/BY,YAAY,EAAE,MAAM;IACpBb,OAAO,EAAEnB,KAAK,CAACoB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IAC9Ba,eAAe,EAAEH,MAAM,GAAG,0BAA0B,GAAG,aAAa;IACpEzB,cAAc,EAAEyB,MAAM,GAAG,YAAY,GAAG,MAAM;IAC9CI,MAAM,EAAEJ,MAAM,GAAG,oCAAoC,GAAG,uBAAuB;IAC/E1B,SAAS,EAAE0B,MAAM,GAAG,+BAA+B,GAAG,MAAM;IAC5DK,UAAU,EAAE,uCAAuC;IACnD7B,QAAQ,EAAE,UAAU;IACpB8B,QAAQ,EAAE,QAAQ;IAClB,SAAS,EAAE;MACTH,eAAe,EAAE,2BAA2B;MAC5CR,SAAS,EAAE,6BAA6B;MACxCrB,SAAS,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACVO,OAAO,EAAE,IAAI;MACbL,QAAQ,EAAE,UAAU;MACpBM,IAAI,EAAE,CAAC;MACPL,GAAG,EAAE,KAAK;MACVkB,SAAS,EAAE,kBAAkB;MAC7BC,KAAK,EAAEI,MAAM,GAAG,KAAK,GAAG,KAAK;MAC7BtB,MAAM,EAAE,KAAK;MACbP,UAAU,EAAE,2CAA2C;MACvD+B,YAAY,EAAE,aAAa;MAC3BG,UAAU,EAAE,iBAAiB;MAC7B/B,SAAS,EAAE0B,MAAM,GAAG,iCAAiC,GAAG;IAC1D,CAAC;IACD,SAAS,EAAE;MACTnB,OAAO,EAAE,IAAI;MACbL,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,CAAC;MACNK,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTb,UAAU,EAAE6B,MAAM,GAAG,wEAAwE,GAAG,aAAa;MAC7Gf,aAAa,EAAE,MAAM;MACrBiB,YAAY,EAAE;IAChB;EACF,CAAC;AAAA,CAAC,CAAC;AAACK,GAAA,GAxCET,oBAAoB;AA0C1B,MAAMU,WAAW,GAAG9F,MAAM,CAACP,GAAG,CAAC,CAACsG,KAAA;EAAA,IAAC;IAAEvC;EAAM,CAAC,GAAAuC,KAAA;EAAA,OAAM;IAC9CpB,OAAO,EAAEnB,KAAK,CAACoB,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;IAC9BoB,SAAS,EAAExC,KAAK,CAACoB,OAAO,CAAC,GAAG;EAC9B,CAAC;AAAA,CAAC,CAAC;AAACqB,GAAA,GAHEH,WAAW;AAKjB,MAAMI,YAAY,GAAGlG,MAAM,CAACN,UAAU,CAAC,CAACyG,KAAA;EAAA,IAAC;IAAE3C;EAAM,CAAC,GAAA2C,KAAA;EAAA,OAAM;IACtDC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,GAAG;IACfC,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE,KAAK;IACpBC,OAAO,EAAE,GAAG;IACZC,YAAY,EAAEjD,KAAK,CAACoB,OAAO,CAAC,GAAG,CAAC;IAChC8B,UAAU,EAAElD,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;IAC5BnB,UAAU,EAAE,2CAA2C;IACvDkD,cAAc,EAAE,MAAM;IACtBC,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE,aAAa;IAClCC,UAAU,EAAE;EACd,CAAC;AAAA,CAAC,CAAC;AAACC,GAAA,GAbEb,YAAY;AAelB,MAAMc,iBAAiB,GAAGC,KAAA,IAA8C;EAAAC,EAAA;EAAA,IAA7C;IAAEC,IAAI;IAAEC,OAAO;IAAEC,OAAO,GAAG;EAAY,CAAC,GAAAJ,KAAA;EACjE,MAAMzD,KAAK,GAAG7D,QAAQ,EAAE;EACxB,MAAM2H,QAAQ,GAAG1H,aAAa,CAAC4D,KAAK,CAAC+D,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAMC,QAAQ,GAAGtH,WAAW,EAAE;EAC9B;;EAEA;EACA,MAAMuH,aAAa,GAAG,CACpB;IACEC,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAEvE,OAAA,CAAChD,QAAQ;MAAAwH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAClBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,eAAEvE,OAAA,CAAC5B,QAAQ;MAAAoG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAClBC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAEvE,OAAA,CAACxC,iBAAiB;MAAAgH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC3BC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,eAAEvE,OAAA,CAAC9C,SAAS;MAAAsH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnBC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAEvE,OAAA,CAAC1C,qBAAqB;MAAAkH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC/BC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAEvE,OAAA,CAAC5C,cAAc;MAAAoH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACxBC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,gBAAgB;IACtBC,IAAI,eAAEvE,OAAA,CAAC1B,WAAW;MAAAkG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACrBC,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAEvE,OAAA,CAACtB,iBAAiB;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC3BC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAEvE,OAAA,CAACxB,eAAe;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,eAAe;IACrBC,IAAI,eAAEvE,OAAA,CAACV,uBAAuB;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACjCC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,eAAEvE,OAAA,CAACtC,gBAAgB;MAAA8G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC1BC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IACER,IAAI,EAAE,SAAS;IACfC,IAAI,eAAEvE,OAAA,CAAClC,yBAAyB;MAAA0G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnCC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdC,IAAI,eAAEvE,OAAA,CAAChC,aAAa;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAME,QAAQ,GAAIH,IAAI,IAAK;IACzB,IAAIA,IAAI,KAAK,kBAAkB,EAAE;MAC/B,OAAOR,QAAQ,CAACY,QAAQ,KAAK,GAAG,IAAIZ,QAAQ,CAACY,QAAQ,KAAK,kBAAkB;IAC9E;IACA,OAAOZ,QAAQ,CAACY,QAAQ,CAACC,UAAU,CAACL,IAAI,CAAC;EAC3C,CAAC;EAED,MAAMM,cAAc,GAAGA,CAACC,IAAI,EAAEC,KAAK,kBACjCpF,OAAA,CAACpD,MAAM,CAACyI,GAAG;IAETC,OAAO,EAAE;MAAEnC,OAAO,EAAE,CAAC;MAAEoC,CAAC,EAAE,CAAC;IAAG,CAAE;IAChCC,OAAO,EAAE;MAAErC,OAAO,EAAE,CAAC;MAAEoC,CAAC,EAAE;IAAE,CAAE;IAC9BjD,UAAU,EAAE;MAAEmD,KAAK,EAAEL,KAAK,GAAG;IAAK,CAAE;IAAAM,QAAA,eAEpC1F,OAAA,CAAC+B,oBAAoB;MACnB4D,SAAS,EAAE9I,IAAK;MAChB+I,EAAE,EAAET,IAAI,CAACP,IAAK;MACd3C,MAAM,EAAE8C,QAAQ,CAACI,IAAI,CAACP,IAAI,CAAE;MAC5BiB,OAAO,EAAE5B,QAAQ,GAAGF,OAAO,GAAG+B,SAAU;MAAAJ,QAAA,gBAExC1F,OAAA,CAAC/D,YAAY;QAAC8J,EAAE,EAAE;UAAE1F,KAAK,EAAE,SAAS;UAAE2F,QAAQ,EAAE;QAAG,CAAE;QAAAN,QAAA,EAClDP,IAAI,CAACZ;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACG,eACf3E,OAAA,CAAC9D,YAAY;QACX+J,OAAO,EAAEd,IAAI,CAACb,IAAK;QACnB4B,sBAAsB,EAAE;UACtBnD,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE+B,QAAQ,CAACI,IAAI,CAACP,IAAI,CAAC,GAAG,GAAG,GAAG;QAC1C;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACF,EACDQ,IAAI,CAACN,KAAK,iBACT7E,OAAA,CAACvD,IAAI;QACH0J,KAAK,EAAEhB,IAAI,CAACN,KAAM;QAClBuB,IAAI,EAAC,OAAO;QACZL,EAAE,EAAE;UACFM,OAAO,EAAE,SAAS;UAClBhG,KAAK,EAAE,MAAM;UACb0C,QAAQ,EAAE,QAAQ;UAClBpC,MAAM,EAAE,EAAE;UACVqF,QAAQ,EAAE,EAAE;UACZhD,UAAU,EAAE;QACd;MAAE;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAEL;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACoB,GAnClBQ,IAAI,CAACb,IAAI;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAqCjB;EAED,MAAM2B,aAAa,gBACjBtG,OAAA,CAAC5D,GAAG;IAAC2J,EAAE,EAAE;MAAEpF,MAAM,EAAE,MAAM;MAAEa,OAAO,EAAE,MAAM;MAAE+E,aAAa,EAAE;IAAS,CAAE;IAAAb,QAAA,gBACpE1F,OAAA,CAACoB,aAAa;MAAAsE,QAAA,gBACZ1F,OAAA,CAACxD,MAAM;QACLuJ,EAAE,EAAE;UACFM,OAAO,EAAE,0BAA0B;UACnCxE,KAAK,EAAE,EAAE;UACTlB,MAAM,EAAE,EAAE;UACVJ,SAAS,EAAE;QACb,CAAE;QAAAmF,QAAA,eAEF1F,OAAA,CAAC9B,UAAU;UAAC6H,EAAE,EAAE;YAAEhD,QAAQ,EAAE;UAAG;QAAE;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC7B,eACT3E,OAAA,CAAC5D,GAAG;QAAAsJ,QAAA,gBACF1F,OAAA,CAAC3D,UAAU;UAAC2H,OAAO,EAAC,IAAI;UAAChB,UAAU,EAAC,MAAM;UAAC+C,EAAE,EAAE;YAAEhD,QAAQ,EAAE;UAAS,CAAE;UAAA2C,QAAA,EAAC;QAEvE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACb3E,OAAA,CAAC3D,UAAU;UAAC2H,OAAO,EAAC,SAAS;UAAC+B,EAAE,EAAE;YAAE5C,OAAO,EAAE,GAAG;YAAEJ,QAAQ,EAAE;UAAS,CAAE;UAAA2C,QAAA,EAAC;QAExE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACQ,eAEhB3E,OAAA,CAAC5D,GAAG;MAAC2J,EAAE,EAAE;QAAES,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE,MAAM;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAhB,QAAA,gBAE7C1F,OAAA,CAACyC,WAAW;QAAAiD,QAAA,gBACV1F,OAAA,CAAC6C,YAAY;UAAA6C,QAAA,EAAC;QAAY;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAe,eACzC3E,OAAA,CAACjE,IAAI;UAAC4J,SAAS,EAAC,KAAK;UAACI,EAAE,EAAE;YAAEzE,OAAO,EAAE;UAAE,CAAE;UAAAoE,QAAA,EACtCrB,aAAa,CAACsC,GAAG,CAAC,CAACxB,IAAI,EAAEC,KAAK,KAAKF,cAAc,CAACC,IAAI,EAAEC,KAAK,CAAC;QAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAC3D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACK,eAEd3E,OAAA,CAAC7D,OAAO;QAAC4J,EAAE,EAAE;UAAEa,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAER,OAAO,EAAE;QAA2B;MAAE;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAGtE3E,OAAA,CAACyC,WAAW;QAAAiD,QAAA,gBACV1F,OAAA,CAAC6C,YAAY;UAAA6C,QAAA,EAAC;QAAU;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAe,eACvC3E,OAAA,CAACjE,IAAI;UAAC4J,SAAS,EAAC,KAAK;UAACI,EAAE,EAAE;YAAEzE,OAAO,EAAE;UAAE,CAAE;UAAAoE,QAAA,EACtCZ,aAAa,CAAC6B,GAAG,CAAC,CAACxB,IAAI,EAAEC,KAAK,KAAKF,cAAc,CAACC,IAAI,EAAEC,KAAK,CAAC;QAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAC3D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACK;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACV;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAET;EAED,IAAIV,QAAQ,EAAE;IACZ,oBACEjE,OAAA,CAACC,YAAY;MACX+D,OAAO,EAAC,WAAW;MACnBF,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAEA,OAAQ;MACjB+C,UAAU,EAAE;QACVC,WAAW,EAAE,IAAI,CAAE;MACrB,CAAE;;MACFhB,EAAE,EAAE;QACF,oBAAoB,EAAE;UACpBlE,KAAK,EAAE,GAAG;UACVnB,GAAG,EAAE,MAAM;UACXC,MAAM,EAAE;QACV;MACF,CAAE;MAAA+E,QAAA,EAEDY;IAAa;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD;EAEnB;EAEA,oBACE3E,OAAA,CAACC,YAAY;IACX+D,OAAO,EAAEA,OAAQ;IACjBF,IAAI,EAAEA,IAAK;IACXiC,EAAE,EAAE;MACF,oBAAoB,EAAE;QACpBlE,KAAK,EAAEiC,IAAI,GAAG,GAAG,GAAG,EAAE;QACtBxB,UAAU,EAAEnC,KAAK,CAAC6G,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;UAC5CC,MAAM,EAAE/G,KAAK,CAAC6G,WAAW,CAACE,MAAM,CAACC,KAAK;UACtCC,QAAQ,EAAEjH,KAAK,CAAC6G,WAAW,CAACI,QAAQ,CAACC;QACvC,CAAC,CAAC;QACFC,SAAS,EAAE,QAAQ;QACnB5G,GAAG,EAAE,MAAM;QACXC,MAAM,EAAE;MACV;IACF,CAAE;IAAA+E,QAAA,EAEDY;EAAa;IAAA9B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACD;AAEnB,CAAC;AAACd,EAAA,CAnOIF,iBAAiB;EAAA,QACPrH,QAAQ,EACLC,aAAa,EACbO,WAAW;AAAA;AAAAyK,GAAA,GAHxB5D,iBAAiB;AAqOvB,eAAeA,iBAAiB;AAAC,IAAAxC,EAAA,EAAAW,GAAA,EAAAU,GAAA,EAAAI,GAAA,EAAAc,GAAA,EAAA6D,GAAA;AAAAC,YAAA,CAAArG,EAAA;AAAAqG,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA9D,GAAA;AAAA8D,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}