{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\components\\\\theme\\\\ThemeProvider.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { createTheme, ThemeProvider as MuiThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext();\nexport const useTheme = () => {\n  _s();\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst lightTheme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#667eea',\n      light: '#9bb5ff',\n      dark: '#3f51b5'\n    },\n    secondary: {\n      main: '#764ba2',\n      light: '#a777d3',\n      dark: '#4a2c73'\n    },\n    background: {\n      default: '#f8fafc',\n      paper: '#ffffff'\n    },\n    text: {\n      primary: '#1a202c',\n      secondary: '#4a5568'\n    }\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontWeight: 700\n    },\n    h2: {\n      fontWeight: 600\n    },\n    h3: {\n      fontWeight: 600\n    },\n    h4: {\n      fontWeight: 600\n    },\n    h5: {\n      fontWeight: 600\n    },\n    h6: {\n      fontWeight: 600\n    }\n  },\n  shape: {\n    borderRadius: 12\n  },\n  components: {\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'\n        }\n      }\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          borderRadius: 8,\n          fontWeight: 600\n        }\n      }\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'\n        }\n      }\n    }\n  }\n});\nconst darkTheme = createTheme({\n  palette: {\n    mode: 'dark',\n    primary: {\n      main: '#9bb5ff',\n      light: '#cce7ff',\n      dark: '#6a85cc'\n    },\n    secondary: {\n      main: '#a777d3',\n      light: '#d9a7ff',\n      dark: '#7547a0'\n    },\n    background: {\n      default: '#0f172a',\n      paper: '#1e293b'\n    },\n    text: {\n      primary: '#f1f5f9',\n      secondary: '#cbd5e1'\n    }\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontWeight: 700\n    },\n    h2: {\n      fontWeight: 600\n    },\n    h3: {\n      fontWeight: 600\n    },\n    h4: {\n      fontWeight: 600\n    },\n    h5: {\n      fontWeight: 600\n    },\n    h6: {\n      fontWeight: 600\n    }\n  },\n  shape: {\n    borderRadius: 12\n  },\n  components: {\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n          backgroundImage: 'none'\n        }\n      }\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          borderRadius: 8,\n          fontWeight: 600\n        }\n      }\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',\n          backgroundImage: 'none'\n        }\n      }\n    }\n  }\n});\nexport const ThemeProvider = _ref => {\n  _s2();\n  let {\n    children\n  } = _ref;\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    const saved = localStorage.getItem('darkMode');\n    return saved ? JSON.parse(saved) : false;\n  });\n  useEffect(() => {\n    localStorage.setItem('darkMode', JSON.stringify(isDarkMode));\n  }, [isDarkMode]);\n  const toggleTheme = () => {\n    setIsDarkMode(!isDarkMode);\n  };\n  const theme = isDarkMode ? darkTheme : lightTheme;\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: {\n      isDarkMode,\n      toggleTheme\n    },\n    children: /*#__PURE__*/_jsxDEV(MuiThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), children]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 5\n  }, this);\n};\n_s2(ThemeProvider, \"4DS008y4VJK1TxF9oE4udbx1JAI=\");\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "createTheme", "ThemeProvider", "MuiThemeProvider", "CssBaseline", "jsxDEV", "_jsxDEV", "ThemeContext", "useTheme", "_s", "context", "Error", "lightTheme", "palette", "mode", "primary", "main", "light", "dark", "secondary", "background", "default", "paper", "text", "typography", "fontFamily", "h1", "fontWeight", "h2", "h3", "h4", "h5", "h6", "shape", "borderRadius", "components", "MuiPaper", "styleOverrides", "root", "boxShadow", "MuiB<PERSON>on", "textTransform", "MuiCard", "darkTheme", "backgroundImage", "_ref", "_s2", "children", "isDarkMode", "setIsDarkMode", "saved", "localStorage", "getItem", "JSON", "parse", "setItem", "stringify", "toggleTheme", "theme", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/components/theme/ThemeProvider.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { createTheme, ThemeProvider as MuiThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\n\nconst ThemeContext = createContext();\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nconst lightTheme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#667eea',\n      light: '#9bb5ff',\n      dark: '#3f51b5',\n    },\n    secondary: {\n      main: '#764ba2',\n      light: '#a777d3',\n      dark: '#4a2c73',\n    },\n    background: {\n      default: '#f8fafc',\n      paper: '#ffffff',\n    },\n    text: {\n      primary: '#1a202c',\n      secondary: '#4a5568',\n    },\n  },\n  typography: {\n    fontFamily: '\"<PERSON>\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontWeight: 700,\n    },\n    h2: {\n      fontWeight: 600,\n    },\n    h3: {\n      fontWeight: 600,\n    },\n    h4: {\n      fontWeight: 600,\n    },\n    h5: {\n      fontWeight: 600,\n    },\n    h6: {\n      fontWeight: 600,\n    },\n  },\n  shape: {\n    borderRadius: 12,\n  },\n  components: {\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          borderRadius: 8,\n          fontWeight: 600,\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n        },\n      },\n    },\n  },\n});\n\nconst darkTheme = createTheme({\n  palette: {\n    mode: 'dark',\n    primary: {\n      main: '#9bb5ff',\n      light: '#cce7ff',\n      dark: '#6a85cc',\n    },\n    secondary: {\n      main: '#a777d3',\n      light: '#d9a7ff',\n      dark: '#7547a0',\n    },\n    background: {\n      default: '#0f172a',\n      paper: '#1e293b',\n    },\n    text: {\n      primary: '#f1f5f9',\n      secondary: '#cbd5e1',\n    },\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontWeight: 700,\n    },\n    h2: {\n      fontWeight: 600,\n    },\n    h3: {\n      fontWeight: 600,\n    },\n    h4: {\n      fontWeight: 600,\n    },\n    h5: {\n      fontWeight: 600,\n    },\n    h6: {\n      fontWeight: 600,\n    },\n  },\n  shape: {\n    borderRadius: 12,\n  },\n  components: {\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n          backgroundImage: 'none',\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          borderRadius: 8,\n          fontWeight: 600,\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',\n          backgroundImage: 'none',\n        },\n      },\n    },\n  },\n});\n\nexport const ThemeProvider = ({ children }) => {\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    const saved = localStorage.getItem('darkMode');\n    return saved ? JSON.parse(saved) : false;\n  });\n\n  useEffect(() => {\n    localStorage.setItem('darkMode', JSON.stringify(isDarkMode));\n  }, [isDarkMode]);\n\n  const toggleTheme = () => {\n    setIsDarkMode(!isDarkMode);\n  };\n\n  const theme = isDarkMode ? darkTheme : lightTheme;\n\n  return (\n    <ThemeContext.Provider value={{ isDarkMode, toggleTheme }}>\n      <MuiThemeProvider theme={theme}>\n        <CssBaseline />\n        {children}\n      </MuiThemeProvider>\n    </ThemeContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,WAAW,EAAEC,aAAa,IAAIC,gBAAgB,QAAQ,sBAAsB;AACrF,SAASC,WAAW,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,YAAY,gBAAGV,aAAa,EAAE;AAEpC,OAAO,MAAMW,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGZ,UAAU,CAACS,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAQrB,MAAMI,UAAU,GAAGX,WAAW,CAAC;EAC7BY,OAAO,EAAE;IACPC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,OAAO,EAAE,SAAS;MAClBI,SAAS,EAAE;IACb;EACF,CAAC;EACDK,UAAU,EAAE;IACVC,UAAU,EAAE,qDAAqD;IACjEC,EAAE,EAAE;MACFC,UAAU,EAAE;IACd,CAAC;IACDC,EAAE,EAAE;MACFD,UAAU,EAAE;IACd,CAAC;IACDE,EAAE,EAAE;MACFF,UAAU,EAAE;IACd,CAAC;IACDG,EAAE,EAAE;MACFH,UAAU,EAAE;IACd,CAAC;IACDI,EAAE,EAAE;MACFJ,UAAU,EAAE;IACd,CAAC;IACDK,EAAE,EAAE;MACFL,UAAU,EAAE;IACd;EACF,CAAC;EACDM,KAAK,EAAE;IACLC,YAAY,EAAE;EAChB,CAAC;EACDC,UAAU,EAAE;IACVC,QAAQ,EAAE;MACRC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,SAAS,EAAE;QACb;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTH,cAAc,EAAE;QACdC,IAAI,EAAE;UACJG,aAAa,EAAE,MAAM;UACrBP,YAAY,EAAE,CAAC;UACfP,UAAU,EAAE;QACd;MACF;IACF,CAAC;IACDe,OAAO,EAAE;MACPL,cAAc,EAAE;QACdC,IAAI,EAAE;UACJJ,YAAY,EAAE,EAAE;UAChBK,SAAS,EAAE;QACb;MACF;IACF;EACF;AACF,CAAC,CAAC;AAEF,MAAMI,SAAS,GAAG1C,WAAW,CAAC;EAC5BY,OAAO,EAAE;IACPC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,OAAO,EAAE,SAAS;MAClBI,SAAS,EAAE;IACb;EACF,CAAC;EACDK,UAAU,EAAE;IACVC,UAAU,EAAE,qDAAqD;IACjEC,EAAE,EAAE;MACFC,UAAU,EAAE;IACd,CAAC;IACDC,EAAE,EAAE;MACFD,UAAU,EAAE;IACd,CAAC;IACDE,EAAE,EAAE;MACFF,UAAU,EAAE;IACd,CAAC;IACDG,EAAE,EAAE;MACFH,UAAU,EAAE;IACd,CAAC;IACDI,EAAE,EAAE;MACFJ,UAAU,EAAE;IACd,CAAC;IACDK,EAAE,EAAE;MACFL,UAAU,EAAE;IACd;EACF,CAAC;EACDM,KAAK,EAAE;IACLC,YAAY,EAAE;EAChB,CAAC;EACDC,UAAU,EAAE;IACVC,QAAQ,EAAE;MACRC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,SAAS,EAAE,+BAA+B;UAC1CK,eAAe,EAAE;QACnB;MACF;IACF,CAAC;IACDJ,SAAS,EAAE;MACTH,cAAc,EAAE;QACdC,IAAI,EAAE;UACJG,aAAa,EAAE,MAAM;UACrBP,YAAY,EAAE,CAAC;UACfP,UAAU,EAAE;QACd;MACF;IACF,CAAC;IACDe,OAAO,EAAE;MACPL,cAAc,EAAE;QACdC,IAAI,EAAE;UACJJ,YAAY,EAAE,EAAE;UAChBK,SAAS,EAAE,+BAA+B;UAC1CK,eAAe,EAAE;QACnB;MACF;IACF;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM1C,aAAa,GAAG2C,IAAA,IAAkB;EAAAC,GAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EACxC,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,MAAM;IACjD,MAAMmD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAC9C,OAAOF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,KAAK;EAC1C,CAAC,CAAC;EAEFlD,SAAS,CAAC,MAAM;IACdmD,YAAY,CAACI,OAAO,CAAC,UAAU,EAAEF,IAAI,CAACG,SAAS,CAACR,UAAU,CAAC,CAAC;EAC9D,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAMS,WAAW,GAAGA,CAAA,KAAM;IACxBR,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMU,KAAK,GAAGV,UAAU,GAAGL,SAAS,GAAG/B,UAAU;EAEjD,oBACEN,OAAA,CAACC,YAAY,CAACoD,QAAQ;IAACC,KAAK,EAAE;MAAEZ,UAAU;MAAES;IAAY,CAAE;IAAAV,QAAA,eACxDzC,OAAA,CAACH,gBAAgB;MAACuD,KAAK,EAAEA,KAAM;MAAAX,QAAA,gBAC7BzC,OAAA,CAACF,WAAW;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,EACdjB,QAAQ;IAAA;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACQ;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACG;AAE5B,CAAC;AAAClB,GAAA,CAxBW5C,aAAa;AAAA+D,EAAA,GAAb/D,aAAa;AAAA,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}