{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\components\\\\layout\\\\ResponsiveSidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Drawer, List, ListItemButton, ListItemIcon, ListItemText, Divider, Box, Typography, useTheme, useMediaQuery, Avatar, Chip, Collapse } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Home as HomeIcon, Class as ClassIcon, Assignment as AssignmentIcon, SupervisorAccount as SupervisorAccountIcon, PersonOutline as PersonOutlineIcon, Announcement as AnnouncementIcon, Report as ReportIcon, AccountCircleOutlined as AccountCircleOutlinedIcon, ExitToApp as ExitToAppIcon, School as SchoolIcon,\n// New icons for additional modules\nInfo as InfoIcon, Payment as PaymentIcon, Description as DescriptionIcon, DirectionsBus as DirectionsBusIcon, People as PeopleIcon, PersonAdd as PersonAddIcon, EventAvailable as EventAvailableIcon, Quiz as QuizIcon, MenuBook as MenuBookIcon, NotificationsActive as NotificationsActiveIcon, Hotel as HotelIcon, Web as WebIcon, Business as BusinessIcon, ExpandLess, ExpandMore } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledDrawer = styled(Drawer)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    '& .MuiDrawer-paper': {\n      background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%)',\n      color: 'white',\n      borderRight: 'none',\n      boxShadow: '8px 0 32px rgba(0, 0, 0, 0.15)',\n      backdropFilter: 'blur(10px)',\n      position: 'relative',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n        pointerEvents: 'none'\n      }\n    }\n  };\n});\n_c = StyledDrawer;\nconst SidebarHeader = styled(Box)(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    padding: theme.spacing(4, 2),\n    display: 'flex',\n    alignItems: 'center',\n    gap: theme.spacing(2),\n    borderBottom: '2px solid rgba(255, 255, 255, 0.15)',\n    background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n    backdropFilter: 'blur(10px)',\n    position: 'relative',\n    '&::after': {\n      content: '\"\"',\n      position: 'absolute',\n      bottom: 0,\n      left: '50%',\n      transform: 'translateX(-50%)',\n      width: '60%',\n      height: '2px',\n      background: 'linear-gradient(90deg, transparent, #FFD700, transparent)'\n    }\n  };\n});\n_c2 = SidebarHeader;\nconst StyledListItemButton = styled(ListItemButton)(_ref3 => {\n  let {\n    theme,\n    active\n  } = _ref3;\n  return {\n    margin: theme.spacing(0.5, 1.5),\n    borderRadius: '16px',\n    padding: theme.spacing(1.5, 2),\n    backgroundColor: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',\n    backdropFilter: active ? 'blur(15px)' : 'none',\n    border: active ? '1px solid rgba(255, 255, 255, 0.3)' : '1px solid transparent',\n    boxShadow: active ? '0 8px 32px rgba(0, 0, 0, 0.1)' : 'none',\n    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n    position: 'relative',\n    overflow: 'hidden',\n    '&:hover': {\n      backgroundColor: 'rgba(255, 255, 255, 0.15)',\n      transform: 'translateX(8px) scale(1.02)',\n      boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)'\n    },\n    '&:before': {\n      content: '\"\"',\n      position: 'absolute',\n      left: 0,\n      top: '50%',\n      transform: 'translateY(-50%)',\n      width: active ? '5px' : '0px',\n      height: '70%',\n      background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n      borderRadius: '0 8px 8px 0',\n      transition: 'width 0.3s ease',\n      boxShadow: active ? '0 0 20px rgba(255, 215, 0, 0.5)' : 'none'\n    },\n    '&:after': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: active ? 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))' : 'transparent',\n      pointerEvents: 'none',\n      borderRadius: '16px'\n    }\n  };\n});\n_c3 = StyledListItemButton;\nconst MenuSection = styled(Box)(_ref4 => {\n  let {\n    theme\n  } = _ref4;\n  return {\n    padding: theme.spacing(1, 1.5),\n    marginTop: theme.spacing(1.5)\n  };\n});\n_c4 = MenuSection;\nconst SectionTitle = styled(Typography)(_ref5 => {\n  let {\n    theme\n  } = _ref5;\n  return {\n    fontSize: '0.8rem',\n    fontWeight: 700,\n    textTransform: 'uppercase',\n    letterSpacing: '1px',\n    opacity: 0.9,\n    marginBottom: theme.spacing(1.5),\n    marginLeft: theme.spacing(1),\n    background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n    textShadow: '0 2px 4px rgba(0,0,0,0.1)'\n  };\n});\n_c5 = SectionTitle;\nconst ResponsiveSidebar = _ref6 => {\n  _s();\n  let {\n    open,\n    onClose,\n    variant = 'permanent'\n  } = _ref6;\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const location = useLocation();\n  // Simplified menu without collapsible sections\n\n  // Simplified main menu items\n  const mainMenuItems = [{\n    text: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/dashboard',\n    badge: null\n  }, {\n    text: 'Student Info',\n    icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/student-info',\n    badge: null\n  }, {\n    text: 'Students',\n    icon: /*#__PURE__*/_jsxDEV(PersonOutlineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/students',\n    badge: null\n  }, {\n    text: 'Classes',\n    icon: /*#__PURE__*/_jsxDEV(ClassIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/classes',\n    badge: null\n  }, {\n    text: 'Teachers',\n    icon: /*#__PURE__*/_jsxDEV(SupervisorAccountIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/teachers',\n    badge: null\n  }, {\n    text: 'Subjects',\n    icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/subjects',\n    badge: null\n  }, {\n    text: 'Fee Management',\n    icon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/fee-management',\n    badge: '12'\n  }, {\n    text: 'Transport',\n    icon: /*#__PURE__*/_jsxDEV(DirectionsBusIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/transport',\n    badge: null\n  }, {\n    text: 'Documents',\n    icon: /*#__PURE__*/_jsxDEV(DescriptionIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/documents',\n    badge: null\n  }, {\n    text: 'Notifications',\n    icon: /*#__PURE__*/_jsxDEV(NotificationsActiveIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/notifications',\n    badge: '15'\n  }, {\n    text: 'Notices',\n    icon: /*#__PURE__*/_jsxDEV(AnnouncementIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/notices',\n    badge: '3'\n  }];\n\n  // Academic Management\n  const academicItems = [{\n    text: 'Classes',\n    icon: /*#__PURE__*/_jsxDEV(ClassIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/classes',\n    badge: null\n  }, {\n    text: 'Subjects',\n    icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/subjects',\n    badge: null\n  }, {\n    text: 'Academics',\n    icon: /*#__PURE__*/_jsxDEV(MenuBookIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/academics',\n    badge: null\n  }, {\n    text: 'Examination',\n    icon: /*#__PURE__*/_jsxDEV(QuizIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/examination',\n    badge: '2'\n  }];\n\n  // Staff Management\n  const staffItems = [{\n    text: 'Teachers',\n    icon: /*#__PURE__*/_jsxDEV(SupervisorAccountIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/teachers',\n    badge: null\n  }, {\n    text: 'Attendance',\n    icon: /*#__PURE__*/_jsxDEV(EventAvailableIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/attendance',\n    badge: null\n  }];\n\n  // Financial Management\n  const financialItems = [{\n    text: 'Fee Management',\n    icon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/fee-management',\n    badge: '12'\n  }, {\n    text: 'Fee Due',\n    icon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/fee-due',\n    badge: '8'\n  }];\n\n  // Operations\n  const operationsItems = [{\n    text: 'Transport',\n    icon: /*#__PURE__*/_jsxDEV(DirectionsBusIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/transport',\n    badge: null\n  }, {\n    text: 'Hostel',\n    icon: /*#__PURE__*/_jsxDEV(HotelIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/hostel',\n    badge: '3'\n  }, {\n    text: 'Front Office',\n    icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/front-office',\n    badge: null\n  }];\n\n  // Communication\n  const communicationItems = [{\n    text: 'Notifications',\n    icon: /*#__PURE__*/_jsxDEV(NotificationsActiveIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/notifications',\n    badge: '15'\n  }, {\n    text: 'Notices',\n    icon: /*#__PURE__*/_jsxDEV(AnnouncementIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/notices',\n    badge: '3'\n  }, {\n    text: 'CMS',\n    icon: /*#__PURE__*/_jsxDEV(WebIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/cms',\n    badge: null\n  }];\n\n  // Documents & Reports\n  const documentsItems = [{\n    text: 'Documents',\n    icon: /*#__PURE__*/_jsxDEV(DescriptionIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/documents',\n    badge: null\n  }, {\n    text: 'Complaints',\n    icon: /*#__PURE__*/_jsxDEV(ReportIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/complains',\n    badge: '2'\n  }];\n  const userMenuItems = [{\n    text: 'Profile',\n    icon: /*#__PURE__*/_jsxDEV(AccountCircleOutlinedIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/profile',\n    badge: null\n  }, {\n    text: 'Logout',\n    icon: /*#__PURE__*/_jsxDEV(ExitToAppIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 13\n    }, this),\n    path: '/logout',\n    badge: null\n  }];\n  const isActive = path => {\n    if (path === '/Admin/dashboard') {\n      return location.pathname === '/' || location.pathname === '/Admin/dashboard';\n    }\n    return location.pathname.startsWith(path);\n  };\n  const renderMenuSection = (title, items, sectionKey) => /*#__PURE__*/_jsxDEV(MenuSection, {\n    children: [/*#__PURE__*/_jsxDEV(StyledListItemButton, {\n      onClick: () => handleSectionToggle(sectionKey),\n      sx: {\n        justifyContent: 'space-between',\n        backgroundColor: 'rgba(255, 255, 255, 0.05)',\n        mb: 1,\n        '&:hover': {\n          backgroundColor: 'rgba(255, 255, 255, 0.1)'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), expandedSections[sectionKey] ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 41\n      }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 58\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: expandedSections[sectionKey],\n      timeout: \"auto\",\n      unmountOnExit: true,\n      children: /*#__PURE__*/_jsxDEV(List, {\n        component: \"nav\",\n        sx: {\n          padding: 0,\n          pl: 1\n        },\n        children: items.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: index * 0.05\n          },\n          children: /*#__PURE__*/_jsxDEV(StyledListItemButton, {\n            component: Link,\n            to: item.path,\n            active: isActive(item.path),\n            onClick: isMobile ? onClose : undefined,\n            sx: {\n              pl: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              sx: {\n                color: 'inherit',\n                minWidth: 40\n              },\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: item.text,\n              primaryTypographyProps: {\n                fontSize: '0.85rem',\n                fontWeight: isActive(item.path) ? 600 : 400\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), item.badge && /*#__PURE__*/_jsxDEV(Chip, {\n              label: item.badge,\n              size: \"small\",\n              sx: {\n                bgcolor: '#FFD700',\n                color: '#333',\n                fontSize: '0.7rem',\n                height: 18,\n                minWidth: 18\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this)\n        }, item.text, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this)]\n  }, sectionKey, true, {\n    fileName: _jsxFileName,\n    lineNumber: 372,\n    columnNumber: 5\n  }, this);\n  const drawerContent = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(SidebarHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        sx: {\n          bgcolor: 'rgba(255, 255, 255, 0.2)',\n          width: 48,\n          height: 48\n        },\n        children: /*#__PURE__*/_jsxDEV(SchoolIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"bold\",\n          children: \"School Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            opacity: 0.8\n          },\n          children: \"Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        overflowY: 'auto',\n        py: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(MenuSection, {\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          sx: {\n            padding: 0\n          },\n          children: dashboardItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(StyledListItemButton, {\n              component: Link,\n              to: item.path,\n              active: isActive(item.path),\n              onClick: isMobile ? onClose : undefined,\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                sx: {\n                  color: 'inherit',\n                  minWidth: 40\n                },\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: item.text,\n                primaryTypographyProps: {\n                  fontSize: '0.9rem',\n                  fontWeight: isActive(item.path) ? 600 : 400\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this)\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mx: 2,\n          my: 1,\n          bgcolor: 'rgba(255, 255, 255, 0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this), renderMenuSection('👥 Student Management', studentManagementItems, 'students'), renderMenuSection('📚 Academic Management', academicItems, 'academics'), renderMenuSection('👨‍🏫 Staff Management', staffItems, 'staff'), renderMenuSection('💰 Financial Management', financialItems, 'finance'), renderMenuSection('🚌 Operations', operationsItems, 'operations'), renderMenuSection('📢 Communication', communicationItems, 'communication'), renderMenuSection('📄 Documents & Reports', documentsItems, 'documents'), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mx: 2,\n          my: 1,\n          bgcolor: 'rgba(255, 255, 255, 0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"\\uD83D\\uDC64 Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          sx: {\n            padding: 0\n          },\n          children: userMenuItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(StyledListItemButton, {\n              component: Link,\n              to: item.path,\n              active: isActive(item.path),\n              onClick: isMobile ? onClose : undefined,\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                sx: {\n                  color: 'inherit',\n                  minWidth: 40\n                },\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: item.text,\n                primaryTypographyProps: {\n                  fontSize: '0.9rem',\n                  fontWeight: isActive(item.path) ? 600 : 400\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this)\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 436,\n    columnNumber: 5\n  }, this);\n  if (isMobile) {\n    return /*#__PURE__*/_jsxDEV(StyledDrawer, {\n      variant: \"temporary\",\n      open: open,\n      onClose: onClose,\n      ModalProps: {\n        keepMounted: true // Better open performance on mobile.\n      },\n\n      sx: {\n        '& .MuiDrawer-paper': {\n          width: 280\n        }\n      },\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(StyledDrawer, {\n    variant: variant,\n    open: open,\n    sx: {\n      '& .MuiDrawer-paper': {\n        width: open ? 280 : 70,\n        transition: theme.transitions.create('width', {\n          easing: theme.transitions.easing.sharp,\n          duration: theme.transitions.duration.enteringScreen\n        }),\n        overflowX: 'hidden'\n      }\n    },\n    children: drawerContent\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 572,\n    columnNumber: 5\n  }, this);\n};\n_s(ResponsiveSidebar, \"EDcwBIz4vaW9vHEUm4iGTJ9ZhSk=\", false, function () {\n  return [useTheme, useMediaQuery, useLocation];\n});\n_c6 = ResponsiveSidebar;\nexport default ResponsiveSidebar;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"StyledDrawer\");\n$RefreshReg$(_c2, \"SidebarHeader\");\n$RefreshReg$(_c3, \"StyledListItemButton\");\n$RefreshReg$(_c4, \"MenuSection\");\n$RefreshReg$(_c5, \"SectionTitle\");\n$RefreshReg$(_c6, \"ResponsiveSidebar\");", "map": {"version": 3, "names": ["React", "Drawer", "List", "ListItemButton", "ListItemIcon", "ListItemText", "Divider", "Box", "Typography", "useTheme", "useMediaQuery", "Avatar", "Chip", "Collapse", "styled", "motion", "Link", "useLocation", "Home", "HomeIcon", "Class", "ClassIcon", "Assignment", "AssignmentIcon", "SupervisorAccount", "SupervisorAccountIcon", "PersonOutline", "PersonOutlineIcon", "Announcement", "AnnouncementIcon", "Report", "ReportIcon", "AccountCircleOutlined", "AccountCircleOutlinedIcon", "ExitToApp", "ExitToAppIcon", "School", "SchoolIcon", "Info", "InfoIcon", "Payment", "PaymentIcon", "Description", "DescriptionIcon", "DirectionsBus", "DirectionsBusIcon", "People", "PeopleIcon", "PersonAdd", "PersonAddIcon", "EventAvailable", "EventAvailableIcon", "Quiz", "QuizIcon", "MenuBook", "MenuBookIcon", "NotificationsActive", "NotificationsActiveIcon", "Hotel", "HotelIcon", "Web", "WebIcon", "Business", "BusinessIcon", "ExpandLess", "ExpandMore", "jsxDEV", "_jsxDEV", "StyledDrawer", "_ref", "theme", "background", "color", "borderRight", "boxShadow", "<PERSON><PERSON>ilter", "position", "content", "top", "left", "right", "bottom", "pointerEvents", "_c", "SidebarHeader", "_ref2", "padding", "spacing", "display", "alignItems", "gap", "borderBottom", "transform", "width", "height", "_c2", "StyledListItemButton", "_ref3", "active", "margin", "borderRadius", "backgroundColor", "border", "transition", "overflow", "_c3", "MenuSection", "_ref4", "marginTop", "_c4", "SectionTitle", "_ref5", "fontSize", "fontWeight", "textTransform", "letterSpacing", "opacity", "marginBottom", "marginLeft", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "_c5", "ResponsiveSidebar", "_ref6", "_s", "open", "onClose", "variant", "isMobile", "breakpoints", "down", "location", "mainMenuItems", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "badge", "academicItems", "staffItems", "financialItems", "operationsItems", "communicationItems", "documentsItems", "userMenuItems", "isActive", "pathname", "startsWith", "renderMenuSection", "title", "items", "sectionKey", "children", "onClick", "handleSectionToggle", "sx", "justifyContent", "mb", "expandedSections", "in", "timeout", "unmountOnExit", "component", "pl", "map", "item", "index", "div", "initial", "x", "animate", "delay", "to", "undefined", "min<PERSON><PERSON><PERSON>", "primary", "primaryTypographyProps", "label", "size", "bgcolor", "drawerContent", "flexDirection", "flex", "overflowY", "py", "dashboardItems", "mx", "my", "studentManagementItems", "ModalProps", "keepMounted", "transitions", "create", "easing", "sharp", "duration", "enteringScreen", "overflowX", "_c6", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/components/layout/ResponsiveSidebar.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Drawer,\n  List,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Box,\n  Typography,\n  useTheme,\n  useMediaQuery,\n  Avatar,\n  Chip,\n  Collapse\n} from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport {\n  Home as HomeIcon,\n  Class as ClassIcon,\n  Assignment as AssignmentIcon,\n  SupervisorAccount as SupervisorAccountIcon,\n  PersonOutline as PersonOutlineIcon,\n  Announcement as AnnouncementIcon,\n  Report as ReportIcon,\n  AccountCircleOutlined as AccountCircleOutlinedIcon,\n  ExitToApp as ExitToAppIcon,\n  School as SchoolIcon,\n  // New icons for additional modules\n  Info as InfoIcon,\n  Payment as PaymentIcon,\n  Description as DescriptionIcon,\n  DirectionsBus as DirectionsBusIcon,\n  People as PeopleIcon,\n  PersonAdd as PersonAddIcon,\n  EventAvailable as EventAvailableIcon,\n  Quiz as QuizIcon,\n  MenuBook as MenuBookIcon,\n  NotificationsActive as NotificationsActiveIcon,\n  Hotel as HotelIcon,\n  Web as WebIcon,\n  Business as BusinessIcon,\n  ExpandLess,\n  ExpandMore,\n} from '@mui/icons-material';\n\nconst StyledDrawer = styled(Drawer)(({ theme }) => ({\n  '& .MuiDrawer-paper': {\n    background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%)',\n    color: 'white',\n    borderRight: 'none',\n    boxShadow: '8px 0 32px rgba(0, 0, 0, 0.15)',\n    backdropFilter: 'blur(10px)',\n    position: 'relative',\n    '&::before': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n      pointerEvents: 'none',\n    }\n  },\n}));\n\nconst SidebarHeader = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(4, 2),\n  display: 'flex',\n  alignItems: 'center',\n  gap: theme.spacing(2),\n  borderBottom: '2px solid rgba(255, 255, 255, 0.15)',\n  background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n  backdropFilter: 'blur(10px)',\n  position: 'relative',\n  '&::after': {\n    content: '\"\"',\n    position: 'absolute',\n    bottom: 0,\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '60%',\n    height: '2px',\n    background: 'linear-gradient(90deg, transparent, #FFD700, transparent)',\n  }\n}));\n\nconst StyledListItemButton = styled(ListItemButton)(({ theme, active }) => ({\n  margin: theme.spacing(0.5, 1.5),\n  borderRadius: '16px',\n  padding: theme.spacing(1.5, 2),\n  backgroundColor: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',\n  backdropFilter: active ? 'blur(15px)' : 'none',\n  border: active ? '1px solid rgba(255, 255, 255, 0.3)' : '1px solid transparent',\n  boxShadow: active ? '0 8px 32px rgba(0, 0, 0, 0.1)' : 'none',\n  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n  position: 'relative',\n  overflow: 'hidden',\n  '&:hover': {\n    backgroundColor: 'rgba(255, 255, 255, 0.15)',\n    transform: 'translateX(8px) scale(1.02)',\n    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',\n  },\n  '&:before': {\n    content: '\"\"',\n    position: 'absolute',\n    left: 0,\n    top: '50%',\n    transform: 'translateY(-50%)',\n    width: active ? '5px' : '0px',\n    height: '70%',\n    background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n    borderRadius: '0 8px 8px 0',\n    transition: 'width 0.3s ease',\n    boxShadow: active ? '0 0 20px rgba(255, 215, 0, 0.5)' : 'none',\n  },\n  '&:after': {\n    content: '\"\"',\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: active ? 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))' : 'transparent',\n    pointerEvents: 'none',\n    borderRadius: '16px',\n  },\n}));\n\nconst MenuSection = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(1, 1.5),\n  marginTop: theme.spacing(1.5),\n}));\n\nconst SectionTitle = styled(Typography)(({ theme }) => ({\n  fontSize: '0.8rem',\n  fontWeight: 700,\n  textTransform: 'uppercase',\n  letterSpacing: '1px',\n  opacity: 0.9,\n  marginBottom: theme.spacing(1.5),\n  marginLeft: theme.spacing(1),\n  background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n  backgroundClip: 'text',\n  WebkitBackgroundClip: 'text',\n  WebkitTextFillColor: 'transparent',\n  textShadow: '0 2px 4px rgba(0,0,0,0.1)',\n}));\n\nconst ResponsiveSidebar = ({ open, onClose, variant = 'permanent' }) => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const location = useLocation();\n  // Simplified menu without collapsible sections\n\n  // Simplified main menu items\n  const mainMenuItems = [\n    {\n      text: 'Dashboard',\n      icon: <HomeIcon />,\n      path: '/Admin/dashboard',\n      badge: null\n    },\n    {\n      text: 'Student Info',\n      icon: <InfoIcon />,\n      path: '/Admin/student-info',\n      badge: null\n    },\n    {\n      text: 'Students',\n      icon: <PersonOutlineIcon />,\n      path: '/Admin/students',\n      badge: null\n    },\n    {\n      text: 'Classes',\n      icon: <ClassIcon />,\n      path: '/Admin/classes',\n      badge: null\n    },\n    {\n      text: 'Teachers',\n      icon: <SupervisorAccountIcon />,\n      path: '/Admin/teachers',\n      badge: null\n    },\n    {\n      text: 'Subjects',\n      icon: <AssignmentIcon />,\n      path: '/Admin/subjects',\n      badge: null\n    },\n    {\n      text: 'Fee Management',\n      icon: <PaymentIcon />,\n      path: '/Admin/fee-management',\n      badge: '12'\n    },\n    {\n      text: 'Transport',\n      icon: <DirectionsBusIcon />,\n      path: '/Admin/transport',\n      badge: null\n    },\n    {\n      text: 'Documents',\n      icon: <DescriptionIcon />,\n      path: '/Admin/documents',\n      badge: null\n    },\n    {\n      text: 'Notifications',\n      icon: <NotificationsActiveIcon />,\n      path: '/Admin/notifications',\n      badge: '15'\n    },\n    {\n      text: 'Notices',\n      icon: <AnnouncementIcon />,\n      path: '/Admin/notices',\n      badge: '3'\n    },\n  ];\n\n  // Academic Management\n  const academicItems = [\n    {\n      text: 'Classes',\n      icon: <ClassIcon />,\n      path: '/Admin/classes',\n      badge: null\n    },\n    {\n      text: 'Subjects',\n      icon: <AssignmentIcon />,\n      path: '/Admin/subjects',\n      badge: null\n    },\n    {\n      text: 'Academics',\n      icon: <MenuBookIcon />,\n      path: '/Admin/academics',\n      badge: null\n    },\n    {\n      text: 'Examination',\n      icon: <QuizIcon />,\n      path: '/Admin/examination',\n      badge: '2'\n    },\n  ];\n\n  // Staff Management\n  const staffItems = [\n    {\n      text: 'Teachers',\n      icon: <SupervisorAccountIcon />,\n      path: '/Admin/teachers',\n      badge: null\n    },\n    {\n      text: 'Attendance',\n      icon: <EventAvailableIcon />,\n      path: '/Admin/attendance',\n      badge: null\n    },\n  ];\n\n  // Financial Management\n  const financialItems = [\n    {\n      text: 'Fee Management',\n      icon: <PaymentIcon />,\n      path: '/Admin/fee-management',\n      badge: '12'\n    },\n    {\n      text: 'Fee Due',\n      icon: <PaymentIcon />,\n      path: '/Admin/fee-due',\n      badge: '8'\n    },\n  ];\n\n  // Operations\n  const operationsItems = [\n    {\n      text: 'Transport',\n      icon: <DirectionsBusIcon />,\n      path: '/Admin/transport',\n      badge: null\n    },\n    {\n      text: 'Hostel',\n      icon: <HotelIcon />,\n      path: '/Admin/hostel',\n      badge: '3'\n    },\n    {\n      text: 'Front Office',\n      icon: <BusinessIcon />,\n      path: '/Admin/front-office',\n      badge: null\n    },\n  ];\n\n  // Communication\n  const communicationItems = [\n    {\n      text: 'Notifications',\n      icon: <NotificationsActiveIcon />,\n      path: '/Admin/notifications',\n      badge: '15'\n    },\n    {\n      text: 'Notices',\n      icon: <AnnouncementIcon />,\n      path: '/Admin/notices',\n      badge: '3'\n    },\n    {\n      text: 'CMS',\n      icon: <WebIcon />,\n      path: '/Admin/cms',\n      badge: null\n    },\n  ];\n\n  // Documents & Reports\n  const documentsItems = [\n    {\n      text: 'Documents',\n      icon: <DescriptionIcon />,\n      path: '/Admin/documents',\n      badge: null\n    },\n    {\n      text: 'Complaints',\n      icon: <ReportIcon />,\n      path: '/Admin/complains',\n      badge: '2'\n    },\n  ];\n\n  const userMenuItems = [\n    {\n      text: 'Profile',\n      icon: <AccountCircleOutlinedIcon />,\n      path: '/Admin/profile',\n      badge: null\n    },\n    {\n      text: 'Logout',\n      icon: <ExitToAppIcon />,\n      path: '/logout',\n      badge: null\n    },\n  ];\n\n  const isActive = (path) => {\n    if (path === '/Admin/dashboard') {\n      return location.pathname === '/' || location.pathname === '/Admin/dashboard';\n    }\n    return location.pathname.startsWith(path);\n  };\n\n  const renderMenuSection = (title, items, sectionKey) => (\n    <MenuSection key={sectionKey}>\n      <StyledListItemButton\n        onClick={() => handleSectionToggle(sectionKey)}\n        sx={{\n          justifyContent: 'space-between',\n          backgroundColor: 'rgba(255, 255, 255, 0.05)',\n          mb: 1,\n          '&:hover': {\n            backgroundColor: 'rgba(255, 255, 255, 0.1)',\n          }\n        }}\n      >\n        <SectionTitle>{title}</SectionTitle>\n        {expandedSections[sectionKey] ? <ExpandLess /> : <ExpandMore />}\n      </StyledListItemButton>\n\n      <Collapse in={expandedSections[sectionKey]} timeout=\"auto\" unmountOnExit>\n        <List component=\"nav\" sx={{ padding: 0, pl: 1 }}>\n          {items.map((item, index) => (\n            <motion.div\n              key={item.text}\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: index * 0.05 }}\n            >\n              <StyledListItemButton\n                component={Link}\n                to={item.path}\n                active={isActive(item.path)}\n                onClick={isMobile ? onClose : undefined}\n                sx={{ pl: 2 }}\n              >\n                <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>\n                  {item.icon}\n                </ListItemIcon>\n                <ListItemText\n                  primary={item.text}\n                  primaryTypographyProps={{\n                    fontSize: '0.85rem',\n                    fontWeight: isActive(item.path) ? 600 : 400,\n                  }}\n                />\n                {item.badge && (\n                  <Chip\n                    label={item.badge}\n                    size=\"small\"\n                    sx={{\n                      bgcolor: '#FFD700',\n                      color: '#333',\n                      fontSize: '0.7rem',\n                      height: 18,\n                      minWidth: 18,\n                    }}\n                  />\n                )}\n              </StyledListItemButton>\n            </motion.div>\n          ))}\n        </List>\n      </Collapse>\n    </MenuSection>\n  );\n\n  const drawerContent = (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <SidebarHeader>\n        <Avatar\n          sx={{\n            bgcolor: 'rgba(255, 255, 255, 0.2)',\n            width: 48,\n            height: 48,\n          }}\n        >\n          <SchoolIcon />\n        </Avatar>\n        <Box>\n          <Typography variant=\"h6\" fontWeight=\"bold\">\n            School Admin\n          </Typography>\n          <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n            Management System\n          </Typography>\n        </Box>\n      </SidebarHeader>\n\n      <Box sx={{ flex: 1, overflowY: 'auto', py: 1 }}>\n        {/* Dashboard */}\n        <MenuSection>\n          <List component=\"nav\" sx={{ padding: 0 }}>\n            {dashboardItems.map((item, index) => (\n              <motion.div\n                key={item.text}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.1 }}\n              >\n                <StyledListItemButton\n                  component={Link}\n                  to={item.path}\n                  active={isActive(item.path)}\n                  onClick={isMobile ? onClose : undefined}\n                >\n                  <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>\n                    {item.icon}\n                  </ListItemIcon>\n                  <ListItemText\n                    primary={item.text}\n                    primaryTypographyProps={{\n                      fontSize: '0.9rem',\n                      fontWeight: isActive(item.path) ? 600 : 400,\n                    }}\n                  />\n                </StyledListItemButton>\n              </motion.div>\n            ))}\n          </List>\n        </MenuSection>\n\n        <Divider sx={{ mx: 2, my: 1, bgcolor: 'rgba(255, 255, 255, 0.1)' }} />\n\n        {/* Student Management */}\n        {renderMenuSection('👥 Student Management', studentManagementItems, 'students')}\n\n        {/* Academic Management */}\n        {renderMenuSection('📚 Academic Management', academicItems, 'academics')}\n\n        {/* Staff Management */}\n        {renderMenuSection('👨‍🏫 Staff Management', staffItems, 'staff')}\n\n        {/* Financial Management */}\n        {renderMenuSection('💰 Financial Management', financialItems, 'finance')}\n\n        {/* Operations */}\n        {renderMenuSection('🚌 Operations', operationsItems, 'operations')}\n\n        {/* Communication */}\n        {renderMenuSection('📢 Communication', communicationItems, 'communication')}\n\n        {/* Documents & Reports */}\n        {renderMenuSection('📄 Documents & Reports', documentsItems, 'documents')}\n\n        <Divider sx={{ mx: 2, my: 1, bgcolor: 'rgba(255, 255, 255, 0.1)' }} />\n\n        {/* User Account */}\n        <MenuSection>\n          <SectionTitle>👤 Account</SectionTitle>\n          <List component=\"nav\" sx={{ padding: 0 }}>\n            {userMenuItems.map((item, index) => (\n              <motion.div\n                key={item.text}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.1 }}\n              >\n                <StyledListItemButton\n                  component={Link}\n                  to={item.path}\n                  active={isActive(item.path)}\n                  onClick={isMobile ? onClose : undefined}\n                >\n                  <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>\n                    {item.icon}\n                  </ListItemIcon>\n                  <ListItemText\n                    primary={item.text}\n                    primaryTypographyProps={{\n                      fontSize: '0.9rem',\n                      fontWeight: isActive(item.path) ? 600 : 400,\n                    }}\n                  />\n                </StyledListItemButton>\n              </motion.div>\n            ))}\n          </List>\n        </MenuSection>\n      </Box>\n    </Box>\n  );\n\n  if (isMobile) {\n    return (\n      <StyledDrawer\n        variant=\"temporary\"\n        open={open}\n        onClose={onClose}\n        ModalProps={{\n          keepMounted: true, // Better open performance on mobile.\n        }}\n        sx={{\n          '& .MuiDrawer-paper': {\n            width: 280,\n          },\n        }}\n      >\n        {drawerContent}\n      </StyledDrawer>\n    );\n  }\n\n  return (\n    <StyledDrawer\n      variant={variant}\n      open={open}\n      sx={{\n        '& .MuiDrawer-paper': {\n          width: open ? 280 : 70,\n          transition: theme.transitions.create('width', {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.enteringScreen,\n          }),\n          overflowX: 'hidden',\n        },\n      }}\n    >\n      {drawerContent}\n    </StyledDrawer>\n  );\n};\n\nexport default ResponsiveSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,IAAI,EACJC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,aAAa,EACbC,MAAM,EACNC,IAAI,EACJC,QAAQ,QACH,eAAe;AACtB,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SACEC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,iBAAiB,IAAIC,qBAAqB,EAC1CC,aAAa,IAAIC,iBAAiB,EAClCC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,qBAAqB,IAAIC,yBAAyB,EAClDC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU;AACpB;AACAC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,eAAe,EAC9BC,aAAa,IAAIC,iBAAiB,EAClCC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,cAAc,IAAIC,kBAAkB,EACpCC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,mBAAmB,IAAIC,uBAAuB,EAC9CC,KAAK,IAAIC,SAAS,EAClBC,GAAG,IAAIC,OAAO,EACdC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,EACVC,UAAU,QACL,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,YAAY,GAAGtD,MAAM,CAACb,MAAM,CAAC,CAACoE,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IAClD,oBAAoB,EAAE;MACpBE,UAAU,EAAE,gEAAgE;MAC5EC,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE,gCAAgC;MAC3CC,cAAc,EAAE,YAAY;MAC5BC,QAAQ,EAAE,UAAU;MACpB,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbD,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTV,UAAU,EAAE,gFAAgF;QAC5FW,aAAa,EAAE;MACjB;IACF;EACF,CAAC;AAAA,CAAC,CAAC;AAACC,EAAA,GAnBEf,YAAY;AAqBlB,MAAMgB,aAAa,GAAGtE,MAAM,CAACP,GAAG,CAAC,CAAC8E,KAAA;EAAA,IAAC;IAAEf;EAAM,CAAC,GAAAe,KAAA;EAAA,OAAM;IAChDC,OAAO,EAAEhB,KAAK,CAACiB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5BC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAEpB,KAAK,CAACiB,OAAO,CAAC,CAAC,CAAC;IACrBI,YAAY,EAAE,qCAAqC;IACnDpB,UAAU,EAAE,gFAAgF;IAC5FI,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,UAAU;IACpB,UAAU,EAAE;MACVC,OAAO,EAAE,IAAI;MACbD,QAAQ,EAAE,UAAU;MACpBK,MAAM,EAAE,CAAC;MACTF,IAAI,EAAE,KAAK;MACXa,SAAS,EAAE,kBAAkB;MAC7BC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,KAAK;MACbvB,UAAU,EAAE;IACd;EACF,CAAC;AAAA,CAAC,CAAC;AAACwB,GAAA,GAnBEX,aAAa;AAqBnB,MAAMY,oBAAoB,GAAGlF,MAAM,CAACX,cAAc,CAAC,CAAC8F,KAAA;EAAA,IAAC;IAAE3B,KAAK;IAAE4B;EAAO,CAAC,GAAAD,KAAA;EAAA,OAAM;IAC1EE,MAAM,EAAE7B,KAAK,CAACiB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAC/Ba,YAAY,EAAE,MAAM;IACpBd,OAAO,EAAEhB,KAAK,CAACiB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IAC9Bc,eAAe,EAAEH,MAAM,GAAG,0BAA0B,GAAG,aAAa;IACpEvB,cAAc,EAAEuB,MAAM,GAAG,YAAY,GAAG,MAAM;IAC9CI,MAAM,EAAEJ,MAAM,GAAG,oCAAoC,GAAG,uBAAuB;IAC/ExB,SAAS,EAAEwB,MAAM,GAAG,+BAA+B,GAAG,MAAM;IAC5DK,UAAU,EAAE,uCAAuC;IACnD3B,QAAQ,EAAE,UAAU;IACpB4B,QAAQ,EAAE,QAAQ;IAClB,SAAS,EAAE;MACTH,eAAe,EAAE,2BAA2B;MAC5CT,SAAS,EAAE,6BAA6B;MACxClB,SAAS,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACVG,OAAO,EAAE,IAAI;MACbD,QAAQ,EAAE,UAAU;MACpBG,IAAI,EAAE,CAAC;MACPD,GAAG,EAAE,KAAK;MACVc,SAAS,EAAE,kBAAkB;MAC7BC,KAAK,EAAEK,MAAM,GAAG,KAAK,GAAG,KAAK;MAC7BJ,MAAM,EAAE,KAAK;MACbvB,UAAU,EAAE,2CAA2C;MACvD6B,YAAY,EAAE,aAAa;MAC3BG,UAAU,EAAE,iBAAiB;MAC7B7B,SAAS,EAAEwB,MAAM,GAAG,iCAAiC,GAAG;IAC1D,CAAC;IACD,SAAS,EAAE;MACTrB,OAAO,EAAE,IAAI;MACbD,QAAQ,EAAE,UAAU;MACpBE,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTV,UAAU,EAAE2B,MAAM,GAAG,wEAAwE,GAAG,aAAa;MAC7GhB,aAAa,EAAE,MAAM;MACrBkB,YAAY,EAAE;IAChB;EACF,CAAC;AAAA,CAAC,CAAC;AAACK,GAAA,GAxCET,oBAAoB;AA0C1B,MAAMU,WAAW,GAAG5F,MAAM,CAACP,GAAG,CAAC,CAACoG,KAAA;EAAA,IAAC;IAAErC;EAAM,CAAC,GAAAqC,KAAA;EAAA,OAAM;IAC9CrB,OAAO,EAAEhB,KAAK,CAACiB,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;IAC9BqB,SAAS,EAAEtC,KAAK,CAACiB,OAAO,CAAC,GAAG;EAC9B,CAAC;AAAA,CAAC,CAAC;AAACsB,GAAA,GAHEH,WAAW;AAKjB,MAAMI,YAAY,GAAGhG,MAAM,CAACN,UAAU,CAAC,CAACuG,KAAA;EAAA,IAAC;IAAEzC;EAAM,CAAC,GAAAyC,KAAA;EAAA,OAAM;IACtDC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,GAAG;IACfC,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE,KAAK;IACpBC,OAAO,EAAE,GAAG;IACZC,YAAY,EAAE/C,KAAK,CAACiB,OAAO,CAAC,GAAG,CAAC;IAChC+B,UAAU,EAAEhD,KAAK,CAACiB,OAAO,CAAC,CAAC,CAAC;IAC5BhB,UAAU,EAAE,2CAA2C;IACvDgD,cAAc,EAAE,MAAM;IACtBC,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE,aAAa;IAClCC,UAAU,EAAE;EACd,CAAC;AAAA,CAAC,CAAC;AAACC,GAAA,GAbEb,YAAY;AAelB,MAAMc,iBAAiB,GAAGC,KAAA,IAA8C;EAAAC,EAAA;EAAA,IAA7C;IAAEC,IAAI;IAAEC,OAAO;IAAEC,OAAO,GAAG;EAAY,CAAC,GAAAJ,KAAA;EACjE,MAAMvD,KAAK,GAAG7D,QAAQ,EAAE;EACxB,MAAMyH,QAAQ,GAAGxH,aAAa,CAAC4D,KAAK,CAAC6D,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAMC,QAAQ,GAAGpH,WAAW,EAAE;EAC9B;;EAEA;EACA,MAAMqH,aAAa,GAAG,CACpB;IACEC,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAErE,OAAA,CAAChD,QAAQ;MAAAsH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAClBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,eAAErE,OAAA,CAAC5B,QAAQ;MAAAkG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAClBC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAErE,OAAA,CAACxC,iBAAiB;MAAA8G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC3BC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,eAAErE,OAAA,CAAC9C,SAAS;MAAAoH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnBC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAErE,OAAA,CAAC1C,qBAAqB;MAAAgH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC/BC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAErE,OAAA,CAAC5C,cAAc;MAAAkH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACxBC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,gBAAgB;IACtBC,IAAI,eAAErE,OAAA,CAAC1B,WAAW;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACrBC,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAErE,OAAA,CAACtB,iBAAiB;MAAA4F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC3BC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAErE,OAAA,CAACxB,eAAe;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,eAAe;IACrBC,IAAI,eAAErE,OAAA,CAACV,uBAAuB;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACjCC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,eAAErE,OAAA,CAACtC,gBAAgB;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC1BC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IACER,IAAI,EAAE,SAAS;IACfC,IAAI,eAAErE,OAAA,CAAC9C,SAAS;MAAAoH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnBC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAErE,OAAA,CAAC5C,cAAc;MAAAkH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACxBC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAErE,OAAA,CAACZ,YAAY;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACtBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,aAAa;IACnBC,IAAI,eAAErE,OAAA,CAACd,QAAQ;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAClBC,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAME,UAAU,GAAG,CACjB;IACET,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAErE,OAAA,CAAC1C,qBAAqB;MAAAgH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC/BC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,eAAErE,OAAA,CAAChB,kBAAkB;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC5BC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMG,cAAc,GAAG,CACrB;IACEV,IAAI,EAAE,gBAAgB;IACtBC,IAAI,eAAErE,OAAA,CAAC1B,WAAW;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACrBC,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,eAAErE,OAAA,CAAC1B,WAAW;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACrBC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMI,eAAe,GAAG,CACtB;IACEX,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAErE,OAAA,CAACtB,iBAAiB;MAAA4F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC3BC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdC,IAAI,eAAErE,OAAA,CAACR,SAAS;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnBC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,eAAErE,OAAA,CAACJ,YAAY;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACtBC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMK,kBAAkB,GAAG,CACzB;IACEZ,IAAI,EAAE,eAAe;IACrBC,IAAI,eAAErE,OAAA,CAACV,uBAAuB;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACjCC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,eAAErE,OAAA,CAACtC,gBAAgB;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC1BC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,KAAK;IACXC,IAAI,eAAErE,OAAA,CAACN,OAAO;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACjBC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMM,cAAc,GAAG,CACrB;IACEb,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAErE,OAAA,CAACxB,eAAe;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,eAAErE,OAAA,CAACpC,UAAU;MAAA0G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACpBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMO,aAAa,GAAG,CACpB;IACEd,IAAI,EAAE,SAAS;IACfC,IAAI,eAAErE,OAAA,CAAClC,yBAAyB;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnCC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdC,IAAI,eAAErE,OAAA,CAAChC,aAAa;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMQ,QAAQ,GAAIT,IAAI,IAAK;IACzB,IAAIA,IAAI,KAAK,kBAAkB,EAAE;MAC/B,OAAOR,QAAQ,CAACkB,QAAQ,KAAK,GAAG,IAAIlB,QAAQ,CAACkB,QAAQ,KAAK,kBAAkB;IAC9E;IACA,OAAOlB,QAAQ,CAACkB,QAAQ,CAACC,UAAU,CAACX,IAAI,CAAC;EAC3C,CAAC;EAED,MAAMY,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,UAAU,kBACjDzF,OAAA,CAACuC,WAAW;IAAAmD,QAAA,gBACV1F,OAAA,CAAC6B,oBAAoB;MACnB8D,OAAO,EAAEA,CAAA,KAAMC,mBAAmB,CAACH,UAAU,CAAE;MAC/CI,EAAE,EAAE;QACFC,cAAc,EAAE,eAAe;QAC/B5D,eAAe,EAAE,2BAA2B;QAC5C6D,EAAE,EAAE,CAAC;QACL,SAAS,EAAE;UACT7D,eAAe,EAAE;QACnB;MACF,CAAE;MAAAwD,QAAA,gBAEF1F,OAAA,CAAC2C,YAAY;QAAA+C,QAAA,EAAEH;MAAK;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAgB,EACnCuB,gBAAgB,CAACP,UAAU,CAAC,gBAAGzF,OAAA,CAACH,UAAU;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,gBAAGzE,OAAA,CAACF,UAAU;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC1C,eAEvBzE,OAAA,CAACtD,QAAQ;MAACuJ,EAAE,EAAED,gBAAgB,CAACP,UAAU,CAAE;MAACS,OAAO,EAAC,MAAM;MAACC,aAAa;MAAAT,QAAA,eACtE1F,OAAA,CAACjE,IAAI;QAACqK,SAAS,EAAC,KAAK;QAACP,EAAE,EAAE;UAAE1E,OAAO,EAAE,CAAC;UAAEkF,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,EAC7CF,KAAK,CAACc,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBxG,OAAA,CAACpD,MAAM,CAAC6J,GAAG;UAETC,OAAO,EAAE;YAAEzD,OAAO,EAAE,CAAC;YAAE0D,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAE3D,OAAO,EAAE,CAAC;YAAE0D,CAAC,EAAE;UAAE,CAAE;UAC9BvE,UAAU,EAAE;YAAEyE,KAAK,EAAEL,KAAK,GAAG;UAAK,CAAE;UAAAd,QAAA,eAEpC1F,OAAA,CAAC6B,oBAAoB;YACnBuE,SAAS,EAAEvJ,IAAK;YAChBiK,EAAE,EAAEP,IAAI,CAAC7B,IAAK;YACd3C,MAAM,EAAEoD,QAAQ,CAACoB,IAAI,CAAC7B,IAAI,CAAE;YAC5BiB,OAAO,EAAE5B,QAAQ,GAAGF,OAAO,GAAGkD,SAAU;YACxClB,EAAE,EAAE;cAAEQ,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,gBAEd1F,OAAA,CAAC/D,YAAY;cAAC4J,EAAE,EAAE;gBAAExF,KAAK,EAAE,SAAS;gBAAE2G,QAAQ,EAAE;cAAG,CAAE;cAAAtB,QAAA,EAClDa,IAAI,CAAClC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACG,eACfzE,OAAA,CAAC9D,YAAY;cACX+K,OAAO,EAAEV,IAAI,CAACnC,IAAK;cACnB8C,sBAAsB,EAAE;gBACtBrE,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAEqC,QAAQ,CAACoB,IAAI,CAAC7B,IAAI,CAAC,GAAG,GAAG,GAAG;cAC1C;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACF,EACD8B,IAAI,CAAC5B,KAAK,iBACT3E,OAAA,CAACvD,IAAI;cACH0K,KAAK,EAAEZ,IAAI,CAAC5B,KAAM;cAClByC,IAAI,EAAC,OAAO;cACZvB,EAAE,EAAE;gBACFwB,OAAO,EAAE,SAAS;gBAClBhH,KAAK,EAAE,MAAM;gBACbwC,QAAQ,EAAE,QAAQ;gBAClBlB,MAAM,EAAE,EAAE;gBACVqF,QAAQ,EAAE;cACZ;YAAE;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAEL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACoB,GAnClB8B,IAAI,CAACnC,IAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAqCjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACG;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACE;EAAA,GA3DKgB,UAAU;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QA6D7B;EAED,MAAM6C,aAAa,gBACjBtH,OAAA,CAAC5D,GAAG;IAACyJ,EAAE,EAAE;MAAElE,MAAM,EAAE,MAAM;MAAEN,OAAO,EAAE,MAAM;MAAEkG,aAAa,EAAE;IAAS,CAAE;IAAA7B,QAAA,gBACpE1F,OAAA,CAACiB,aAAa;MAAAyE,QAAA,gBACZ1F,OAAA,CAACxD,MAAM;QACLqJ,EAAE,EAAE;UACFwB,OAAO,EAAE,0BAA0B;UACnC3F,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE;QACV,CAAE;QAAA+D,QAAA,eAEF1F,OAAA,CAAC9B,UAAU;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACP,eACTzE,OAAA,CAAC5D,GAAG;QAAAsJ,QAAA,gBACF1F,OAAA,CAAC3D,UAAU;UAACyH,OAAO,EAAC,IAAI;UAAChB,UAAU,EAAC,MAAM;UAAA4C,QAAA,EAAC;QAE3C;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACbzE,OAAA,CAAC3D,UAAU;UAACyH,OAAO,EAAC,SAAS;UAAC+B,EAAE,EAAE;YAAE5C,OAAO,EAAE;UAAI,CAAE;UAAAyC,QAAA,EAAC;QAEpD;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACQ,eAEhBzE,OAAA,CAAC5D,GAAG;MAACyJ,EAAE,EAAE;QAAE2B,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE,MAAM;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAhC,QAAA,gBAE7C1F,OAAA,CAACuC,WAAW;QAAAmD,QAAA,eACV1F,OAAA,CAACjE,IAAI;UAACqK,SAAS,EAAC,KAAK;UAACP,EAAE,EAAE;YAAE1E,OAAO,EAAE;UAAE,CAAE;UAAAuE,QAAA,EACtCiC,cAAc,CAACrB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC9BxG,OAAA,CAACpD,MAAM,CAAC6J,GAAG;YAETC,OAAO,EAAE;cAAEzD,OAAO,EAAE,CAAC;cAAE0D,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAE3D,OAAO,EAAE,CAAC;cAAE0D,CAAC,EAAE;YAAE,CAAE;YAC9BvE,UAAU,EAAE;cAAEyE,KAAK,EAAEL,KAAK,GAAG;YAAI,CAAE;YAAAd,QAAA,eAEnC1F,OAAA,CAAC6B,oBAAoB;cACnBuE,SAAS,EAAEvJ,IAAK;cAChBiK,EAAE,EAAEP,IAAI,CAAC7B,IAAK;cACd3C,MAAM,EAAEoD,QAAQ,CAACoB,IAAI,CAAC7B,IAAI,CAAE;cAC5BiB,OAAO,EAAE5B,QAAQ,GAAGF,OAAO,GAAGkD,SAAU;cAAArB,QAAA,gBAExC1F,OAAA,CAAC/D,YAAY;gBAAC4J,EAAE,EAAE;kBAAExF,KAAK,EAAE,SAAS;kBAAE2G,QAAQ,EAAE;gBAAG,CAAE;gBAAAtB,QAAA,EAClDa,IAAI,CAAClC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACG,eACfzE,OAAA,CAAC9D,YAAY;gBACX+K,OAAO,EAAEV,IAAI,CAACnC,IAAK;gBACnB8C,sBAAsB,EAAE;kBACtBrE,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAEqC,QAAQ,CAACoB,IAAI,CAAC7B,IAAI,CAAC,GAAG,GAAG,GAAG;gBAC1C;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACmB,GArBlB8B,IAAI,CAACnC,IAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAuBjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACK,eAEdzE,OAAA,CAAC7D,OAAO;QAAC0J,EAAE,EAAE;UAAE+B,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAER,OAAO,EAAE;QAA2B;MAAE;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,EAGrEa,iBAAiB,CAAC,uBAAuB,EAAEwC,sBAAsB,EAAE,UAAU,CAAC,EAG9ExC,iBAAiB,CAAC,wBAAwB,EAAEV,aAAa,EAAE,WAAW,CAAC,EAGvEU,iBAAiB,CAAC,wBAAwB,EAAET,UAAU,EAAE,OAAO,CAAC,EAGhES,iBAAiB,CAAC,yBAAyB,EAAER,cAAc,EAAE,SAAS,CAAC,EAGvEQ,iBAAiB,CAAC,eAAe,EAAEP,eAAe,EAAE,YAAY,CAAC,EAGjEO,iBAAiB,CAAC,kBAAkB,EAAEN,kBAAkB,EAAE,eAAe,CAAC,EAG1EM,iBAAiB,CAAC,wBAAwB,EAAEL,cAAc,EAAE,WAAW,CAAC,eAEzEjF,OAAA,CAAC7D,OAAO;QAAC0J,EAAE,EAAE;UAAE+B,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAER,OAAO,EAAE;QAA2B;MAAE;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAGtEzE,OAAA,CAACuC,WAAW;QAAAmD,QAAA,gBACV1F,OAAA,CAAC2C,YAAY;UAAA+C,QAAA,EAAC;QAAU;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAe,eACvCzE,OAAA,CAACjE,IAAI;UAACqK,SAAS,EAAC,KAAK;UAACP,EAAE,EAAE;YAAE1E,OAAO,EAAE;UAAE,CAAE;UAAAuE,QAAA,EACtCR,aAAa,CAACoB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7BxG,OAAA,CAACpD,MAAM,CAAC6J,GAAG;YAETC,OAAO,EAAE;cAAEzD,OAAO,EAAE,CAAC;cAAE0D,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAE3D,OAAO,EAAE,CAAC;cAAE0D,CAAC,EAAE;YAAE,CAAE;YAC9BvE,UAAU,EAAE;cAAEyE,KAAK,EAAEL,KAAK,GAAG;YAAI,CAAE;YAAAd,QAAA,eAEnC1F,OAAA,CAAC6B,oBAAoB;cACnBuE,SAAS,EAAEvJ,IAAK;cAChBiK,EAAE,EAAEP,IAAI,CAAC7B,IAAK;cACd3C,MAAM,EAAEoD,QAAQ,CAACoB,IAAI,CAAC7B,IAAI,CAAE;cAC5BiB,OAAO,EAAE5B,QAAQ,GAAGF,OAAO,GAAGkD,SAAU;cAAArB,QAAA,gBAExC1F,OAAA,CAAC/D,YAAY;gBAAC4J,EAAE,EAAE;kBAAExF,KAAK,EAAE,SAAS;kBAAE2G,QAAQ,EAAE;gBAAG,CAAE;gBAAAtB,QAAA,EAClDa,IAAI,CAAClC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACG,eACfzE,OAAA,CAAC9D,YAAY;gBACX+K,OAAO,EAAEV,IAAI,CAACnC,IAAK;gBACnB8C,sBAAsB,EAAE;kBACtBrE,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAEqC,QAAQ,CAACoB,IAAI,CAAC7B,IAAI,CAAC,GAAG,GAAG,GAAG;gBAC1C;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACmB,GArBlB8B,IAAI,CAACnC,IAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAuBjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACK;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACV;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAET;EAED,IAAIV,QAAQ,EAAE;IACZ,oBACE/D,OAAA,CAACC,YAAY;MACX6D,OAAO,EAAC,WAAW;MACnBF,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAEA,OAAQ;MACjBkE,UAAU,EAAE;QACVC,WAAW,EAAE,IAAI,CAAE;MACrB,CAAE;;MACFnC,EAAE,EAAE;QACF,oBAAoB,EAAE;UACpBnE,KAAK,EAAE;QACT;MACF,CAAE;MAAAgE,QAAA,EAED4B;IAAa;MAAAhD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD;EAEnB;EAEA,oBACEzE,OAAA,CAACC,YAAY;IACX6D,OAAO,EAAEA,OAAQ;IACjBF,IAAI,EAAEA,IAAK;IACXiC,EAAE,EAAE;MACF,oBAAoB,EAAE;QACpBnE,KAAK,EAAEkC,IAAI,GAAG,GAAG,GAAG,EAAE;QACtBxB,UAAU,EAAEjC,KAAK,CAAC8H,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;UAC5CC,MAAM,EAAEhI,KAAK,CAAC8H,WAAW,CAACE,MAAM,CAACC,KAAK;UACtCC,QAAQ,EAAElI,KAAK,CAAC8H,WAAW,CAACI,QAAQ,CAACC;QACvC,CAAC,CAAC;QACFC,SAAS,EAAE;MACb;IACF,CAAE;IAAA7C,QAAA,EAED4B;EAAa;IAAAhD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACD;AAEnB,CAAC;AAACd,EAAA,CApbIF,iBAAiB;EAAA,QACPnH,QAAQ,EACLC,aAAa,EACbO,WAAW;AAAA;AAAA0L,GAAA,GAHxB/E,iBAAiB;AAsbvB,eAAeA,iBAAiB;AAAC,IAAAzC,EAAA,EAAAY,GAAA,EAAAU,GAAA,EAAAI,GAAA,EAAAc,GAAA,EAAAgF,GAAA;AAAAC,YAAA,CAAAzH,EAAA;AAAAyH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}