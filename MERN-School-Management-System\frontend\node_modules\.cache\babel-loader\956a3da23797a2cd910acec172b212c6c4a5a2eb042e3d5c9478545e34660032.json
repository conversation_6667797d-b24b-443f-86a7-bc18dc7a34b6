{"ast": null, "code": "import axios from 'axios';\nimport { getRequest, getSuccess, getFailed, getError, getStudentsSuccess, detailsSuccess, getFailedTwo, getSubjectsSuccess, getSubDetailsSuccess, getSubDetailsRequest } from './sclassSlice';\nconst REACT_APP_BASE_URL = \"http://localhost:5000\";\nexport const getAllSclasses = (id, address) => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const url = `${REACT_APP_BASE_URL}/${address}List/${id}`;\n    console.log('getAllSclasses - Making request to:', url);\n    console.log('getAllSclasses - Admin ID:', id);\n    const result = await axios.get(url);\n    console.log('getAllSclasses - Response:', result.data);\n    if (result.data.message) {\n      console.log('getAllSclasses - Failed with message:', result.data.message);\n      dispatch(getFailedTwo(result.data.message));\n    } else {\n      console.log('getAllSclasses - Success with data:', result.data);\n      dispatch(getSuccess(result.data));\n    }\n  } catch (error) {\n    console.log('getAllSclasses - Error:', error);\n    dispatch(getError(error));\n  }\n};\nexport const getClassStudents = id => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/Sclass/Students/${id}`);\n    if (result.data.message) {\n      dispatch(getFailedTwo(result.data.message));\n    } else {\n      dispatch(getStudentsSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const getClassDetails = (id, address) => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\n    if (result.data) {\n      dispatch(detailsSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const getSubjectList = (id, address) => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\n    if (result.data.message) {\n      dispatch(getFailed(result.data.message));\n    } else {\n      dispatch(getSubjectsSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const getTeacherFreeClassSubjects = id => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/FreeSubjectList/${id}`);\n    if (result.data.message) {\n      dispatch(getFailed(result.data.message));\n    } else {\n      dispatch(getSubjectsSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const getSubjectDetails = (id, address) => async dispatch => {\n  dispatch(getSubDetailsRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\n    if (result.data) {\n      dispatch(getSubDetailsSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};", "map": {"version": 3, "names": ["axios", "getRequest", "getSuccess", "getFailed", "getError", "getStudentsSuccess", "detailsSuccess", "getFailedTwo", "getSubjectsSuccess", "getSubDetailsSuccess", "getSubDetailsRequest", "REACT_APP_BASE_URL", "getAllSclasses", "id", "address", "dispatch", "url", "console", "log", "result", "get", "data", "message", "error", "getClassStudents", "process", "env", "getClassDetails", "getSubjectList", "getTeacherFreeClassSubjects", "getSubjectDetails"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/redux/sclassRelated/sclassHandle.js"], "sourcesContent": ["import axios from 'axios';\r\nimport {\r\n    getRequest,\r\n    getSuccess,\r\n    getFailed,\r\n    getError,\r\n    getStudentsSuccess,\r\n    detailsSuccess,\r\n    getFailedTwo,\r\n    getSubjectsSuccess,\r\n    getSubDetailsSuccess,\r\n    getSubDetailsRequest\r\n} from './sclassSlice';\r\n\r\nconst REACT_APP_BASE_URL = \"http://localhost:5000\";\r\n\r\nexport const getAllSclasses = (id, address) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const url = `${REACT_APP_BASE_URL}/${address}List/${id}`;\r\n        console.log('getAllSclasses - Making request to:', url);\r\n        console.log('getAllSclasses - Admin ID:', id);\r\n\r\n        const result = await axios.get(url);\r\n        console.log('getAllSclasses - Response:', result.data);\r\n\r\n        if (result.data.message) {\r\n            console.log('getAllSclasses - Failed with message:', result.data.message);\r\n            dispatch(getFailedTwo(result.data.message));\r\n        } else {\r\n            console.log('getAllSclasses - Success with data:', result.data);\r\n            dispatch(getSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        console.log('getAllSclasses - Error:', error);\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const getClassStudents = (id) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/Sclass/Students/${id}`);\r\n        if (result.data.message) {\r\n            dispatch(getFailedTwo(result.data.message));\r\n        } else {\r\n            dispatch(getStudentsSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const getClassDetails = (id, address) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\r\n        if (result.data) {\r\n            dispatch(detailsSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const getSubjectList = (id, address) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\r\n        if (result.data.message) {\r\n            dispatch(getFailed(result.data.message));\r\n        } else {\r\n            dispatch(getSubjectsSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const getTeacherFreeClassSubjects = (id) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/FreeSubjectList/${id}`);\r\n        if (result.data.message) {\r\n            dispatch(getFailed(result.data.message));\r\n        } else {\r\n            dispatch(getSubjectsSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const getSubjectDetails = (id, address) => async (dispatch) => {\r\n    dispatch(getSubDetailsRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\r\n        if (result.data) {\r\n            dispatch(getSubDetailsSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,UAAU,EACVC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,kBAAkB,EAClBC,oBAAoB,EACpBC,oBAAoB,QACjB,eAAe;AAEtB,MAAMC,kBAAkB,GAAG,uBAAuB;AAElD,OAAO,MAAMC,cAAc,GAAGA,CAACC,EAAE,EAAEC,OAAO,KAAK,MAAOC,QAAQ,IAAK;EAC/DA,QAAQ,CAACd,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMe,GAAG,GAAI,GAAEL,kBAAmB,IAAGG,OAAQ,QAAOD,EAAG,EAAC;IACxDI,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEF,GAAG,CAAC;IACvDC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEL,EAAE,CAAC;IAE7C,MAAMM,MAAM,GAAG,MAAMnB,KAAK,CAACoB,GAAG,CAACJ,GAAG,CAAC;IACnCC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,MAAM,CAACE,IAAI,CAAC;IAEtD,IAAIF,MAAM,CAACE,IAAI,CAACC,OAAO,EAAE;MACrBL,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEC,MAAM,CAACE,IAAI,CAACC,OAAO,CAAC;MACzEP,QAAQ,CAACR,YAAY,CAACY,MAAM,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC;IAC/C,CAAC,MAAM;MACHL,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEC,MAAM,CAACE,IAAI,CAAC;MAC/DN,QAAQ,CAACb,UAAU,CAACiB,MAAM,CAACE,IAAI,CAAC,CAAC;IACrC;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZN,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEK,KAAK,CAAC;IAC7CR,QAAQ,CAACX,QAAQ,CAACmB,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAIX,EAAE,IAAK,MAAOE,QAAQ,IAAK;EACxDA,QAAQ,CAACd,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMkB,MAAM,GAAG,MAAMnB,KAAK,CAACoB,GAAG,CAAE,GAAEK,OAAO,CAACC,GAAG,CAACf,kBAAmB,oBAAmBE,EAAG,EAAC,CAAC;IACzF,IAAIM,MAAM,CAACE,IAAI,CAACC,OAAO,EAAE;MACrBP,QAAQ,CAACR,YAAY,CAACY,MAAM,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC;IAC/C,CAAC,MAAM;MACHP,QAAQ,CAACV,kBAAkB,CAACc,MAAM,CAACE,IAAI,CAAC,CAAC;IAC7C;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZR,QAAQ,CAACX,QAAQ,CAACmB,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMI,eAAe,GAAGA,CAACd,EAAE,EAAEC,OAAO,KAAK,MAAOC,QAAQ,IAAK;EAChEA,QAAQ,CAACd,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMkB,MAAM,GAAG,MAAMnB,KAAK,CAACoB,GAAG,CAAE,GAAEK,OAAO,CAACC,GAAG,CAACf,kBAAmB,IAAGG,OAAQ,IAAGD,EAAG,EAAC,CAAC;IACpF,IAAIM,MAAM,CAACE,IAAI,EAAE;MACbN,QAAQ,CAACT,cAAc,CAACa,MAAM,CAACE,IAAI,CAAC,CAAC;IACzC;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZR,QAAQ,CAACX,QAAQ,CAACmB,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMK,cAAc,GAAGA,CAACf,EAAE,EAAEC,OAAO,KAAK,MAAOC,QAAQ,IAAK;EAC/DA,QAAQ,CAACd,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMkB,MAAM,GAAG,MAAMnB,KAAK,CAACoB,GAAG,CAAE,GAAEK,OAAO,CAACC,GAAG,CAACf,kBAAmB,IAAGG,OAAQ,IAAGD,EAAG,EAAC,CAAC;IACpF,IAAIM,MAAM,CAACE,IAAI,CAACC,OAAO,EAAE;MACrBP,QAAQ,CAACZ,SAAS,CAACgB,MAAM,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC;IAC5C,CAAC,MAAM;MACHP,QAAQ,CAACP,kBAAkB,CAACW,MAAM,CAACE,IAAI,CAAC,CAAC;IAC7C;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZR,QAAQ,CAACX,QAAQ,CAACmB,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMM,2BAA2B,GAAIhB,EAAE,IAAK,MAAOE,QAAQ,IAAK;EACnEA,QAAQ,CAACd,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMkB,MAAM,GAAG,MAAMnB,KAAK,CAACoB,GAAG,CAAE,GAAEK,OAAO,CAACC,GAAG,CAACf,kBAAmB,oBAAmBE,EAAG,EAAC,CAAC;IACzF,IAAIM,MAAM,CAACE,IAAI,CAACC,OAAO,EAAE;MACrBP,QAAQ,CAACZ,SAAS,CAACgB,MAAM,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC;IAC5C,CAAC,MAAM;MACHP,QAAQ,CAACP,kBAAkB,CAACW,MAAM,CAACE,IAAI,CAAC,CAAC;IAC7C;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZR,QAAQ,CAACX,QAAQ,CAACmB,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMO,iBAAiB,GAAGA,CAACjB,EAAE,EAAEC,OAAO,KAAK,MAAOC,QAAQ,IAAK;EAClEA,QAAQ,CAACL,oBAAoB,EAAE,CAAC;EAEhC,IAAI;IACA,MAAMS,MAAM,GAAG,MAAMnB,KAAK,CAACoB,GAAG,CAAE,GAAEK,OAAO,CAACC,GAAG,CAACf,kBAAmB,IAAGG,OAAQ,IAAGD,EAAG,EAAC,CAAC;IACpF,IAAIM,MAAM,CAACE,IAAI,EAAE;MACbN,QAAQ,CAACN,oBAAoB,CAACU,MAAM,CAACE,IAAI,CAAC,CAAC;IAC/C;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZR,QAAQ,CAACX,QAAQ,CAACmB,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}