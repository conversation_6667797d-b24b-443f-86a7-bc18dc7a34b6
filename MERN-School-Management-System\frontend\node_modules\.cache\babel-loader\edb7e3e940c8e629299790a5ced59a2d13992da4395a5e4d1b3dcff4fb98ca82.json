{"ast": null, "code": "function hasTarget(target, targets) {\n  return targets.has(target) && Object.keys(targets.get(target)).length > 0;\n}\nexport { hasTarget };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "target", "targets", "has", "Object", "keys", "get", "length"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/view/utils/has-target.mjs"], "sourcesContent": ["function hasTarget(target, targets) {\n    return targets.has(target) && Object.keys(targets.get(target)).length > 0;\n}\n\nexport { hasTarget };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAChC,OAAOA,OAAO,CAACC,GAAG,CAACF,MAAM,CAAC,IAAIG,MAAM,CAACC,IAAI,CAACH,OAAO,CAACI,GAAG,CAACL,MAAM,CAAC,CAAC,CAACM,MAAM,GAAG,CAAC;AAC7E;AAEA,SAASP,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}