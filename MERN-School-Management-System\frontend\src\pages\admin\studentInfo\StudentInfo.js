import React, { useState } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Avatar,
  Chip,
  Button,
  TextField,
  InputAdornment,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  Search as SearchIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Home as HomeIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  Print as PrintIcon,
  Download as DownloadIcon
} from '@mui/icons-material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
}));

const StudentCard = styled(Card)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
  },
}));

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`student-tabpanel-${index}`}
    aria-labelledby={`student-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const StudentInfo = () => {
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');

  // Sample student data
  const students = [
    {
      id: 1,
      name: 'John Doe',
      rollNumber: 'ST001',
      class: '10A',
      section: 'A',
      admissionNumber: 'ADM2024001',
      dateOfBirth: '2008-05-15',
      gender: 'Male',
      bloodGroup: 'O+',
      phone: '+1234567890',
      email: '<EMAIL>',
      address: '123 Main St, City, State',
      fatherName: 'Robert Doe',
      motherName: 'Jane Doe',
      guardianPhone: '+1234567891',
      status: 'Active',
      avatar: null
    },
    {
      id: 2,
      name: 'Alice Smith',
      rollNumber: 'ST002',
      class: '10A',
      section: 'A',
      admissionNumber: 'ADM2024002',
      dateOfBirth: '2008-08-22',
      gender: 'Female',
      bloodGroup: 'A+',
      phone: '+1234567892',
      email: '<EMAIL>',
      address: '456 Oak Ave, City, State',
      fatherName: 'Michael Smith',
      motherName: 'Sarah Smith',
      guardianPhone: '+1234567893',
      status: 'Active',
      avatar: null
    },
  ];

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.rollNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.admissionNumber.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const renderStudentCard = (student) => (
    <Grid item xs={12} sm={6} md={4} key={student.id}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <StudentCard>
          <CardContent>
            <Box display="flex" alignItems="center" mb={2}>
              <Avatar
                sx={{
                  width: 60,
                  height: 60,
                  bgcolor: 'primary.main',
                  mr: 2
                }}
              >
                {student.avatar ? (
                  <img src={student.avatar} alt={student.name} />
                ) : (
                  <PersonIcon fontSize="large" />
                )}
              </Avatar>
              <Box flex={1}>
                <Typography variant="h6" fontWeight="bold">
                  {student.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Roll: {student.rollNumber} | Class: {student.class}
                </Typography>
                <Chip
                  label={student.status}
                  color={student.status === 'Active' ? 'success' : 'default'}
                  size="small"
                  sx={{ mt: 0.5 }}
                />
              </Box>
            </Box>

            <Box mb={2}>
              <Box display="flex" alignItems="center" mb={1}>
                <SchoolIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">
                  Admission: {student.admissionNumber}
                </Typography>
              </Box>
              <Box display="flex" alignItems="center" mb={1}>
                <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">{student.phone}</Typography>
              </Box>
              <Box display="flex" alignItems="center" mb={1}>
                <EmailIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" noWrap>{student.email}</Typography>
              </Box>
            </Box>

            <Box display="flex" justifyContent="space-between">
              <Tooltip title="View Details">
                <IconButton size="small" color="primary">
                  <VisibilityIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit">
                <IconButton size="small" color="secondary">
                  <EditIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Print">
                <IconButton size="small" color="info">
                  <PrintIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Download">
                <IconButton size="small" color="success">
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </CardContent>
        </StudentCard>
      </motion.div>
    </Grid>
  );

  const renderStudentTable = () => (
    <TableContainer component={Paper} sx={{ borderRadius: '16px' }}>
      <Table>
        <TableHead>
          <TableRow sx={{ bgcolor: 'primary.main' }}>
            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Student</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Roll Number</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Class</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Contact</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Status</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {filteredStudents.map((student) => (
            <TableRow key={student.id} hover>
              <TableCell>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ mr: 2, width: 40, height: 40 }}>
                    <PersonIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" fontWeight="bold">
                      {student.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {student.admissionNumber}
                    </Typography>
                  </Box>
                </Box>
              </TableCell>
              <TableCell>{student.rollNumber}</TableCell>
              <TableCell>{student.class}</TableCell>
              <TableCell>
                <Typography variant="body2">{student.phone}</Typography>
                <Typography variant="caption" color="text.secondary">
                  {student.email}
                </Typography>
              </TableCell>
              <TableCell>
                <Chip
                  label={student.status}
                  color={student.status === 'Active' ? 'success' : 'default'}
                  size="small"
                />
              </TableCell>
              <TableCell>
                <Box display="flex" gap={1}>
                  <Tooltip title="View">
                    <IconButton size="small" color="primary">
                      <VisibilityIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Edit">
                    <IconButton size="small" color="secondary">
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box mb={4}>
          <Typography variant="h4" fontWeight="bold" color="primary" gutterBottom>
            👥 Student Information Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Comprehensive student profiles, documents, and records management
          </Typography>
        </Box>
      </motion.div>

      <StyledPaper sx={{ mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <TextField
            placeholder="Search students..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300 }}
          />
          <Box display="flex" gap={2}>
            <Button variant="contained" color="primary">
              Add New Student
            </Button>
            <Button variant="outlined" color="secondary">
              Import Students
            </Button>
            <Button variant="outlined" color="info">
              Export Data
            </Button>
          </Box>
        </Box>

        <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="Card View" />
          <Tab label="Table View" />
          <Tab label="Academic Records" />
          <Tab label="Documents" />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            {filteredStudents.map(renderStudentCard)}
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {renderStudentTable()}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Academic Records
          </Typography>
          <Typography color="text.secondary">
            Academic performance, grades, and progress tracking will be displayed here.
          </Typography>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>
            Student Documents
          </Typography>
          <Typography color="text.secondary">
            Student documents, certificates, and file management will be displayed here.
          </Typography>
        </TabPanel>
      </StyledPaper>
    </Container>
  );
};

export default StudentInfo;
