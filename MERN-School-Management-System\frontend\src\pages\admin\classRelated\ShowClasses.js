import { useEffect, useState } from 'react';
import {
  IconButton,
  Box,
  Menu,
  MenuItem,
  ListItemIcon,
  Tooltip,
  Typography,
  Card,
  CardContent,
  CardActions,
  Grid,
  Chip,
  Avatar,
  Paper,
  Container,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button
} from '@mui/material';
import DeleteIcon from "@mui/icons-material/Delete";
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { deleteUser } from '../../../redux/userRelated/userHandle';
import { getAllSclasses } from '../../../redux/sclassRelated/sclassHandle';
import { BlueButton, GreenButton } from '../../../components/buttonStyles';
import TableTemplate from '../../../components/TableTemplate';

import SpeedDialIcon from '@mui/material/SpeedDialIcon';
import PostAddIcon from '@mui/icons-material/PostAdd';
import PersonAddAlt1Icon from '@mui/icons-material/PersonAddAlt1';
import AddCardIcon from '@mui/icons-material/AddCard';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EditIcon from '@mui/icons-material/Edit';
import SchoolIcon from '@mui/icons-material/School';
import GroupIcon from '@mui/icons-material/Group';
import BookIcon from '@mui/icons-material/Book';
import AddIcon from '@mui/icons-material/Add';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import ClassIcon from '@mui/icons-material/Class';
import styled from 'styled-components';
import SpeedDialTemplate from '../../../components/SpeedDialTemplate';
import Popup from '../../../components/Popup';
import { motion } from 'framer-motion';

const ShowClasses = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch();

  const { sclassesList, loading, error, getresponse } = useSelector((state) => state.sclass);
  const { currentUser } = useSelector(state => state.user)

  const adminID = currentUser._id

  useEffect(() => {
    dispatch(getAllSclasses(adminID, "Sclass"));
  }, [adminID, dispatch]);

  if (error) {
    console.log(error)
  }

  const [showPopup, setShowPopup] = useState(false);
  const [message, setMessage] = useState("");
  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState(null);

  const deleteHandler = (deleteID, address) => {
    setDeleteTarget({ id: deleteID, address });
    setDeleteDialog(true);
  };

  const confirmDelete = () => {
    if (deleteTarget) {
      dispatch(deleteUser(deleteTarget.id, deleteTarget.address))
        .then(() => {
          dispatch(getAllSclasses(adminID, "Sclass"));
          setMessage("Class deleted successfully!");
          setShowPopup(true);
        })
        .catch(() => {
          setMessage("Error deleting class. Please try again.");
          setShowPopup(true);
        });
    }
    setDeleteDialog(false);
    setDeleteTarget(null);
  };

  const sclassColumns = [
    { id: 'name', label: 'Class Name', minWidth: 170 },
  ]

  const sclassRows = sclassesList && sclassesList.length > 0 && sclassesList.map((sclass) => {
    return {
      name: sclass.sclassName,
      id: sclass._id,
    };
  });

  // Card View Component
  const ClassCard = ({ sclass, index }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      setAnchorEl(event.currentTarget);
    };

    const handleMenuClose = () => {
      setAnchorEl(null);
    };

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.1 }}
      >
        <StyledCard>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar
                sx={{
                  bgcolor: '#667eea',
                  width: 56,
                  height: 56,
                  mr: 2
                }}
              >
                <ClassIcon sx={{ fontSize: 30 }} />
              </Avatar>
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="h5" component="h2" fontWeight="bold">
                  {sclass.sclassName}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Academic Year 2024-25
                </Typography>
              </Box>
              <IconButton onClick={handleMenuClick}>
                <MoreVertIcon />
              </IconButton>
            </Box>

            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <Chip
                icon={<GroupIcon />}
                label="0 Students"
                size="small"
                variant="outlined"
                color="primary"
              />
              <Chip
                icon={<BookIcon />}
                label="5 Subjects"
                size="small"
                variant="outlined"
                color="secondary"
              />
            </Box>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Manage students, subjects, and academic activities for this class.
            </Typography>
          </CardContent>

          <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
            <Button
              variant="contained"
              startIcon={<VisibilityIcon />}
              onClick={() => navigate("/Admin/classes/class/" + sclass._id)}
              sx={{
                bgcolor: '#667eea',
                '&:hover': { bgcolor: '#5a67d8' }
              }}
            >
              View Details
            </Button>
            <Box>
              <Tooltip title="Add Students">
                <IconButton
                  color="primary"
                  onClick={() => navigate("/Admin/class/addstudents/" + sclass._id)}
                >
                  <PersonAddAlt1Icon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Add Subjects">
                <IconButton
                  color="secondary"
                  onClick={() => navigate("/Admin/addsubject/" + sclass._id)}
                >
                  <PostAddIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Delete Class">
                <IconButton
                  color="error"
                  onClick={() => deleteHandler(sclass._id, "Sclass")}
                >
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </CardActions>

          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleMenuClose}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            <MenuItem onClick={() => {
              navigate("/Admin/classes/class/" + sclass._id);
              handleMenuClose();
            }}>
              <ListItemIcon>
                <VisibilityIcon fontSize="small" />
              </ListItemIcon>
              View Details
            </MenuItem>
            <MenuItem onClick={() => {
              navigate("/Admin/class/addstudents/" + sclass._id);
              handleMenuClose();
            }}>
              <ListItemIcon>
                <PersonAddAlt1Icon fontSize="small" />
              </ListItemIcon>
              Add Students
            </MenuItem>
            <MenuItem onClick={() => {
              navigate("/Admin/addsubject/" + sclass._id);
              handleMenuClose();
            }}>
              <ListItemIcon>
                <PostAddIcon fontSize="small" />
              </ListItemIcon>
              Add Subjects
            </MenuItem>
            <MenuItem onClick={() => {
              deleteHandler(sclass._id, "Sclass");
              handleMenuClose();
            }}>
              <ListItemIcon>
                <DeleteIcon fontSize="small" />
              </ListItemIcon>
              Delete Class
            </MenuItem>
          </Menu>
        </StyledCard>
      </motion.div>
    );
  };

  const SclassButtonHaver = ({ row }) => {
    const actions = [
      { icon: <PostAddIcon />, name: 'Add Subjects', action: () => navigate("/Admin/addsubject/" + row.id) },
      { icon: <PersonAddAlt1Icon />, name: 'Add Student', action: () => navigate("/Admin/class/addstudents/" + row.id) },
    ];
    return (
      <ButtonContainer>
        <IconButton onClick={() => deleteHandler(row.id, "Sclass")} color="secondary">
          <DeleteIcon color="error" />
        </IconButton>
        <BlueButton variant="contained"
          onClick={() => navigate("/Admin/classes/class/" + row.id)}>
          View
        </BlueButton>
        <ActionMenu actions={actions} />
      </ButtonContainer>
    );
  };

  const ActionMenu = ({ actions }) => {
    const [anchorEl, setAnchorEl] = useState(null);

    const open = Boolean(anchorEl);

    const handleClick = (event) => {
      setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
      setAnchorEl(null);
    };
    return (
      <>
        <Box sx={{ display: 'flex', alignItems: 'center', textAlign: 'center' }}>
          <Tooltip title="Add Students & Subjects">
            <IconButton
              onClick={handleClick}
              size="small"
              sx={{ ml: 2 }}
              aria-controls={open ? 'account-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={open ? 'true' : undefined}
            >
              <h5>Add</h5>
              <SpeedDialIcon />
            </IconButton>
          </Tooltip>
        </Box>
        <Menu
          anchorEl={anchorEl}
          id="account-menu"
          open={open}
          onClose={handleClose}
          onClick={handleClose}
          PaperProps={{
            elevation: 0,
            sx: styles.styledPaper,
          }}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          {actions.map((action) => (
            <MenuItem onClick={action.action}>
              <ListItemIcon fontSize="small">
                {action.icon}
              </ListItemIcon>
              {action.name}
            </MenuItem>
          ))}
        </Menu>
      </>
    );
  }

  const actions = [
    {
      icon: <AddCardIcon color="primary" />, name: 'Add New Class',
      action: () => navigate("/Admin/addclass")
    },
    {
      icon: <DeleteIcon color="error" />, name: 'Delete All Classes',
      action: () => deleteHandler(adminID, "Sclasses")
    },
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header Section */}
      <Paper elevation={3} sx={{ p: 3, mb: 4, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Class Management
            </Typography>
            <Typography variant="h6" sx={{ opacity: 0.9 }}>
              Manage all classes, students, and subjects in your school
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Chip
              icon={<SchoolIcon />}
              label={`${sclassesList?.length || 0} Classes`}
              sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate("/Admin/addclass")}
              sx={{
                bgcolor: 'rgba(255,255,255,0.2)',
                '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' }
              }}
            >
              Add New Class
            </Button>
          </Box>
        </Box>
      </Paper>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <Typography variant="h6">Loading classes...</Typography>
        </Box>
      ) : getresponse ? (
        <Paper elevation={2} sx={{ p: 6, textAlign: 'center' }}>
          <SchoolIcon sx={{ fontSize: 80, color: '#667eea', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            No Classes Found
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Start by creating your first class to organize students and subjects.
          </Typography>
          <Button
            variant="contained"
            size="large"
            startIcon={<AddIcon />}
            onClick={() => navigate("/Admin/addclass")}
            sx={{
              bgcolor: '#667eea',
              '&:hover': { bgcolor: '#5a67d8' }
            }}
          >
            Create First Class
          </Button>
        </Paper>
      ) : (
        <>
          {/* View Toggle */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" color="text.secondary">
              {sclassesList?.length} {sclassesList?.length === 1 ? 'Class' : 'Classes'} Available
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant={viewMode === 'cards' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('cards')}
                size="small"
              >
                Card View
              </Button>
              <Button
                variant={viewMode === 'table' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('table')}
                size="small"
              >
                Table View
              </Button>
            </Box>
          </Box>

          {/* Content */}
          {viewMode === 'cards' ? (
            <Grid container spacing={3}>
              {Array.isArray(sclassesList) && sclassesList.length > 0 &&
                sclassesList.map((sclass, index) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={sclass._id}>
                    <ClassCard sclass={sclass} index={index} />
                  </Grid>
                ))
              }
            </Grid>
          ) : (
            Array.isArray(sclassesList) && sclassesList.length > 0 && (
              <TableTemplate buttonHaver={SclassButtonHaver} columns={sclassColumns} rows={sclassRows} />
            )
          )}

          <SpeedDialTemplate actions={actions} />
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this class? This action will also remove all associated students and subjects.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />
    </Container>
  );
};

export default ShowClasses;

const styles = {
  styledPaper: {
    overflow: 'visible',
    filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
    mt: 1.5,
    '& .MuiAvatar-root': {
      width: 32,
      height: 32,
      ml: -0.5,
      mr: 1,
    },
    '&:before': {
      content: '""',
      display: 'block',
      position: 'absolute',
      top: 0,
      right: 14,
      width: 10,
      height: 10,
      bgcolor: 'background.paper',
      transform: 'translateY(-50%) rotate(45deg)',
      zIndex: 0,
    },
  }
}

const ButtonContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
`;

const StyledCard = styled(Card)`
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  border-radius: 16px;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;