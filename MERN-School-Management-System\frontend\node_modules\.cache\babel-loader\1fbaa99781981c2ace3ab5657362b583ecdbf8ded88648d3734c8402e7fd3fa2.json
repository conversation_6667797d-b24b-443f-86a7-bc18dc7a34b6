{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\pages\\\\LoginPage.js\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Button, Grid, Box, Typography, Paper, Checkbox, FormControlLabel, TextField, CssBaseline, IconButton, InputAdornment, CircularProgress, Backdrop } from '@mui/material';\nimport { createTheme, ThemeProvider } from '@mui/material/styles';\nimport { Visibility, VisibilityOff } from '@mui/icons-material';\nimport bgpic from \"../assets/designlogin.jpg\";\nimport { LightPurpleButton } from '../components/buttonStyles';\nimport styled from 'styled-components';\nimport { loginUser } from '../redux/userRelated/userHandle';\nimport Popup from '../components/Popup';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst defaultTheme = createTheme();\nconst LoginPage = _ref => {\n  _s();\n  let {\n    role\n  } = _ref;\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    status,\n    currentUser,\n    response,\n    error,\n    currentRole\n  } = useSelector(state => state.user);\n  ;\n  const [toggle, setToggle] = useState(false);\n  const [loader, setLoader] = useState(false);\n  const [showPopup, setShowPopup] = useState(false);\n  const [message, setMessage] = useState(\"\");\n  const [emailError, setEmailError] = useState(false);\n  const [passwordError, setPasswordError] = useState(false);\n  const [rollNumberError, setRollNumberError] = useState(false);\n  const [studentNameError, setStudentNameError] = useState(false);\n  const handleSubmit = event => {\n    event.preventDefault();\n    if (role === \"Student\") {\n      const rollNum = event.target.rollNumber.value;\n      const studentName = event.target.studentName.value;\n      const password = event.target.password.value;\n      if (!rollNum || !studentName || !password) {\n        if (!rollNum) setRollNumberError(true);\n        if (!studentName) setStudentNameError(true);\n        if (!password) setPasswordError(true);\n        return;\n      }\n      const fields = {\n        rollNum,\n        studentName,\n        password\n      };\n      setLoader(true);\n      dispatch(loginUser(fields, role));\n    } else {\n      const email = event.target.email.value;\n      const password = event.target.password.value;\n      if (!email || !password) {\n        if (!email) setEmailError(true);\n        if (!password) setPasswordError(true);\n        return;\n      }\n      const fields = {\n        email,\n        password\n      };\n      setLoader(true);\n      dispatch(loginUser(fields, role));\n    }\n  };\n  const handleInputChange = event => {\n    const {\n      name\n    } = event.target;\n    if (name === 'email') setEmailError(false);\n    if (name === 'password') setPasswordError(false);\n    if (name === 'rollNumber') setRollNumberError(false);\n    if (name === 'studentName') setStudentNameError(false);\n  };\n  useEffect(() => {\n    if (status === 'success' || currentUser !== null) {\n      if (currentRole === 'Admin') {\n        navigate('/Admin/dashboard');\n      } else if (currentRole === 'Student') {\n        navigate('/Student/dashboard');\n      } else if (currentRole === 'Teacher') {\n        navigate('/Teacher/dashboard');\n      }\n    } else if (status === 'failed') {\n      setMessage(response);\n      setShowPopup(true);\n      setLoader(false);\n    } else if (status === 'error') {\n      setMessage(\"Network Error\");\n      setShowPopup(true);\n      setLoader(false);\n    }\n  }, [status, currentRole, navigate, error, response, currentUser]);\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: defaultTheme,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      component: \"main\",\n      sx: {\n        height: '100vh'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 8,\n        md: 5,\n        component: Paper,\n        elevation: 6,\n        square: true,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            my: 8,\n            mx: 4,\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              mb: 2,\n              color: \"#2c2143\"\n            },\n            children: [role, \" Login\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h7\",\n            children: \"Welcome back! Please enter your details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            component: \"form\",\n            noValidate: true,\n            onSubmit: handleSubmit,\n            sx: {\n              mt: 2\n            },\n            children: [role === \"Student\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                margin: \"normal\",\n                required: true,\n                fullWidth: true,\n                id: \"rollNumber\",\n                label: \"Enter your Roll Number\",\n                name: \"rollNumber\",\n                autoComplete: \"off\",\n                type: \"number\",\n                autoFocus: true,\n                error: rollNumberError,\n                helperText: rollNumberError && 'Roll Number is required',\n                onChange: handleInputChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                margin: \"normal\",\n                required: true,\n                fullWidth: true,\n                id: \"studentName\",\n                label: \"Enter your name\",\n                name: \"studentName\",\n                autoComplete: \"name\",\n                autoFocus: true,\n                error: studentNameError,\n                helperText: studentNameError && 'Name is required',\n                onChange: handleInputChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"normal\",\n              required: true,\n              fullWidth: true,\n              id: \"email\",\n              label: \"Enter your email\",\n              name: \"email\",\n              autoComplete: \"email\",\n              autoFocus: true,\n              error: emailError,\n              helperText: emailError && 'Email is required',\n              onChange: handleInputChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"normal\",\n              required: true,\n              fullWidth: true,\n              name: \"password\",\n              label: \"Password\",\n              type: toggle ? 'text' : 'password',\n              id: \"password\",\n              autoComplete: \"current-password\",\n              error: passwordError,\n              helperText: passwordError && 'Password is required',\n              onChange: handleInputChange,\n              InputProps: {\n                endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => setToggle(!toggle),\n                    children: toggle ? /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 53\n                    }, this) : /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 41\n                }, this)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              sx: {\n                display: \"flex\",\n                justifyContent: \"space-between\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"remember\",\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 46\n                }, this),\n                label: \"Remember me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(StyledLink, {\n                href: \"#\",\n                children: \"Forgot password?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(LightPurpleButton, {\n              type: \"submit\",\n              fullWidth: true,\n              variant: \"contained\",\n              sx: {\n                mt: 3\n              },\n              children: loader ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 24,\n                color: \"inherit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 37\n              }, this) : \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 29\n            }, this), role === \"Admin\" && /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                children: \"Don't have an account?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                sx: {\n                  ml: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(StyledLink, {\n                  to: \"/Adminregister\",\n                  children: \"Sign up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: false,\n        sm: 4,\n        md: 7,\n        sx: {\n          backgroundImage: `url(${bgpic})`,\n          backgroundRepeat: 'no-repeat',\n          backgroundColor: t => t.palette.mode === 'light' ? t.palette.grey[50] : t.palette.grey[900],\n          backgroundSize: 'cover',\n          backgroundPosition: 'center'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Popup, {\n      message: message,\n      setShowPopup: setShowPopup,\n      showPopup: showPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 9\n  }, this);\n};\n_s(LoginPage, \"xbksb4m866SvViGroBQzZs4Yjp4=\", false, function () {\n  return [useDispatch, useNavigate, useSelector];\n});\n_c = LoginPage;\nexport default LoginPage;\nconst StyledLink = styled(Link)`\n  margin-top: 9px;\n  text-decoration: none;\n  color: #7f56da;\n`;\n_c2 = StyledLink;\nvar _c, _c2;\n$RefreshReg$(_c, \"LoginPage\");\n$RefreshReg$(_c2, \"StyledLink\");", "map": {"version": 3, "names": ["useEffect", "useState", "Link", "useNavigate", "useDispatch", "useSelector", "<PERSON><PERSON>", "Grid", "Box", "Typography", "Paper", "Checkbox", "FormControlLabel", "TextField", "CssBaseline", "IconButton", "InputAdornment", "CircularProgress", "Backdrop", "createTheme", "ThemeProvider", "Visibility", "VisibilityOff", "bgpic", "LightPurpleButton", "styled", "loginUser", "Popup", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "defaultTheme", "LoginPage", "_ref", "_s", "role", "dispatch", "navigate", "status", "currentUser", "response", "error", "currentRole", "state", "user", "toggle", "<PERSON><PERSON><PERSON><PERSON>", "loader", "<PERSON><PERSON><PERSON><PERSON>", "showPopup", "setShowPopup", "message", "setMessage", "emailError", "setEmailError", "passwordError", "setPasswordError", "rollNumberError", "setRollNumberError", "studentNameError", "setStudentNameError", "handleSubmit", "event", "preventDefault", "rollNum", "target", "rollNumber", "value", "studentName", "password", "fields", "email", "handleInputChange", "name", "theme", "children", "container", "component", "sx", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "item", "xs", "sm", "md", "elevation", "square", "my", "mx", "display", "flexDirection", "alignItems", "variant", "mb", "color", "noValidate", "onSubmit", "mt", "margin", "required", "fullWidth", "id", "label", "autoComplete", "type", "autoFocus", "helperText", "onChange", "InputProps", "endAdornment", "position", "onClick", "justifyContent", "control", "StyledLink", "href", "size", "ml", "to", "backgroundImage", "backgroundRepeat", "backgroundColor", "t", "palette", "mode", "grey", "backgroundSize", "backgroundPosition", "_c", "_c2", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/pages/LoginPage.js"], "sourcesContent": ["import { useEffect, useState } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { Button, Grid, Box, Typography, Paper, Checkbox, FormControlLabel, TextField, CssBaseline, IconButton, InputAdornment, CircularProgress, Backdrop } from '@mui/material';\r\nimport { createTheme, ThemeProvider } from '@mui/material/styles';\r\nimport { Visibility, VisibilityOff } from '@mui/icons-material';\r\nimport bgpic from \"../assets/designlogin.jpg\"\r\nimport { LightPurpleButton } from '../components/buttonStyles';\r\nimport styled from 'styled-components';\r\nimport { loginUser } from '../redux/userRelated/userHandle';\r\nimport Popup from '../components/Popup';\r\n\r\nconst defaultTheme = createTheme();\r\n\r\nconst LoginPage = ({ role }) => {\r\n\r\n    const dispatch = useDispatch()\r\n    const navigate = useNavigate()\r\n\r\n    const { status, currentUser, response, error, currentRole } = useSelector(state => state.user);;\r\n\r\n    const [toggle, setToggle] = useState(false)\r\n    const [loader, setLoader] = useState(false)\r\n    const [showPopup, setShowPopup] = useState(false);\r\n    const [message, setMessage] = useState(\"\");\r\n\r\n    const [emailError, setEmailError] = useState(false);\r\n    const [passwordError, setPasswordError] = useState(false);\r\n    const [rollNumberError, setRollNumberError] = useState(false);\r\n    const [studentNameError, setStudentNameError] = useState(false);\r\n\r\n    const handleSubmit = (event) => {\r\n        event.preventDefault();\r\n\r\n        if (role === \"Student\") {\r\n            const rollNum = event.target.rollNumber.value;\r\n            const studentName = event.target.studentName.value;\r\n            const password = event.target.password.value;\r\n\r\n            if (!rollNum || !studentName || !password) {\r\n                if (!rollNum) setRollNumberError(true);\r\n                if (!studentName) setStudentNameError(true);\r\n                if (!password) setPasswordError(true);\r\n                return;\r\n            }\r\n            const fields = { rollNum, studentName, password }\r\n            setLoader(true)\r\n            dispatch(loginUser(fields, role))\r\n        }\r\n\r\n        else {\r\n            const email = event.target.email.value;\r\n            const password = event.target.password.value;\r\n\r\n            if (!email || !password) {\r\n                if (!email) setEmailError(true);\r\n                if (!password) setPasswordError(true);\r\n                return;\r\n            }\r\n\r\n            const fields = { email, password }\r\n            setLoader(true)\r\n            dispatch(loginUser(fields, role))\r\n        }\r\n    };\r\n\r\n    const handleInputChange = (event) => {\r\n        const { name } = event.target;\r\n        if (name === 'email') setEmailError(false);\r\n        if (name === 'password') setPasswordError(false);\r\n        if (name === 'rollNumber') setRollNumberError(false);\r\n        if (name === 'studentName') setStudentNameError(false);\r\n    };\r\n\r\n\r\n\r\n    useEffect(() => {\r\n        if (status === 'success' || currentUser !== null) {\r\n            if (currentRole === 'Admin') {\r\n                navigate('/Admin/dashboard');\r\n            }\r\n            else if (currentRole === 'Student') {\r\n                navigate('/Student/dashboard');\r\n            } else if (currentRole === 'Teacher') {\r\n                navigate('/Teacher/dashboard');\r\n            }\r\n        }\r\n        else if (status === 'failed') {\r\n            setMessage(response)\r\n            setShowPopup(true)\r\n            setLoader(false)\r\n        }\r\n        else if (status === 'error') {\r\n            setMessage(\"Network Error\")\r\n            setShowPopup(true)\r\n            setLoader(false)\r\n        }\r\n    }, [status, currentRole, navigate, error, response, currentUser]);\r\n\r\n    return (\r\n        <ThemeProvider theme={defaultTheme}>\r\n            <Grid container component=\"main\" sx={{ height: '100vh' }}>\r\n                <CssBaseline />\r\n                <Grid item xs={12} sm={8} md={5} component={Paper} elevation={6} square>\r\n                    <Box\r\n                        sx={{\r\n                            my: 8,\r\n                            mx: 4,\r\n                            display: 'flex',\r\n                            flexDirection: 'column',\r\n                            alignItems: 'center',\r\n                        }}\r\n                    >\r\n                        <Typography variant=\"h4\" sx={{ mb: 2, color: \"#2c2143\" }}>\r\n                            {role} Login\r\n                        </Typography>\r\n                        <Typography variant=\"h7\">\r\n                            Welcome back! Please enter your details\r\n                        </Typography>\r\n                        <Box component=\"form\" noValidate onSubmit={handleSubmit} sx={{ mt: 2 }}>\r\n                            {role === \"Student\" ? (\r\n                                <>\r\n                                    <TextField\r\n                                        margin=\"normal\"\r\n                                        required\r\n                                        fullWidth\r\n                                        id=\"rollNumber\"\r\n                                        label=\"Enter your Roll Number\"\r\n                                        name=\"rollNumber\"\r\n                                        autoComplete=\"off\"\r\n                                        type=\"number\"\r\n                                        autoFocus\r\n                                        error={rollNumberError}\r\n                                        helperText={rollNumberError && 'Roll Number is required'}\r\n                                        onChange={handleInputChange}\r\n                                    />\r\n                                    <TextField\r\n                                        margin=\"normal\"\r\n                                        required\r\n                                        fullWidth\r\n                                        id=\"studentName\"\r\n                                        label=\"Enter your name\"\r\n                                        name=\"studentName\"\r\n                                        autoComplete=\"name\"\r\n                                        autoFocus\r\n                                        error={studentNameError}\r\n                                        helperText={studentNameError && 'Name is required'}\r\n                                        onChange={handleInputChange}\r\n                                    />\r\n                                </>\r\n                            ) : (\r\n                                <TextField\r\n                                    margin=\"normal\"\r\n                                    required\r\n                                    fullWidth\r\n                                    id=\"email\"\r\n                                    label=\"Enter your email\"\r\n                                    name=\"email\"\r\n                                    autoComplete=\"email\"\r\n                                    autoFocus\r\n                                    error={emailError}\r\n                                    helperText={emailError && 'Email is required'}\r\n                                    onChange={handleInputChange}\r\n                                />\r\n                            )}\r\n                            <TextField\r\n                                margin=\"normal\"\r\n                                required\r\n                                fullWidth\r\n                                name=\"password\"\r\n                                label=\"Password\"\r\n                                type={toggle ? 'text' : 'password'}\r\n                                id=\"password\"\r\n                                autoComplete=\"current-password\"\r\n                                error={passwordError}\r\n                                helperText={passwordError && 'Password is required'}\r\n                                onChange={handleInputChange}\r\n                                InputProps={{\r\n                                    endAdornment: (\r\n                                        <InputAdornment position=\"end\">\r\n                                            <IconButton onClick={() => setToggle(!toggle)}>\r\n                                                {toggle ? (\r\n                                                    <Visibility />\r\n                                                ) : (\r\n                                                    <VisibilityOff />\r\n                                                )}\r\n                                            </IconButton>\r\n                                        </InputAdornment>\r\n                                    ),\r\n                                }}\r\n                            />\r\n                            <Grid container sx={{ display: \"flex\", justifyContent: \"space-between\" }}>\r\n                                <FormControlLabel\r\n                                    control={<Checkbox value=\"remember\" color=\"primary\" />}\r\n                                    label=\"Remember me\"\r\n                                />\r\n                                <StyledLink href=\"#\">\r\n                                    Forgot password?\r\n                                </StyledLink>\r\n                            </Grid>\r\n                            <LightPurpleButton\r\n                                type=\"submit\"\r\n                                fullWidth\r\n                                variant=\"contained\"\r\n                                sx={{ mt: 3 }}\r\n                            >\r\n                                {loader ?\r\n                                    <CircularProgress size={24} color=\"inherit\" />\r\n                                    : \"Login\"}\r\n                            </LightPurpleButton>\r\n\r\n                            {role === \"Admin\" &&\r\n                                <Grid container>\r\n                                    <Grid>\r\n                                        Don't have an account?\r\n                                    </Grid>\r\n                                    <Grid item sx={{ ml: 2 }}>\r\n                                        <StyledLink to=\"/Adminregister\">\r\n                                            Sign up\r\n                                        </StyledLink>\r\n                                    </Grid>\r\n                                </Grid>\r\n                            }\r\n                        </Box>\r\n                    </Box>\r\n                </Grid>\r\n                <Grid\r\n                    item\r\n                    xs={false}\r\n                    sm={4}\r\n                    md={7}\r\n                    sx={{\r\n                        backgroundImage: `url(${bgpic})`,\r\n                        backgroundRepeat: 'no-repeat',\r\n                        backgroundColor: (t) =>\r\n                            t.palette.mode === 'light' ? t.palette.grey[50] : t.palette.grey[900],\r\n                        backgroundSize: 'cover',\r\n                        backgroundPosition: 'center',\r\n                    }}\r\n                />\r\n            </Grid>\r\n\r\n            <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n        </ThemeProvider>\r\n    );\r\n}\r\n\r\nexport default LoginPage\r\n\r\nconst StyledLink = styled(Link)`\r\n  margin-top: 9px;\r\n  text-decoration: none;\r\n  color: #7f56da;\r\n`;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,eAAe;AAChL,SAASC,WAAW,EAAEC,aAAa,QAAQ,sBAAsB;AACjE,SAASC,UAAU,EAAEC,aAAa,QAAQ,qBAAqB;AAC/D,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,OAAOC,KAAK,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,YAAY,GAAGb,WAAW,EAAE;AAElC,MAAMc,SAAS,GAAGC,IAAA,IAAc;EAAAC,EAAA;EAAA,IAAb;IAAEC;EAAK,CAAC,GAAAF,IAAA;EAEvB,MAAMG,QAAQ,GAAGjC,WAAW,EAAE;EAC9B,MAAMkC,QAAQ,GAAGnC,WAAW,EAAE;EAE9B,MAAM;IAAEoC,MAAM;IAAEC,WAAW;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAY,CAAC,GAAGtC,WAAW,CAACuC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EAAC;EAE/F,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC+C,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyD,eAAe,EAAEC,kBAAkB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAM6D,YAAY,GAAIC,KAAK,IAAK;IAC5BA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI5B,IAAI,KAAK,SAAS,EAAE;MACpB,MAAM6B,OAAO,GAAGF,KAAK,CAACG,MAAM,CAACC,UAAU,CAACC,KAAK;MAC7C,MAAMC,WAAW,GAAGN,KAAK,CAACG,MAAM,CAACG,WAAW,CAACD,KAAK;MAClD,MAAME,QAAQ,GAAGP,KAAK,CAACG,MAAM,CAACI,QAAQ,CAACF,KAAK;MAE5C,IAAI,CAACH,OAAO,IAAI,CAACI,WAAW,IAAI,CAACC,QAAQ,EAAE;QACvC,IAAI,CAACL,OAAO,EAAEN,kBAAkB,CAAC,IAAI,CAAC;QACtC,IAAI,CAACU,WAAW,EAAER,mBAAmB,CAAC,IAAI,CAAC;QAC3C,IAAI,CAACS,QAAQ,EAAEb,gBAAgB,CAAC,IAAI,CAAC;QACrC;MACJ;MACA,MAAMc,MAAM,GAAG;QAAEN,OAAO;QAAEI,WAAW;QAAEC;MAAS,CAAC;MACjDrB,SAAS,CAAC,IAAI,CAAC;MACfZ,QAAQ,CAACX,SAAS,CAAC6C,MAAM,EAAEnC,IAAI,CAAC,CAAC;IACrC,CAAC,MAEI;MACD,MAAMoC,KAAK,GAAGT,KAAK,CAACG,MAAM,CAACM,KAAK,CAACJ,KAAK;MACtC,MAAME,QAAQ,GAAGP,KAAK,CAACG,MAAM,CAACI,QAAQ,CAACF,KAAK;MAE5C,IAAI,CAACI,KAAK,IAAI,CAACF,QAAQ,EAAE;QACrB,IAAI,CAACE,KAAK,EAAEjB,aAAa,CAAC,IAAI,CAAC;QAC/B,IAAI,CAACe,QAAQ,EAAEb,gBAAgB,CAAC,IAAI,CAAC;QACrC;MACJ;MAEA,MAAMc,MAAM,GAAG;QAAEC,KAAK;QAAEF;MAAS,CAAC;MAClCrB,SAAS,CAAC,IAAI,CAAC;MACfZ,QAAQ,CAACX,SAAS,CAAC6C,MAAM,EAAEnC,IAAI,CAAC,CAAC;IACrC;EACJ,CAAC;EAED,MAAMqC,iBAAiB,GAAIV,KAAK,IAAK;IACjC,MAAM;MAAEW;IAAK,CAAC,GAAGX,KAAK,CAACG,MAAM;IAC7B,IAAIQ,IAAI,KAAK,OAAO,EAAEnB,aAAa,CAAC,KAAK,CAAC;IAC1C,IAAImB,IAAI,KAAK,UAAU,EAAEjB,gBAAgB,CAAC,KAAK,CAAC;IAChD,IAAIiB,IAAI,KAAK,YAAY,EAAEf,kBAAkB,CAAC,KAAK,CAAC;IACpD,IAAIe,IAAI,KAAK,aAAa,EAAEb,mBAAmB,CAAC,KAAK,CAAC;EAC1D,CAAC;EAID7D,SAAS,CAAC,MAAM;IACZ,IAAIuC,MAAM,KAAK,SAAS,IAAIC,WAAW,KAAK,IAAI,EAAE;MAC9C,IAAIG,WAAW,KAAK,OAAO,EAAE;QACzBL,QAAQ,CAAC,kBAAkB,CAAC;MAChC,CAAC,MACI,IAAIK,WAAW,KAAK,SAAS,EAAE;QAChCL,QAAQ,CAAC,oBAAoB,CAAC;MAClC,CAAC,MAAM,IAAIK,WAAW,KAAK,SAAS,EAAE;QAClCL,QAAQ,CAAC,oBAAoB,CAAC;MAClC;IACJ,CAAC,MACI,IAAIC,MAAM,KAAK,QAAQ,EAAE;MAC1Bc,UAAU,CAACZ,QAAQ,CAAC;MACpBU,YAAY,CAAC,IAAI,CAAC;MAClBF,SAAS,CAAC,KAAK,CAAC;IACpB,CAAC,MACI,IAAIV,MAAM,KAAK,OAAO,EAAE;MACzBc,UAAU,CAAC,eAAe,CAAC;MAC3BF,YAAY,CAAC,IAAI,CAAC;MAClBF,SAAS,CAAC,KAAK,CAAC;IACpB;EACJ,CAAC,EAAE,CAACV,MAAM,EAAEI,WAAW,EAAEL,QAAQ,EAAEI,KAAK,EAAED,QAAQ,EAAED,WAAW,CAAC,CAAC;EAEjE,oBACIX,OAAA,CAACT,aAAa;IAACuD,KAAK,EAAE3C,YAAa;IAAA4C,QAAA,gBAC/B/C,OAAA,CAACtB,IAAI;MAACsE,SAAS;MAACC,SAAS,EAAC,MAAM;MAACC,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAJ,QAAA,gBACrD/C,OAAA,CAACf,WAAW;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eACfvD,OAAA,CAACtB,IAAI;QAAC8E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACV,SAAS,EAAEpE,KAAM;QAAC+E,SAAS,EAAE,CAAE;QAACC,MAAM;QAAAd,QAAA,eACnE/C,OAAA,CAACrB,GAAG;UACAuE,EAAE,EAAE;YACAY,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,CAAC;YACLC,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE;UAChB,CAAE;UAAAnB,QAAA,gBAEF/C,OAAA,CAACpB,UAAU;YAACuF,OAAO,EAAC,IAAI;YAACjB,EAAE,EAAE;cAAEkB,EAAE,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAtB,QAAA,GACpDxC,IAAI,EAAC,QACV;UAAA;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAa,eACbvD,OAAA,CAACpB,UAAU;YAACuF,OAAO,EAAC,IAAI;YAAApB,QAAA,EAAC;UAEzB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAa,eACbvD,OAAA,CAACrB,GAAG;YAACsE,SAAS,EAAC,MAAM;YAACqB,UAAU;YAACC,QAAQ,EAAEtC,YAAa;YAACiB,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE,CAAE;YAAAzB,QAAA,GAClExC,IAAI,KAAK,SAAS,gBACfP,OAAA,CAAAE,SAAA;cAAA6C,QAAA,gBACI/C,OAAA,CAAChB,SAAS;gBACNyF,MAAM,EAAC,QAAQ;gBACfC,QAAQ;gBACRC,SAAS;gBACTC,EAAE,EAAC,YAAY;gBACfC,KAAK,EAAC,wBAAwB;gBAC9BhC,IAAI,EAAC,YAAY;gBACjBiC,YAAY,EAAC,KAAK;gBAClBC,IAAI,EAAC,QAAQ;gBACbC,SAAS;gBACTnE,KAAK,EAAEgB,eAAgB;gBACvBoD,UAAU,EAAEpD,eAAe,IAAI,yBAA0B;gBACzDqD,QAAQ,EAAEtC;cAAkB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAC9B,eACFvD,OAAA,CAAChB,SAAS;gBACNyF,MAAM,EAAC,QAAQ;gBACfC,QAAQ;gBACRC,SAAS;gBACTC,EAAE,EAAC,aAAa;gBAChBC,KAAK,EAAC,iBAAiB;gBACvBhC,IAAI,EAAC,aAAa;gBAClBiC,YAAY,EAAC,MAAM;gBACnBE,SAAS;gBACTnE,KAAK,EAAEkB,gBAAiB;gBACxBkD,UAAU,EAAElD,gBAAgB,IAAI,kBAAmB;gBACnDmD,QAAQ,EAAEtC;cAAkB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAC9B;YAAA,gBACH,gBAEHvD,OAAA,CAAChB,SAAS;cACNyF,MAAM,EAAC,QAAQ;cACfC,QAAQ;cACRC,SAAS;cACTC,EAAE,EAAC,OAAO;cACVC,KAAK,EAAC,kBAAkB;cACxBhC,IAAI,EAAC,OAAO;cACZiC,YAAY,EAAC,OAAO;cACpBE,SAAS;cACTnE,KAAK,EAAEY,UAAW;cAClBwD,UAAU,EAAExD,UAAU,IAAI,mBAAoB;cAC9CyD,QAAQ,EAAEtC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAEnC,eACDvD,OAAA,CAAChB,SAAS;cACNyF,MAAM,EAAC,QAAQ;cACfC,QAAQ;cACRC,SAAS;cACT9B,IAAI,EAAC,UAAU;cACfgC,KAAK,EAAC,UAAU;cAChBE,IAAI,EAAE9D,MAAM,GAAG,MAAM,GAAG,UAAW;cACnC2D,EAAE,EAAC,UAAU;cACbE,YAAY,EAAC,kBAAkB;cAC/BjE,KAAK,EAAEc,aAAc;cACrBsD,UAAU,EAAEtD,aAAa,IAAI,sBAAuB;cACpDuD,QAAQ,EAAEtC,iBAAkB;cAC5BuC,UAAU,EAAE;gBACRC,YAAY,eACRpF,OAAA,CAACb,cAAc;kBAACkG,QAAQ,EAAC,KAAK;kBAAAtC,QAAA,eAC1B/C,OAAA,CAACd,UAAU;oBAACoG,OAAO,EAAEA,CAAA,KAAMpE,SAAS,CAAC,CAACD,MAAM,CAAE;oBAAA8B,QAAA,EACzC9B,MAAM,gBACHjB,OAAA,CAACR,UAAU;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,gBAEdvD,OAAA,CAACP,aAAa;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBACjB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACQ;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAGzB;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACJ,eACFvD,OAAA,CAACtB,IAAI;cAACsE,SAAS;cAACE,EAAE,EAAE;gBAAEc,OAAO,EAAE,MAAM;gBAAEuB,cAAc,EAAE;cAAgB,CAAE;cAAAxC,QAAA,gBACrE/C,OAAA,CAACjB,gBAAgB;gBACbyG,OAAO,eAAExF,OAAA,CAAClB,QAAQ;kBAACyD,KAAK,EAAC,UAAU;kBAAC8B,KAAK,EAAC;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAI;gBACvDsB,KAAK,EAAC;cAAa;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACrB,eACFvD,OAAA,CAACyF,UAAU;gBAACC,IAAI,EAAC,GAAG;gBAAA3C,QAAA,EAAC;cAErB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAa;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACV,eACPvD,OAAA,CAACL,iBAAiB;cACdoF,IAAI,EAAC,QAAQ;cACbJ,SAAS;cACTR,OAAO,EAAC,WAAW;cACnBjB,EAAE,EAAE;gBAAEsB,EAAE,EAAE;cAAE,CAAE;cAAAzB,QAAA,EAEb5B,MAAM,gBACHnB,OAAA,CAACZ,gBAAgB;gBAACuG,IAAI,EAAE,EAAG;gBAACtB,KAAK,EAAC;cAAS;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAG,GAC5C;YAAO;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACG,EAEnBhD,IAAI,KAAK,OAAO,iBACbP,OAAA,CAACtB,IAAI;cAACsE,SAAS;cAAAD,QAAA,gBACX/C,OAAA,CAACtB,IAAI;gBAAAqE,QAAA,EAAC;cAEN;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAO,eACPvD,OAAA,CAACtB,IAAI;gBAAC8E,IAAI;gBAACN,EAAE,EAAE;kBAAE0C,EAAE,EAAE;gBAAE,CAAE;gBAAA7C,QAAA,eACrB/C,OAAA,CAACyF,UAAU;kBAACI,EAAE,EAAC,gBAAgB;kBAAA9C,QAAA,EAAC;gBAEhC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAa;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAET;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACJ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACH,eACPvD,OAAA,CAACtB,IAAI;QACD8E,IAAI;QACJC,EAAE,EAAE,KAAM;QACVC,EAAE,EAAE,CAAE;QACNC,EAAE,EAAE,CAAE;QACNT,EAAE,EAAE;UACA4C,eAAe,EAAG,OAAMpG,KAAM,GAAE;UAChCqG,gBAAgB,EAAE,WAAW;UAC7BC,eAAe,EAAGC,CAAC,IACfA,CAAC,CAACC,OAAO,CAACC,IAAI,KAAK,OAAO,GAAGF,CAAC,CAACC,OAAO,CAACE,IAAI,CAAC,EAAE,CAAC,GAAGH,CAAC,CAACC,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;UACzEC,cAAc,EAAE,OAAO;UACvBC,kBAAkB,EAAE;QACxB;MAAE;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACJ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACC,eAEPvD,OAAA,CAACF,KAAK;MAACyB,OAAO,EAAEA,OAAQ;MAACD,YAAY,EAAEA,YAAa;MAACD,SAAS,EAAEA;IAAU;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACjE;AAExB,CAAC;AAAAjD,EAAA,CAvOKF,SAAS;EAAA,QAEM7B,WAAW,EACXD,WAAW,EAEkCE,WAAW;AAAA;AAAA+H,EAAA,GALvEnG,SAAS;AAyOf,eAAeA,SAAS;AAExB,MAAMqF,UAAU,GAAG7F,MAAM,CAACvB,IAAI,CAAE;AAChC;AACA;AACA;AACA,CAAC;AAACmI,GAAA,GAJIf,UAAU;AAAA,IAAAc,EAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAF,EAAA;AAAAE,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}