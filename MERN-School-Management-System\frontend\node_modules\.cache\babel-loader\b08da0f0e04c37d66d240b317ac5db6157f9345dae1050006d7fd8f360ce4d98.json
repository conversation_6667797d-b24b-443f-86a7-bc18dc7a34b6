{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\pages\\\\admin\\\\classRelated\\\\AddClass.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Button, CircularProgress, Stack, TextField, Typography, Paper, Container, Card, CardContent, Grid, Chip, Avatar } from \"@mui/material\";\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { addStuff } from '../../../redux/userRelated/userHandle';\nimport { underControl } from '../../../redux/userRelated/userSlice';\nimport Popup from \"../../../components/Popup\";\nimport Classroom from \"../../../assets/classroom.png\";\nimport styled from \"styled-components\";\nimport ClassIcon from '@mui/icons-material/Class';\nimport SchoolIcon from '@mui/icons-material/School';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddClass = () => {\n  _s();\n  const [sclassName, setSclassName] = useState(\"\");\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const userState = useSelector(state => state.user);\n  const {\n    status,\n    currentUser,\n    response,\n    error,\n    tempDetails\n  } = userState;\n  const adminID = currentUser._id;\n  const address = \"Sclass\";\n  const [loader, setLoader] = useState(false);\n  const [message, setMessage] = useState(\"\");\n  const [showPopup, setShowPopup] = useState(false);\n  const fields = {\n    sclassName,\n    adminID\n  };\n  const submitHandler = event => {\n    event.preventDefault();\n    setLoader(true);\n    dispatch(addStuff(fields, address));\n  };\n  useEffect(() => {\n    if (status === 'added' && tempDetails) {\n      navigate(\"/Admin/classes/class/\" + tempDetails._id);\n      dispatch(underControl());\n      setLoader(false);\n    } else if (status === 'failed') {\n      setMessage(response);\n      setShowPopup(true);\n      setLoader(false);\n    } else if (status === 'error') {\n      setMessage(\"Network Error\");\n      setShowPopup(true);\n      setLoader(false);\n    }\n  }, [status, navigate, error, response, dispatch, tempDetails]);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 3,\n        mb: 4,\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        color: 'white'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 36\n          }, this),\n          onClick: () => navigate(-1),\n          sx: {\n            color: 'white',\n            '&:hover': {\n              bgcolor: 'rgba(255,255,255,0.1)'\n            }\n          },\n          children: \"Back to Classes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexGrow: 1,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h1\",\n            fontWeight: \"bold\",\n            children: \"Create New Class\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              opacity: 0.9\n            },\n            children: \"Add a new class to organize students and subjects\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          sx: {\n            height: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: '#667eea',\n                  mr: 2,\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(ClassIcon, {\n                  sx: {\n                    fontSize: 30\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  fontWeight: \"bold\",\n                  children: \"Class Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Enter the details for the new class\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: submitHandler,\n              children: /*#__PURE__*/_jsxDEV(Stack, {\n                spacing: 3,\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  label: \"Class Name\",\n                  placeholder: \"e.g., Class 1, Grade 5, 10th Standard\",\n                  variant: \"outlined\",\n                  value: sclassName,\n                  onChange: event => {\n                    setSclassName(event.target.value);\n                  },\n                  required: true,\n                  fullWidth: true,\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      '&:hover fieldset': {\n                        borderColor: '#667eea'\n                      },\n                      '&.Mui-focused fieldset': {\n                        borderColor: '#667eea'\n                      }\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    fullWidth: true,\n                    size: \"large\",\n                    variant: \"contained\",\n                    type: \"submit\",\n                    disabled: loader,\n                    sx: {\n                      bgcolor: '#667eea',\n                      '&:hover': {\n                        bgcolor: '#5a67d8'\n                      },\n                      py: 1.5\n                    },\n                    children: loader ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                      size: 24,\n                      color: \"inherit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 151,\n                      columnNumber: 55\n                    }, this) : \"Create Class\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    onClick: () => navigate(-1),\n                    size: \"large\",\n                    sx: {\n                      py: 1.5\n                    },\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            elevation: 2,\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                textAlign: 'center',\n                p: 4\n              },\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: Classroom,\n                alt: \"classroom\",\n                style: {\n                  width: '100%',\n                  maxWidth: '300px',\n                  height: 'auto'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 2,\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                gutterBottom: true,\n                children: \"\\uD83D\\uDCA1 Tips for Creating Classes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"\\u2022 Use clear naming conventions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: \"Examples: \\\"Class 1\\\", \\\"Grade 5\\\", \\\"10th Standard\\\"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"\\u2022 Consider academic levels\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: \"Group students by age and academic progress\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"\\u2022 Plan for growth\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: \"Create classes that can accommodate future students\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 2,\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                gutterBottom: true,\n                children: \"\\uD83D\\uDCCA After Creating This Class\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Chip, {\n                  icon: /*#__PURE__*/_jsxDEV(SchoolIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 47\n                  }, this),\n                  label: \"Add Students\",\n                  variant: \"outlined\",\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  icon: /*#__PURE__*/_jsxDEV(ClassIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 47\n                  }, this),\n                  label: \"Add Subjects\",\n                  variant: \"outlined\",\n                  color: \"secondary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mt: 2\n                },\n                children: \"You'll be able to add students and subjects to this class immediately after creation.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Popup, {\n      message: message,\n      setShowPopup: setShowPopup,\n      showPopup: showPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 9\n  }, this);\n};\n_s(AddClass, \"IL9v/YSOx/tg8MCx2zUIGVp6+2U=\", false, function () {\n  return [useDispatch, useNavigate, useSelector];\n});\n_c = AddClass;\nexport default AddClass;\nvar _c;\n$RefreshReg$(_c, \"AddClass\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "TextField", "Typography", "Paper", "Container", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "Chip", "Avatar", "useNavigate", "useDispatch", "useSelector", "addStuff", "underControl", "Popup", "Classroom", "styled", "ClassIcon", "SchoolIcon", "ArrowBackIcon", "jsxDEV", "_jsxDEV", "AddClass", "_s", "sclassName", "setSclassName", "dispatch", "navigate", "userState", "state", "user", "status", "currentUser", "response", "error", "tempDetails", "adminID", "_id", "address", "loader", "<PERSON><PERSON><PERSON><PERSON>", "message", "setMessage", "showPopup", "setShowPopup", "fields", "<PERSON><PERSON><PERSON><PERSON>", "event", "preventDefault", "max<PERSON><PERSON><PERSON>", "sx", "py", "children", "elevation", "p", "mb", "background", "color", "display", "alignItems", "gap", "startIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "bgcolor", "flexGrow", "textAlign", "variant", "component", "fontWeight", "opacity", "container", "spacing", "item", "xs", "md", "height", "mr", "width", "fontSize", "onSubmit", "label", "placeholder", "value", "onChange", "target", "required", "fullWidth", "borderColor", "size", "type", "disabled", "src", "alt", "style", "gutterBottom", "direction", "icon", "mt", "_c", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/pages/admin/classRelated/AddClass.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n  Box,\r\n  Button,\r\n  CircularProgress,\r\n  Stack,\r\n  TextField,\r\n  Typography,\r\n  Paper,\r\n  Container,\r\n  Card,\r\n  CardContent,\r\n  Grid,\r\n  Chip,\r\n  Avatar\r\n} from \"@mui/material\";\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { addStuff } from '../../../redux/userRelated/userHandle';\r\nimport { underControl } from '../../../redux/userRelated/userSlice';\r\nimport Popup from \"../../../components/Popup\";\r\nimport Classroom from \"../../../assets/classroom.png\";\r\nimport styled from \"styled-components\";\r\nimport ClassIcon from '@mui/icons-material/Class';\r\nimport SchoolIcon from '@mui/icons-material/School';\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\n\r\nconst AddClass = () => {\r\n    const [sclassName, setSclassName] = useState(\"\");\r\n\r\n    const dispatch = useDispatch()\r\n    const navigate = useNavigate()\r\n\r\n    const userState = useSelector(state => state.user);\r\n    const { status, currentUser, response, error, tempDetails } = userState;\r\n\r\n    const adminID = currentUser._id\r\n    const address = \"Sclass\"\r\n\r\n    const [loader, setLoader] = useState(false)\r\n    const [message, setMessage] = useState(\"\");\r\n    const [showPopup, setShowPopup] = useState(false);\r\n\r\n    const fields = {\r\n        sclassName,\r\n        adminID,\r\n    };\r\n\r\n    const submitHandler = (event) => {\r\n        event.preventDefault()\r\n        setLoader(true)\r\n        dispatch(addStuff(fields, address))\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (status === 'added' && tempDetails) {\r\n            navigate(\"/Admin/classes/class/\" + tempDetails._id)\r\n            dispatch(underControl())\r\n            setLoader(false)\r\n        }\r\n        else if (status === 'failed') {\r\n            setMessage(response)\r\n            setShowPopup(true)\r\n            setLoader(false)\r\n        }\r\n        else if (status === 'error') {\r\n            setMessage(\"Network Error\")\r\n            setShowPopup(true)\r\n            setLoader(false)\r\n        }\r\n    }, [status, navigate, error, response, dispatch, tempDetails]);\r\n    return (\r\n        <Container maxWidth=\"lg\" sx={{ py: 4 }}>\r\n            {/* Header */}\r\n            <Paper elevation={3} sx={{ p: 3, mb: 4, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Button\r\n                        startIcon={<ArrowBackIcon />}\r\n                        onClick={() => navigate(-1)}\r\n                        sx={{ color: 'white', '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' } }}\r\n                    >\r\n                        Back to Classes\r\n                    </Button>\r\n                    <Box sx={{ flexGrow: 1, textAlign: 'center' }}>\r\n                        <Typography variant=\"h4\" component=\"h1\" fontWeight=\"bold\">\r\n                            Create New Class\r\n                        </Typography>\r\n                        <Typography variant=\"h6\" sx={{ opacity: 0.9 }}>\r\n                            Add a new class to organize students and subjects\r\n                        </Typography>\r\n                    </Box>\r\n                </Box>\r\n            </Paper>\r\n\r\n            <Grid container spacing={4}>\r\n                {/* Form Section */}\r\n                <Grid item xs={12} md={6}>\r\n                    <Card elevation={3} sx={{ height: '100%' }}>\r\n                        <CardContent sx={{ p: 4 }}>\r\n                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\r\n                                <Avatar sx={{ bgcolor: '#667eea', mr: 2, width: 56, height: 56 }}>\r\n                                    <ClassIcon sx={{ fontSize: 30 }} />\r\n                                </Avatar>\r\n                                <Box>\r\n                                    <Typography variant=\"h5\" fontWeight=\"bold\">\r\n                                        Class Information\r\n                                    </Typography>\r\n                                    <Typography variant=\"body2\" color=\"text.secondary\">\r\n                                        Enter the details for the new class\r\n                                    </Typography>\r\n                                </Box>\r\n                            </Box>\r\n\r\n                            <form onSubmit={submitHandler}>\r\n                                <Stack spacing={3}>\r\n                                    <TextField\r\n                                        label=\"Class Name\"\r\n                                        placeholder=\"e.g., Class 1, Grade 5, 10th Standard\"\r\n                                        variant=\"outlined\"\r\n                                        value={sclassName}\r\n                                        onChange={(event) => {\r\n                                            setSclassName(event.target.value);\r\n                                        }}\r\n                                        required\r\n                                        fullWidth\r\n                                        sx={{\r\n                                            '& .MuiOutlinedInput-root': {\r\n                                                '&:hover fieldset': {\r\n                                                    borderColor: '#667eea',\r\n                                                },\r\n                                                '&.Mui-focused fieldset': {\r\n                                                    borderColor: '#667eea',\r\n                                                },\r\n                                            },\r\n                                        }}\r\n                                    />\r\n\r\n                                    <Box sx={{ display: 'flex', gap: 2 }}>\r\n                                        <Button\r\n                                            fullWidth\r\n                                            size=\"large\"\r\n                                            variant=\"contained\"\r\n                                            type=\"submit\"\r\n                                            disabled={loader}\r\n                                            sx={{\r\n                                                bgcolor: '#667eea',\r\n                                                '&:hover': { bgcolor: '#5a67d8' },\r\n                                                py: 1.5\r\n                                            }}\r\n                                        >\r\n                                            {loader ? <CircularProgress size={24} color=\"inherit\" /> : \"Create Class\"}\r\n                                        </Button>\r\n                                        <Button\r\n                                            variant=\"outlined\"\r\n                                            onClick={() => navigate(-1)}\r\n                                            size=\"large\"\r\n                                            sx={{ py: 1.5 }}\r\n                                        >\r\n                                            Cancel\r\n                                        </Button>\r\n                                    </Box>\r\n                                </Stack>\r\n                            </form>\r\n                        </CardContent>\r\n                    </Card>\r\n                </Grid>\r\n\r\n                {/* Info Section */}\r\n                <Grid item xs={12} md={6}>\r\n                    <Stack spacing={3}>\r\n                        {/* Illustration */}\r\n                        <Card elevation={2}>\r\n                            <CardContent sx={{ textAlign: 'center', p: 4 }}>\r\n                                <img\r\n                                    src={Classroom}\r\n                                    alt=\"classroom\"\r\n                                    style={{ width: '100%', maxWidth: '300px', height: 'auto' }}\r\n                                />\r\n                            </CardContent>\r\n                        </Card>\r\n\r\n                        {/* Tips */}\r\n                        <Card elevation={2}>\r\n                            <CardContent>\r\n                                <Typography variant=\"h6\" fontWeight=\"bold\" gutterBottom>\r\n                                    💡 Tips for Creating Classes\r\n                                </Typography>\r\n                                <Stack spacing={2}>\r\n                                    <Box>\r\n                                        <Typography variant=\"body2\" fontWeight=\"bold\">\r\n                                            • Use clear naming conventions\r\n                                        </Typography>\r\n                                        <Typography variant=\"body2\" color=\"text.secondary\">\r\n                                            Examples: \"Class 1\", \"Grade 5\", \"10th Standard\"\r\n                                        </Typography>\r\n                                    </Box>\r\n                                    <Box>\r\n                                        <Typography variant=\"body2\" fontWeight=\"bold\">\r\n                                            • Consider academic levels\r\n                                        </Typography>\r\n                                        <Typography variant=\"body2\" color=\"text.secondary\">\r\n                                            Group students by age and academic progress\r\n                                        </Typography>\r\n                                    </Box>\r\n                                    <Box>\r\n                                        <Typography variant=\"body2\" fontWeight=\"bold\">\r\n                                            • Plan for growth\r\n                                        </Typography>\r\n                                        <Typography variant=\"body2\" color=\"text.secondary\">\r\n                                            Create classes that can accommodate future students\r\n                                        </Typography>\r\n                                    </Box>\r\n                                </Stack>\r\n                            </CardContent>\r\n                        </Card>\r\n\r\n                        {/* Quick Stats */}\r\n                        <Card elevation={2}>\r\n                            <CardContent>\r\n                                <Typography variant=\"h6\" fontWeight=\"bold\" gutterBottom>\r\n                                    📊 After Creating This Class\r\n                                </Typography>\r\n                                <Stack direction=\"row\" spacing={2}>\r\n                                    <Chip\r\n                                        icon={<SchoolIcon />}\r\n                                        label=\"Add Students\"\r\n                                        variant=\"outlined\"\r\n                                        color=\"primary\"\r\n                                    />\r\n                                    <Chip\r\n                                        icon={<ClassIcon />}\r\n                                        label=\"Add Subjects\"\r\n                                        variant=\"outlined\"\r\n                                        color=\"secondary\"\r\n                                    />\r\n                                </Stack>\r\n                                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 2 }}>\r\n                                    You'll be able to add students and subjects to this class immediately after creation.\r\n                                </Typography>\r\n                            </CardContent>\r\n                        </Card>\r\n                    </Stack>\r\n                </Grid>\r\n            </Grid>\r\n\r\n            <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n        </Container>\r\n    )\r\n}\r\n\r\nexport default AddClass"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,MAAM,EACNC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,IAAI,EACJC,MAAM,QACD,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,QAAQ,uCAAuC;AAChE,SAASC,YAAY,QAAQ,sCAAsC;AACnE,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,OAAOC,SAAS,MAAM,+BAA+B;AACrD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,aAAa,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM+B,QAAQ,GAAGhB,WAAW,EAAE;EAC9B,MAAMiB,QAAQ,GAAGlB,WAAW,EAAE;EAE9B,MAAMmB,SAAS,GAAGjB,WAAW,CAACkB,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EAClD,MAAM;IAAEC,MAAM;IAAEC,WAAW;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAY,CAAC,GAAGP,SAAS;EAEvE,MAAMQ,OAAO,GAAGJ,WAAW,CAACK,GAAG;EAC/B,MAAMC,OAAO,GAAG,QAAQ;EAExB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMkD,MAAM,GAAG;IACXrB,UAAU;IACVY;EACJ,CAAC;EAED,MAAMU,aAAa,GAAIC,KAAK,IAAK;IAC7BA,KAAK,CAACC,cAAc,EAAE;IACtBR,SAAS,CAAC,IAAI,CAAC;IACfd,QAAQ,CAACd,QAAQ,CAACiC,MAAM,EAAEP,OAAO,CAAC,CAAC;EACvC,CAAC;EAED5C,SAAS,CAAC,MAAM;IACZ,IAAIqC,MAAM,KAAK,OAAO,IAAII,WAAW,EAAE;MACnCR,QAAQ,CAAC,uBAAuB,GAAGQ,WAAW,CAACE,GAAG,CAAC;MACnDX,QAAQ,CAACb,YAAY,EAAE,CAAC;MACxB2B,SAAS,CAAC,KAAK,CAAC;IACpB,CAAC,MACI,IAAIT,MAAM,KAAK,QAAQ,EAAE;MAC1BW,UAAU,CAACT,QAAQ,CAAC;MACpBW,YAAY,CAAC,IAAI,CAAC;MAClBJ,SAAS,CAAC,KAAK,CAAC;IACpB,CAAC,MACI,IAAIT,MAAM,KAAK,OAAO,EAAE;MACzBW,UAAU,CAAC,eAAe,CAAC;MAC3BE,YAAY,CAAC,IAAI,CAAC;MAClBJ,SAAS,CAAC,KAAK,CAAC;IACpB;EACJ,CAAC,EAAE,CAACT,MAAM,EAAEJ,QAAQ,EAAEO,KAAK,EAAED,QAAQ,EAAEP,QAAQ,EAAES,WAAW,CAAC,CAAC;EAC9D,oBACId,OAAA,CAAClB,SAAS;IAAC8C,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAEnC/B,OAAA,CAACnB,KAAK;MAACmD,SAAS,EAAE,CAAE;MAACH,EAAE,EAAE;QAAEI,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,UAAU,EAAE,mDAAmD;QAAEC,KAAK,EAAE;MAAQ,CAAE;MAAAL,QAAA,eACtH/B,OAAA,CAACzB,GAAG;QAACsD,EAAE,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACvD/B,OAAA,CAACxB,MAAM;UACHgE,SAAS,eAAExC,OAAA,CAACF,aAAa;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAI;UAC7BC,OAAO,EAAEA,CAAA,KAAMvC,QAAQ,CAAC,CAAC,CAAC,CAAE;UAC5BuB,EAAE,EAAE;YAAEO,KAAK,EAAE,OAAO;YAAE,SAAS,EAAE;cAAEU,OAAO,EAAE;YAAwB;UAAE,CAAE;UAAAf,QAAA,EAC3E;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS,eACT5C,OAAA,CAACzB,GAAG;UAACsD,EAAE,EAAE;YAAEkB,QAAQ,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAjB,QAAA,gBAC1C/B,OAAA,CAACpB,UAAU;YAACqE,OAAO,EAAC,IAAI;YAACC,SAAS,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAApB,QAAA,EAAC;UAE1D;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAa,eACb5C,OAAA,CAACpB,UAAU;YAACqE,OAAO,EAAC,IAAI;YAACpB,EAAE,EAAE;cAAEuB,OAAO,EAAE;YAAI,CAAE;YAAArB,QAAA,EAAC;UAE/C;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAa;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACJ;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF,eAER5C,OAAA,CAACf,IAAI;MAACoE,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAvB,QAAA,gBAEvB/B,OAAA,CAACf,IAAI;QAACsE,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACrB/B,OAAA,CAACjB,IAAI;UAACiD,SAAS,EAAE,CAAE;UAACH,EAAE,EAAE;YAAE6B,MAAM,EAAE;UAAO,CAAE;UAAA3B,QAAA,eACvC/B,OAAA,CAAChB,WAAW;YAAC6C,EAAE,EAAE;cAAEI,CAAC,EAAE;YAAE,CAAE;YAAAF,QAAA,gBACtB/B,OAAA,CAACzB,GAAG;cAACsD,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACtD/B,OAAA,CAACb,MAAM;gBAAC0C,EAAE,EAAE;kBAAEiB,OAAO,EAAE,SAAS;kBAAEa,EAAE,EAAE,CAAC;kBAAEC,KAAK,EAAE,EAAE;kBAAEF,MAAM,EAAE;gBAAG,CAAE;gBAAA3B,QAAA,eAC7D/B,OAAA,CAACJ,SAAS;kBAACiC,EAAE,EAAE;oBAAEgC,QAAQ,EAAE;kBAAG;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAC9B,eACT5C,OAAA,CAACzB,GAAG;gBAAAwD,QAAA,gBACA/B,OAAA,CAACpB,UAAU;kBAACqE,OAAO,EAAC,IAAI;kBAACE,UAAU,EAAC,MAAM;kBAAApB,QAAA,EAAC;gBAE3C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAa,eACb5C,OAAA,CAACpB,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAACb,KAAK,EAAC,gBAAgB;kBAAAL,QAAA,EAAC;gBAEnD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAa;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACJ,eAEN5C,OAAA;cAAM8D,QAAQ,EAAErC,aAAc;cAAAM,QAAA,eAC1B/B,OAAA,CAACtB,KAAK;gBAAC4E,OAAO,EAAE,CAAE;gBAAAvB,QAAA,gBACd/B,OAAA,CAACrB,SAAS;kBACNoF,KAAK,EAAC,YAAY;kBAClBC,WAAW,EAAC,uCAAuC;kBACnDf,OAAO,EAAC,UAAU;kBAClBgB,KAAK,EAAE9D,UAAW;kBAClB+D,QAAQ,EAAGxC,KAAK,IAAK;oBACjBtB,aAAa,CAACsB,KAAK,CAACyC,MAAM,CAACF,KAAK,CAAC;kBACrC,CAAE;kBACFG,QAAQ;kBACRC,SAAS;kBACTxC,EAAE,EAAE;oBACA,0BAA0B,EAAE;sBACxB,kBAAkB,EAAE;wBAChByC,WAAW,EAAE;sBACjB,CAAC;sBACD,wBAAwB,EAAE;wBACtBA,WAAW,EAAE;sBACjB;oBACJ;kBACJ;gBAAE;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACJ,eAEF5C,OAAA,CAACzB,GAAG;kBAACsD,EAAE,EAAE;oBAAEQ,OAAO,EAAE,MAAM;oBAAEE,GAAG,EAAE;kBAAE,CAAE;kBAAAR,QAAA,gBACjC/B,OAAA,CAACxB,MAAM;oBACH6F,SAAS;oBACTE,IAAI,EAAC,OAAO;oBACZtB,OAAO,EAAC,WAAW;oBACnBuB,IAAI,EAAC,QAAQ;oBACbC,QAAQ,EAAEvD,MAAO;oBACjBW,EAAE,EAAE;sBACAiB,OAAO,EAAE,SAAS;sBAClB,SAAS,EAAE;wBAAEA,OAAO,EAAE;sBAAU,CAAC;sBACjChB,EAAE,EAAE;oBACR,CAAE;oBAAAC,QAAA,EAEDb,MAAM,gBAAGlB,OAAA,CAACvB,gBAAgB;sBAAC8F,IAAI,EAAE,EAAG;sBAACnC,KAAK,EAAC;oBAAS;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,GAAG;kBAAc;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACpE,eACT5C,OAAA,CAACxB,MAAM;oBACHyE,OAAO,EAAC,UAAU;oBAClBJ,OAAO,EAAEA,CAAA,KAAMvC,QAAQ,CAAC,CAAC,CAAC,CAAE;oBAC5BiE,IAAI,EAAC,OAAO;oBACZ1C,EAAE,EAAE;sBAAEC,EAAE,EAAE;oBAAI,CAAE;oBAAAC,QAAA,EACnB;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAS;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACG;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACJ,eAGP5C,OAAA,CAACf,IAAI;QAACsE,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACrB/B,OAAA,CAACtB,KAAK;UAAC4E,OAAO,EAAE,CAAE;UAAAvB,QAAA,gBAEd/B,OAAA,CAACjB,IAAI;YAACiD,SAAS,EAAE,CAAE;YAAAD,QAAA,eACf/B,OAAA,CAAChB,WAAW;cAAC6C,EAAE,EAAE;gBAAEmB,SAAS,EAAE,QAAQ;gBAAEf,CAAC,EAAE;cAAE,CAAE;cAAAF,QAAA,eAC3C/B,OAAA;gBACI0E,GAAG,EAAEhF,SAAU;gBACfiF,GAAG,EAAC,WAAW;gBACfC,KAAK,EAAE;kBAAEhB,KAAK,EAAE,MAAM;kBAAEhC,QAAQ,EAAE,OAAO;kBAAE8B,MAAM,EAAE;gBAAO;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAC9D;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACX,eAGP5C,OAAA,CAACjB,IAAI;YAACiD,SAAS,EAAE,CAAE;YAAAD,QAAA,eACf/B,OAAA,CAAChB,WAAW;cAAA+C,QAAA,gBACR/B,OAAA,CAACpB,UAAU;gBAACqE,OAAO,EAAC,IAAI;gBAACE,UAAU,EAAC,MAAM;gBAAC0B,YAAY;gBAAA9C,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAa,eACb5C,OAAA,CAACtB,KAAK;gBAAC4E,OAAO,EAAE,CAAE;gBAAAvB,QAAA,gBACd/B,OAAA,CAACzB,GAAG;kBAAAwD,QAAA,gBACA/B,OAAA,CAACpB,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAACE,UAAU,EAAC,MAAM;oBAAApB,QAAA,EAAC;kBAE9C;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAa,eACb5C,OAAA,CAACpB,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAACb,KAAK,EAAC,gBAAgB;oBAAAL,QAAA,EAAC;kBAEnD;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAa;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACX,eACN5C,OAAA,CAACzB,GAAG;kBAAAwD,QAAA,gBACA/B,OAAA,CAACpB,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAACE,UAAU,EAAC,MAAM;oBAAApB,QAAA,EAAC;kBAE9C;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAa,eACb5C,OAAA,CAACpB,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAACb,KAAK,EAAC,gBAAgB;oBAAAL,QAAA,EAAC;kBAEnD;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAa;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACX,eACN5C,OAAA,CAACzB,GAAG;kBAAAwD,QAAA,gBACA/B,OAAA,CAACpB,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAACE,UAAU,EAAC,MAAM;oBAAApB,QAAA,EAAC;kBAE9C;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAa,eACb5C,OAAA,CAACpB,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAACb,KAAK,EAAC,gBAAgB;oBAAAL,QAAA,EAAC;kBAEnD;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAa;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACX;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACX,eAGP5C,OAAA,CAACjB,IAAI;YAACiD,SAAS,EAAE,CAAE;YAAAD,QAAA,eACf/B,OAAA,CAAChB,WAAW;cAAA+C,QAAA,gBACR/B,OAAA,CAACpB,UAAU;gBAACqE,OAAO,EAAC,IAAI;gBAACE,UAAU,EAAC,MAAM;gBAAC0B,YAAY;gBAAA9C,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAa,eACb5C,OAAA,CAACtB,KAAK;gBAACoG,SAAS,EAAC,KAAK;gBAACxB,OAAO,EAAE,CAAE;gBAAAvB,QAAA,gBAC9B/B,OAAA,CAACd,IAAI;kBACD6F,IAAI,eAAE/E,OAAA,CAACH,UAAU;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAI;kBACrBmB,KAAK,EAAC,cAAc;kBACpBd,OAAO,EAAC,UAAU;kBAClBb,KAAK,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACjB,eACF5C,OAAA,CAACd,IAAI;kBACD6F,IAAI,eAAE/E,OAAA,CAACJ,SAAS;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAI;kBACpBmB,KAAK,EAAC,cAAc;kBACpBd,OAAO,EAAC,UAAU;kBAClBb,KAAK,EAAC;gBAAW;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACnB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACE,eACR5C,OAAA,CAACpB,UAAU;gBAACqE,OAAO,EAAC,OAAO;gBAACb,KAAK,EAAC,gBAAgB;gBAACP,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBAAAjD,QAAA,EAAC;cAElE;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAa;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACL;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACJ,eAEP5C,OAAA,CAACP,KAAK;MAAC2B,OAAO,EAAEA,OAAQ;MAACG,YAAY,EAAEA,YAAa;MAACD,SAAS,EAAEA;IAAU;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACrE;AAEpB,CAAC;AAAA1C,EAAA,CA7NKD,QAAQ;EAAA,QAGOZ,WAAW,EACXD,WAAW,EAEVE,WAAW;AAAA;AAAA2F,EAAA,GAN3BhF,QAAQ;AA+Nd,eAAeA,QAAQ;AAAA,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}