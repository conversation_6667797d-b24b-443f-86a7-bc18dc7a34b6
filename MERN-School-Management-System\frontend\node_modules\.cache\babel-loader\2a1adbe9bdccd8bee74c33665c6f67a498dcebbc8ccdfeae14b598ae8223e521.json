{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\pages\\\\admin\\\\documents\\\\DocumentGeneration.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Grid, Paper, Typography, Box, Card, CardContent, Button, TextField, MenuItem, FormControl, InputLabel, Select, Chip, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, List, ListItem, ListItemIcon, ListItemText, Divider } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Description as DescriptionIcon, Print as PrintIcon, Download as DownloadIcon, Preview as PreviewIcon, School as SchoolIcon, CardMembership as CardMembershipIcon, Assignment as AssignmentIcon, WorkspacePremium as CertificateIcon, Email as EmailIcon, Share as ShareIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledPaper = styled(Paper)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    padding: theme.spacing(3),\n    borderRadius: '16px',\n    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'\n  };\n});\n_c = StyledPaper;\nconst DocumentCard = styled(Card)(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    borderRadius: '16px',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n    transition: 'all 0.3s ease',\n    cursor: 'pointer',\n    '&:hover': {\n      transform: 'translateY(-4px)',\n      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)'\n    }\n  };\n});\n_c2 = DocumentCard;\nconst DocumentGeneration = () => {\n  _s();\n  const [selectedStudent, setSelectedStudent] = useState('');\n  const [selectedClass, setSelectedClass] = useState('');\n  const [previewDialog, setPreviewDialog] = useState(false);\n  const [selectedDocument, setSelectedDocument] = useState(null);\n\n  // Sample data\n  const students = [{\n    id: 1,\n    name: 'John Doe',\n    rollNumber: 'ST001',\n    class: '10A'\n  }, {\n    id: 2,\n    name: 'Alice Smith',\n    rollNumber: 'ST002',\n    class: '10A'\n  }, {\n    id: 3,\n    name: 'Bob Johnson',\n    rollNumber: 'ST003',\n    class: '9B'\n  }];\n  const classes = ['10A', '10B', '9A', '9B', '8A', '8B'];\n  const documentTypes = [{\n    id: 1,\n    title: 'Admission Letter',\n    description: 'Official admission confirmation letter',\n    icon: /*#__PURE__*/_jsxDEV(SchoolIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 13\n    }, this),\n    color: '#667eea',\n    category: 'Admission'\n  }, {\n    id: 2,\n    title: 'Study Certificate',\n    description: 'Academic study certificate',\n    icon: /*#__PURE__*/_jsxDEV(CertificateIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 13\n    }, this),\n    color: '#f093fb',\n    category: 'Academic'\n  }, {\n    id: 3,\n    title: 'Transfer Certificate',\n    description: 'School transfer certificate',\n    icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 13\n    }, this),\n    color: '#4facfe',\n    category: 'Transfer'\n  }, {\n    id: 4,\n    title: 'Character Certificate',\n    description: 'Student character certificate',\n    icon: /*#__PURE__*/_jsxDEV(CardMembershipIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this),\n    color: '#fa709a',\n    category: 'Character'\n  }, {\n    id: 5,\n    title: 'Bonafide Certificate',\n    description: 'Student bonafide certificate',\n    icon: /*#__PURE__*/_jsxDEV(DescriptionIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 13\n    }, this),\n    color: '#a8edea',\n    category: 'Bonafide'\n  }, {\n    id: 6,\n    title: 'ID Card',\n    description: 'Student identification card',\n    icon: /*#__PURE__*/_jsxDEV(CardMembershipIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 13\n    }, this),\n    color: '#ffeaa7',\n    category: 'Identity'\n  }];\n  const handleDocumentSelect = document => {\n    setSelectedDocument(document);\n    setPreviewDialog(true);\n  };\n  const handleGenerate = format => {\n    console.log(`Generating ${selectedDocument === null || selectedDocument === void 0 ? void 0 : selectedDocument.title} in ${format} format`);\n    // Implementation for document generation\n    setPreviewDialog(false);\n  };\n  const renderDocumentCard = document => /*#__PURE__*/_jsxDEV(Grid, {\n    item: true,\n    xs: 12,\n    sm: 6,\n    md: 4,\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.3\n      },\n      children: /*#__PURE__*/_jsxDEV(DocumentCard, {\n        onClick: () => handleDocumentSelect(document),\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            mb: 2,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: 60,\n                height: 60,\n                borderRadius: '12px',\n                bgcolor: document.color,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                mr: 2\n              },\n              children: /*#__PURE__*/React.cloneElement(document.icon, {\n                fontSize: 'large'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              flex: 1,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                children: document.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: document.category,\n                size: \"small\",\n                sx: {\n                  bgcolor: document.color,\n                  color: 'white',\n                  mt: 0.5\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            mb: 2,\n            children: document.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Preview\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"primary\",\n                children: /*#__PURE__*/_jsxDEV(PreviewIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Print\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"secondary\",\n                children: /*#__PURE__*/_jsxDEV(PrintIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Download\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"success\",\n                children: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Email\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"info\",\n                children: /*#__PURE__*/_jsxDEV(EmailIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this)\n  }, document.id, false, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      mt: 2,\n      mb: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          fontWeight: \"bold\",\n          color: \"primary\",\n          gutterBottom: true,\n          children: \"\\uD83D\\uDCC4 Document Generation System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"Generate admission letters, certificates, and student documents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StyledPaper, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        fontWeight: \"bold\",\n        mb: 3,\n        children: \"Student Selection\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Select Class\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedClass,\n              onChange: e => setSelectedClass(e.target.value),\n              label: \"Select Class\",\n              children: classes.map(cls => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: cls,\n                children: [\"Class \", cls]\n              }, cls, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Select Student\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedStudent,\n              onChange: e => setSelectedStudent(e.target.value),\n              label: \"Select Student\",\n              children: students.filter(student => !selectedClass || student.class === selectedClass).map(student => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: student.id,\n                children: [student.name, \" (\", student.rollNumber, \")\"]\n              }, student.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            gap: 2,\n            height: \"100%\",\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              fullWidth: true,\n              children: \"Generate for All Students\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"secondary\",\n              fullWidth: true,\n              children: \"Bulk Generation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StyledPaper, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        fontWeight: \"bold\",\n        mb: 3,\n        children: \"Available Documents\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: documentTypes.map(renderDocumentCard)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: previewDialog,\n      onClose: () => setPreviewDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          children: [selectedDocument === null || selectedDocument === void 0 ? void 0 : selectedDocument.icon, /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            ml: 1,\n            children: [selectedDocument === null || selectedDocument === void 0 ? void 0 : selectedDocument.title, \" - Preview & Generate\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            mb: 2,\n            children: selectedDocument === null || selectedDocument === void 0 ? void 0 : selectedDocument.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Generation Options\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(PrintIcon, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Print Document\",\n              secondary: \"Print directly to connected printer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"primary\",\n              onClick: () => handleGenerate('print'),\n              children: \"Print\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(DownloadIcon, {\n                color: \"secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Download PDF\",\n              secondary: \"Download as PDF file\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"secondary\",\n              onClick: () => handleGenerate('pdf'),\n              children: \"Download\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EmailIcon, {\n                color: \"info\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Email Document\",\n              secondary: \"Send via email to student/parent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"info\",\n              onClick: () => handleGenerate('email'),\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ShareIcon, {\n                color: \"success\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Share Link\",\n              secondary: \"Generate shareable link\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"success\",\n              onClick: () => handleGenerate('share'),\n              children: \"Share\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setPreviewDialog(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: () => handleGenerate('preview'),\n          children: \"Preview Document\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 210,\n    columnNumber: 5\n  }, this);\n};\n_s(DocumentGeneration, \"oOdCxl8OWHYrULY6GxVuKYrGhPk=\");\n_c3 = DocumentGeneration;\nexport default DocumentGeneration;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"StyledPaper\");\n$RefreshReg$(_c2, \"DocumentCard\");\n$RefreshReg$(_c3, \"DocumentGeneration\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Grid", "Paper", "Typography", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "TextField", "MenuItem", "FormControl", "InputLabel", "Select", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "styled", "motion", "Description", "DescriptionIcon", "Print", "PrintIcon", "Download", "DownloadIcon", "Preview", "PreviewIcon", "School", "SchoolIcon", "CardMembership", "CardMembershipIcon", "Assignment", "AssignmentIcon", "WorkspacePremium", "CertificateIcon", "Email", "EmailIcon", "Share", "ShareIcon", "jsxDEV", "_jsxDEV", "StyledPaper", "_ref", "theme", "padding", "spacing", "borderRadius", "boxShadow", "_c", "DocumentCard", "_ref2", "transition", "cursor", "transform", "_c2", "DocumentGeneration", "_s", "selectedStudent", "setSelectedStudent", "selectedClass", "setSelectedClass", "previewDialog", "setPreviewDialog", "selectedDocument", "setSelectedDocument", "students", "id", "name", "rollNumber", "class", "classes", "documentTypes", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "category", "handleDocumentSelect", "document", "handleGenerate", "format", "console", "log", "renderDocumentCard", "item", "xs", "sm", "md", "children", "div", "initial", "opacity", "y", "animate", "duration", "onClick", "display", "alignItems", "mb", "sx", "width", "height", "bgcolor", "justifyContent", "mr", "cloneElement", "fontSize", "flex", "variant", "fontWeight", "label", "size", "mt", "max<PERSON><PERSON><PERSON>", "gutterBottom", "container", "fullWidth", "value", "onChange", "e", "target", "map", "cls", "filter", "student", "gap", "open", "onClose", "ml", "primary", "secondary", "_c3", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/pages/admin/documents/DocumentGeneration.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Container,\n  Grid,\n  Paper,\n  Typography,\n  Box,\n  Card,\n  CardContent,\n  Button,\n  TextField,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  Select,\n  Chip,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport {\n  Description as DescriptionIcon,\n  Print as PrintIcon,\n  Download as DownloadIcon,\n  Preview as PreviewIcon,\n  School as SchoolIcon,\n  CardMembership as CardMembershipIcon,\n  Assignment as AssignmentIcon,\n  WorkspacePremium as CertificateIcon,\n  Email as EmailIcon,\n  Share as ShareIcon\n} from '@mui/icons-material';\n\nconst StyledPaper = styled(Paper)(({ theme }) => ({\n  padding: theme.spacing(3),\n  borderRadius: '16px',\n  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n}));\n\nconst DocumentCard = styled(Card)(({ theme }) => ({\n  borderRadius: '16px',\n  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n  transition: 'all 0.3s ease',\n  cursor: 'pointer',\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',\n  },\n}));\n\nconst DocumentGeneration = () => {\n  const [selectedStudent, setSelectedStudent] = useState('');\n  const [selectedClass, setSelectedClass] = useState('');\n  const [previewDialog, setPreviewDialog] = useState(false);\n  const [selectedDocument, setSelectedDocument] = useState(null);\n\n  // Sample data\n  const students = [\n    { id: 1, name: 'John Doe', rollNumber: 'ST001', class: '10A' },\n    { id: 2, name: 'Alice Smith', rollNumber: 'ST002', class: '10A' },\n    { id: 3, name: 'Bob Johnson', rollNumber: 'ST003', class: '9B' },\n  ];\n\n  const classes = ['10A', '10B', '9A', '9B', '8A', '8B'];\n\n  const documentTypes = [\n    {\n      id: 1,\n      title: 'Admission Letter',\n      description: 'Official admission confirmation letter',\n      icon: <SchoolIcon />,\n      color: '#667eea',\n      category: 'Admission'\n    },\n    {\n      id: 2,\n      title: 'Study Certificate',\n      description: 'Academic study certificate',\n      icon: <CertificateIcon />,\n      color: '#f093fb',\n      category: 'Academic'\n    },\n    {\n      id: 3,\n      title: 'Transfer Certificate',\n      description: 'School transfer certificate',\n      icon: <AssignmentIcon />,\n      color: '#4facfe',\n      category: 'Transfer'\n    },\n    {\n      id: 4,\n      title: 'Character Certificate',\n      description: 'Student character certificate',\n      icon: <CardMembershipIcon />,\n      color: '#fa709a',\n      category: 'Character'\n    },\n    {\n      id: 5,\n      title: 'Bonafide Certificate',\n      description: 'Student bonafide certificate',\n      icon: <DescriptionIcon />,\n      color: '#a8edea',\n      category: 'Bonafide'\n    },\n    {\n      id: 6,\n      title: 'ID Card',\n      description: 'Student identification card',\n      icon: <CardMembershipIcon />,\n      color: '#ffeaa7',\n      category: 'Identity'\n    },\n  ];\n\n  const handleDocumentSelect = (document) => {\n    setSelectedDocument(document);\n    setPreviewDialog(true);\n  };\n\n  const handleGenerate = (format) => {\n    console.log(`Generating ${selectedDocument?.title} in ${format} format`);\n    // Implementation for document generation\n    setPreviewDialog(false);\n  };\n\n  const renderDocumentCard = (document) => (\n    <Grid item xs={12} sm={6} md={4} key={document.id}>\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        <DocumentCard onClick={() => handleDocumentSelect(document)}>\n          <CardContent>\n            <Box display=\"flex\" alignItems=\"center\" mb={2}>\n              <Box\n                sx={{\n                  width: 60,\n                  height: 60,\n                  borderRadius: '12px',\n                  bgcolor: document.color,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: 'white',\n                  mr: 2\n                }}\n              >\n                {React.cloneElement(document.icon, { fontSize: 'large' })}\n              </Box>\n              <Box flex={1}>\n                <Typography variant=\"h6\" fontWeight=\"bold\">\n                  {document.title}\n                </Typography>\n                <Chip\n                  label={document.category}\n                  size=\"small\"\n                  sx={{ \n                    bgcolor: document.color,\n                    color: 'white',\n                    mt: 0.5\n                  }}\n                />\n              </Box>\n            </Box>\n            <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\n              {document.description}\n            </Typography>\n            <Box display=\"flex\" justifyContent=\"space-between\">\n              <Tooltip title=\"Preview\">\n                <IconButton size=\"small\" color=\"primary\">\n                  <PreviewIcon />\n                </IconButton>\n              </Tooltip>\n              <Tooltip title=\"Print\">\n                <IconButton size=\"small\" color=\"secondary\">\n                  <PrintIcon />\n                </IconButton>\n              </Tooltip>\n              <Tooltip title=\"Download\">\n                <IconButton size=\"small\" color=\"success\">\n                  <DownloadIcon />\n                </IconButton>\n              </Tooltip>\n              <Tooltip title=\"Email\">\n                <IconButton size=\"small\" color=\"info\">\n                  <EmailIcon />\n                </IconButton>\n              </Tooltip>\n            </Box>\n          </CardContent>\n        </DocumentCard>\n      </motion.div>\n    </Grid>\n  );\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ mt: 2, mb: 4 }}>\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box mb={4}>\n          <Typography variant=\"h4\" fontWeight=\"bold\" color=\"primary\" gutterBottom>\n            📄 Document Generation System\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Generate admission letters, certificates, and student documents\n          </Typography>\n        </Box>\n      </motion.div>\n\n      {/* Selection Panel */}\n      <StyledPaper sx={{ mb: 3 }}>\n        <Typography variant=\"h6\" fontWeight=\"bold\" mb={3}>\n          Student Selection\n        </Typography>\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={4}>\n            <FormControl fullWidth>\n              <InputLabel>Select Class</InputLabel>\n              <Select\n                value={selectedClass}\n                onChange={(e) => setSelectedClass(e.target.value)}\n                label=\"Select Class\"\n              >\n                {classes.map((cls) => (\n                  <MenuItem key={cls} value={cls}>\n                    Class {cls}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} md={4}>\n            <FormControl fullWidth>\n              <InputLabel>Select Student</InputLabel>\n              <Select\n                value={selectedStudent}\n                onChange={(e) => setSelectedStudent(e.target.value)}\n                label=\"Select Student\"\n              >\n                {students\n                  .filter(student => !selectedClass || student.class === selectedClass)\n                  .map((student) => (\n                    <MenuItem key={student.id} value={student.id}>\n                      {student.name} ({student.rollNumber})\n                    </MenuItem>\n                  ))}\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} md={4}>\n            <Box display=\"flex\" gap={2} height=\"100%\" alignItems=\"center\">\n              <Button variant=\"contained\" color=\"primary\" fullWidth>\n                Generate for All Students\n              </Button>\n              <Button variant=\"outlined\" color=\"secondary\" fullWidth>\n                Bulk Generation\n              </Button>\n            </Box>\n          </Grid>\n        </Grid>\n      </StyledPaper>\n\n      {/* Document Types */}\n      <StyledPaper>\n        <Typography variant=\"h6\" fontWeight=\"bold\" mb={3}>\n          Available Documents\n        </Typography>\n        <Grid container spacing={3}>\n          {documentTypes.map(renderDocumentCard)}\n        </Grid>\n      </StyledPaper>\n\n      {/* Preview Dialog */}\n      <Dialog \n        open={previewDialog} \n        onClose={() => setPreviewDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box display=\"flex\" alignItems=\"center\">\n            {selectedDocument?.icon}\n            <Typography variant=\"h6\" ml={1}>\n              {selectedDocument?.title} - Preview & Generate\n            </Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Box mb={3}>\n            <Typography variant=\"body1\" color=\"text.secondary\" mb={2}>\n              {selectedDocument?.description}\n            </Typography>\n            <Divider />\n          </Box>\n\n          <Typography variant=\"h6\" gutterBottom>\n            Generation Options\n          </Typography>\n          <List>\n            <ListItem>\n              <ListItemIcon>\n                <PrintIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText \n                primary=\"Print Document\" \n                secondary=\"Print directly to connected printer\"\n              />\n              <Button \n                variant=\"outlined\" \n                color=\"primary\"\n                onClick={() => handleGenerate('print')}\n              >\n                Print\n              </Button>\n            </ListItem>\n            <ListItem>\n              <ListItemIcon>\n                <DownloadIcon color=\"secondary\" />\n              </ListItemIcon>\n              <ListItemText \n                primary=\"Download PDF\" \n                secondary=\"Download as PDF file\"\n              />\n              <Button \n                variant=\"outlined\" \n                color=\"secondary\"\n                onClick={() => handleGenerate('pdf')}\n              >\n                Download\n              </Button>\n            </ListItem>\n            <ListItem>\n              <ListItemIcon>\n                <EmailIcon color=\"info\" />\n              </ListItemIcon>\n              <ListItemText \n                primary=\"Email Document\" \n                secondary=\"Send via email to student/parent\"\n              />\n              <Button \n                variant=\"outlined\" \n                color=\"info\"\n                onClick={() => handleGenerate('email')}\n              >\n                Email\n              </Button>\n            </ListItem>\n            <ListItem>\n              <ListItemIcon>\n                <ShareIcon color=\"success\" />\n              </ListItemIcon>\n              <ListItemText \n                primary=\"Share Link\" \n                secondary=\"Generate shareable link\"\n              />\n              <Button \n                variant=\"outlined\" \n                color=\"success\"\n                onClick={() => handleGenerate('share')}\n              >\n                Share\n              </Button>\n            </ListItem>\n          </List>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setPreviewDialog(false)}>\n            Cancel\n          </Button>\n          <Button \n            variant=\"contained\" \n            color=\"primary\"\n            onClick={() => handleGenerate('preview')}\n          >\n            Preview Document\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Container>\n  );\n};\n\nexport default DocumentGeneration;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,SAAS,EACTC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,cAAc,IAAIC,kBAAkB,EACpCC,UAAU,IAAIC,cAAc,EAC5BC,gBAAgB,IAAIC,eAAe,EACnCC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,WAAW,GAAGxB,MAAM,CAACvB,KAAK,CAAC,CAACgD,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IAChDE,OAAO,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;IACzBC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE;EACb,CAAC;AAAA,CAAC,CAAC;AAACC,EAAA,GAJEP,WAAW;AAMjB,MAAMQ,YAAY,GAAGhC,MAAM,CAACpB,IAAI,CAAC,CAACqD,KAAA;EAAA,IAAC;IAAEP;EAAM,CAAC,GAAAO,KAAA;EAAA,OAAM;IAChDJ,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE,gCAAgC;IAC3CI,UAAU,EAAE,eAAe;IAC3BC,MAAM,EAAE,SAAS;IACjB,SAAS,EAAE;MACTC,SAAS,EAAE,kBAAkB;MAC7BN,SAAS,EAAE;IACb;EACF,CAAC;AAAA,CAAC,CAAC;AAACO,GAAA,GATEL,YAAY;AAWlB,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoE,aAAa,EAAEC,gBAAgB,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsE,aAAa,EAAEC,gBAAgB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM0E,QAAQ,GAAG,CACf;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,UAAU;IAAEC,UAAU,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC9D;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,aAAa;IAAEC,UAAU,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAM,CAAC,EACjE;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,aAAa;IAAEC,UAAU,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAK,CAAC,CACjE;EAED,MAAMC,OAAO,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEtD,MAAMC,aAAa,GAAG,CACpB;IACEL,EAAE,EAAE,CAAC;IACLM,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,wCAAwC;IACrDC,IAAI,eAAElC,OAAA,CAACZ,UAAU;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACpBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACLM,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,4BAA4B;IACzCC,IAAI,eAAElC,OAAA,CAACN,eAAe;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACzBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACLM,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,6BAA6B;IAC1CC,IAAI,eAAElC,OAAA,CAACR,cAAc;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACxBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACLM,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,+BAA+B;IAC5CC,IAAI,eAAElC,OAAA,CAACV,kBAAkB;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC5BC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACLM,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,8BAA8B;IAC3CC,IAAI,eAAElC,OAAA,CAACpB,eAAe;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACzBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACLM,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,6BAA6B;IAC1CC,IAAI,eAAElC,OAAA,CAACV,kBAAkB;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC5BC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,oBAAoB,GAAIC,QAAQ,IAAK;IACzClB,mBAAmB,CAACkB,QAAQ,CAAC;IAC7BpB,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMqB,cAAc,GAAIC,MAAM,IAAK;IACjCC,OAAO,CAACC,GAAG,CAAE,cAAavB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAES,KAAM,OAAMY,MAAO,SAAQ,CAAC;IACxE;IACAtB,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;EAED,MAAMyB,kBAAkB,GAAIL,QAAQ,iBAClC1C,OAAA,CAAC/C,IAAI;IAAC+F,IAAI;IAACC,EAAE,EAAE,EAAG;IAACC,EAAE,EAAE,CAAE;IAACC,EAAE,EAAE,CAAE;IAAAC,QAAA,eAC9BpD,OAAA,CAACtB,MAAM,CAAC2E,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9B7C,UAAU,EAAE;QAAE+C,QAAQ,EAAE;MAAI,CAAE;MAAAN,QAAA,eAE9BpD,OAAA,CAACS,YAAY;QAACkD,OAAO,EAAEA,CAAA,KAAMlB,oBAAoB,CAACC,QAAQ,CAAE;QAAAU,QAAA,eAC1DpD,OAAA,CAAC1C,WAAW;UAAA8F,QAAA,gBACVpD,OAAA,CAAC5C,GAAG;YAACwG,OAAO,EAAC,MAAM;YAACC,UAAU,EAAC,QAAQ;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBAC5CpD,OAAA,CAAC5C,GAAG;cACF2G,EAAE,EAAE;gBACFC,KAAK,EAAE,EAAE;gBACTC,MAAM,EAAE,EAAE;gBACV3D,YAAY,EAAE,MAAM;gBACpB4D,OAAO,EAAExB,QAAQ,CAACH,KAAK;gBACvBqB,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBM,cAAc,EAAE,QAAQ;gBACxB5B,KAAK,EAAE,OAAO;gBACd6B,EAAE,EAAE;cACN,CAAE;cAAAhB,QAAA,eAEDtG,KAAK,CAACuH,YAAY,CAAC3B,QAAQ,CAACR,IAAI,EAAE;gBAAEoC,QAAQ,EAAE;cAAQ,CAAC;YAAC;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACrD,eACNtC,OAAA,CAAC5C,GAAG;cAACmH,IAAI,EAAE,CAAE;cAAAnB,QAAA,gBACXpD,OAAA,CAAC7C,UAAU;gBAACqH,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAArB,QAAA,EACvCV,QAAQ,CAACV;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACJ,eACbtC,OAAA,CAACnC,IAAI;gBACH6G,KAAK,EAAEhC,QAAQ,CAACF,QAAS;gBACzBmC,IAAI,EAAC,OAAO;gBACZZ,EAAE,EAAE;kBACFG,OAAO,EAAExB,QAAQ,CAACH,KAAK;kBACvBA,KAAK,EAAE,OAAO;kBACdqC,EAAE,EAAE;gBACN;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eACNtC,OAAA,CAAC7C,UAAU;YAACqH,OAAO,EAAC,OAAO;YAACjC,KAAK,EAAC,gBAAgB;YAACuB,EAAE,EAAE,CAAE;YAAAV,QAAA,EACtDV,QAAQ,CAACT;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACV,eACbtC,OAAA,CAAC5C,GAAG;YAACwG,OAAO,EAAC,MAAM;YAACO,cAAc,EAAC,eAAe;YAAAf,QAAA,gBAChDpD,OAAA,CAACjC,OAAO;cAACiE,KAAK,EAAC,SAAS;cAAAoB,QAAA,eACtBpD,OAAA,CAAClC,UAAU;gBAAC6G,IAAI,EAAC,OAAO;gBAACpC,KAAK,EAAC,SAAS;gBAAAa,QAAA,eACtCpD,OAAA,CAACd,WAAW;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACJ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACL,eACVtC,OAAA,CAACjC,OAAO;cAACiE,KAAK,EAAC,OAAO;cAAAoB,QAAA,eACpBpD,OAAA,CAAClC,UAAU;gBAAC6G,IAAI,EAAC,OAAO;gBAACpC,KAAK,EAAC,WAAW;gBAAAa,QAAA,eACxCpD,OAAA,CAAClB,SAAS;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACL,eACVtC,OAAA,CAACjC,OAAO;cAACiE,KAAK,EAAC,UAAU;cAAAoB,QAAA,eACvBpD,OAAA,CAAClC,UAAU;gBAAC6G,IAAI,EAAC,OAAO;gBAACpC,KAAK,EAAC,SAAS;gBAAAa,QAAA,eACtCpD,OAAA,CAAChB,YAAY;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACL,eACVtC,OAAA,CAACjC,OAAO;cAACiE,KAAK,EAAC,OAAO;cAAAoB,QAAA,eACpBpD,OAAA,CAAClC,UAAU;gBAAC6G,IAAI,EAAC,OAAO;gBAACpC,KAAK,EAAC,MAAM;gBAAAa,QAAA,eACnCpD,OAAA,CAACJ,SAAS;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACM;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACJ,GAlEuBI,QAAQ,CAAChB,EAAE;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAoElD;EAED,oBACEtC,OAAA,CAAChD,SAAS;IAAC6H,QAAQ,EAAC,IAAI;IAACd,EAAE,EAAE;MAAEa,EAAE,EAAE,CAAC;MAAEd,EAAE,EAAE;IAAE,CAAE;IAAAV,QAAA,gBAC5CpD,OAAA,CAACtB,MAAM,CAAC2E,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9B7C,UAAU,EAAE;QAAE+C,QAAQ,EAAE;MAAI,CAAE;MAAAN,QAAA,eAE9BpD,OAAA,CAAC5C,GAAG;QAAC0G,EAAE,EAAE,CAAE;QAAAV,QAAA,gBACTpD,OAAA,CAAC7C,UAAU;UAACqH,OAAO,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAAClC,KAAK,EAAC,SAAS;UAACuC,YAAY;UAAA1B,QAAA,EAAC;QAExE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACbtC,OAAA,CAAC7C,UAAU;UAACqH,OAAO,EAAC,OAAO;UAACjC,KAAK,EAAC,gBAAgB;UAAAa,QAAA,EAAC;QAEnD;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACK,eAGbtC,OAAA,CAACC,WAAW;MAAC8D,EAAE,EAAE;QAAED,EAAE,EAAE;MAAE,CAAE;MAAAV,QAAA,gBACzBpD,OAAA,CAAC7C,UAAU;QAACqH,OAAO,EAAC,IAAI;QAACC,UAAU,EAAC,MAAM;QAACX,EAAE,EAAE,CAAE;QAAAV,QAAA,EAAC;MAElD;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAa,eACbtC,OAAA,CAAC/C,IAAI;QAAC8H,SAAS;QAAC1E,OAAO,EAAE,CAAE;QAAA+C,QAAA,gBACzBpD,OAAA,CAAC/C,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAC,QAAA,eACvBpD,OAAA,CAACtC,WAAW;YAACsH,SAAS;YAAA5B,QAAA,gBACpBpD,OAAA,CAACrC,UAAU;cAAAyF,QAAA,EAAC;YAAY;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAa,eACrCtC,OAAA,CAACpC,MAAM;cACLqH,KAAK,EAAE9D,aAAc;cACrB+D,QAAQ,EAAGC,CAAC,IAAK/D,gBAAgB,CAAC+D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAClDP,KAAK,EAAC,cAAc;cAAAtB,QAAA,EAEnBtB,OAAO,CAACuD,GAAG,CAAEC,GAAG,iBACftF,OAAA,CAACvC,QAAQ;gBAAWwH,KAAK,EAAEK,GAAI;gBAAAlC,QAAA,GAAC,QACxB,EAACkC,GAAG;cAAA,GADGA,GAAG;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAGnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACK;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACG;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACT,eACPtC,OAAA,CAAC/C,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAC,QAAA,eACvBpD,OAAA,CAACtC,WAAW;YAACsH,SAAS;YAAA5B,QAAA,gBACpBpD,OAAA,CAACrC,UAAU;cAAAyF,QAAA,EAAC;YAAc;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAa,eACvCtC,OAAA,CAACpC,MAAM;cACLqH,KAAK,EAAEhE,eAAgB;cACvBiE,QAAQ,EAAGC,CAAC,IAAKjE,kBAAkB,CAACiE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACpDP,KAAK,EAAC,gBAAgB;cAAAtB,QAAA,EAErB3B,QAAQ,CACN8D,MAAM,CAACC,OAAO,IAAI,CAACrE,aAAa,IAAIqE,OAAO,CAAC3D,KAAK,KAAKV,aAAa,CAAC,CACpEkE,GAAG,CAAEG,OAAO,iBACXxF,OAAA,CAACvC,QAAQ;gBAAkBwH,KAAK,EAAEO,OAAO,CAAC9D,EAAG;gBAAA0B,QAAA,GAC1CoC,OAAO,CAAC7D,IAAI,EAAC,IAAE,EAAC6D,OAAO,CAAC5D,UAAU,EAAC,GACtC;cAAA,GAFe4D,OAAO,CAAC9D,EAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAG1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACG;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACT,eACPtC,OAAA,CAAC/C,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAC,QAAA,eACvBpD,OAAA,CAAC5C,GAAG;YAACwG,OAAO,EAAC,MAAM;YAAC6B,GAAG,EAAE,CAAE;YAACxB,MAAM,EAAC,MAAM;YAACJ,UAAU,EAAC,QAAQ;YAAAT,QAAA,gBAC3DpD,OAAA,CAACzC,MAAM;cAACiH,OAAO,EAAC,WAAW;cAACjC,KAAK,EAAC,SAAS;cAACyC,SAAS;cAAA5B,QAAA,EAAC;YAEtD;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAS,eACTtC,OAAA,CAACzC,MAAM;cAACiH,OAAO,EAAC,UAAU;cAACjC,KAAK,EAAC,WAAW;cAACyC,SAAS;cAAA5B,QAAA,EAAC;YAEvD;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAS;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACK,eAGdtC,OAAA,CAACC,WAAW;MAAAmD,QAAA,gBACVpD,OAAA,CAAC7C,UAAU;QAACqH,OAAO,EAAC,IAAI;QAACC,UAAU,EAAC,MAAM;QAACX,EAAE,EAAE,CAAE;QAAAV,QAAA,EAAC;MAElD;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAa,eACbtC,OAAA,CAAC/C,IAAI;QAAC8H,SAAS;QAAC1E,OAAO,EAAE,CAAE;QAAA+C,QAAA,EACxBrB,aAAa,CAACsD,GAAG,CAACtC,kBAAkB;MAAC;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACjC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACK,eAGdtC,OAAA,CAAChC,MAAM;MACL0H,IAAI,EAAErE,aAAc;MACpBsE,OAAO,EAAEA,CAAA,KAAMrE,gBAAgB,CAAC,KAAK,CAAE;MACvCuD,QAAQ,EAAC,IAAI;MACbG,SAAS;MAAA5B,QAAA,gBAETpD,OAAA,CAAC/B,WAAW;QAAAmF,QAAA,eACVpD,OAAA,CAAC5C,GAAG;UAACwG,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAAAT,QAAA,GACpC7B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEW,IAAI,eACvBlC,OAAA,CAAC7C,UAAU;YAACqH,OAAO,EAAC,IAAI;YAACoB,EAAE,EAAE,CAAE;YAAAxC,QAAA,GAC5B7B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAES,KAAK,EAAC,uBAC3B;UAAA;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAa;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACM,eACdtC,OAAA,CAAC9B,aAAa;QAAAkF,QAAA,gBACZpD,OAAA,CAAC5C,GAAG;UAAC0G,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACTpD,OAAA,CAAC7C,UAAU;YAACqH,OAAO,EAAC,OAAO;YAACjC,KAAK,EAAC,gBAAgB;YAACuB,EAAE,EAAE,CAAE;YAAAV,QAAA,EACtD7B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEU;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACnB,eACbtC,OAAA,CAACxB,OAAO;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACP,eAENtC,OAAA,CAAC7C,UAAU;UAACqH,OAAO,EAAC,IAAI;UAACM,YAAY;UAAA1B,QAAA,EAAC;QAEtC;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACbtC,OAAA,CAAC5B,IAAI;UAAAgF,QAAA,gBACHpD,OAAA,CAAC3B,QAAQ;YAAA+E,QAAA,gBACPpD,OAAA,CAAC1B,YAAY;cAAA8E,QAAA,eACXpD,OAAA,CAAClB,SAAS;gBAACyD,KAAK,EAAC;cAAS;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAChB,eACftC,OAAA,CAACzB,YAAY;cACXsH,OAAO,EAAC,gBAAgB;cACxBC,SAAS,EAAC;YAAqC;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAC/C,eACFtC,OAAA,CAACzC,MAAM;cACLiH,OAAO,EAAC,UAAU;cAClBjC,KAAK,EAAC,SAAS;cACfoB,OAAO,EAAEA,CAAA,KAAMhB,cAAc,CAAC,OAAO,CAAE;cAAAS,QAAA,EACxC;YAED;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAS;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACA,eACXtC,OAAA,CAAC3B,QAAQ;YAAA+E,QAAA,gBACPpD,OAAA,CAAC1B,YAAY;cAAA8E,QAAA,eACXpD,OAAA,CAAChB,YAAY;gBAACuD,KAAK,EAAC;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACrB,eACftC,OAAA,CAACzB,YAAY;cACXsH,OAAO,EAAC,cAAc;cACtBC,SAAS,EAAC;YAAsB;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAChC,eACFtC,OAAA,CAACzC,MAAM;cACLiH,OAAO,EAAC,UAAU;cAClBjC,KAAK,EAAC,WAAW;cACjBoB,OAAO,EAAEA,CAAA,KAAMhB,cAAc,CAAC,KAAK,CAAE;cAAAS,QAAA,EACtC;YAED;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAS;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACA,eACXtC,OAAA,CAAC3B,QAAQ;YAAA+E,QAAA,gBACPpD,OAAA,CAAC1B,YAAY;cAAA8E,QAAA,eACXpD,OAAA,CAACJ,SAAS;gBAAC2C,KAAK,EAAC;cAAM;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACb,eACftC,OAAA,CAACzB,YAAY;cACXsH,OAAO,EAAC,gBAAgB;cACxBC,SAAS,EAAC;YAAkC;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAC5C,eACFtC,OAAA,CAACzC,MAAM;cACLiH,OAAO,EAAC,UAAU;cAClBjC,KAAK,EAAC,MAAM;cACZoB,OAAO,EAAEA,CAAA,KAAMhB,cAAc,CAAC,OAAO,CAAE;cAAAS,QAAA,EACxC;YAED;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAS;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACA,eACXtC,OAAA,CAAC3B,QAAQ;YAAA+E,QAAA,gBACPpD,OAAA,CAAC1B,YAAY;cAAA8E,QAAA,eACXpD,OAAA,CAACF,SAAS;gBAACyC,KAAK,EAAC;cAAS;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAChB,eACftC,OAAA,CAACzB,YAAY;cACXsH,OAAO,EAAC,YAAY;cACpBC,SAAS,EAAC;YAAyB;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACnC,eACFtC,OAAA,CAACzC,MAAM;cACLiH,OAAO,EAAC,UAAU;cAClBjC,KAAK,EAAC,SAAS;cACfoB,OAAO,EAAEA,CAAA,KAAMhB,cAAc,CAAC,OAAO,CAAE;cAAAS,QAAA,EACxC;YAED;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAS;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACA;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACO,eAChBtC,OAAA,CAAC7B,aAAa;QAAAiF,QAAA,gBACZpD,OAAA,CAACzC,MAAM;UAACoG,OAAO,EAAEA,CAAA,KAAMrC,gBAAgB,CAAC,KAAK,CAAE;UAAA8B,QAAA,EAAC;QAEhD;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS,eACTtC,OAAA,CAACzC,MAAM;UACLiH,OAAO,EAAC,WAAW;UACnBjC,KAAK,EAAC,SAAS;UACfoB,OAAO,EAAEA,CAAA,KAAMhB,cAAc,CAAC,SAAS,CAAE;UAAAS,QAAA,EAC1C;QAED;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACK;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACC;AAEhB,CAAC;AAACtB,EAAA,CAhVID,kBAAkB;AAAAgF,GAAA,GAAlBhF,kBAAkB;AAkVxB,eAAeA,kBAAkB;AAAC,IAAAP,EAAA,EAAAM,GAAA,EAAAiF,GAAA;AAAAC,YAAA,CAAAxF,EAAA;AAAAwF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}