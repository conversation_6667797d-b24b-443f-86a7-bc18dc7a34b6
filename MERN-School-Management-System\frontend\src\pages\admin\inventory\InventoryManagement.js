import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Avatar,
  Chip,
  Button,
  TextField,
  InputAdornment,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Badge,
  LinearProgress
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  Search as SearchIcon,
  Inventory as InventoryIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Computer as ComputerIcon,
  MenuBook as BookIcon,
  Sports as SportsIcon,
  Science as ScienceIcon,
  Build as BuildIcon,
  Chair as FurnitureIcon,
  Print as PrintIcon,
  Download as DownloadIcon,
  QrCode as QrCodeIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';

const StyledContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(3),
  paddingBottom: theme.spacing(3),
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
}));

const StatCard = styled(Card)(({ theme }) => ({
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.2)',
  },
}));

const InventoryCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  border: '1px solid',
  borderColor: theme.palette.divider,
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
    borderColor: theme.palette.primary.main,
  },
}));

const CategoryChip = styled(Chip)(({ theme, category }) => {
  const colors = {
    'Electronics': { bg: '#e3f2fd', color: '#1976d2' },
    'Books': { bg: '#f3e5f5', color: '#7b1fa2' },
    'Sports': { bg: '#e8f5e8', color: '#388e3c' },
    'Laboratory': { bg: '#fff3e0', color: '#f57c00' },
    'Furniture': { bg: '#fce4ec', color: '#c2185b' },
    'Maintenance': { bg: '#f1f8e9', color: '#689f38' },
  };
  
  const categoryColor = colors[category] || { bg: '#f5f5f5', color: '#666' };
  
  return {
    backgroundColor: categoryColor.bg,
    color: categoryColor.color,
    fontWeight: 'bold',
    '& .MuiChip-icon': {
      color: categoryColor.color,
    },
  };
});

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`inventory-tabpanel-${index}`}
      aria-labelledby={`inventory-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const InventoryManagement = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentUser } = useSelector(state => state.user);
  
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('All');
  const [statusFilter, setStatusFilter] = useState('All');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertSeverity, setAlertSeverity] = useState('success');

  // Mock inventory data - replace with actual API calls
  const [inventoryItems, setInventoryItems] = useState([
    {
      id: 1,
      name: 'Dell Laptop',
      category: 'Electronics',
      quantity: 25,
      minQuantity: 5,
      location: 'Computer Lab',
      status: 'Available',
      condition: 'Good',
      purchaseDate: '2023-01-15',
      cost: 800,
      supplier: 'Tech Solutions Inc',
      serialNumber: 'DL001-025',
      description: 'Dell Inspiron 15 3000 Series laptops for student use'
    },
    {
      id: 2,
      name: 'Mathematics Textbooks',
      category: 'Books',
      quantity: 150,
      minQuantity: 20,
      location: 'Library',
      status: 'Available',
      condition: 'Excellent',
      purchaseDate: '2023-03-10',
      cost: 45,
      supplier: 'Educational Publishers',
      serialNumber: 'MTH-2023-150',
      description: 'Grade 10 Mathematics textbooks'
    },
    {
      id: 3,
      name: 'Football',
      category: 'Sports',
      quantity: 2,
      minQuantity: 5,
      location: 'Sports Room',
      status: 'Low Stock',
      condition: 'Good',
      purchaseDate: '2022-08-20',
      cost: 25,
      supplier: 'Sports Equipment Co',
      serialNumber: 'FB-2022-002',
      description: 'Official size footballs for PE classes'
    },
    {
      id: 4,
      name: 'Microscopes',
      category: 'Laboratory',
      quantity: 12,
      minQuantity: 8,
      location: 'Biology Lab',
      status: 'Available',
      condition: 'Excellent',
      purchaseDate: '2023-02-05',
      cost: 350,
      supplier: 'Scientific Instruments Ltd',
      serialNumber: 'MIC-BIO-012',
      description: 'Compound microscopes for biology experiments'
    },
    {
      id: 5,
      name: 'Student Desks',
      category: 'Furniture',
      quantity: 0,
      minQuantity: 10,
      location: 'Classroom A',
      status: 'Out of Stock',
      condition: 'Fair',
      purchaseDate: '2021-06-15',
      cost: 120,
      supplier: 'School Furniture Plus',
      serialNumber: 'DSK-CLA-000',
      description: 'Standard student desks with storage'
    }
  ]);

  const categories = ['All', 'Electronics', 'Books', 'Sports', 'Laboratory', 'Furniture', 'Maintenance'];
  const statuses = ['All', 'Available', 'Low Stock', 'Out of Stock', 'Maintenance'];

  // Filter inventory items
  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'All' || item.category === categoryFilter;
    const matchesStatus = statusFilter === 'All' || item.status === statusFilter;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Calculate statistics
  const totalItems = inventoryItems.reduce((sum, item) => sum + item.quantity, 0);
  const totalValue = inventoryItems.reduce((sum, item) => sum + (item.quantity * item.cost), 0);
  const lowStockItems = inventoryItems.filter(item => item.quantity <= item.minQuantity).length;
  const outOfStockItems = inventoryItems.filter(item => item.quantity === 0).length;

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleDeleteItem = (item) => {
    setItemToDelete(item);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (itemToDelete) {
      setInventoryItems(prev => prev.filter(item => item.id !== itemToDelete.id));
      setAlertMessage(`Item "${itemToDelete.name}" has been deleted successfully.`);
      setAlertSeverity('success');
      setShowAlert(true);
      setTimeout(() => setShowAlert(false), 3000);
    }
    setDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Available': return 'success';
      case 'Low Stock': return 'warning';
      case 'Out of Stock': return 'error';
      case 'Maintenance': return 'info';
      default: return 'default';
    }
  };

  const getConditionColor = (condition) => {
    switch (condition) {
      case 'Excellent': return 'success';
      case 'Good': return 'info';
      case 'Fair': return 'warning';
      case 'Poor': return 'error';
      default: return 'default';
    }
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'Electronics': return <ComputerIcon />;
      case 'Books': return <BookIcon />;
      case 'Sports': return <SportsIcon />;
      case 'Laboratory': return <ScienceIcon />;
      case 'Furniture': return <FurnitureIcon />;
      case 'Maintenance': return <BuildIcon />;
      default: return <InventoryIcon />;
    }
  };

  const handleAddNewItem = () => {
    navigate('/Admin/inventory/add');
  };

  return (
    <StyledContainer maxWidth="xl">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
          <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
            <InventoryIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
            Inventory Management
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddNewItem}
            sx={{
              background: 'linear-gradient(45deg, #667eea 30%, #764ba2 90%)',
              color: 'white',
              '&:hover': {
                background: 'linear-gradient(45deg, #5a6fd8 30%, #6a4190 90%)',
              }
            }}
          >
            Add New Item
          </Button>
        </Box>

        {showAlert && (
          <Alert severity={alertSeverity} sx={{ mb: 3 }}>
            {alertMessage}
          </Alert>
        )}

        {/* Statistics Cards */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {totalItems}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Total Items
                    </Typography>
                  </Box>
                  <InventoryIcon sx={{ fontSize: 40, opacity: 0.8 }} />
                </Box>
              </CardContent>
            </StatCard>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      ${totalValue.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Total Value
                    </Typography>
                  </Box>
                  <TrendingUpIcon sx={{ fontSize: 40, opacity: 0.8 }} />
                </Box>
              </CardContent>
            </StatCard>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {lowStockItems}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Low Stock Items
                    </Typography>
                  </Box>
                  <WarningIcon sx={{ fontSize: 40, opacity: 0.8 }} />
                </Box>
              </CardContent>
            </StatCard>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {outOfStockItems}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Out of Stock
                    </Typography>
                  </Box>
                  <ErrorIcon sx={{ fontSize: 40, opacity: 0.8 }} />
                </Box>
              </CardContent>
            </StatCard>
          </Grid>
        </Grid>

        {/* Search and Filters */}
        <StyledPaper sx={{ mb: 3 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search inventory..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px',
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  label="Category"
                  sx={{ borderRadius: '12px' }}
                >
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                  sx={{ borderRadius: '12px' }}
                >
                  {statuses.map((status) => (
                    <MenuItem key={status} value={status}>
                      {status}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterIcon />}
                sx={{ height: '56px', borderRadius: '12px' }}
              >
                Advanced
              </Button>
            </Grid>
          </Grid>
        </StyledPaper>

        {/* Inventory Content */}
        <StyledPaper>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}
          >
            <Tab icon={<InventoryIcon />} label="All Items" />
            <Tab icon={<WarningIcon />} label="Low Stock" />
            <Tab icon={<ErrorIcon />} label="Out of Stock" />
            <Tab icon={<BuildIcon />} label="Maintenance" />
          </Tabs>

          {/* All Items Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              {filteredItems.map((item) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={item.id}>
                  <InventoryCard>
                    <CardContent>
                      <Box display="flex" alignItems="center" mb={2}>
                        <Avatar
                          sx={{
                            bgcolor: 'primary.main',
                            mr: 2,
                            width: 50,
                            height: 50
                          }}
                        >
                          {getCategoryIcon(item.category)}
                        </Avatar>
                        <Box flex={1}>
                          <Typography variant="h6" fontWeight="bold" noWrap>
                            {item.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {item.location}
                          </Typography>
                        </Box>
                      </Box>

                      <Box mb={2}>
                        <CategoryChip
                          category={item.category}
                          label={item.category}
                          size="small"
                          sx={CategoryChip}
                        />
                      </Box>

                      <Box mb={2}>
                        <Box display="flex" justifyContent="space-between" mb={1}>
                          <Typography variant="body2" color="text.secondary">
                            Quantity
                          </Typography>
                          <Typography variant="body2" fontWeight="bold">
                            {item.quantity}
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min((item.quantity / (item.minQuantity * 2)) * 100, 100)}
                          sx={{
                            height: 6,
                            borderRadius: 3,
                            backgroundColor: 'grey.200',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: item.quantity <= item.minQuantity ? 'error.main' : 'success.main',
                            },
                          }}
                        />
                      </Box>

                      <Box display="flex" justifyContent="space-between" mb={2}>
                        <Chip
                          label={item.status}
                          color={getStatusColor(item.status)}
                          size="small"
                        />
                        <Chip
                          label={item.condition}
                          color={getConditionColor(item.condition)}
                          size="small"
                          variant="outlined"
                        />
                      </Box>

                      <Typography variant="body2" color="text.secondary" mb={2}>
                        Cost: ${item.cost} each
                      </Typography>

                      <Box display="flex" justifyContent="space-between">
                        <Tooltip title="View Details">
                          <IconButton size="small" color="primary">
                            <VisibilityIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit">
                          <IconButton size="small" color="secondary">
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="QR Code">
                          <IconButton size="small" color="info">
                            <QrCodeIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteItem(item)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </CardContent>
                  </InventoryCard>
                </Grid>
              ))}
            </Grid>

            {filteredItems.length === 0 && (
              <Box textAlign="center" py={4}>
                <InventoryIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary">
                  No items found
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Try adjusting your search or filters
                </Typography>
              </Box>
            )}
          </TabPanel>

          {/* Low Stock Tab */}
          <TabPanel value={tabValue} index={1}>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Item</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell>Current Stock</TableCell>
                    <TableCell>Min Required</TableCell>
                    <TableCell>Location</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {inventoryItems
                    .filter(item => item.quantity <= item.minQuantity && item.quantity > 0)
                    .map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center">
                            <Avatar sx={{ mr: 2, bgcolor: 'warning.main' }}>
                              {getCategoryIcon(item.category)}
                            </Avatar>
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {item.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {item.serialNumber}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <CategoryChip
                            category={item.category}
                            label={item.category}
                            size="small"
                            sx={CategoryChip}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={item.quantity}
                            color="warning"
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{item.minQuantity}</TableCell>
                        <TableCell>{item.location}</TableCell>
                        <TableCell>
                          <Box display="flex" gap={1}>
                            <Tooltip title="Reorder">
                              <IconButton size="small" color="primary">
                                <AddIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Edit">
                              <IconButton size="small" color="secondary">
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>

          {/* Out of Stock Tab */}
          <TabPanel value={tabValue} index={2}>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Item</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell>Last Purchase</TableCell>
                    <TableCell>Cost</TableCell>
                    <TableCell>Supplier</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {inventoryItems
                    .filter(item => item.quantity === 0)
                    .map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center">
                            <Avatar sx={{ mr: 2, bgcolor: 'error.main' }}>
                              {getCategoryIcon(item.category)}
                            </Avatar>
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {item.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {item.serialNumber}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <CategoryChip
                            category={item.category}
                            label={item.category}
                            size="small"
                            sx={CategoryChip}
                          />
                        </TableCell>
                        <TableCell>{new Date(item.purchaseDate).toLocaleDateString()}</TableCell>
                        <TableCell>${item.cost}</TableCell>
                        <TableCell>{item.supplier}</TableCell>
                        <TableCell>
                          <Box display="flex" gap={1}>
                            <Tooltip title="Reorder">
                              <IconButton size="small" color="primary">
                                <AddIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Contact Supplier">
                              <IconButton size="small" color="info">
                                <PrintIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>

          {/* Maintenance Tab */}
          <TabPanel value={tabValue} index={3}>
            <Box textAlign="center" py={4}>
              <BuildIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                Maintenance Schedule
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Items requiring maintenance will appear here
              </Typography>
            </Box>
          </TabPanel>
        </StyledPaper>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={cancelDelete}
          aria-labelledby="delete-dialog-title"
          aria-describedby="delete-dialog-description"
        >
          <DialogTitle id="delete-dialog-title" sx={{ display: 'flex', alignItems: 'center' }}>
            <WarningIcon color="error" sx={{ mr: 1 }} />
            Confirm Delete
          </DialogTitle>
          <DialogContent>
            <DialogContentText id="delete-dialog-description">
              Are you sure you want to delete <strong>{itemToDelete?.name}</strong>?
              <br />
              <br />
              This action cannot be undone. All data including:
              <br />
              • Purchase history
              <br />
              • Maintenance records
              <br />
              • Usage logs
              <br />
              <br />
              will be permanently removed from the system.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={cancelDelete} color="primary">
              Cancel
            </Button>
            <Button onClick={confirmDelete} color="error" variant="contained">
              Delete Item
            </Button>
          </DialogActions>
        </Dialog>
      </motion.div>
    </StyledContainer>
  );
};

export default InventoryManagement;
