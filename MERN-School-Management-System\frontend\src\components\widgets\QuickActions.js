import React from 'react';
import { 
  Paper, 
  Typography, 
  Box, 
  Grid, 
  IconButton, 
  Tooltip 
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  PersonAdd as PersonAddIcon,
  School as SchoolIcon,
  Assignment as AssignmentIcon,
  Announcement as AnnouncementIcon,
  Class as ClassIcon,
  SupervisorAccount as SupervisorAccountIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
}));

const ActionButton = styled(motion.div)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  padding: theme.spacing(1.5),
  borderRadius: '12px',
  background: 'rgba(255, 255, 255, 0.1)',
  backdropFilter: 'blur(10px)',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    background: 'rgba(255, 255, 255, 0.2)',
    transform: 'translateY(-2px)',
  },
}));

const QuickActions = () => {
  const navigate = useNavigate();

  const actions = [
    {
      icon: <PersonAddIcon fontSize="large" />,
      label: 'Add Student',
      path: '/Admin/addstudents',
      color: '#4ECDC4'
    },
    {
      icon: <SupervisorAccountIcon fontSize="large" />,
      label: 'Add Teacher',
      path: '/Admin/teachers/chooseclass',
      color: '#45B7D1'
    },
    {
      icon: <ClassIcon fontSize="large" />,
      label: 'Add Class',
      path: '/Admin/addclass',
      color: '#96CEB4'
    },
    {
      icon: <AssignmentIcon fontSize="large" />,
      label: 'Add Subject',
      path: '/Admin/subjects/chooseclass',
      color: '#FFEAA7'
    },
    {
      icon: <AnnouncementIcon fontSize="large" />,
      label: 'Add Notice',
      path: '/Admin/addnotice',
      color: '#DDA0DD'
    },
    {
      icon: <SchoolIcon fontSize="large" />,
      label: 'View Reports',
      path: '/Admin/students',
      color: '#98D8C8'
    },
  ];

  const handleActionClick = (path) => {
    navigate(path);
  };

  return (
    <StyledPaper elevation={3}>
      <Box mb={2}>
        <Typography variant="h6" component="h3" fontWeight="bold">
          ⚡ Quick Actions
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.8 }}>
          Frequently used actions
        </Typography>
      </Box>
      <Grid container spacing={1.5}>
        {actions.map((action, index) => (
          <Grid item xs={6} sm={4} md={2} key={index}>
            <Tooltip title={action.label} arrow>
              <ActionButton
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handleActionClick(action.path)}
              >
                <IconButton
                  sx={{
                    color: action.color,
                    mb: 0.5,
                    '&:hover': { backgroundColor: 'transparent' }
                  }}
                >
                  {action.icon}
                </IconButton>
                <Typography 
                  variant="caption" 
                  align="center" 
                  sx={{ 
                    fontSize: '0.75rem',
                    fontWeight: 500,
                    lineHeight: 1.2
                  }}
                >
                  {action.label}
                </Typography>
              </ActionButton>
            </Tooltip>
          </Grid>
        ))}
      </Grid>
    </StyledPaper>
  );
};

export default QuickActions;
