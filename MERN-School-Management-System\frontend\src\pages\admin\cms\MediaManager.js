import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Menu,
  MenuItem,
  Fab,
  ImageList,
  ImageListItem,
  ImageListItemBar,
  LinearProgress,
  Alert,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  CloudUpload as UploadIcon,
  Image as ImageIcon,
  VideoLibrary as VideoIcon,
  Description as DocumentIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  MoreVert as MoreIcon,
  Add as AddIcon,
  Folder as FolderIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
}));

const MediaCard = styled(Card)(({ theme }) => ({
  borderRadius: '12px',
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
  },
}));

const UploadZone = styled(Box)(({ theme }) => ({
  border: '2px dashed #ccc',
  borderRadius: '12px',
  padding: theme.spacing(4),
  textAlign: 'center',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    borderColor: theme.palette.primary.main,
    backgroundColor: 'rgba(102, 126, 234, 0.05)',
  },
  '&.dragover': {
    borderColor: theme.palette.primary.main,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
  },
}));

const MediaManager = () => {
  const [mediaFiles, setMediaFiles] = useState([
    {
      id: 1,
      name: 'school-logo.png',
      type: 'image',
      size: '45 KB',
      url: '/assets/school-logo.png',
      uploadDate: '2024-01-15',
      folder: 'logos',
    },
    {
      id: 2,
      name: 'login-background.jpg',
      type: 'image',
      size: '2.3 MB',
      url: '/assets/designlogin.jpg',
      uploadDate: '2024-01-14',
      folder: 'backgrounds',
    },
    {
      id: 3,
      name: 'welcome-video.mp4',
      type: 'video',
      size: '15.7 MB',
      url: '/assets/welcome-video.mp4',
      uploadDate: '2024-01-13',
      folder: 'videos',
    },
    {
      id: 4,
      name: 'student-handbook.pdf',
      type: 'document',
      size: '1.2 MB',
      url: '/assets/handbook.pdf',
      uploadDate: '2024-01-12',
      folder: 'documents',
    },
  ]);

  const [folders] = useState([
    { name: 'logos', count: 5 },
    { name: 'backgrounds', count: 8 },
    { name: 'videos', count: 3 },
    { name: 'documents', count: 12 },
    { name: 'icons', count: 15 },
  ]);

  const [selectedFolder, setSelectedFolder] = useState('all');
  const [uploadDialog, setUploadDialog] = useState(false);
  const [editDialog, setEditDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    files.forEach(file => {
      setUploading(true);
      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        setUploadProgress(progress);
        if (progress >= 100) {
          clearInterval(interval);
          setUploading(false);
          setUploadProgress(0);
          
          // Add file to media list
          const newFile = {
            id: mediaFiles.length + 1,
            name: file.name,
            type: getFileType(file.type),
            size: formatFileSize(file.size),
            url: URL.createObjectURL(file),
            uploadDate: new Date().toISOString().split('T')[0],
            folder: selectedFolder === 'all' ? 'uploads' : selectedFolder,
          };
          setMediaFiles(prev => [...prev, newFile]);
        }
      }, 200);
    });
  };

  const getFileType = (mimeType) => {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    return 'document';
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type) => {
    switch (type) {
      case 'image': return <ImageIcon />;
      case 'video': return <VideoIcon />;
      case 'document': return <DocumentIcon />;
      default: return <DocumentIcon />;
    }
  };

  const filteredFiles = mediaFiles.filter(file => {
    const matchesFolder = selectedFolder === 'all' || file.folder === selectedFolder;
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFolder && matchesSearch;
  });

  const handleDeleteFile = (fileId) => {
    setMediaFiles(mediaFiles.filter(file => file.id !== fileId));
    setAnchorEl(null);
  };

  const handleEditFile = (file) => {
    setSelectedFile(file);
    setEditDialog(true);
    setAnchorEl(null);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    const files = Array.from(e.dataTransfer.files);
    // Handle dropped files similar to handleFileUpload
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <StyledPaper sx={{ mb: 4 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                📁 Media Manager
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                Upload and manage images, videos, and documents for your CMS
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<UploadIcon />}
              onClick={() => setUploadDialog(true)}
              sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.3)',
                },
              }}
            >
              Upload Files
            </Button>
          </Box>
        </StyledPaper>
      </motion.div>

      <Grid container spacing={3}>
        {/* Left Sidebar - Folders */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Folders
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Button
                  fullWidth
                  variant={selectedFolder === 'all' ? 'contained' : 'outlined'}
                  onClick={() => setSelectedFolder('all')}
                  sx={{ mb: 1, justifyContent: 'flex-start' }}
                >
                  All Files ({mediaFiles.length})
                </Button>
                
                {folders.map((folder) => (
                  <Button
                    key={folder.name}
                    fullWidth
                    variant={selectedFolder === folder.name ? 'contained' : 'outlined'}
                    onClick={() => setSelectedFolder(folder.name)}
                    startIcon={<FolderIcon />}
                    sx={{ mb: 1, justifyContent: 'flex-start' }}
                  >
                    {folder.name} ({folder.count})
                  </Button>
                ))}
              </Box>

              <Button
                fullWidth
                variant="outlined"
                startIcon={<AddIcon />}
                sx={{ mt: 2 }}
              >
                New Folder
              </Button>
            </CardContent>
          </Card>

          {/* Upload Progress */}
          {uploading && (
            <Card sx={{ mt: 2 }}>
              <CardContent>
                <Typography variant="subtitle2" gutterBottom>
                  Uploading...
                </Typography>
                <LinearProgress variant="determinate" value={uploadProgress} />
                <Typography variant="caption" sx={{ mt: 1 }}>
                  {uploadProgress}% complete
                </Typography>
              </CardContent>
            </Card>
          )}
        </Grid>

        {/* Main Content - File Grid */}
        <Grid item xs={12} md={9}>
          {/* Search and Filter Bar */}
          <Box display="flex" gap={2} mb={3}>
            <TextField
              fullWidth
              placeholder="Search files..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />
            <Button variant="outlined" startIcon={<FilterIcon />}>
              Filter
            </Button>
          </Box>

          {/* File Grid */}
          <ImageList cols={4} gap={16}>
            {filteredFiles.map((file) => (
              <ImageListItem key={file.id}>
                <MediaCard>
                  {file.type === 'image' ? (
                    <CardMedia
                      component="img"
                      height="200"
                      image={file.url}
                      alt={file.name}
                    />
                  ) : (
                    <Box
                      sx={{
                        height: 200,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f5f5f5',
                      }}
                    >
                      {getFileIcon(file.type)}
                    </Box>
                  )}
                  
                  <ImageListItemBar
                    title={file.name}
                    subtitle={
                      <Box>
                        <Typography variant="caption" display="block">
                          {file.size} • {file.uploadDate}
                        </Typography>
                        <Chip
                          label={file.folder}
                          size="small"
                          sx={{ mt: 0.5 }}
                        />
                      </Box>
                    }
                    actionIcon={
                      <IconButton
                        sx={{ color: 'rgba(255, 255, 255, 0.54)' }}
                        onClick={(e) => {
                          setSelectedFile(file);
                          setAnchorEl(e.currentTarget);
                        }}
                      >
                        <MoreIcon />
                      </IconButton>
                    }
                  />
                </MediaCard>
              </ImageListItem>
            ))}
          </ImageList>

          {filteredFiles.length === 0 && (
            <Box textAlign="center" py={8}>
              <ImageIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No files found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {searchTerm ? 'Try adjusting your search terms' : 'Upload some files to get started'}
              </Typography>
            </Box>
          )}
        </Grid>
      </Grid>

      {/* Upload Dialog */}
      <Dialog open={uploadDialog} onClose={() => setUploadDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Upload Files</DialogTitle>
        <DialogContent>
          <UploadZone
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => document.getElementById('file-upload').click()}
          >
            <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Drag and drop files here
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              or click to browse files
            </Typography>
            <Button variant="outlined" sx={{ mt: 2 }}>
              Choose Files
            </Button>
            <input
              id="file-upload"
              type="file"
              multiple
              hidden
              onChange={handleFileUpload}
              accept="image/*,video/*,.pdf,.doc,.docx"
            />
          </UploadZone>
          
          <Alert severity="info" sx={{ mt: 2 }}>
            Supported formats: Images (JPG, PNG, GIF), Videos (MP4, AVI), Documents (PDF, DOC, DOCX)
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialog(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>

      {/* File Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
      >
        <MenuItem onClick={() => handleEditFile(selectedFile)}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={() => navigator.clipboard.writeText(selectedFile?.url)}>
          <DocumentIcon sx={{ mr: 1 }} />
          Copy URL
        </MenuItem>
        <MenuItem onClick={() => handleDeleteFile(selectedFile?.id)} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Edit File Dialog */}
      <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit File</DialogTitle>
        <DialogContent>
          {selectedFile && (
            <Box>
              <TextField
                fullWidth
                label="File Name"
                defaultValue={selectedFile.name}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Alt Text"
                placeholder="Describe this image..."
                margin="normal"
              />
              <TextField
                fullWidth
                label="Caption"
                multiline
                rows={3}
                margin="normal"
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)}>Cancel</Button>
          <Button variant="contained">Save Changes</Button>
        </DialogActions>
      </Dialog>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setUploadDialog(true)}
      >
        <AddIcon />
      </Fab>
    </Box>
  );
};

export default MediaManager;
