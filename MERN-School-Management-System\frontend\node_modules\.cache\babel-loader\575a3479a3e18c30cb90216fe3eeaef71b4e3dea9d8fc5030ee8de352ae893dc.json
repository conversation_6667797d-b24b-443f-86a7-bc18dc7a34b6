{"ast": null, "code": "import { invariant } from 'motion-utils';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { animateVisualElement } from '../interfaces/visual-element.mjs';\nfunction stopAnimation(visualElement) {\n  visualElement.values.forEach(value => value.stop());\n}\nfunction setVariants(visualElement, variantLabels) {\n  const reversedLabels = [...variantLabels].reverse();\n  reversedLabels.forEach(key => {\n    const variant = visualElement.getVariant(key);\n    variant && setTarget(visualElement, variant);\n    if (visualElement.variantChildren) {\n      visualElement.variantChildren.forEach(child => {\n        setVariants(child, variantLabels);\n      });\n    }\n  });\n}\nfunction setValues(visualElement, definition) {\n  if (Array.isArray(definition)) {\n    return setVariants(visualElement, definition);\n  } else if (typeof definition === \"string\") {\n    return setVariants(visualElement, [definition]);\n  } else {\n    setTarget(visualElement, definition);\n  }\n}\n/**\n * @public\n */\nfunction animationControls() {\n  /**\n   * Track whether the host component has mounted.\n   */\n  let hasMounted = false;\n  /**\n   * A collection of linked component animation controls.\n   */\n  const subscribers = new Set();\n  const controls = {\n    subscribe(visualElement) {\n      subscribers.add(visualElement);\n      return () => void subscribers.delete(visualElement);\n    },\n    start(definition, transitionOverride) {\n      invariant(hasMounted, \"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.\");\n      const animations = [];\n      subscribers.forEach(visualElement => {\n        animations.push(animateVisualElement(visualElement, definition, {\n          transitionOverride\n        }));\n      });\n      return Promise.all(animations);\n    },\n    set(definition) {\n      invariant(hasMounted, \"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook.\");\n      return subscribers.forEach(visualElement => {\n        setValues(visualElement, definition);\n      });\n    },\n    stop() {\n      subscribers.forEach(visualElement => {\n        stopAnimation(visualElement);\n      });\n    },\n    mount() {\n      hasMounted = true;\n      return () => {\n        hasMounted = false;\n        controls.stop();\n      };\n    }\n  };\n  return controls;\n}\nexport { animationControls, setValues };", "map": {"version": 3, "names": ["invariant", "<PERSON><PERSON><PERSON><PERSON>", "animateVisualElement", "stopAnimation", "visualElement", "values", "for<PERSON>ach", "value", "stop", "setVariants", "variantLabels", "<PERSON><PERSON><PERSON><PERSON>", "reverse", "key", "variant", "getVariant", "variant<PERSON><PERSON><PERSON>n", "child", "set<PERSON><PERSON><PERSON>", "definition", "Array", "isArray", "animationControls", "hasMounted", "subscribers", "Set", "controls", "subscribe", "add", "delete", "start", "transitionOverride", "animations", "push", "Promise", "all", "set", "mount"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/animation/hooks/animation-controls.mjs"], "sourcesContent": ["import { invariant } from 'motion-utils';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { animateVisualElement } from '../interfaces/visual-element.mjs';\n\nfunction stopAnimation(visualElement) {\n    visualElement.values.forEach((value) => value.stop());\n}\nfunction setVariants(visualElement, variantLabels) {\n    const reversedLabels = [...variantLabels].reverse();\n    reversedLabels.forEach((key) => {\n        const variant = visualElement.getVariant(key);\n        variant && setTarget(visualElement, variant);\n        if (visualElement.variantChildren) {\n            visualElement.variantChildren.forEach((child) => {\n                setVariants(child, variantLabels);\n            });\n        }\n    });\n}\nfunction setValues(visualElement, definition) {\n    if (Array.isArray(definition)) {\n        return setVariants(visualElement, definition);\n    }\n    else if (typeof definition === \"string\") {\n        return setVariants(visualElement, [definition]);\n    }\n    else {\n        setTarget(visualElement, definition);\n    }\n}\n/**\n * @public\n */\nfunction animationControls() {\n    /**\n     * Track whether the host component has mounted.\n     */\n    let hasMounted = false;\n    /**\n     * A collection of linked component animation controls.\n     */\n    const subscribers = new Set();\n    const controls = {\n        subscribe(visualElement) {\n            subscribers.add(visualElement);\n            return () => void subscribers.delete(visualElement);\n        },\n        start(definition, transitionOverride) {\n            invariant(hasMounted, \"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.\");\n            const animations = [];\n            subscribers.forEach((visualElement) => {\n                animations.push(animateVisualElement(visualElement, definition, {\n                    transitionOverride,\n                }));\n            });\n            return Promise.all(animations);\n        },\n        set(definition) {\n            invariant(hasMounted, \"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook.\");\n            return subscribers.forEach((visualElement) => {\n                setValues(visualElement, definition);\n            });\n        },\n        stop() {\n            subscribers.forEach((visualElement) => {\n                stopAnimation(visualElement);\n            });\n        },\n        mount() {\n            hasMounted = true;\n            return () => {\n                hasMounted = false;\n                controls.stop();\n            };\n        },\n    };\n    return controls;\n}\n\nexport { animationControls, setValues };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,oBAAoB,QAAQ,kCAAkC;AAEvE,SAASC,aAAaA,CAACC,aAAa,EAAE;EAClCA,aAAa,CAACC,MAAM,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAE,CAAC;AACzD;AACA,SAASC,WAAWA,CAACL,aAAa,EAAEM,aAAa,EAAE;EAC/C,MAAMC,cAAc,GAAG,CAAC,GAAGD,aAAa,CAAC,CAACE,OAAO,EAAE;EACnDD,cAAc,CAACL,OAAO,CAAEO,GAAG,IAAK;IAC5B,MAAMC,OAAO,GAAGV,aAAa,CAACW,UAAU,CAACF,GAAG,CAAC;IAC7CC,OAAO,IAAIb,SAAS,CAACG,aAAa,EAAEU,OAAO,CAAC;IAC5C,IAAIV,aAAa,CAACY,eAAe,EAAE;MAC/BZ,aAAa,CAACY,eAAe,CAACV,OAAO,CAAEW,KAAK,IAAK;QAC7CR,WAAW,CAACQ,KAAK,EAAEP,aAAa,CAAC;MACrC,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;AACN;AACA,SAASQ,SAASA,CAACd,aAAa,EAAEe,UAAU,EAAE;EAC1C,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;IAC3B,OAAOV,WAAW,CAACL,aAAa,EAAEe,UAAU,CAAC;EACjD,CAAC,MACI,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IACrC,OAAOV,WAAW,CAACL,aAAa,EAAE,CAACe,UAAU,CAAC,CAAC;EACnD,CAAC,MACI;IACDlB,SAAS,CAACG,aAAa,EAAEe,UAAU,CAAC;EACxC;AACJ;AACA;AACA;AACA;AACA,SAASG,iBAAiBA,CAAA,EAAG;EACzB;AACJ;AACA;EACI,IAAIC,UAAU,GAAG,KAAK;EACtB;AACJ;AACA;EACI,MAAMC,WAAW,GAAG,IAAIC,GAAG,EAAE;EAC7B,MAAMC,QAAQ,GAAG;IACbC,SAASA,CAACvB,aAAa,EAAE;MACrBoB,WAAW,CAACI,GAAG,CAACxB,aAAa,CAAC;MAC9B,OAAO,MAAM,KAAKoB,WAAW,CAACK,MAAM,CAACzB,aAAa,CAAC;IACvD,CAAC;IACD0B,KAAKA,CAACX,UAAU,EAAEY,kBAAkB,EAAE;MAClC/B,SAAS,CAACuB,UAAU,EAAE,iHAAiH,CAAC;MACxI,MAAMS,UAAU,GAAG,EAAE;MACrBR,WAAW,CAAClB,OAAO,CAAEF,aAAa,IAAK;QACnC4B,UAAU,CAACC,IAAI,CAAC/B,oBAAoB,CAACE,aAAa,EAAEe,UAAU,EAAE;UAC5DY;QACJ,CAAC,CAAC,CAAC;MACP,CAAC,CAAC;MACF,OAAOG,OAAO,CAACC,GAAG,CAACH,UAAU,CAAC;IAClC,CAAC;IACDI,GAAGA,CAACjB,UAAU,EAAE;MACZnB,SAAS,CAACuB,UAAU,EAAE,+GAA+G,CAAC;MACtI,OAAOC,WAAW,CAAClB,OAAO,CAAEF,aAAa,IAAK;QAC1Cc,SAAS,CAACd,aAAa,EAAEe,UAAU,CAAC;MACxC,CAAC,CAAC;IACN,CAAC;IACDX,IAAIA,CAAA,EAAG;MACHgB,WAAW,CAAClB,OAAO,CAAEF,aAAa,IAAK;QACnCD,aAAa,CAACC,aAAa,CAAC;MAChC,CAAC,CAAC;IACN,CAAC;IACDiC,KAAKA,CAAA,EAAG;MACJd,UAAU,GAAG,IAAI;MACjB,OAAO,MAAM;QACTA,UAAU,GAAG,KAAK;QAClBG,QAAQ,CAAClB,IAAI,EAAE;MACnB,CAAC;IACL;EACJ,CAAC;EACD,OAAOkB,QAAQ;AACnB;AAEA,SAASJ,iBAAiB,EAAEJ,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}