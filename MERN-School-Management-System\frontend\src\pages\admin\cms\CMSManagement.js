import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Tabs,
  Tab,
  IconButton,
  Chip,
  Avatar,
  Divider,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  CardActions,
  Rating,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  Web as WebIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Preview as PreviewIcon,
  Image as ImageIcon,
  Palette as PaletteIcon,
  Settings as SettingsIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Login as LoginIcon,
  School as SchoolIcon,
  Security as SecurityIcon,
  Language as LanguageIcon,
  Notifications as NotificationsIcon,
  Star as StarIcon,
  Photo as PhotoIcon,
  Article as ArticleIcon,
  Person as PersonIcon,
  Create as CreateIcon,
} from '@mui/icons-material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
}));

const ContentCard = styled(Card)(({ theme }) => ({
  borderRadius: '12px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
  },
}));

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`cms-tabpanel-${index}`}
    aria-labelledby={`cms-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const CMSManagement = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);
  const [previewMode, setPreviewMode] = useState(false);
  const [saveDialog, setSaveDialog] = useState(false);
  const [loginSettings, setLoginSettings] = useState({
    title: 'Amar Vidya Mandir',
    subtitle: 'Welcome back! Please enter your details',
    backgroundImage: '/assets/designlogin.jpg',
    logoUrl: '/assets/school-logo.png',
    primaryColor: '#667eea',
    secondaryColor: '#764ba2',
    enableRememberMe: true,
    enableForgotPassword: true,
    enableRegistration: false,
    customCSS: '',
    footerText: '© 2024 Amar Vidya Mandir. All rights reserved.',
    enableSocialLogin: false,
    maintenanceMode: false,
    customMessage: '',
    heroTitle: 'Empowering Minds, Shaping Future Leaders',
    heroSubtitle: 'Discover a dynamic educational experience with interactive visuals, engaging content, and personalized learning paths designed to inspire and empower students.',
    heroImage: '',
    showHeroSection: true,
    contactInfo: {
      address: 'Partaj, Anantapuram, AP, India',
      phone: '7799505005',
      alternatePhone: '9866358067',
      email: '<EMAIL>',
      showContact: true
    }
  });

  const [testimonials, setTestimonials] = useState([
    {
      id: 1,
      name: 'Aisha Khan',
      role: 'Student',
      message: "Amar Vidya Mandir's interactive platform transformed my learning experience. The engaging visuals and personalized content made complex topics easy to understand. I highly recommend it to any student seeking academic success.",
      rating: 5,
      image: '',
      isActive: true,
      order: 0
    }
  ]);

  const [galleryItems, setGalleryItems] = useState([
    {
      id: 1,
      title: 'The Future of Education',
      description: 'Exploring innovative teaching methods',
      image: '',
      category: 'education',
      isActive: true,
      order: 0
    },
    {
      id: 2,
      title: 'Tips for Parents',
      description: 'How to support your child\'s learning',
      image: '',
      category: 'tips',
      isActive: true,
      order: 1
    },
    {
      id: 3,
      title: 'Effective Study Strategies',
      description: 'Proven methods for academic success',
      image: '',
      category: 'education',
      isActive: true,
      order: 2
    }
  ]);

  const [blogPosts, setBlogPosts] = useState([
    {
      id: 1,
      title: 'The Future of Education',
      excerpt: 'Exploring innovative teaching methods...',
      content: 'Full content here...',
      image: '',
      author: 'admin',
      category: 'education',
      isPublished: true,
      publishedDate: new Date('2024-10-25')
    },
    {
      id: 2,
      title: 'Tips for Parents',
      excerpt: 'How to support your child\'s learning...',
      content: 'Full content here...',
      image: '',
      author: 'admin',
      category: 'tips',
      isPublished: true,
      publishedDate: new Date('2024-10-25')
    },
    {
      id: 3,
      title: 'Effective Study Strategies',
      excerpt: 'Proven methods for academic success...',
      content: 'Full content here...',
      image: '',
      author: 'admin',
      category: 'education',
      isPublished: true,
      publishedDate: new Date('2024-10-26')
    }
  ]);

  const [themeSettings, setThemeSettings] = useState({
    darkMode: false,
    primaryColor: '#667eea',
    secondaryColor: '#764ba2',
    fontFamily: 'Roboto',
    borderRadius: 8,
    customTheme: false,
  });

  const [securitySettings, setSecuritySettings] = useState({
    enableCaptcha: false,
    maxLoginAttempts: 5,
    lockoutDuration: 30,
    enableTwoFactor: false,
    passwordComplexity: 'medium',
    sessionTimeout: 60,
  });

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleLoginSettingChange = (field, value) => {
    setLoginSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleThemeSettingChange = (field, value) => {
    setThemeSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSecuritySettingChange = (field, value) => {
    setSecuritySettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveSettings = () => {
    // Here you would save the settings to your backend
    console.log('Saving settings:', { loginSettings, themeSettings, securitySettings });
    setSaveDialog(false);
    // Show success message
  };

  const handleImageUpload = (event, field) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        handleLoginSettingChange(field, e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <StyledPaper sx={{ mb: 4 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                🎨 Content Management System
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                Customize your login page, themes, and system settings
              </Typography>
            </Box>
            <Box display="flex" gap={2}>
              <Button
                variant="outlined"
                startIcon={<PreviewIcon />}
                onClick={() => setPreviewMode(!previewMode)}
                sx={{
                  color: 'white',
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                  '&:hover': {
                    borderColor: 'white',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  },
                }}
              >
                {previewMode ? 'Edit Mode' : 'Preview'}
              </Button>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={() => setSaveDialog(true)}
                sx={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                  },
                }}
              >
                Save Changes
              </Button>
            </Box>
          </Box>
        </StyledPaper>
      </motion.div>

      {/* Quick Access Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <ContentCard>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <LoginIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                <Box>
                  <Typography variant="h6">Login Page</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Customize login appearance and functionality
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" sx={{ mb: 2 }}>
                Control colors, branding, features, and security settings for your login page.
              </Typography>
            </CardContent>
            <CardActions>
              <Button size="small" onClick={() => setActiveTab(0)}>
                Customize
              </Button>
              <Button size="small" onClick={() => window.open('/login', '_blank')}>
                Preview
              </Button>
            </CardActions>
          </ContentCard>
        </Grid>

        <Grid item xs={12} md={4}>
          <ContentCard>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <SettingsIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                <Box>
                  <Typography variant="h6">Page Builder</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Create and edit custom pages
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" sx={{ mb: 2 }}>
                Build custom pages with drag-and-drop components and templates.
              </Typography>
            </CardContent>
            <CardActions>
              <Button size="small" onClick={() => navigate('/Admin/cms/pages')}>
                Open Builder
              </Button>
              <Button size="small">
                Templates
              </Button>
            </CardActions>
          </ContentCard>
        </Grid>

        <Grid item xs={12} md={4}>
          <ContentCard>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <ImageIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                <Box>
                  <Typography variant="h6">Media Library</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Manage images, videos, and documents
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" sx={{ mb: 2 }}>
                Upload and organize media files for use across your website.
              </Typography>
            </CardContent>
            <CardActions>
              <Button size="small" onClick={() => navigate('/Admin/cms/media')}>
                Open Library
              </Button>
              <Button size="small">
                Upload
              </Button>
            </CardActions>
          </ContentCard>
        </Grid>
      </Grid>

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Left Panel - Settings */}
        <Grid item xs={12} md={8}>
          <ContentCard>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={activeTab} onChange={handleTabChange} variant="scrollable" scrollButtons="auto">
                <Tab icon={<LoginIcon />} label="Login Page" />
                <Tab icon={<StarIcon />} label="Testimonials" />
                <Tab icon={<PhotoIcon />} label="Gallery" />
                <Tab icon={<ArticleIcon />} label="Blog" />
                <Tab icon={<PaletteIcon />} label="Theme" />
                <Tab icon={<SecurityIcon />} label="Security" />
                <Tab icon={<LanguageIcon />} label="Content" />
              </Tabs>
            </Box>

            {/* Login Page Settings */}
            <TabPanel value={activeTab} index={0}>
              <Typography variant="h6" gutterBottom>
                Login Page Customization
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Page Title"
                    value={loginSettings.title}
                    onChange={(e) => handleLoginSettingChange('title', e.target.value)}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Subtitle"
                    value={loginSettings.subtitle}
                    onChange={(e) => handleLoginSettingChange('subtitle', e.target.value)}
                    margin="normal"
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Background Image
                    </Typography>
                    <Box display="flex" gap={2} alignItems="center">
                      <Button
                        variant="outlined"
                        component="label"
                        startIcon={<ImageIcon />}
                      >
                        Upload Image
                        <input
                          type="file"
                          hidden
                          accept="image/*"
                          onChange={(e) => handleImageUpload(e, 'backgroundImage')}
                        />
                      </Button>
                      {loginSettings.backgroundImage && (
                        <Chip
                          label="Image uploaded"
                          color="success"
                          size="small"
                        />
                      )}
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Primary Color"
                    type="color"
                    value={loginSettings.primaryColor}
                    onChange={(e) => handleLoginSettingChange('primaryColor', e.target.value)}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Secondary Color"
                    type="color"
                    value={loginSettings.secondaryColor}
                    onChange={(e) => handleLoginSettingChange('secondaryColor', e.target.value)}
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                    Login Features
                  </Typography>
                  <Box display="flex" flexDirection="column" gap={1}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={loginSettings.enableRememberMe}
                          onChange={(e) => handleLoginSettingChange('enableRememberMe', e.target.checked)}
                        />
                      }
                      label="Enable Remember Me"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={loginSettings.enableForgotPassword}
                          onChange={(e) => handleLoginSettingChange('enableForgotPassword', e.target.checked)}
                        />
                      }
                      label="Enable Forgot Password"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={loginSettings.enableRegistration}
                          onChange={(e) => handleLoginSettingChange('enableRegistration', e.target.checked)}
                        />
                      }
                      label="Enable Registration"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={loginSettings.maintenanceMode}
                          onChange={(e) => handleLoginSettingChange('maintenanceMode', e.target.checked)}
                        />
                      }
                      label="Maintenance Mode"
                    />
                  </Box>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Testimonials Management */}
            <TabPanel value={activeTab} index={1}>
              <Typography variant="h6" gutterBottom>
                Testimonials Management
              </Typography>

              <Box sx={{ mb: 3 }}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => {
                    const newTestimonial = {
                      id: Date.now(),
                      name: '',
                      role: 'Student',
                      message: '',
                      rating: 5,
                      image: '',
                      isActive: true,
                      order: testimonials.length
                    };
                    setTestimonials([...testimonials, newTestimonial]);
                  }}
                  sx={{ mb: 2 }}
                >
                  Add Testimonial
                </Button>
              </Box>

              <Grid container spacing={3}>
                {testimonials.map((testimonial, index) => (
                  <Grid item xs={12} md={6} key={testimonial.id}>
                    <Card sx={{ p: 2 }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="h6">Testimonial {index + 1}</Typography>
                          <IconButton
                            color="error"
                            onClick={() => {
                              setTestimonials(testimonials.filter(t => t.id !== testimonial.id));
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>

                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="Name"
                              value={testimonial.name}
                              onChange={(e) => {
                                const updated = testimonials.map(t =>
                                  t.id === testimonial.id ? { ...t, name: e.target.value } : t
                                );
                                setTestimonials(updated);
                              }}
                              margin="normal"
                            />
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <FormControl fullWidth margin="normal">
                              <InputLabel>Role</InputLabel>
                              <Select
                                value={testimonial.role}
                                onChange={(e) => {
                                  const updated = testimonials.map(t =>
                                    t.id === testimonial.id ? { ...t, role: e.target.value } : t
                                  );
                                  setTestimonials(updated);
                                }}
                              >
                                <MenuItem value="Student">Student</MenuItem>
                                <MenuItem value="Parent">Parent</MenuItem>
                                <MenuItem value="Teacher">Teacher</MenuItem>
                                <MenuItem value="Alumni">Alumni</MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>
                          <Grid item xs={12}>
                            <TextField
                              fullWidth
                              multiline
                              rows={3}
                              label="Message"
                              value={testimonial.message}
                              onChange={(e) => {
                                const updated = testimonials.map(t =>
                                  t.id === testimonial.id ? { ...t, message: e.target.value } : t
                                );
                                setTestimonials(updated);
                              }}
                              margin="normal"
                            />
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ mt: 2 }}>
                              <Typography component="legend">Rating</Typography>
                              <Rating
                                value={testimonial.rating}
                                onChange={(event, newValue) => {
                                  const updated = testimonials.map(t =>
                                    t.id === testimonial.id ? { ...t, rating: newValue } : t
                                  );
                                  setTestimonials(updated);
                                }}
                              />
                            </Box>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="Image URL"
                              value={testimonial.image}
                              onChange={(e) => {
                                const updated = testimonials.map(t =>
                                  t.id === testimonial.id ? { ...t, image: e.target.value } : t
                                );
                                setTestimonials(updated);
                              }}
                              margin="normal"
                            />
                          </Grid>
                          <Grid item xs={12}>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={testimonial.isActive}
                                  onChange={(e) => {
                                    const updated = testimonials.map(t =>
                                      t.id === testimonial.id ? { ...t, isActive: e.target.checked } : t
                                    );
                                    setTestimonials(updated);
                                  }}
                                />
                              }
                              label="Active"
                            />
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </TabPanel>

            {/* Gallery Management */}
            <TabPanel value={activeTab} index={2}>
              <Typography variant="h6" gutterBottom>
                Gallery Management
              </Typography>

              <Box sx={{ mb: 3 }}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => {
                    const newGalleryItem = {
                      id: Date.now(),
                      title: '',
                      description: '',
                      image: '',
                      category: 'education',
                      isActive: true,
                      order: galleryItems.length
                    };
                    setGalleryItems([...galleryItems, newGalleryItem]);
                  }}
                  sx={{ mb: 2 }}
                >
                  Add Gallery Item
                </Button>
              </Box>

              <Grid container spacing={3}>
                {galleryItems.map((item, index) => (
                  <Grid item xs={12} md={6} key={item.id}>
                    <Card sx={{ p: 2 }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="h6">Gallery Item {index + 1}</Typography>
                          <IconButton
                            color="error"
                            onClick={() => {
                              setGalleryItems(galleryItems.filter(g => g.id !== item.id));
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>

                        <Grid container spacing={2}>
                          <Grid item xs={12}>
                            <TextField
                              fullWidth
                              label="Title"
                              value={item.title}
                              onChange={(e) => {
                                const updated = galleryItems.map(g =>
                                  g.id === item.id ? { ...g, title: e.target.value } : g
                                );
                                setGalleryItems(updated);
                              }}
                              margin="normal"
                            />
                          </Grid>
                          <Grid item xs={12}>
                            <TextField
                              fullWidth
                              label="Description"
                              value={item.description}
                              onChange={(e) => {
                                const updated = galleryItems.map(g =>
                                  g.id === item.id ? { ...g, description: e.target.value } : g
                                );
                                setGalleryItems(updated);
                              }}
                              margin="normal"
                            />
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="Image URL"
                              value={item.image}
                              onChange={(e) => {
                                const updated = galleryItems.map(g =>
                                  g.id === item.id ? { ...g, image: e.target.value } : g
                                );
                                setGalleryItems(updated);
                              }}
                              margin="normal"
                            />
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <FormControl fullWidth margin="normal">
                              <InputLabel>Category</InputLabel>
                              <Select
                                value={item.category}
                                onChange={(e) => {
                                  const updated = galleryItems.map(g =>
                                    g.id === item.id ? { ...g, category: e.target.value } : g
                                  );
                                  setGalleryItems(updated);
                                }}
                              >
                                <MenuItem value="education">Education</MenuItem>
                                <MenuItem value="events">Events</MenuItem>
                                <MenuItem value="facilities">Facilities</MenuItem>
                                <MenuItem value="achievements">Achievements</MenuItem>
                                <MenuItem value="activities">Activities</MenuItem>
                                <MenuItem value="campus">Campus</MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>
                          <Grid item xs={12}>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={item.isActive}
                                  onChange={(e) => {
                                    const updated = galleryItems.map(g =>
                                      g.id === item.id ? { ...g, isActive: e.target.checked } : g
                                    );
                                    setGalleryItems(updated);
                                  }}
                                />
                              }
                              label="Active"
                            />
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </TabPanel>

            {/* Blog Management */}
            <TabPanel value={activeTab} index={3}>
              <Typography variant="h6" gutterBottom>
                Blog Management
              </Typography>

              <Box sx={{ mb: 3 }}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => {
                    const newBlogPost = {
                      id: Date.now(),
                      title: '',
                      excerpt: '',
                      content: '',
                      image: '',
                      author: 'admin',
                      category: 'education',
                      isPublished: true,
                      publishedDate: new Date()
                    };
                    setBlogPosts([...blogPosts, newBlogPost]);
                  }}
                  sx={{ mb: 2 }}
                >
                  Add Blog Post
                </Button>
              </Box>

              <Grid container spacing={3}>
                {blogPosts.map((post, index) => (
                  <Grid item xs={12} key={post.id}>
                    <Card sx={{ p: 2 }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="h6">Blog Post {index + 1}</Typography>
                          <IconButton
                            color="error"
                            onClick={() => {
                              setBlogPosts(blogPosts.filter(b => b.id !== post.id));
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>

                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6}>
                            <TextField
                              fullWidth
                              label="Title"
                              value={post.title}
                              onChange={(e) => {
                                const updated = blogPosts.map(b =>
                                  b.id === post.id ? { ...b, title: e.target.value } : b
                                );
                                setBlogPosts(updated);
                              }}
                              margin="normal"
                            />
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <TextField
                              fullWidth
                              label="Author"
                              value={post.author}
                              onChange={(e) => {
                                const updated = blogPosts.map(b =>
                                  b.id === post.id ? { ...b, author: e.target.value } : b
                                );
                                setBlogPosts(updated);
                              }}
                              margin="normal"
                            />
                          </Grid>
                          <Grid item xs={12}>
                            <TextField
                              fullWidth
                              label="Excerpt"
                              value={post.excerpt}
                              onChange={(e) => {
                                const updated = blogPosts.map(b =>
                                  b.id === post.id ? { ...b, excerpt: e.target.value } : b
                                );
                                setBlogPosts(updated);
                              }}
                              margin="normal"
                            />
                          </Grid>
                          <Grid item xs={12}>
                            <TextField
                              fullWidth
                              multiline
                              rows={4}
                              label="Content"
                              value={post.content}
                              onChange={(e) => {
                                const updated = blogPosts.map(b =>
                                  b.id === post.id ? { ...b, content: e.target.value } : b
                                );
                                setBlogPosts(updated);
                              }}
                              margin="normal"
                            />
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="Image URL"
                              value={post.image}
                              onChange={(e) => {
                                const updated = blogPosts.map(b =>
                                  b.id === post.id ? { ...b, image: e.target.value } : b
                                );
                                setBlogPosts(updated);
                              }}
                              margin="normal"
                            />
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <FormControl fullWidth margin="normal">
                              <InputLabel>Category</InputLabel>
                              <Select
                                value={post.category}
                                onChange={(e) => {
                                  const updated = blogPosts.map(b =>
                                    b.id === post.id ? { ...b, category: e.target.value } : b
                                  );
                                  setBlogPosts(updated);
                                }}
                              >
                                <MenuItem value="education">Education</MenuItem>
                                <MenuItem value="tips">Tips</MenuItem>
                                <MenuItem value="news">News</MenuItem>
                                <MenuItem value="events">Events</MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>
                          <Grid item xs={12}>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={post.isPublished}
                                  onChange={(e) => {
                                    const updated = blogPosts.map(b =>
                                      b.id === post.id ? { ...b, isPublished: e.target.checked } : b
                                    );
                                    setBlogPosts(updated);
                                  }}
                                />
                              }
                              label="Published"
                            />
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </TabPanel>

            {/* Theme Settings */}
            <TabPanel value={activeTab} index={4}>
              <Typography variant="h6" gutterBottom>
                Theme Configuration
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Font Family</InputLabel>
                    <Select
                      value={themeSettings.fontFamily}
                      onChange={(e) => handleThemeSettingChange('fontFamily', e.target.value)}
                    >
                      <MenuItem value="Roboto">Roboto</MenuItem>
                      <MenuItem value="Arial">Arial</MenuItem>
                      <MenuItem value="Helvetica">Helvetica</MenuItem>
                      <MenuItem value="Times New Roman">Times New Roman</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Border Radius"
                    type="number"
                    value={themeSettings.borderRadius}
                    onChange={(e) => handleThemeSettingChange('borderRadius', parseInt(e.target.value))}
                    margin="normal"
                    InputProps={{ endAdornment: 'px' }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={themeSettings.darkMode}
                        onChange={(e) => handleThemeSettingChange('darkMode', e.target.checked)}
                      />
                    }
                    label="Enable Dark Mode"
                  />
                </Grid>
              </Grid>
            </TabPanel>

            {/* Security Settings */}
            <TabPanel value={activeTab} index={5}>
              <Typography variant="h6" gutterBottom>
                Security Configuration
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Max Login Attempts"
                    type="number"
                    value={securitySettings.maxLoginAttempts}
                    onChange={(e) => handleSecuritySettingChange('maxLoginAttempts', parseInt(e.target.value))}
                    margin="normal"
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Lockout Duration (minutes)"
                    type="number"
                    value={securitySettings.lockoutDuration}
                    onChange={(e) => handleSecuritySettingChange('lockoutDuration', parseInt(e.target.value))}
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box display="flex" flexDirection="column" gap={1}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={securitySettings.enableCaptcha}
                          onChange={(e) => handleSecuritySettingChange('enableCaptcha', e.target.checked)}
                        />
                      }
                      label="Enable CAPTCHA"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={securitySettings.enableTwoFactor}
                          onChange={(e) => handleSecuritySettingChange('enableTwoFactor', e.target.checked)}
                        />
                      }
                      label="Enable Two-Factor Authentication"
                    />
                  </Box>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Content Settings */}
            <TabPanel value={activeTab} index={6}>
              <Typography variant="h6" gutterBottom>
                Content Management
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Footer Text"
                    value={loginSettings.footerText}
                    onChange={(e) => handleLoginSettingChange('footerText', e.target.value)}
                    margin="normal"
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Custom Message"
                    multiline
                    rows={4}
                    value={loginSettings.customMessage}
                    onChange={(e) => handleLoginSettingChange('customMessage', e.target.value)}
                    margin="normal"
                    placeholder="Enter any custom message to display on the login page"
                  />
                </Grid>
              </Grid>
            </TabPanel>
          </ContentCard>
        </Grid>

        {/* Right Panel - Preview */}
        <Grid item xs={12} md={4}>
          <ContentCard>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Live Preview
              </Typography>
              <Box
                sx={{
                  border: '2px solid #e0e0e0',
                  borderRadius: '8px',
                  p: 2,
                  minHeight: '400px',
                  background: `linear-gradient(135deg, ${loginSettings.primaryColor}, ${loginSettings.secondaryColor})`,
                  color: 'white',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  textAlign: 'center',
                }}
              >
                <SchoolIcon sx={{ fontSize: 48, mb: 2 }} />
                <Typography variant="h5" gutterBottom>
                  {loginSettings.title}
                </Typography>
                <Typography variant="body2" sx={{ mb: 3, opacity: 0.9 }}>
                  {loginSettings.subtitle}
                </Typography>
                
                <Box sx={{ width: '100%', maxWidth: 300 }}>
                  <TextField
                    fullWidth
                    placeholder="Email"
                    size="small"
                    sx={{ mb: 2, bgcolor: 'rgba(255,255,255,0.9)', borderRadius: 1 }}
                  />
                  <TextField
                    fullWidth
                    placeholder="Password"
                    type="password"
                    size="small"
                    sx={{ mb: 2, bgcolor: 'rgba(255,255,255,0.9)', borderRadius: 1 }}
                  />
                  
                  {loginSettings.enableRememberMe && (
                    <FormControlLabel
                      control={<Switch size="small" />}
                      label="Remember me"
                      sx={{ mb: 2, fontSize: '0.8rem' }}
                    />
                  )}
                  
                  <Button
                    fullWidth
                    variant="contained"
                    sx={{
                      bgcolor: 'rgba(255,255,255,0.2)',
                      '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' }
                    }}
                  >
                    Login
                  </Button>
                  
                  {loginSettings.enableForgotPassword && (
                    <Typography variant="caption" sx={{ mt: 1, display: 'block', opacity: 0.8 }}>
                      Forgot password?
                    </Typography>
                  )}
                </Box>
                
                {loginSettings.customMessage && (
                  <Alert severity="info" sx={{ mt: 2, width: '100%' }}>
                    {loginSettings.customMessage}
                  </Alert>
                )}
              </Box>
            </CardContent>
          </ContentCard>
        </Grid>
      </Grid>

      {/* Save Dialog */}
      <Dialog open={saveDialog} onClose={() => setSaveDialog(false)}>
        <DialogTitle>Save Changes</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to save all changes? This will update the login page and system settings.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveSettings} variant="contained">
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CMSManagement;
