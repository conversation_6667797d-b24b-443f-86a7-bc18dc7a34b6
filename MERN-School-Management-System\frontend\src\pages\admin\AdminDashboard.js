import React, { useState } from 'react';
import {
    CssBaseline,
    Box,
    Toolbar,
    Typography,
    IconButton,
    useTheme,
    useMediaQuery,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import { Navigate, Route, Routes } from 'react-router-dom';
import { AppBar } from '../../components/styles';

// Import new components
import { ThemeProvider } from '../../components/theme/ThemeProvider';
import ThemeToggle from '../../components/theme/ThemeToggle';
import ResponsiveSidebar from '../../components/layout/ResponsiveSidebar';
import NotificationCenter from '../../components/widgets/NotificationCenter';

// Import existing components
import Logout from '../Logout';
import AdminProfile from './AdminProfile';
import AdminSettings from './AdminSettings';
import AdminHomePage from './AdminHomePage';

// Import new module components
import StudentInfo from './studentInfo/StudentInfo';
import FeeManagement from './feeManagement/FeeManagement';
import DocumentGeneration from './documents/DocumentGeneration';
import TransportManagement from './transport/TransportManagement';
import AccountMenu from '../../components/AccountMenu';

import AddStudent from './studentRelated/AddStudent';
import SeeComplains from './studentRelated/SeeComplains';
import ShowStudents from './studentRelated/ShowStudents';
import StudentAttendance from './studentRelated/StudentAttendance';
import StudentExamMarks from './studentRelated/StudentExamMarks';
import ViewStudent from './studentRelated/ViewStudent';
import EnhancedViewStudent from './studentRelated/EnhancedViewStudent';

import AddNotice from './noticeRelated/AddNotice';
import ShowNotices from './noticeRelated/ShowNotices';

import ShowSubjects from './subjectRelated/ShowSubjects';
import SubjectForm from './subjectRelated/SubjectForm';
import ViewSubject from './subjectRelated/ViewSubject';

import AddTeacher from './teacherRelated/AddTeacher';
import ChooseClass from './teacherRelated/ChooseClass';
import ChooseSubject from './teacherRelated/ChooseSubject';
import ShowTeachers from './teacherRelated/ShowTeachers';
import TeacherDetails from './teacherRelated/TeacherDetails';

import AddClass from './classRelated/AddClass';
import ClassDetails from './classRelated/ClassDetails';
import ShowClasses from './classRelated/ShowClasses';

const AdminDashboardContent = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));
    const [sidebarOpen, setSidebarOpen] = useState(!isMobile);

    const handleSidebarToggle = () => {
        setSidebarOpen(!sidebarOpen);
    };

    return (
        <>
            <Box sx={{ display: 'flex' }}>
                <CssBaseline />
                <AppBar
                    position="fixed"
                    sx={{
                        zIndex: theme.zIndex.drawer + 1,
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                    }}
                >
                    <Toolbar>
                        <IconButton
                            color="inherit"
                            aria-label="toggle sidebar"
                            onClick={handleSidebarToggle}
                            edge="start"
                            sx={{
                                mr: 2,
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                    transform: 'scale(1.1)',
                                }
                            }}
                        >
                            <MenuIcon />
                        </IconButton>

                        <Typography
                            variant="h6"
                            noWrap
                            component="div"
                            sx={{
                                flexGrow: 1,
                                fontWeight: 600,
                                background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                                backgroundClip: 'text',
                                WebkitBackgroundClip: 'text',
                                WebkitTextFillColor: 'transparent',
                            }}
                        >
                            🎓 School Management System
                        </Typography>

                        {/* Theme Toggle */}
                        <ThemeToggle />

                        {/* Notification Center */}
                        <NotificationCenter />

                        {/* Account Menu */}
                        <AccountMenu />
                    </Toolbar>
                </AppBar>
                {/* Enhanced Sidebar */}
                <ResponsiveSidebar
                    open={sidebarOpen}
                    onClose={() => setSidebarOpen(false)}
                    variant={isMobile ? 'temporary' : 'permanent'}
                />

                {/* Main Content */}
                <Box
                    component="main"
                    sx={{
                        flexGrow: 1,
                        p: { xs: 1, sm: 1.5, md: 2 }, // Reduced padding
                        width: { sm: `calc(100% - ${sidebarOpen ? 280 : 70}px)` },
                        ml: { sm: sidebarOpen ? '280px' : '70px' },
                        transition: theme.transitions.create(['margin', 'width'], {
                            easing: theme.transitions.easing.sharp,
                            duration: theme.transitions.duration.leavingScreen,
                        }),
                        backgroundColor: theme.palette.background.default,
                        minHeight: '100vh',
                        pt: { xs: '72px', sm: '80px', md: '88px' }, // Increased padding for different screen sizes
                        position: 'relative',
                    }}
                >
                    <Routes>
                        <Route path="/" element={<AdminHomePage />} />
                        <Route path='*' element={<Navigate to="/" />} />
                        <Route path="/Admin/dashboard" element={<AdminHomePage />} />
                        <Route path="/Admin/profile" element={<AdminProfile />} />
                        <Route path="/Admin/settings" element={<AdminSettings />} />
                        <Route path="/Admin/complains" element={<SeeComplains />} />

                        {/* Notice */}
                        <Route path="/Admin/addnotice" element={<AddNotice />} />
                        <Route path="/Admin/notices" element={<ShowNotices />} />

                        {/* Subject */}
                        <Route path="/Admin/subjects" element={<ShowSubjects />} />
                        <Route path="/Admin/subjects/subject/:classID/:subjectID" element={<ViewSubject />} />
                        <Route path="/Admin/subjects/chooseclass" element={<ChooseClass situation="Subject" />} />

                        <Route path="/Admin/addsubject/:id" element={<SubjectForm />} />
                        <Route path="/Admin/class/subject/:classID/:subjectID" element={<ViewSubject />} />

                        <Route path="/Admin/subject/student/attendance/:studentID/:subjectID" element={<StudentAttendance situation="Subject" />} />
                        <Route path="/Admin/subject/student/marks/:studentID/:subjectID" element={<StudentExamMarks situation="Subject" />} />

                        {/* Class */}
                        <Route path="/Admin/addclass" element={<AddClass />} />
                        <Route path="/Admin/classes" element={<ShowClasses />} />
                        <Route path="/Admin/classes/class/:id" element={<ClassDetails />} />
                        <Route path="/Admin/class/addstudents/:id" element={<AddStudent situation="Class" />} />

                        {/* Student */}
                        <Route path="/Admin/addstudents" element={<AddStudent situation="Student" />} />
                        <Route path="/Admin/students" element={<ShowStudents />} />
                        <Route path="/Admin/students/student/:id" element={<EnhancedViewStudent />} />
                        <Route path="/Admin/students/student/attendance/:id" element={<StudentAttendance situation="Student" />} />
                        <Route path="/Admin/students/student/marks/:id" element={<StudentExamMarks situation="Student" />} />

                        {/* Teacher */}
                        <Route path="/Admin/teachers" element={<ShowTeachers />} />
                        <Route path="/Admin/teachers/teacher/:id" element={<TeacherDetails />} />
                        <Route path="/Admin/teachers/chooseclass" element={<ChooseClass situation="Teacher" />} />
                        <Route path="/Admin/teachers/choosesubject/:id" element={<ChooseSubject situation="Norm" />} />
                        <Route path="/Admin/teachers/choosesubject/:classID/:teacherID" element={<ChooseSubject situation="Teacher" />} />
                        <Route path="/Admin/teachers/addteacher/:id" element={<AddTeacher />} />

                        {/* New Module Routes */}
                        <Route path="/Admin/student-info" element={<StudentInfo />} />
                        <Route path="/Admin/fee-management" element={<FeeManagement />} />
                        <Route path="/Admin/fee-due" element={<FeeManagement />} />
                        <Route path="/Admin/documents" element={<DocumentGeneration />} />
                        <Route path="/Admin/transport" element={<TransportManagement />} />
                        <Route path="/Admin/admission" element={<StudentInfo />} />
                        <Route path="/Admin/academics" element={<ShowSubjects />} />
                        <Route path="/Admin/examination" element={<ShowSubjects />} />
                        <Route path="/Admin/attendance" element={<ShowSubjects />} />
                        <Route path="/Admin/notifications" element={<ShowNotices />} />
                        <Route path="/Admin/hostel" element={<ShowSubjects />} />
                        <Route path="/Admin/cms" element={<ShowSubjects />} />
                        <Route path="/Admin/front-office" element={<ShowSubjects />} />

                        <Route path="/logout" element={<Logout />} />
                    </Routes>
                </Box>
            </Box>
        </>
    );
};

// Main AdminDashboard component with ThemeProvider wrapper
const AdminDashboard = () => {
    return (
        <ThemeProvider>
            <AdminDashboardContent />
        </ThemeProvider>
    );
};

export default AdminDashboard;