const router = require('express').Router();

// const { adminRegister, adminLogIn, deleteAdmin, getAdminDetail, updateAdmin } = require('../controllers/admin-controller.js');

const { adminRegister, adminLogIn, getAdminDetail} = require('../controllers/admin-controller.js');

const { sclassCreate, sclassList, deleteSclass, deleteSclasses, getSclassDetail, getSclassStudents } = require('../controllers/class-controller.js');
const { complainCreate, complainList } = require('../controllers/complain-controller.js');
const { noticeCreate, noticeList, deleteNotices, deleteNotice, updateNotice } = require('../controllers/notice-controller.js');
const {
    studentRegister,
    studentLogIn,
    getStudents,
    getStudentDetail,
    deleteStudents,
    deleteStudent,
    updateStudent,
    studentAttendance,
    deleteStudentsByClass,
    updateExamResult,
    clearAllStudentsAttendanceBySubject,
    clearAllStudentsAttendance,
    removeStudentAttendanceBySubject,
    removeStudentAttendance } = require('../controllers/student_controller.js');
const { subjectCreate, classSubjects, deleteSubjectsByClass, getSubjectDetail, deleteSubject, freeSubjectList, allSubjects, deleteSubjects } = require('../controllers/subject-controller.js');
const { teacherRegister, teacherLogIn, getTeachers, getTeacherDetail, deleteTeachers, deleteTeachersByClass, deleteTeacher, updateTeacherSubject, teacherAttendance } = require('../controllers/teacher-controller.js');

// Admin
router.post('/AdminReg', adminRegister);
router.post('/AdminLogin', adminLogIn);

router.get("/Admin/:id", getAdminDetail)
// router.delete("/Admin/:id", deleteAdmin)

// router.put("/Admin/:id", updateAdmin)

// Student

router.post('/StudentReg', studentRegister);
router.post('/StudentLogin', studentLogIn)

router.get("/Students/:id", getStudents)
router.get("/Student/:id", getStudentDetail)

router.delete("/Students/:id", deleteStudents)
router.delete("/StudentsClass/:id", deleteStudentsByClass)
router.delete("/Student/:id", deleteStudent)

router.put("/Student/:id", updateStudent)

router.put('/UpdateExamResult/:id', updateExamResult)

router.put('/StudentAttendance/:id', studentAttendance)

router.put('/RemoveAllStudentsSubAtten/:id', clearAllStudentsAttendanceBySubject);
router.put('/RemoveAllStudentsAtten/:id', clearAllStudentsAttendance);

router.put('/RemoveStudentSubAtten/:id', removeStudentAttendanceBySubject);
router.put('/RemoveStudentAtten/:id', removeStudentAttendance)

// Teacher

router.post('/TeacherReg', teacherRegister);
router.post('/TeacherLogin', teacherLogIn)

router.get("/Teachers/:id", getTeachers)
router.get("/Teacher/:id", getTeacherDetail)

router.delete("/Teachers/:id", deleteTeachers)
router.delete("/TeachersClass/:id", deleteTeachersByClass)
router.delete("/Teacher/:id", deleteTeacher)

router.put("/TeacherSubject", updateTeacherSubject)

router.post('/TeacherAttendance/:id', teacherAttendance)

// Notice

router.post('/NoticeCreate', noticeCreate);

router.get('/NoticeList/:id', noticeList);

router.delete("/Notices/:id", deleteNotices)
router.delete("/Notice/:id", deleteNotice)

router.put("/Notice/:id", updateNotice)

// Complain

router.post('/ComplainCreate', complainCreate);

router.get('/ComplainList/:id', complainList);

// Sclass

router.post('/SclassCreate', sclassCreate);

router.get('/SclassList/:id', sclassList);
router.get("/Sclass/:id", getSclassDetail)

router.get("/Sclass/Students/:id", getSclassStudents)

router.delete("/Sclasses/:id", deleteSclasses)
router.delete("/Sclass/:id", deleteSclass)

// Subject

router.post('/SubjectCreate', subjectCreate);

router.get('/AllSubjects/:id', allSubjects);
router.get('/ClassSubjects/:id', classSubjects);
router.get('/FreeSubjectList/:id', freeSubjectList);
router.get("/Subject/:id", getSubjectDetail)

router.delete("/Subject/:id", deleteSubject)
router.delete("/Subjects/:id", deleteSubjects)
router.delete("/SubjectsClass/:id", deleteSubjectsByClass)

// Simple database fallback when MongoDB is not available
router.get('/test-simple-db', (req, res) => {
    try {
        const { simpleDB, initializeDB } = require('../simpleDB.js');

        // Initialize if needed
        initializeDB();

        const admin = simpleDB.getAdmin();
        const classes = simpleDB.getClasses(admin._id);

        res.json({
            success: true,
            message: 'Simple database working!',
            admin: {
                id: admin._id,
                email: admin.email,
                name: admin.name
            },
            classes: classes.length,
            classNames: classes.map(c => c.sclassName)
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Test endpoint to check database connection and create sample data
router.get('/test-db', async (req, res) => {
    try {
        const Admin = require('../models/adminSchema.js');
        const Sclass = require('../models/sclassSchema.js');

        // Check if admin exists
        let admin = await Admin.findOne({ email: '<EMAIL>' });

        if (!admin) {
            // Create admin if doesn't exist
            admin = new Admin({
                name: 'School Administrator',
                email: '<EMAIL>',
                password: 'admin123', // Plain text password as expected by the login system
                role: 'Admin',
                schoolName: 'Amar Vidya Mandir Higher Secondary School'
            });
            await admin.save();
        }

        // Check classes
        const classes = await Sclass.find({ school: admin._id });

        if (classes.length === 0) {
            // Create sample classes
            const classesData = [
                { sclassName: 'Class 1', school: admin._id },
                { sclassName: 'Class 2', school: admin._id },
                { sclassName: 'Class 3', school: admin._id },
                { sclassName: 'Class 4', school: admin._id },
                { sclassName: 'Class 5', school: admin._id }
            ];
            await Sclass.insertMany(classesData);
        }

        const updatedClasses = await Sclass.find({ school: admin._id });

        res.json({
            success: true,
            admin: {
                id: admin._id,
                email: admin.email,
                name: admin.name
            },
            classes: updatedClasses.length,
            classNames: updatedClasses.map(c => c.sclassName)
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;