{"ast": null, "code": "import { activeAnimations } from '../../stats/animation-count.mjs';\nimport { statsBuffer } from '../../stats/buffer.mjs';\nimport { mapEasingToNativeEasing } from './easing/map-easing.mjs';\nfunction startWaapiAnimation(element, valueName, keyframes) {\n  let {\n    delay = 0,\n    duration = 300,\n    repeat = 0,\n    repeatType = \"loop\",\n    ease = \"easeOut\",\n    times\n  } = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  let pseudoElement = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : undefined;\n  const keyframeOptions = {\n    [valueName]: keyframes\n  };\n  if (times) keyframeOptions.offset = times;\n  const easing = mapEasingToNativeEasing(ease, duration);\n  /**\n   * If this is an easing array, apply to keyframes, not animation as a whole\n   */\n  if (Array.isArray(easing)) keyframeOptions.easing = easing;\n  if (statsBuffer.value) {\n    activeAnimations.waapi++;\n  }\n  const options = {\n    delay,\n    duration,\n    easing: !Array.isArray(easing) ? easing : \"linear\",\n    fill: \"both\",\n    iterations: repeat + 1,\n    direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\"\n  };\n  if (pseudoElement) options.pseudoElement = pseudoElement;\n  const animation = element.animate(keyframeOptions, options);\n  if (statsBuffer.value) {\n    animation.finished.finally(() => {\n      activeAnimations.waapi--;\n    });\n  }\n  return animation;\n}\nexport { startWaapiAnimation };", "map": {"version": 3, "names": ["activeAnimations", "statsBuffer", "mapEasingToNativeEasing", "startWaapiAnimation", "element", "valueName", "keyframes", "delay", "duration", "repeat", "repeatType", "ease", "times", "arguments", "length", "undefined", "pseudoElement", "keyframeOptions", "offset", "easing", "Array", "isArray", "value", "waapi", "options", "fill", "iterations", "direction", "animation", "animate", "finished", "finally"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs"], "sourcesContent": ["import { activeAnimations } from '../../stats/animation-count.mjs';\nimport { statsBuffer } from '../../stats/buffer.mjs';\nimport { mapEasingToNativeEasing } from './easing/map-easing.mjs';\n\nfunction startWaapiAnimation(element, valueName, keyframes, { delay = 0, duration = 300, repeat = 0, repeatType = \"loop\", ease = \"easeOut\", times, } = {}, pseudoElement = undefined) {\n    const keyframeOptions = {\n        [valueName]: keyframes,\n    };\n    if (times)\n        keyframeOptions.offset = times;\n    const easing = mapEasingToNativeEasing(ease, duration);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing))\n        keyframeOptions.easing = easing;\n    if (statsBuffer.value) {\n        activeAnimations.waapi++;\n    }\n    const options = {\n        delay,\n        duration,\n        easing: !Array.isArray(easing) ? easing : \"linear\",\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n    };\n    if (pseudoElement)\n        options.pseudoElement = pseudoElement;\n    const animation = element.animate(keyframeOptions, options);\n    if (statsBuffer.value) {\n        animation.finished.finally(() => {\n            activeAnimations.waapi--;\n        });\n    }\n    return animation;\n}\n\nexport { startWaapiAnimation };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,uBAAuB,QAAQ,yBAAyB;AAEjE,SAASC,mBAAmBA,CAACC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAA4H;EAAA,IAA1H;IAAEC,KAAK,GAAG,CAAC;IAAEC,QAAQ,GAAG,GAAG;IAAEC,MAAM,GAAG,CAAC;IAAEC,UAAU,GAAG,MAAM;IAAEC,IAAI,GAAG,SAAS;IAAEC;EAAO,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEG,aAAa,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGE,SAAS;EAChL,MAAME,eAAe,GAAG;IACpB,CAACZ,SAAS,GAAGC;EACjB,CAAC;EACD,IAAIM,KAAK,EACLK,eAAe,CAACC,MAAM,GAAGN,KAAK;EAClC,MAAMO,MAAM,GAAGjB,uBAAuB,CAACS,IAAI,EAAEH,QAAQ,CAAC;EACtD;AACJ;AACA;EACI,IAAIY,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EACrBF,eAAe,CAACE,MAAM,GAAGA,MAAM;EACnC,IAAIlB,WAAW,CAACqB,KAAK,EAAE;IACnBtB,gBAAgB,CAACuB,KAAK,EAAE;EAC5B;EACA,MAAMC,OAAO,GAAG;IACZjB,KAAK;IACLC,QAAQ;IACRW,MAAM,EAAE,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,GAAGA,MAAM,GAAG,QAAQ;IAClDM,IAAI,EAAE,MAAM;IACZC,UAAU,EAAEjB,MAAM,GAAG,CAAC;IACtBkB,SAAS,EAAEjB,UAAU,KAAK,SAAS,GAAG,WAAW,GAAG;EACxD,CAAC;EACD,IAAIM,aAAa,EACbQ,OAAO,CAACR,aAAa,GAAGA,aAAa;EACzC,MAAMY,SAAS,GAAGxB,OAAO,CAACyB,OAAO,CAACZ,eAAe,EAAEO,OAAO,CAAC;EAC3D,IAAIvB,WAAW,CAACqB,KAAK,EAAE;IACnBM,SAAS,CAACE,QAAQ,CAACC,OAAO,CAAC,MAAM;MAC7B/B,gBAAgB,CAACuB,KAAK,EAAE;IAC5B,CAAC,CAAC;EACN;EACA,OAAOK,SAAS;AACpB;AAEA,SAASzB,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}