{"ast": null, "code": "const invisibleValues = new Set([\"none\", \"hidden\"]);\n/**\n * Returns a function that, when provided a progress value between 0 and 1,\n * will return the \"none\" or \"hidden\" string only when the progress is that of\n * the origin or target.\n */\nfunction mixVisibility(origin, target) {\n  if (invisibleValues.has(origin)) {\n    return p => p <= 0 ? origin : target;\n  } else {\n    return p => p >= 1 ? target : origin;\n  }\n}\nexport { invisibleValues, mixVisibility };", "map": {"version": 3, "names": ["invisibleV<PERSON>ues", "Set", "mixVisibility", "origin", "target", "has", "p"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/utils/mix/visibility.mjs"], "sourcesContent": ["const invisibleValues = new Set([\"none\", \"hidden\"]);\n/**\n * Returns a function that, when provided a progress value between 0 and 1,\n * will return the \"none\" or \"hidden\" string only when the progress is that of\n * the origin or target.\n */\nfunction mixVisibility(origin, target) {\n    if (invisibleValues.has(origin)) {\n        return (p) => (p <= 0 ? origin : target);\n    }\n    else {\n        return (p) => (p >= 1 ? target : origin);\n    }\n}\n\nexport { invisibleValues, mixVisibility };\n"], "mappings": "AAAA,MAAMA,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACnC,IAAIJ,eAAe,CAACK,GAAG,CAACF,MAAM,CAAC,EAAE;IAC7B,OAAQG,CAAC,IAAMA,CAAC,IAAI,CAAC,GAAGH,MAAM,GAAGC,MAAO;EAC5C,CAAC,MACI;IACD,OAAQE,CAAC,IAAMA,CAAC,IAAI,CAAC,GAAGF,MAAM,GAAGD,MAAO;EAC5C;AACJ;AAEA,SAASH,eAAe,EAAEE,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}