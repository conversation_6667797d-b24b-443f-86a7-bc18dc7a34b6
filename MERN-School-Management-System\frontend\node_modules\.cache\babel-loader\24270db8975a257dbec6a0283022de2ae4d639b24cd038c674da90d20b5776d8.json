{"ast": null, "code": "import { scale, alpha } from '../numbers/index.mjs';\nimport { degrees, px, progressPercentage } from '../numbers/units.mjs';\nconst transformValueTypes = {\n  rotate: degrees,\n  rotateX: degrees,\n  rotateY: degrees,\n  rotateZ: degrees,\n  scale,\n  scaleX: scale,\n  scaleY: scale,\n  scaleZ: scale,\n  skew: degrees,\n  skewX: degrees,\n  skewY: degrees,\n  distance: px,\n  translateX: px,\n  translateY: px,\n  translateZ: px,\n  x: px,\n  y: px,\n  z: px,\n  perspective: px,\n  transformPerspective: px,\n  opacity: alpha,\n  originX: progressPercentage,\n  originY: progressPercentage,\n  originZ: px\n};\nexport { transformValueTypes };", "map": {"version": 3, "names": ["scale", "alpha", "degrees", "px", "progressPercentage", "transformValueTypes", "rotate", "rotateX", "rotateY", "rotateZ", "scaleX", "scaleY", "scaleZ", "skew", "skewX", "skewY", "distance", "translateX", "translateY", "translateZ", "x", "y", "z", "perspective", "transformPerspective", "opacity", "originX", "originY", "originZ"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/value/types/maps/transform.mjs"], "sourcesContent": ["import { scale, alpha } from '../numbers/index.mjs';\nimport { degrees, px, progressPercentage } from '../numbers/units.mjs';\n\nconst transformValueTypes = {\n    rotate: degrees,\n    rotateX: degrees,\n    rotateY: degrees,\n    rotateZ: degrees,\n    scale,\n    scaleX: scale,\n    scaleY: scale,\n    scaleZ: scale,\n    skew: degrees,\n    skewX: degrees,\n    skewY: degrees,\n    distance: px,\n    translateX: px,\n    translateY: px,\n    translateZ: px,\n    x: px,\n    y: px,\n    z: px,\n    perspective: px,\n    transformPerspective: px,\n    opacity: alpha,\n    originX: progressPercentage,\n    originY: progressPercentage,\n    originZ: px,\n};\n\nexport { transformValueTypes };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,KAAK,QAAQ,sBAAsB;AACnD,SAASC,OAAO,EAAEC,EAAE,EAAEC,kBAAkB,QAAQ,sBAAsB;AAEtE,MAAMC,mBAAmB,GAAG;EACxBC,MAAM,EAAEJ,OAAO;EACfK,OAAO,EAAEL,OAAO;EAChBM,OAAO,EAAEN,OAAO;EAChBO,OAAO,EAAEP,OAAO;EAChBF,KAAK;EACLU,MAAM,EAAEV,KAAK;EACbW,MAAM,EAAEX,KAAK;EACbY,MAAM,EAAEZ,KAAK;EACba,IAAI,EAAEX,OAAO;EACbY,KAAK,EAAEZ,OAAO;EACda,KAAK,EAAEb,OAAO;EACdc,QAAQ,EAAEb,EAAE;EACZc,UAAU,EAAEd,EAAE;EACde,UAAU,EAAEf,EAAE;EACdgB,UAAU,EAAEhB,EAAE;EACdiB,CAAC,EAAEjB,EAAE;EACLkB,CAAC,EAAElB,EAAE;EACLmB,CAAC,EAAEnB,EAAE;EACLoB,WAAW,EAAEpB,EAAE;EACfqB,oBAAoB,EAAErB,EAAE;EACxBsB,OAAO,EAAExB,KAAK;EACdyB,OAAO,EAAEtB,kBAAkB;EAC3BuB,OAAO,EAAEvB,kBAAkB;EAC3BwB,OAAO,EAAEzB;AACb,CAAC;AAED,SAASE,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}