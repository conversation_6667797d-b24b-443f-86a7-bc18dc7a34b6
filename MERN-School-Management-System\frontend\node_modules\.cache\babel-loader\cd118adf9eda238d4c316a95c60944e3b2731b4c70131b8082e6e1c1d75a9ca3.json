{"ast": null, "code": "import { press, frame } from 'motion-dom';\nimport { extractEventInfo } from '../events/event-info.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\nfunction handlePressEvent(node, event, lifecycle) {\n  const {\n    props\n  } = node;\n  if (node.current instanceof HTMLButtonElement && node.current.disabled) {\n    return;\n  }\n  if (node.animationState && props.whileTap) {\n    node.animationState.setActive(\"whileTap\", lifecycle === \"Start\");\n  }\n  const eventName = \"onTap\" + (lifecycle === \"End\" ? \"\" : lifecycle);\n  const callback = props[eventName];\n  if (callback) {\n    frame.postRender(() => callback(event, extractEventInfo(event)));\n  }\n}\nclass PressGesture extends Feature {\n  mount() {\n    const {\n      current\n    } = this.node;\n    if (!current) return;\n    this.unmount = press(current, (_element, startEvent) => {\n      handlePressEvent(this.node, startEvent, \"Start\");\n      return (endEvent, _ref) => {\n        let {\n          success\n        } = _ref;\n        return handlePressEvent(this.node, endEvent, success ? \"End\" : \"Cancel\");\n      };\n    }, {\n      useGlobalTarget: this.node.props.globalTapTarget\n    });\n  }\n  unmount() {}\n}\nexport { PressGesture };", "map": {"version": 3, "names": ["press", "frame", "extractEventInfo", "Feature", "handlePressEvent", "node", "event", "lifecycle", "props", "current", "HTMLButtonElement", "disabled", "animationState", "whileTap", "setActive", "eventName", "callback", "postRender", "PressGesture", "mount", "unmount", "_element", "startEvent", "endEvent", "_ref", "success", "useGlobalTarget", "globalTapTarget"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/gestures/press.mjs"], "sourcesContent": ["import { press, frame } from 'motion-dom';\nimport { extractEventInfo } from '../events/event-info.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\n\nfunction handlePressEvent(node, event, lifecycle) {\n    const { props } = node;\n    if (node.current instanceof HTMLButtonElement && node.current.disabled) {\n        return;\n    }\n    if (node.animationState && props.whileTap) {\n        node.animationState.setActive(\"whileTap\", lifecycle === \"Start\");\n    }\n    const eventName = (\"onTap\" + (lifecycle === \"End\" ? \"\" : lifecycle));\n    const callback = props[eventName];\n    if (callback) {\n        frame.postRender(() => callback(event, extractEventInfo(event)));\n    }\n}\nclass PressGesture extends Feature {\n    mount() {\n        const { current } = this.node;\n        if (!current)\n            return;\n        this.unmount = press(current, (_element, startEvent) => {\n            handlePressEvent(this.node, startEvent, \"Start\");\n            return (endEvent, { success }) => handlePressEvent(this.node, endEvent, success ? \"End\" : \"Cancel\");\n        }, { useGlobalTarget: this.node.props.globalTapTarget });\n    }\n    unmount() { }\n}\n\nexport { PressGesture };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,KAAK,QAAQ,YAAY;AACzC,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,OAAO,QAAQ,gCAAgC;AAExD,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC9C,MAAM;IAAEC;EAAM,CAAC,GAAGH,IAAI;EACtB,IAAIA,IAAI,CAACI,OAAO,YAAYC,iBAAiB,IAAIL,IAAI,CAACI,OAAO,CAACE,QAAQ,EAAE;IACpE;EACJ;EACA,IAAIN,IAAI,CAACO,cAAc,IAAIJ,KAAK,CAACK,QAAQ,EAAE;IACvCR,IAAI,CAACO,cAAc,CAACE,SAAS,CAAC,UAAU,EAAEP,SAAS,KAAK,OAAO,CAAC;EACpE;EACA,MAAMQ,SAAS,GAAI,OAAO,IAAIR,SAAS,KAAK,KAAK,GAAG,EAAE,GAAGA,SAAS,CAAE;EACpE,MAAMS,QAAQ,GAAGR,KAAK,CAACO,SAAS,CAAC;EACjC,IAAIC,QAAQ,EAAE;IACVf,KAAK,CAACgB,UAAU,CAAC,MAAMD,QAAQ,CAACV,KAAK,EAAEJ,gBAAgB,CAACI,KAAK,CAAC,CAAC,CAAC;EACpE;AACJ;AACA,MAAMY,YAAY,SAASf,OAAO,CAAC;EAC/BgB,KAAKA,CAAA,EAAG;IACJ,MAAM;MAAEV;IAAQ,CAAC,GAAG,IAAI,CAACJ,IAAI;IAC7B,IAAI,CAACI,OAAO,EACR;IACJ,IAAI,CAACW,OAAO,GAAGpB,KAAK,CAACS,OAAO,EAAE,CAACY,QAAQ,EAAEC,UAAU,KAAK;MACpDlB,gBAAgB,CAAC,IAAI,CAACC,IAAI,EAAEiB,UAAU,EAAE,OAAO,CAAC;MAChD,OAAO,CAACC,QAAQ,EAAAC,IAAA;QAAA,IAAE;UAAEC;QAAQ,CAAC,GAAAD,IAAA;QAAA,OAAKpB,gBAAgB,CAAC,IAAI,CAACC,IAAI,EAAEkB,QAAQ,EAAEE,OAAO,GAAG,KAAK,GAAG,QAAQ,CAAC;MAAA;IACvG,CAAC,EAAE;MAAEC,eAAe,EAAE,IAAI,CAACrB,IAAI,CAACG,KAAK,CAACmB;IAAgB,CAAC,CAAC;EAC5D;EACAP,OAAOA,CAAA,EAAG,CAAE;AAChB;AAEA,SAASF,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}