import React, { useState } from 'react';
import { 
  Paper, 
  Typography, 
  Box, 
  List, 
  ListItem, 
  ListItemText,
  Chip,
  IconButton
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { 
  ChevronLeft, 
  ChevronRight,
  Event as EventIcon 
} from '@mui/icons-material';
import moment from 'moment';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  height: '400px',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
}));

const CalendarGrid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(7, 1fr)',
  gap: theme.spacing(0.5),
  marginBottom: theme.spacing(2),
}));

const DayCell = styled(motion.div)(({ theme, isToday, hasEvent }) => ({
  padding: theme.spacing(1),
  textAlign: 'center',
  borderRadius: '8px',
  cursor: 'pointer',
  fontSize: '0.875rem',
  fontWeight: isToday ? 'bold' : 'normal',
  backgroundColor: isToday 
    ? 'rgba(255, 255, 255, 0.3)' 
    : hasEvent 
    ? 'rgba(255, 255, 255, 0.1)' 
    : 'transparent',
  border: hasEvent ? '1px solid rgba(255, 255, 255, 0.3)' : 'none',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
}));

const CalendarWidget = () => {
  const [currentDate, setCurrentDate] = useState(moment());
  
  // Sample events
  const events = [
    { date: moment().format('YYYY-MM-DD'), title: 'Parent Meeting', type: 'meeting' },
    { date: moment().add(2, 'days').format('YYYY-MM-DD'), title: 'Science Fair', type: 'event' },
    { date: moment().add(5, 'days').format('YYYY-MM-DD'), title: 'Math Exam', type: 'exam' },
    { date: moment().add(7, 'days').format('YYYY-MM-DD'), title: 'Sports Day', type: 'sports' },
  ];

  const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  
  const startOfMonth = currentDate.clone().startOf('month');
  const endOfMonth = currentDate.clone().endOf('month');
  const startOfCalendar = startOfMonth.clone().startOf('week');
  const endOfCalendar = endOfMonth.clone().endOf('week');

  const calendarDays = [];
  let day = startOfCalendar.clone();
  
  while (day.isSameOrBefore(endOfCalendar)) {
    calendarDays.push(day.clone());
    day.add(1, 'day');
  }

  const hasEvent = (date) => {
    return events.some(event => event.date === date.format('YYYY-MM-DD'));
  };

  const getUpcomingEvents = () => {
    return events
      .filter(event => moment(event.date).isSameOrAfter(moment(), 'day'))
      .slice(0, 3);
  };

  const getEventColor = (type) => {
    switch (type) {
      case 'meeting': return 'warning';
      case 'event': return 'info';
      case 'exam': return 'error';
      case 'sports': return 'success';
      default: return 'default';
    }
  };

  return (
    <StyledPaper elevation={3}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6" fontWeight="bold">
          📅 {currentDate.format('MMMM YYYY')}
        </Typography>
        <Box>
          <IconButton 
            size="small" 
            onClick={() => setCurrentDate(currentDate.clone().subtract(1, 'month'))}
            sx={{ color: 'white' }}
          >
            <ChevronLeft />
          </IconButton>
          <IconButton 
            size="small" 
            onClick={() => setCurrentDate(currentDate.clone().add(1, 'month'))}
            sx={{ color: 'white' }}
          >
            <ChevronRight />
          </IconButton>
        </Box>
      </Box>

      <CalendarGrid>
        {daysOfWeek.map(day => (
          <Typography key={day} variant="caption" textAlign="center" fontWeight="bold">
            {day}
          </Typography>
        ))}
        {calendarDays.map((day, index) => (
          <DayCell
            key={index}
            isToday={day.isSame(moment(), 'day')}
            hasEvent={hasEvent(day)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            {day.format('D')}
          </DayCell>
        ))}
      </CalendarGrid>

      <Box>
        <Typography variant="subtitle2" fontWeight="bold" mb={1}>
          Upcoming Events
        </Typography>
        <List dense sx={{ maxHeight: '120px', overflowY: 'auto' }}>
          {getUpcomingEvents().map((event, index) => (
            <ListItem key={index} sx={{ padding: '4px 0' }}>
              <EventIcon fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
              <ListItemText
                primary={
                  <Typography variant="body2" fontSize="0.8rem">
                    {event.title}
                  </Typography>
                }
                secondary={
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="caption" sx={{ opacity: 0.8 }}>
                      {moment(event.date).format('MMM DD')}
                    </Typography>
                    <Chip 
                      label={event.type} 
                      size="small" 
                      color={getEventColor(event.type)}
                      sx={{ height: 16, fontSize: '0.6rem' }}
                    />
                  </Box>
                }
              />
            </ListItem>
          ))}
        </List>
      </Box>
    </StyledPaper>
  );
};

export default CalendarWidget;
