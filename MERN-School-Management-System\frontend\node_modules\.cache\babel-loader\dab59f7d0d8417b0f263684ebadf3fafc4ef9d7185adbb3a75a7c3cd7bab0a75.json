{"ast": null, "code": "import { frame } from 'motion-dom';\nimport { MotionGlobalConfig } from 'motion-utils';\nimport { useRef, useEffect } from 'react';\nimport { useInstantLayoutTransition } from '../projection/use-instant-layout-transition.mjs';\nimport { useForceUpdate } from './use-force-update.mjs';\nfunction useInstantTransition() {\n  const [forceUpdate, forcedRenderCount] = useForceUpdate();\n  const startInstantLayoutTransition = useInstantLayoutTransition();\n  const unlockOnFrameRef = useRef(-1);\n  useEffect(() => {\n    /**\n     * Unblock after two animation frames, otherwise this will unblock too soon.\n     */\n    frame.postRender(() => frame.postRender(() => {\n      /**\n       * If the callback has been called again after the effect\n       * triggered this 2 frame delay, don't unblock animations. This\n       * prevents the previous effect from unblocking the current\n       * instant transition too soon. This becomes more likely when\n       * used in conjunction with React.startTransition().\n       */\n      if (forcedRenderCount !== unlockOnFrameRef.current) return;\n      MotionGlobalConfig.instantAnimations = false;\n    }));\n  }, [forcedRenderCount]);\n  return callback => {\n    startInstantLayoutTransition(() => {\n      MotionGlobalConfig.instantAnimations = true;\n      forceUpdate();\n      callback();\n      unlockOnFrameRef.current = forcedRenderCount + 1;\n    });\n  };\n}\nfunction disableInstantTransitions() {\n  MotionGlobalConfig.instantAnimations = false;\n}\nexport { disableInstantTransitions, useInstantTransition };", "map": {"version": 3, "names": ["frame", "MotionGlobalConfig", "useRef", "useEffect", "useInstantLayoutTransition", "useForceUpdate", "useInstantTransition", "forceUpdate", "forcedRenderCount", "startInstantLayoutTransition", "unlockOnFrameRef", "postRender", "current", "instantAnimations", "callback", "disableInstantTransitions"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/utils/use-instant-transition.mjs"], "sourcesContent": ["import { frame } from 'motion-dom';\nimport { MotionGlobalConfig } from 'motion-utils';\nimport { useRef, useEffect } from 'react';\nimport { useInstantLayoutTransition } from '../projection/use-instant-layout-transition.mjs';\nimport { useForceUpdate } from './use-force-update.mjs';\n\nfunction useInstantTransition() {\n    const [forceUpdate, forcedRenderCount] = useForceUpdate();\n    const startInstantLayoutTransition = useInstantLayoutTransition();\n    const unlockOnFrameRef = useRef(-1);\n    useEffect(() => {\n        /**\n         * Unblock after two animation frames, otherwise this will unblock too soon.\n         */\n        frame.postRender(() => frame.postRender(() => {\n            /**\n             * If the callback has been called again after the effect\n             * triggered this 2 frame delay, don't unblock animations. This\n             * prevents the previous effect from unblocking the current\n             * instant transition too soon. This becomes more likely when\n             * used in conjunction with React.startTransition().\n             */\n            if (forcedRenderCount !== unlockOnFrameRef.current)\n                return;\n            MotionGlobalConfig.instantAnimations = false;\n        }));\n    }, [forcedRenderCount]);\n    return (callback) => {\n        startInstantLayoutTransition(() => {\n            MotionGlobalConfig.instantAnimations = true;\n            forceUpdate();\n            callback();\n            unlockOnFrameRef.current = forcedRenderCount + 1;\n        });\n    };\n}\nfunction disableInstantTransitions() {\n    MotionGlobalConfig.instantAnimations = false;\n}\n\nexport { disableInstantTransitions, useInstantTransition };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,SAASC,kBAAkB,QAAQ,cAAc;AACjD,SAASC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,SAASC,0BAA0B,QAAQ,iDAAiD;AAC5F,SAASC,cAAc,QAAQ,wBAAwB;AAEvD,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,MAAM,CAACC,WAAW,EAAEC,iBAAiB,CAAC,GAAGH,cAAc,EAAE;EACzD,MAAMI,4BAA4B,GAAGL,0BAA0B,EAAE;EACjE,MAAMM,gBAAgB,GAAGR,MAAM,CAAC,CAAC,CAAC,CAAC;EACnCC,SAAS,CAAC,MAAM;IACZ;AACR;AACA;IACQH,KAAK,CAACW,UAAU,CAAC,MAAMX,KAAK,CAACW,UAAU,CAAC,MAAM;MAC1C;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAIH,iBAAiB,KAAKE,gBAAgB,CAACE,OAAO,EAC9C;MACJX,kBAAkB,CAACY,iBAAiB,GAAG,KAAK;IAChD,CAAC,CAAC,CAAC;EACP,CAAC,EAAE,CAACL,iBAAiB,CAAC,CAAC;EACvB,OAAQM,QAAQ,IAAK;IACjBL,4BAA4B,CAAC,MAAM;MAC/BR,kBAAkB,CAACY,iBAAiB,GAAG,IAAI;MAC3CN,WAAW,EAAE;MACbO,QAAQ,EAAE;MACVJ,gBAAgB,CAACE,OAAO,GAAGJ,iBAAiB,GAAG,CAAC;IACpD,CAAC,CAAC;EACN,CAAC;AACL;AACA,SAASO,yBAAyBA,CAAA,EAAG;EACjCd,kBAAkB,CAACY,iBAAiB,GAAG,KAAK;AAChD;AAEA,SAASE,yBAAyB,EAAET,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}