const mongoose = require('mongoose');

const checkMongoDB = async () => {
    try {
        console.log('🔍 Checking MongoDB connection...');
        
        await mongoose.connect('mongodb://localhost:27017/school_management_system', {
            useNewUrlParser: true,
            useUnifiedTopology: true,
            serverSelectionTimeoutMS: 5000 // 5 second timeout
        });
        
        console.log('✅ MongoDB is running and accessible!');
        console.log('🗄️ Database: school_management_system');
        console.log('🌐 Connection: mongodb://localhost:27017');
        
        // List databases
        const admin = mongoose.connection.db.admin();
        const dbs = await admin.listDatabases();
        console.log('\n📚 Available databases:');
        dbs.databases.forEach(db => {
            console.log(`   - ${db.name}`);
        });
        
        // Check collections in our database
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log('\n📋 Collections in school_management_system:');
        if (collections.length === 0) {
            console.log('   (No collections yet - run setup-local-db.js to create sample data)');
        } else {
            collections.forEach(col => {
                console.log(`   - ${col.name}`);
            });
        }
        
        await mongoose.disconnect();
        console.log('\n🎉 MongoDB check complete!');
        process.exit(0);
        
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error.message);
        console.log('\n💡 Troubleshooting steps:');
        console.log('   1. Install MongoDB Community Edition');
        console.log('   2. Start MongoDB service: net start MongoDB');
        console.log('   3. Check if port 27017 is available');
        console.log('   4. Try: mongod --dbpath C:\\data\\db');
        console.log('\n📖 See install-mongodb.md for detailed instructions');
        process.exit(1);
    }
};

checkMongoDB();
