{"ast": null, "code": "import { KeyframeResolver, time, frame, isMotionValue, cancelFrame, transformProps, motionValue, findValueType, complex, getAnimatableNone } from 'motion-dom';\nimport { warnOnce, isNumericalString, isZeroValueString, SubscriptionManager } from 'motion-utils';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\nconst propEventHandlers = [\"AnimationStart\", \"AnimationComplete\", \"Update\", \"BeforeLayoutMeasure\", \"LayoutMeasure\", \"LayoutAnimationStart\", \"LayoutAnimationComplete\"];\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n  /**\n   * This method takes React props and returns found MotionValues. For example, HTML\n   * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n   *\n   * This isn't an abstract method as it needs calling in the constructor, but it is\n   * intended to be one.\n   */\n  scrapeMotionValuesFromProps(_props, _prevProps, _visualElement) {\n    return {};\n  }\n  constructor(_ref) {\n    let {\n      parent,\n      props,\n      presenceContext,\n      reducedMotionConfig,\n      blockInitialAnimation,\n      visualState\n    } = _ref;\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    /**\n     * A reference to the current underlying Instance, e.g. a HTMLElement\n     * or Three.Mesh etc.\n     */\n    this.current = null;\n    /**\n     * A set containing references to this VisualElement's children.\n     */\n    this.children = new Set();\n    /**\n     * Determine what role this visual element should take in the variant tree.\n     */\n    this.isVariantNode = false;\n    this.isControllingVariants = false;\n    /**\n     * Decides whether this VisualElement should animate in reduced motion\n     * mode.\n     *\n     * TODO: This is currently set on every individual VisualElement but feels\n     * like it could be set globally.\n     */\n    this.shouldReduceMotion = null;\n    /**\n     * A map of all motion values attached to this visual element. Motion\n     * values are source of truth for any given animated value. A motion\n     * value might be provided externally by the component via props.\n     */\n    this.values = new Map();\n    this.KeyframeResolver = KeyframeResolver;\n    /**\n     * Cleanup functions for active features (hover/tap/exit etc)\n     */\n    this.features = {};\n    /**\n     * A map of every subscription that binds the provided or generated\n     * motion values onChange listeners to this visual element.\n     */\n    this.valueSubscriptions = new Map();\n    /**\n     * A reference to the previously-provided motion values as returned\n     * from scrapeMotionValuesFromProps. We use the keys in here to determine\n     * if any motion values need to be removed after props are updated.\n     */\n    this.prevMotionValues = {};\n    /**\n     * An object containing a SubscriptionManager for each active event.\n     */\n    this.events = {};\n    /**\n     * An object containing an unsubscribe function for each prop event subscription.\n     * For example, every \"Update\" event can have multiple subscribers via\n     * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n     */\n    this.propEventSubscriptions = {};\n    this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n    this.render = () => {\n      if (!this.current) return;\n      this.triggerBuild();\n      this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n    };\n    this.renderScheduledAt = 0.0;\n    this.scheduleRender = () => {\n      const now = time.now();\n      if (this.renderScheduledAt < now) {\n        this.renderScheduledAt = now;\n        frame.render(this.render, false, true);\n      }\n    };\n    const {\n      latestValues,\n      renderState\n    } = visualState;\n    this.latestValues = latestValues;\n    this.baseTarget = {\n      ...latestValues\n    };\n    this.initialValues = props.initial ? {\n      ...latestValues\n    } : {};\n    this.renderState = renderState;\n    this.parent = parent;\n    this.props = props;\n    this.presenceContext = presenceContext;\n    this.depth = parent ? parent.depth + 1 : 0;\n    this.reducedMotionConfig = reducedMotionConfig;\n    this.options = options;\n    this.blockInitialAnimation = Boolean(blockInitialAnimation);\n    this.isControllingVariants = isControllingVariants(props);\n    this.isVariantNode = isVariantNode(props);\n    if (this.isVariantNode) {\n      this.variantChildren = new Set();\n    }\n    this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n    /**\n     * Any motion values that are provided to the element when created\n     * aren't yet bound to the element, as this would technically be impure.\n     * However, we iterate through the motion values and set them to the\n     * initial values for this component.\n     *\n     * TODO: This is impure and we should look at changing this to run on mount.\n     * Doing so will break some tests but this isn't necessarily a breaking change,\n     * more a reflection of the test.\n     */\n    const {\n      willChange,\n      ...initialMotionValues\n    } = this.scrapeMotionValuesFromProps(props, {}, this);\n    for (const key in initialMotionValues) {\n      const value = initialMotionValues[key];\n      if (latestValues[key] !== undefined && isMotionValue(value)) {\n        value.set(latestValues[key], false);\n      }\n    }\n  }\n  mount(instance) {\n    this.current = instance;\n    visualElementStore.set(instance, this);\n    if (this.projection && !this.projection.instance) {\n      this.projection.mount(instance);\n    }\n    if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n      this.removeFromVariantTree = this.parent.addVariantChild(this);\n    }\n    this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n    if (!hasReducedMotionListener.current) {\n      initPrefersReducedMotion();\n    }\n    this.shouldReduceMotion = this.reducedMotionConfig === \"never\" ? false : this.reducedMotionConfig === \"always\" ? true : prefersReducedMotion.current;\n    if (process.env.NODE_ENV !== \"production\") {\n      warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n    }\n    if (this.parent) this.parent.children.add(this);\n    this.update(this.props, this.presenceContext);\n  }\n  unmount() {\n    this.projection && this.projection.unmount();\n    cancelFrame(this.notifyUpdate);\n    cancelFrame(this.render);\n    this.valueSubscriptions.forEach(remove => remove());\n    this.valueSubscriptions.clear();\n    this.removeFromVariantTree && this.removeFromVariantTree();\n    this.parent && this.parent.children.delete(this);\n    for (const key in this.events) {\n      this.events[key].clear();\n    }\n    for (const key in this.features) {\n      const feature = this.features[key];\n      if (feature) {\n        feature.unmount();\n        feature.isMounted = false;\n      }\n    }\n    this.current = null;\n  }\n  bindToMotionValue(key, value) {\n    if (this.valueSubscriptions.has(key)) {\n      this.valueSubscriptions.get(key)();\n    }\n    const valueIsTransform = transformProps.has(key);\n    if (valueIsTransform && this.onBindTransform) {\n      this.onBindTransform();\n    }\n    const removeOnChange = value.on(\"change\", latestValue => {\n      this.latestValues[key] = latestValue;\n      this.props.onUpdate && frame.preRender(this.notifyUpdate);\n      if (valueIsTransform && this.projection) {\n        this.projection.isTransformDirty = true;\n      }\n    });\n    const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n    let removeSyncCheck;\n    if (window.MotionCheckAppearSync) {\n      removeSyncCheck = window.MotionCheckAppearSync(this, key, value);\n    }\n    this.valueSubscriptions.set(key, () => {\n      removeOnChange();\n      removeOnRenderRequest();\n      if (removeSyncCheck) removeSyncCheck();\n      if (value.owner) value.stop();\n    });\n  }\n  sortNodePosition(other) {\n    /**\n     * If these nodes aren't even of the same type we can't compare their depth.\n     */\n    if (!this.current || !this.sortInstanceNodePosition || this.type !== other.type) {\n      return 0;\n    }\n    return this.sortInstanceNodePosition(this.current, other.current);\n  }\n  updateFeatures() {\n    let key = \"animation\";\n    for (key in featureDefinitions) {\n      const featureDefinition = featureDefinitions[key];\n      if (!featureDefinition) continue;\n      const {\n        isEnabled,\n        Feature: FeatureConstructor\n      } = featureDefinition;\n      /**\n       * If this feature is enabled but not active, make a new instance.\n       */\n      if (!this.features[key] && FeatureConstructor && isEnabled(this.props)) {\n        this.features[key] = new FeatureConstructor(this);\n      }\n      /**\n       * If we have a feature, mount or update it.\n       */\n      if (this.features[key]) {\n        const feature = this.features[key];\n        if (feature.isMounted) {\n          feature.update();\n        } else {\n          feature.mount();\n          feature.isMounted = true;\n        }\n      }\n    }\n  }\n  triggerBuild() {\n    this.build(this.renderState, this.latestValues, this.props);\n  }\n  /**\n   * Measure the current viewport box with or without transforms.\n   * Only measures axis-aligned boxes, rotate and skew must be manually\n   * removed with a re-render to work.\n   */\n  measureViewportBox() {\n    return this.current ? this.measureInstanceViewportBox(this.current, this.props) : createBox();\n  }\n  getStaticValue(key) {\n    return this.latestValues[key];\n  }\n  setStaticValue(key, value) {\n    this.latestValues[key] = value;\n  }\n  /**\n   * Update the provided props. Ensure any newly-added motion values are\n   * added to our map, old ones removed, and listeners updated.\n   */\n  update(props, presenceContext) {\n    if (props.transformTemplate || this.props.transformTemplate) {\n      this.scheduleRender();\n    }\n    this.prevProps = this.props;\n    this.props = props;\n    this.prevPresenceContext = this.presenceContext;\n    this.presenceContext = presenceContext;\n    /**\n     * Update prop event handlers ie onAnimationStart, onAnimationComplete\n     */\n    for (let i = 0; i < propEventHandlers.length; i++) {\n      const key = propEventHandlers[i];\n      if (this.propEventSubscriptions[key]) {\n        this.propEventSubscriptions[key]();\n        delete this.propEventSubscriptions[key];\n      }\n      const listenerName = \"on\" + key;\n      const listener = props[listenerName];\n      if (listener) {\n        this.propEventSubscriptions[key] = this.on(key, listener);\n      }\n    }\n    this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps, this), this.prevMotionValues);\n    if (this.handleChildMotionValue) {\n      this.handleChildMotionValue();\n    }\n  }\n  getProps() {\n    return this.props;\n  }\n  /**\n   * Returns the variant definition with a given name.\n   */\n  getVariant(name) {\n    return this.props.variants ? this.props.variants[name] : undefined;\n  }\n  /**\n   * Returns the defined default transition on this component.\n   */\n  getDefaultTransition() {\n    return this.props.transition;\n  }\n  getTransformPagePoint() {\n    return this.props.transformPagePoint;\n  }\n  getClosestVariantNode() {\n    return this.isVariantNode ? this : this.parent ? this.parent.getClosestVariantNode() : undefined;\n  }\n  /**\n   * Add a child visual element to our set of children.\n   */\n  addVariantChild(child) {\n    const closestVariantNode = this.getClosestVariantNode();\n    if (closestVariantNode) {\n      closestVariantNode.variantChildren && closestVariantNode.variantChildren.add(child);\n      return () => closestVariantNode.variantChildren.delete(child);\n    }\n  }\n  /**\n   * Add a motion value and bind it to this visual element.\n   */\n  addValue(key, value) {\n    // Remove existing value if it exists\n    const existingValue = this.values.get(key);\n    if (value !== existingValue) {\n      if (existingValue) this.removeValue(key);\n      this.bindToMotionValue(key, value);\n      this.values.set(key, value);\n      this.latestValues[key] = value.get();\n    }\n  }\n  /**\n   * Remove a motion value and unbind any active subscriptions.\n   */\n  removeValue(key) {\n    this.values.delete(key);\n    const unsubscribe = this.valueSubscriptions.get(key);\n    if (unsubscribe) {\n      unsubscribe();\n      this.valueSubscriptions.delete(key);\n    }\n    delete this.latestValues[key];\n    this.removeValueFromRenderState(key, this.renderState);\n  }\n  /**\n   * Check whether we have a motion value for this key\n   */\n  hasValue(key) {\n    return this.values.has(key);\n  }\n  getValue(key, defaultValue) {\n    if (this.props.values && this.props.values[key]) {\n      return this.props.values[key];\n    }\n    let value = this.values.get(key);\n    if (value === undefined && defaultValue !== undefined) {\n      value = motionValue(defaultValue === null ? undefined : defaultValue, {\n        owner: this\n      });\n      this.addValue(key, value);\n    }\n    return value;\n  }\n  /**\n   * If we're trying to animate to a previously unencountered value,\n   * we need to check for it in our state and as a last resort read it\n   * directly from the instance (which might have performance implications).\n   */\n  readValue(key, target) {\n    let value = this.latestValues[key] !== undefined || !this.current ? this.latestValues[key] : this.getBaseTargetFromProps(this.props, key) ?? this.readValueFromInstance(this.current, key, this.options);\n    if (value !== undefined && value !== null) {\n      if (typeof value === \"string\" && (isNumericalString(value) || isZeroValueString(value))) {\n        // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n        value = parseFloat(value);\n      } else if (!findValueType(value) && complex.test(target)) {\n        value = getAnimatableNone(key, target);\n      }\n      this.setBaseTarget(key, isMotionValue(value) ? value.get() : value);\n    }\n    return isMotionValue(value) ? value.get() : value;\n  }\n  /**\n   * Set the base target to later animate back to. This is currently\n   * only hydrated on creation and when we first read a value.\n   */\n  setBaseTarget(key, value) {\n    this.baseTarget[key] = value;\n  }\n  /**\n   * Find the base target for a value thats been removed from all animation\n   * props.\n   */\n  getBaseTarget(key) {\n    const {\n      initial\n    } = this.props;\n    let valueFromInitial;\n    if (typeof initial === \"string\" || typeof initial === \"object\") {\n      const variant = resolveVariantFromProps(this.props, initial, this.presenceContext?.custom);\n      if (variant) {\n        valueFromInitial = variant[key];\n      }\n    }\n    /**\n     * If this value still exists in the current initial variant, read that.\n     */\n    if (initial && valueFromInitial !== undefined) {\n      return valueFromInitial;\n    }\n    /**\n     * Alternatively, if this VisualElement config has defined a getBaseTarget\n     * so we can read the value from an alternative source, try that.\n     */\n    const target = this.getBaseTargetFromProps(this.props, key);\n    if (target !== undefined && !isMotionValue(target)) return target;\n    /**\n     * If the value was initially defined on initial, but it doesn't any more,\n     * return undefined. Otherwise return the value as initially read from the DOM.\n     */\n    return this.initialValues[key] !== undefined && valueFromInitial === undefined ? undefined : this.baseTarget[key];\n  }\n  on(eventName, callback) {\n    if (!this.events[eventName]) {\n      this.events[eventName] = new SubscriptionManager();\n    }\n    return this.events[eventName].add(callback);\n  }\n  notify(eventName) {\n    if (this.events[eventName]) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      this.events[eventName].notify(...args);\n    }\n  }\n}\nexport { VisualElement };", "map": {"version": 3, "names": ["KeyframeResolver", "time", "frame", "isMotionValue", "cancelFrame", "transformProps", "motionValue", "findValueType", "complex", "getAnimatableNone", "warnOnce", "isNumericalString", "isZeroValueString", "SubscriptionManager", "featureDefinitions", "createBox", "initPrefersReducedMotion", "hasReducedMotionListener", "prefersReducedMotion", "visualElementStore", "isControllingVariants", "isVariantNode", "updateMotionValuesFromProps", "resolveVariantFromProps", "propEventHandlers", "VisualElement", "scrapeMotionValuesFromProps", "_props", "_prevProps", "_visualElement", "constructor", "_ref", "parent", "props", "presenceContext", "reducedMotionConfig", "blockInitialAnimation", "visualState", "options", "arguments", "length", "undefined", "current", "children", "Set", "shouldReduceMotion", "values", "Map", "features", "valueSubscriptions", "prevMotionValues", "events", "propEventSubscriptions", "notifyUpdate", "notify", "latestValues", "render", "triggerBuild", "renderInstance", "renderState", "style", "projection", "renderScheduledAt", "scheduleRender", "now", "baseTarget", "initialValues", "initial", "depth", "Boolean", "variant<PERSON><PERSON><PERSON>n", "manuallyAnimateOnMount", "<PERSON><PERSON><PERSON><PERSON>", "initialMotionValues", "key", "value", "set", "mount", "instance", "removeFromVariantTree", "addVariant<PERSON>hild", "for<PERSON>ach", "bindToMotionValue", "process", "env", "NODE_ENV", "add", "update", "unmount", "remove", "clear", "delete", "feature", "isMounted", "has", "get", "valueIsTransform", "onBindTransform", "removeOnChange", "on", "latestValue", "onUpdate", "preRender", "isTransformDirty", "removeOnRenderRequest", "removeSyncCheck", "window", "MotionCheckAppearSync", "owner", "stop", "sortNodePosition", "other", "sortInstanceNodePosition", "type", "updateFeatures", "featureDefinition", "isEnabled", "Feature", "FeatureConstructor", "build", "measureViewportBox", "measureInstanceViewportBox", "getStaticValue", "setStaticValue", "transformTemplate", "prevProps", "prevPresenceContext", "i", "listenerName", "listener", "handleChildMotionValue", "getProps", "getVariant", "name", "variants", "getDefaultTransition", "transition", "getTransformPagePoint", "transformPagePoint", "getClosestVariantNode", "child", "closestVariantNode", "addValue", "existingValue", "removeValue", "unsubscribe", "removeValueFromRenderState", "hasValue", "getValue", "defaultValue", "readValue", "target", "getBaseTargetFromProps", "readValueFromInstance", "parseFloat", "test", "set<PERSON><PERSON><PERSON><PERSON>get", "getBase<PERSON>arget", "valueFromInitial", "variant", "custom", "eventName", "callback", "_len", "args", "Array", "_key"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/render/VisualElement.mjs"], "sourcesContent": ["import { KeyframeResolver, time, frame, isMotionValue, cancelFrame, transformProps, motionValue, findValueType, complex, getAnimatableNone } from 'motion-dom';\nimport { warnOnce, isNumericalString, isZeroValueString, SubscriptionManager } from 'motion-utils';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\n\nconst propEventHandlers = [\n    \"AnimationStart\",\n    \"AnimationComplete\",\n    \"Update\",\n    \"BeforeLayoutMeasure\",\n    \"LayoutMeasure\",\n    \"LayoutAnimationStart\",\n    \"LayoutAnimationComplete\",\n];\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n    /**\n     * This method takes React props and returns found MotionValues. For example, HTML\n     * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n     *\n     * This isn't an abstract method as it needs calling in the constructor, but it is\n     * intended to be one.\n     */\n    scrapeMotionValuesFromProps(_props, _prevProps, _visualElement) {\n        return {};\n    }\n    constructor({ parent, props, presenceContext, reducedMotionConfig, blockInitialAnimation, visualState, }, options = {}) {\n        /**\n         * A reference to the current underlying Instance, e.g. a HTMLElement\n         * or Three.Mesh etc.\n         */\n        this.current = null;\n        /**\n         * A set containing references to this VisualElement's children.\n         */\n        this.children = new Set();\n        /**\n         * Determine what role this visual element should take in the variant tree.\n         */\n        this.isVariantNode = false;\n        this.isControllingVariants = false;\n        /**\n         * Decides whether this VisualElement should animate in reduced motion\n         * mode.\n         *\n         * TODO: This is currently set on every individual VisualElement but feels\n         * like it could be set globally.\n         */\n        this.shouldReduceMotion = null;\n        /**\n         * A map of all motion values attached to this visual element. Motion\n         * values are source of truth for any given animated value. A motion\n         * value might be provided externally by the component via props.\n         */\n        this.values = new Map();\n        this.KeyframeResolver = KeyframeResolver;\n        /**\n         * Cleanup functions for active features (hover/tap/exit etc)\n         */\n        this.features = {};\n        /**\n         * A map of every subscription that binds the provided or generated\n         * motion values onChange listeners to this visual element.\n         */\n        this.valueSubscriptions = new Map();\n        /**\n         * A reference to the previously-provided motion values as returned\n         * from scrapeMotionValuesFromProps. We use the keys in here to determine\n         * if any motion values need to be removed after props are updated.\n         */\n        this.prevMotionValues = {};\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        /**\n         * An object containing an unsubscribe function for each prop event subscription.\n         * For example, every \"Update\" event can have multiple subscribers via\n         * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n         */\n        this.propEventSubscriptions = {};\n        this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n        this.render = () => {\n            if (!this.current)\n                return;\n            this.triggerBuild();\n            this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n        };\n        this.renderScheduledAt = 0.0;\n        this.scheduleRender = () => {\n            const now = time.now();\n            if (this.renderScheduledAt < now) {\n                this.renderScheduledAt = now;\n                frame.render(this.render, false, true);\n            }\n        };\n        const { latestValues, renderState } = visualState;\n        this.latestValues = latestValues;\n        this.baseTarget = { ...latestValues };\n        this.initialValues = props.initial ? { ...latestValues } : {};\n        this.renderState = renderState;\n        this.parent = parent;\n        this.props = props;\n        this.presenceContext = presenceContext;\n        this.depth = parent ? parent.depth + 1 : 0;\n        this.reducedMotionConfig = reducedMotionConfig;\n        this.options = options;\n        this.blockInitialAnimation = Boolean(blockInitialAnimation);\n        this.isControllingVariants = isControllingVariants(props);\n        this.isVariantNode = isVariantNode(props);\n        if (this.isVariantNode) {\n            this.variantChildren = new Set();\n        }\n        this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n        /**\n         * Any motion values that are provided to the element when created\n         * aren't yet bound to the element, as this would technically be impure.\n         * However, we iterate through the motion values and set them to the\n         * initial values for this component.\n         *\n         * TODO: This is impure and we should look at changing this to run on mount.\n         * Doing so will break some tests but this isn't necessarily a breaking change,\n         * more a reflection of the test.\n         */\n        const { willChange, ...initialMotionValues } = this.scrapeMotionValuesFromProps(props, {}, this);\n        for (const key in initialMotionValues) {\n            const value = initialMotionValues[key];\n            if (latestValues[key] !== undefined && isMotionValue(value)) {\n                value.set(latestValues[key], false);\n            }\n        }\n    }\n    mount(instance) {\n        this.current = instance;\n        visualElementStore.set(instance, this);\n        if (this.projection && !this.projection.instance) {\n            this.projection.mount(instance);\n        }\n        if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n            this.removeFromVariantTree = this.parent.addVariantChild(this);\n        }\n        this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n        if (!hasReducedMotionListener.current) {\n            initPrefersReducedMotion();\n        }\n        this.shouldReduceMotion =\n            this.reducedMotionConfig === \"never\"\n                ? false\n                : this.reducedMotionConfig === \"always\"\n                    ? true\n                    : prefersReducedMotion.current;\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n        }\n        if (this.parent)\n            this.parent.children.add(this);\n        this.update(this.props, this.presenceContext);\n    }\n    unmount() {\n        this.projection && this.projection.unmount();\n        cancelFrame(this.notifyUpdate);\n        cancelFrame(this.render);\n        this.valueSubscriptions.forEach((remove) => remove());\n        this.valueSubscriptions.clear();\n        this.removeFromVariantTree && this.removeFromVariantTree();\n        this.parent && this.parent.children.delete(this);\n        for (const key in this.events) {\n            this.events[key].clear();\n        }\n        for (const key in this.features) {\n            const feature = this.features[key];\n            if (feature) {\n                feature.unmount();\n                feature.isMounted = false;\n            }\n        }\n        this.current = null;\n    }\n    bindToMotionValue(key, value) {\n        if (this.valueSubscriptions.has(key)) {\n            this.valueSubscriptions.get(key)();\n        }\n        const valueIsTransform = transformProps.has(key);\n        if (valueIsTransform && this.onBindTransform) {\n            this.onBindTransform();\n        }\n        const removeOnChange = value.on(\"change\", (latestValue) => {\n            this.latestValues[key] = latestValue;\n            this.props.onUpdate && frame.preRender(this.notifyUpdate);\n            if (valueIsTransform && this.projection) {\n                this.projection.isTransformDirty = true;\n            }\n        });\n        const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n        let removeSyncCheck;\n        if (window.MotionCheckAppearSync) {\n            removeSyncCheck = window.MotionCheckAppearSync(this, key, value);\n        }\n        this.valueSubscriptions.set(key, () => {\n            removeOnChange();\n            removeOnRenderRequest();\n            if (removeSyncCheck)\n                removeSyncCheck();\n            if (value.owner)\n                value.stop();\n        });\n    }\n    sortNodePosition(other) {\n        /**\n         * If these nodes aren't even of the same type we can't compare their depth.\n         */\n        if (!this.current ||\n            !this.sortInstanceNodePosition ||\n            this.type !== other.type) {\n            return 0;\n        }\n        return this.sortInstanceNodePosition(this.current, other.current);\n    }\n    updateFeatures() {\n        let key = \"animation\";\n        for (key in featureDefinitions) {\n            const featureDefinition = featureDefinitions[key];\n            if (!featureDefinition)\n                continue;\n            const { isEnabled, Feature: FeatureConstructor } = featureDefinition;\n            /**\n             * If this feature is enabled but not active, make a new instance.\n             */\n            if (!this.features[key] &&\n                FeatureConstructor &&\n                isEnabled(this.props)) {\n                this.features[key] = new FeatureConstructor(this);\n            }\n            /**\n             * If we have a feature, mount or update it.\n             */\n            if (this.features[key]) {\n                const feature = this.features[key];\n                if (feature.isMounted) {\n                    feature.update();\n                }\n                else {\n                    feature.mount();\n                    feature.isMounted = true;\n                }\n            }\n        }\n    }\n    triggerBuild() {\n        this.build(this.renderState, this.latestValues, this.props);\n    }\n    /**\n     * Measure the current viewport box with or without transforms.\n     * Only measures axis-aligned boxes, rotate and skew must be manually\n     * removed with a re-render to work.\n     */\n    measureViewportBox() {\n        return this.current\n            ? this.measureInstanceViewportBox(this.current, this.props)\n            : createBox();\n    }\n    getStaticValue(key) {\n        return this.latestValues[key];\n    }\n    setStaticValue(key, value) {\n        this.latestValues[key] = value;\n    }\n    /**\n     * Update the provided props. Ensure any newly-added motion values are\n     * added to our map, old ones removed, and listeners updated.\n     */\n    update(props, presenceContext) {\n        if (props.transformTemplate || this.props.transformTemplate) {\n            this.scheduleRender();\n        }\n        this.prevProps = this.props;\n        this.props = props;\n        this.prevPresenceContext = this.presenceContext;\n        this.presenceContext = presenceContext;\n        /**\n         * Update prop event handlers ie onAnimationStart, onAnimationComplete\n         */\n        for (let i = 0; i < propEventHandlers.length; i++) {\n            const key = propEventHandlers[i];\n            if (this.propEventSubscriptions[key]) {\n                this.propEventSubscriptions[key]();\n                delete this.propEventSubscriptions[key];\n            }\n            const listenerName = (\"on\" + key);\n            const listener = props[listenerName];\n            if (listener) {\n                this.propEventSubscriptions[key] = this.on(key, listener);\n            }\n        }\n        this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps, this), this.prevMotionValues);\n        if (this.handleChildMotionValue) {\n            this.handleChildMotionValue();\n        }\n    }\n    getProps() {\n        return this.props;\n    }\n    /**\n     * Returns the variant definition with a given name.\n     */\n    getVariant(name) {\n        return this.props.variants ? this.props.variants[name] : undefined;\n    }\n    /**\n     * Returns the defined default transition on this component.\n     */\n    getDefaultTransition() {\n        return this.props.transition;\n    }\n    getTransformPagePoint() {\n        return this.props.transformPagePoint;\n    }\n    getClosestVariantNode() {\n        return this.isVariantNode\n            ? this\n            : this.parent\n                ? this.parent.getClosestVariantNode()\n                : undefined;\n    }\n    /**\n     * Add a child visual element to our set of children.\n     */\n    addVariantChild(child) {\n        const closestVariantNode = this.getClosestVariantNode();\n        if (closestVariantNode) {\n            closestVariantNode.variantChildren &&\n                closestVariantNode.variantChildren.add(child);\n            return () => closestVariantNode.variantChildren.delete(child);\n        }\n    }\n    /**\n     * Add a motion value and bind it to this visual element.\n     */\n    addValue(key, value) {\n        // Remove existing value if it exists\n        const existingValue = this.values.get(key);\n        if (value !== existingValue) {\n            if (existingValue)\n                this.removeValue(key);\n            this.bindToMotionValue(key, value);\n            this.values.set(key, value);\n            this.latestValues[key] = value.get();\n        }\n    }\n    /**\n     * Remove a motion value and unbind any active subscriptions.\n     */\n    removeValue(key) {\n        this.values.delete(key);\n        const unsubscribe = this.valueSubscriptions.get(key);\n        if (unsubscribe) {\n            unsubscribe();\n            this.valueSubscriptions.delete(key);\n        }\n        delete this.latestValues[key];\n        this.removeValueFromRenderState(key, this.renderState);\n    }\n    /**\n     * Check whether we have a motion value for this key\n     */\n    hasValue(key) {\n        return this.values.has(key);\n    }\n    getValue(key, defaultValue) {\n        if (this.props.values && this.props.values[key]) {\n            return this.props.values[key];\n        }\n        let value = this.values.get(key);\n        if (value === undefined && defaultValue !== undefined) {\n            value = motionValue(defaultValue === null ? undefined : defaultValue, { owner: this });\n            this.addValue(key, value);\n        }\n        return value;\n    }\n    /**\n     * If we're trying to animate to a previously unencountered value,\n     * we need to check for it in our state and as a last resort read it\n     * directly from the instance (which might have performance implications).\n     */\n    readValue(key, target) {\n        let value = this.latestValues[key] !== undefined || !this.current\n            ? this.latestValues[key]\n            : this.getBaseTargetFromProps(this.props, key) ??\n                this.readValueFromInstance(this.current, key, this.options);\n        if (value !== undefined && value !== null) {\n            if (typeof value === \"string\" &&\n                (isNumericalString(value) || isZeroValueString(value))) {\n                // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n                value = parseFloat(value);\n            }\n            else if (!findValueType(value) && complex.test(target)) {\n                value = getAnimatableNone(key, target);\n            }\n            this.setBaseTarget(key, isMotionValue(value) ? value.get() : value);\n        }\n        return isMotionValue(value) ? value.get() : value;\n    }\n    /**\n     * Set the base target to later animate back to. This is currently\n     * only hydrated on creation and when we first read a value.\n     */\n    setBaseTarget(key, value) {\n        this.baseTarget[key] = value;\n    }\n    /**\n     * Find the base target for a value thats been removed from all animation\n     * props.\n     */\n    getBaseTarget(key) {\n        const { initial } = this.props;\n        let valueFromInitial;\n        if (typeof initial === \"string\" || typeof initial === \"object\") {\n            const variant = resolveVariantFromProps(this.props, initial, this.presenceContext?.custom);\n            if (variant) {\n                valueFromInitial = variant[key];\n            }\n        }\n        /**\n         * If this value still exists in the current initial variant, read that.\n         */\n        if (initial && valueFromInitial !== undefined) {\n            return valueFromInitial;\n        }\n        /**\n         * Alternatively, if this VisualElement config has defined a getBaseTarget\n         * so we can read the value from an alternative source, try that.\n         */\n        const target = this.getBaseTargetFromProps(this.props, key);\n        if (target !== undefined && !isMotionValue(target))\n            return target;\n        /**\n         * If the value was initially defined on initial, but it doesn't any more,\n         * return undefined. Otherwise return the value as initially read from the DOM.\n         */\n        return this.initialValues[key] !== undefined &&\n            valueFromInitial === undefined\n            ? undefined\n            : this.baseTarget[key];\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        return this.events[eventName].add(callback);\n    }\n    notify(eventName, ...args) {\n        if (this.events[eventName]) {\n            this.events[eventName].notify(...args);\n        }\n    }\n}\n\nexport { VisualElement };\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,IAAI,EAAEC,KAAK,EAAEC,aAAa,EAAEC,WAAW,EAAEC,cAAc,EAAEC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAEC,iBAAiB,QAAQ,YAAY;AAC9J,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,mBAAmB,QAAQ,cAAc;AAClG,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,wBAAwB,EAAEC,oBAAoB,QAAQ,mCAAmC;AAClG,SAASC,kBAAkB,QAAQ,aAAa;AAChD,SAASC,qBAAqB,EAAEC,aAAa,QAAQ,qCAAqC;AAC1F,SAASC,2BAA2B,QAAQ,2BAA2B;AACvE,SAASC,uBAAuB,QAAQ,8BAA8B;AAEtE,MAAMC,iBAAiB,GAAG,CACtB,gBAAgB,EAChB,mBAAmB,EACnB,QAAQ,EACR,qBAAqB,EACrB,eAAe,EACf,sBAAsB,EACtB,yBAAyB,CAC5B;AACD;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,2BAA2BA,CAACC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAE;IAC5D,OAAO,CAAC,CAAC;EACb;EACAC,WAAWA,CAAAC,IAAA,EAA6G;IAAA,IAA5G;MAAEC,MAAM;MAAEC,KAAK;MAAEC,eAAe;MAAEC,mBAAmB;MAAEC,qBAAqB;MAAEC;IAAa,CAAC,GAAAN,IAAA;IAAA,IAAEO,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAClH;AACR;AACA;AACA;IACQ,IAAI,CAACG,OAAO,GAAG,IAAI;IACnB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,EAAE;IACzB;AACR;AACA;IACQ,IAAI,CAACvB,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,qBAAqB,GAAG,KAAK;IAClC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACyB,kBAAkB,GAAG,IAAI;IAC9B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,IAAIC,GAAG,EAAE;IACvB,IAAI,CAAC/C,gBAAgB,GAAGA,gBAAgB;IACxC;AACR;AACA;IACQ,IAAI,CAACgD,QAAQ,GAAG,CAAC,CAAC;IAClB;AACR;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,IAAIF,GAAG,EAAE;IACnC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACG,gBAAgB,GAAG,CAAC,CAAC;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,sBAAsB,GAAG,CAAC,CAAC;IAChC,IAAI,CAACC,YAAY,GAAG,MAAM,IAAI,CAACC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACC,YAAY,CAAC;IAClE,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAAC,IAAI,CAACd,OAAO,EACb;MACJ,IAAI,CAACe,YAAY,EAAE;MACnB,IAAI,CAACC,cAAc,CAAC,IAAI,CAAChB,OAAO,EAAE,IAAI,CAACiB,WAAW,EAAE,IAAI,CAAC1B,KAAK,CAAC2B,KAAK,EAAE,IAAI,CAACC,UAAU,CAAC;IAC1F,CAAC;IACD,IAAI,CAACC,iBAAiB,GAAG,GAAG;IAC5B,IAAI,CAACC,cAAc,GAAG,MAAM;MACxB,MAAMC,GAAG,GAAG/D,IAAI,CAAC+D,GAAG,EAAE;MACtB,IAAI,IAAI,CAACF,iBAAiB,GAAGE,GAAG,EAAE;QAC9B,IAAI,CAACF,iBAAiB,GAAGE,GAAG;QAC5B9D,KAAK,CAACsD,MAAM,CAAC,IAAI,CAACA,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;MAC1C;IACJ,CAAC;IACD,MAAM;MAAED,YAAY;MAAEI;IAAY,CAAC,GAAGtB,WAAW;IACjD,IAAI,CAACkB,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACU,UAAU,GAAG;MAAE,GAAGV;IAAa,CAAC;IACrC,IAAI,CAACW,aAAa,GAAGjC,KAAK,CAACkC,OAAO,GAAG;MAAE,GAAGZ;IAAa,CAAC,GAAG,CAAC,CAAC;IAC7D,IAAI,CAACI,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC3B,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACkC,KAAK,GAAGpC,MAAM,GAAGA,MAAM,CAACoC,KAAK,GAAG,CAAC,GAAG,CAAC;IAC1C,IAAI,CAACjC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACF,qBAAqB,GAAGiC,OAAO,CAACjC,qBAAqB,CAAC;IAC3D,IAAI,CAAChB,qBAAqB,GAAGA,qBAAqB,CAACa,KAAK,CAAC;IACzD,IAAI,CAACZ,aAAa,GAAGA,aAAa,CAACY,KAAK,CAAC;IACzC,IAAI,IAAI,CAACZ,aAAa,EAAE;MACpB,IAAI,CAACiD,eAAe,GAAG,IAAI1B,GAAG,EAAE;IACpC;IACA,IAAI,CAAC2B,sBAAsB,GAAGF,OAAO,CAACrC,MAAM,IAAIA,MAAM,CAACU,OAAO,CAAC;IAC/D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM;MAAE8B,UAAU;MAAE,GAAGC;IAAoB,CAAC,GAAG,IAAI,CAAC/C,2BAA2B,CAACO,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;IAChG,KAAK,MAAMyC,GAAG,IAAID,mBAAmB,EAAE;MACnC,MAAME,KAAK,GAAGF,mBAAmB,CAACC,GAAG,CAAC;MACtC,IAAInB,YAAY,CAACmB,GAAG,CAAC,KAAKjC,SAAS,IAAItC,aAAa,CAACwE,KAAK,CAAC,EAAE;QACzDA,KAAK,CAACC,GAAG,CAACrB,YAAY,CAACmB,GAAG,CAAC,EAAE,KAAK,CAAC;MACvC;IACJ;EACJ;EACAG,KAAKA,CAACC,QAAQ,EAAE;IACZ,IAAI,CAACpC,OAAO,GAAGoC,QAAQ;IACvB3D,kBAAkB,CAACyD,GAAG,CAACE,QAAQ,EAAE,IAAI,CAAC;IACtC,IAAI,IAAI,CAACjB,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACiB,QAAQ,EAAE;MAC9C,IAAI,CAACjB,UAAU,CAACgB,KAAK,CAACC,QAAQ,CAAC;IACnC;IACA,IAAI,IAAI,CAAC9C,MAAM,IAAI,IAAI,CAACX,aAAa,IAAI,CAAC,IAAI,CAACD,qBAAqB,EAAE;MAClE,IAAI,CAAC2D,qBAAqB,GAAG,IAAI,CAAC/C,MAAM,CAACgD,eAAe,CAAC,IAAI,CAAC;IAClE;IACA,IAAI,CAAClC,MAAM,CAACmC,OAAO,CAAC,CAACN,KAAK,EAAED,GAAG,KAAK,IAAI,CAACQ,iBAAiB,CAACR,GAAG,EAAEC,KAAK,CAAC,CAAC;IACvE,IAAI,CAAC1D,wBAAwB,CAACyB,OAAO,EAAE;MACnC1B,wBAAwB,EAAE;IAC9B;IACA,IAAI,CAAC6B,kBAAkB,GACnB,IAAI,CAACV,mBAAmB,KAAK,OAAO,GAC9B,KAAK,GACL,IAAI,CAACA,mBAAmB,KAAK,QAAQ,GACjC,IAAI,GACJjB,oBAAoB,CAACwB,OAAO;IAC1C,IAAIyC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvC3E,QAAQ,CAAC,IAAI,CAACmC,kBAAkB,KAAK,IAAI,EAAE,wFAAwF,CAAC;IACxI;IACA,IAAI,IAAI,CAACb,MAAM,EACX,IAAI,CAACA,MAAM,CAACW,QAAQ,CAAC2C,GAAG,CAAC,IAAI,CAAC;IAClC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACtD,KAAK,EAAE,IAAI,CAACC,eAAe,CAAC;EACjD;EACAsD,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC3B,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC2B,OAAO,EAAE;IAC5CpF,WAAW,CAAC,IAAI,CAACiD,YAAY,CAAC;IAC9BjD,WAAW,CAAC,IAAI,CAACoD,MAAM,CAAC;IACxB,IAAI,CAACP,kBAAkB,CAACgC,OAAO,CAAEQ,MAAM,IAAKA,MAAM,EAAE,CAAC;IACrD,IAAI,CAACxC,kBAAkB,CAACyC,KAAK,EAAE;IAC/B,IAAI,CAACX,qBAAqB,IAAI,IAAI,CAACA,qBAAqB,EAAE;IAC1D,IAAI,CAAC/C,MAAM,IAAI,IAAI,CAACA,MAAM,CAACW,QAAQ,CAACgD,MAAM,CAAC,IAAI,CAAC;IAChD,KAAK,MAAMjB,GAAG,IAAI,IAAI,CAACvB,MAAM,EAAE;MAC3B,IAAI,CAACA,MAAM,CAACuB,GAAG,CAAC,CAACgB,KAAK,EAAE;IAC5B;IACA,KAAK,MAAMhB,GAAG,IAAI,IAAI,CAAC1B,QAAQ,EAAE;MAC7B,MAAM4C,OAAO,GAAG,IAAI,CAAC5C,QAAQ,CAAC0B,GAAG,CAAC;MAClC,IAAIkB,OAAO,EAAE;QACTA,OAAO,CAACJ,OAAO,EAAE;QACjBI,OAAO,CAACC,SAAS,GAAG,KAAK;MAC7B;IACJ;IACA,IAAI,CAACnD,OAAO,GAAG,IAAI;EACvB;EACAwC,iBAAiBA,CAACR,GAAG,EAAEC,KAAK,EAAE;IAC1B,IAAI,IAAI,CAAC1B,kBAAkB,CAAC6C,GAAG,CAACpB,GAAG,CAAC,EAAE;MAClC,IAAI,CAACzB,kBAAkB,CAAC8C,GAAG,CAACrB,GAAG,CAAC,EAAE;IACtC;IACA,MAAMsB,gBAAgB,GAAG3F,cAAc,CAACyF,GAAG,CAACpB,GAAG,CAAC;IAChD,IAAIsB,gBAAgB,IAAI,IAAI,CAACC,eAAe,EAAE;MAC1C,IAAI,CAACA,eAAe,EAAE;IAC1B;IACA,MAAMC,cAAc,GAAGvB,KAAK,CAACwB,EAAE,CAAC,QAAQ,EAAGC,WAAW,IAAK;MACvD,IAAI,CAAC7C,YAAY,CAACmB,GAAG,CAAC,GAAG0B,WAAW;MACpC,IAAI,CAACnE,KAAK,CAACoE,QAAQ,IAAInG,KAAK,CAACoG,SAAS,CAAC,IAAI,CAACjD,YAAY,CAAC;MACzD,IAAI2C,gBAAgB,IAAI,IAAI,CAACnC,UAAU,EAAE;QACrC,IAAI,CAACA,UAAU,CAAC0C,gBAAgB,GAAG,IAAI;MAC3C;IACJ,CAAC,CAAC;IACF,MAAMC,qBAAqB,GAAG7B,KAAK,CAACwB,EAAE,CAAC,eAAe,EAAE,IAAI,CAACpC,cAAc,CAAC;IAC5E,IAAI0C,eAAe;IACnB,IAAIC,MAAM,CAACC,qBAAqB,EAAE;MAC9BF,eAAe,GAAGC,MAAM,CAACC,qBAAqB,CAAC,IAAI,EAAEjC,GAAG,EAAEC,KAAK,CAAC;IACpE;IACA,IAAI,CAAC1B,kBAAkB,CAAC2B,GAAG,CAACF,GAAG,EAAE,MAAM;MACnCwB,cAAc,EAAE;MAChBM,qBAAqB,EAAE;MACvB,IAAIC,eAAe,EACfA,eAAe,EAAE;MACrB,IAAI9B,KAAK,CAACiC,KAAK,EACXjC,KAAK,CAACkC,IAAI,EAAE;IACpB,CAAC,CAAC;EACN;EACAC,gBAAgBA,CAACC,KAAK,EAAE;IACpB;AACR;AACA;IACQ,IAAI,CAAC,IAAI,CAACrE,OAAO,IACb,CAAC,IAAI,CAACsE,wBAAwB,IAC9B,IAAI,CAACC,IAAI,KAAKF,KAAK,CAACE,IAAI,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACD,wBAAwB,CAAC,IAAI,CAACtE,OAAO,EAAEqE,KAAK,CAACrE,OAAO,CAAC;EACrE;EACAwE,cAAcA,CAAA,EAAG;IACb,IAAIxC,GAAG,GAAG,WAAW;IACrB,KAAKA,GAAG,IAAI5D,kBAAkB,EAAE;MAC5B,MAAMqG,iBAAiB,GAAGrG,kBAAkB,CAAC4D,GAAG,CAAC;MACjD,IAAI,CAACyC,iBAAiB,EAClB;MACJ,MAAM;QAAEC,SAAS;QAAEC,OAAO,EAAEC;MAAmB,CAAC,GAAGH,iBAAiB;MACpE;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACnE,QAAQ,CAAC0B,GAAG,CAAC,IACnB4C,kBAAkB,IAClBF,SAAS,CAAC,IAAI,CAACnF,KAAK,CAAC,EAAE;QACvB,IAAI,CAACe,QAAQ,CAAC0B,GAAG,CAAC,GAAG,IAAI4C,kBAAkB,CAAC,IAAI,CAAC;MACrD;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAACtE,QAAQ,CAAC0B,GAAG,CAAC,EAAE;QACpB,MAAMkB,OAAO,GAAG,IAAI,CAAC5C,QAAQ,CAAC0B,GAAG,CAAC;QAClC,IAAIkB,OAAO,CAACC,SAAS,EAAE;UACnBD,OAAO,CAACL,MAAM,EAAE;QACpB,CAAC,MACI;UACDK,OAAO,CAACf,KAAK,EAAE;UACfe,OAAO,CAACC,SAAS,GAAG,IAAI;QAC5B;MACJ;IACJ;EACJ;EACApC,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC8D,KAAK,CAAC,IAAI,CAAC5D,WAAW,EAAE,IAAI,CAACJ,YAAY,EAAE,IAAI,CAACtB,KAAK,CAAC;EAC/D;EACA;AACJ;AACA;AACA;AACA;EACIuF,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC9E,OAAO,GACb,IAAI,CAAC+E,0BAA0B,CAAC,IAAI,CAAC/E,OAAO,EAAE,IAAI,CAACT,KAAK,CAAC,GACzDlB,SAAS,EAAE;EACrB;EACA2G,cAAcA,CAAChD,GAAG,EAAE;IAChB,OAAO,IAAI,CAACnB,YAAY,CAACmB,GAAG,CAAC;EACjC;EACAiD,cAAcA,CAACjD,GAAG,EAAEC,KAAK,EAAE;IACvB,IAAI,CAACpB,YAAY,CAACmB,GAAG,CAAC,GAAGC,KAAK;EAClC;EACA;AACJ;AACA;AACA;EACIY,MAAMA,CAACtD,KAAK,EAAEC,eAAe,EAAE;IAC3B,IAAID,KAAK,CAAC2F,iBAAiB,IAAI,IAAI,CAAC3F,KAAK,CAAC2F,iBAAiB,EAAE;MACzD,IAAI,CAAC7D,cAAc,EAAE;IACzB;IACA,IAAI,CAAC8D,SAAS,GAAG,IAAI,CAAC5F,KAAK;IAC3B,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC6F,mBAAmB,GAAG,IAAI,CAAC5F,eAAe;IAC/C,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC;AACR;AACA;IACQ,KAAK,IAAI6F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvG,iBAAiB,CAACgB,MAAM,EAAEuF,CAAC,EAAE,EAAE;MAC/C,MAAMrD,GAAG,GAAGlD,iBAAiB,CAACuG,CAAC,CAAC;MAChC,IAAI,IAAI,CAAC3E,sBAAsB,CAACsB,GAAG,CAAC,EAAE;QAClC,IAAI,CAACtB,sBAAsB,CAACsB,GAAG,CAAC,EAAE;QAClC,OAAO,IAAI,CAACtB,sBAAsB,CAACsB,GAAG,CAAC;MAC3C;MACA,MAAMsD,YAAY,GAAI,IAAI,GAAGtD,GAAI;MACjC,MAAMuD,QAAQ,GAAGhG,KAAK,CAAC+F,YAAY,CAAC;MACpC,IAAIC,QAAQ,EAAE;QACV,IAAI,CAAC7E,sBAAsB,CAACsB,GAAG,CAAC,GAAG,IAAI,CAACyB,EAAE,CAACzB,GAAG,EAAEuD,QAAQ,CAAC;MAC7D;IACJ;IACA,IAAI,CAAC/E,gBAAgB,GAAG5B,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAACI,2BAA2B,CAACO,KAAK,EAAE,IAAI,CAAC4F,SAAS,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC3E,gBAAgB,CAAC;IAC/I,IAAI,IAAI,CAACgF,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,EAAE;IACjC;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAClG,KAAK;EACrB;EACA;AACJ;AACA;EACImG,UAAUA,CAACC,IAAI,EAAE;IACb,OAAO,IAAI,CAACpG,KAAK,CAACqG,QAAQ,GAAG,IAAI,CAACrG,KAAK,CAACqG,QAAQ,CAACD,IAAI,CAAC,GAAG5F,SAAS;EACtE;EACA;AACJ;AACA;EACI8F,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACtG,KAAK,CAACuG,UAAU;EAChC;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACxG,KAAK,CAACyG,kBAAkB;EACxC;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACtH,aAAa,GACnB,IAAI,GACJ,IAAI,CAACW,MAAM,GACP,IAAI,CAACA,MAAM,CAAC2G,qBAAqB,EAAE,GACnClG,SAAS;EACvB;EACA;AACJ;AACA;EACIuC,eAAeA,CAAC4D,KAAK,EAAE;IACnB,MAAMC,kBAAkB,GAAG,IAAI,CAACF,qBAAqB,EAAE;IACvD,IAAIE,kBAAkB,EAAE;MACpBA,kBAAkB,CAACvE,eAAe,IAC9BuE,kBAAkB,CAACvE,eAAe,CAACgB,GAAG,CAACsD,KAAK,CAAC;MACjD,OAAO,MAAMC,kBAAkB,CAACvE,eAAe,CAACqB,MAAM,CAACiD,KAAK,CAAC;IACjE;EACJ;EACA;AACJ;AACA;EACIE,QAAQA,CAACpE,GAAG,EAAEC,KAAK,EAAE;IACjB;IACA,MAAMoE,aAAa,GAAG,IAAI,CAACjG,MAAM,CAACiD,GAAG,CAACrB,GAAG,CAAC;IAC1C,IAAIC,KAAK,KAAKoE,aAAa,EAAE;MACzB,IAAIA,aAAa,EACb,IAAI,CAACC,WAAW,CAACtE,GAAG,CAAC;MACzB,IAAI,CAACQ,iBAAiB,CAACR,GAAG,EAAEC,KAAK,CAAC;MAClC,IAAI,CAAC7B,MAAM,CAAC8B,GAAG,CAACF,GAAG,EAAEC,KAAK,CAAC;MAC3B,IAAI,CAACpB,YAAY,CAACmB,GAAG,CAAC,GAAGC,KAAK,CAACoB,GAAG,EAAE;IACxC;EACJ;EACA;AACJ;AACA;EACIiD,WAAWA,CAACtE,GAAG,EAAE;IACb,IAAI,CAAC5B,MAAM,CAAC6C,MAAM,CAACjB,GAAG,CAAC;IACvB,MAAMuE,WAAW,GAAG,IAAI,CAAChG,kBAAkB,CAAC8C,GAAG,CAACrB,GAAG,CAAC;IACpD,IAAIuE,WAAW,EAAE;MACbA,WAAW,EAAE;MACb,IAAI,CAAChG,kBAAkB,CAAC0C,MAAM,CAACjB,GAAG,CAAC;IACvC;IACA,OAAO,IAAI,CAACnB,YAAY,CAACmB,GAAG,CAAC;IAC7B,IAAI,CAACwE,0BAA0B,CAACxE,GAAG,EAAE,IAAI,CAACf,WAAW,CAAC;EAC1D;EACA;AACJ;AACA;EACIwF,QAAQA,CAACzE,GAAG,EAAE;IACV,OAAO,IAAI,CAAC5B,MAAM,CAACgD,GAAG,CAACpB,GAAG,CAAC;EAC/B;EACA0E,QAAQA,CAAC1E,GAAG,EAAE2E,YAAY,EAAE;IACxB,IAAI,IAAI,CAACpH,KAAK,CAACa,MAAM,IAAI,IAAI,CAACb,KAAK,CAACa,MAAM,CAAC4B,GAAG,CAAC,EAAE;MAC7C,OAAO,IAAI,CAACzC,KAAK,CAACa,MAAM,CAAC4B,GAAG,CAAC;IACjC;IACA,IAAIC,KAAK,GAAG,IAAI,CAAC7B,MAAM,CAACiD,GAAG,CAACrB,GAAG,CAAC;IAChC,IAAIC,KAAK,KAAKlC,SAAS,IAAI4G,YAAY,KAAK5G,SAAS,EAAE;MACnDkC,KAAK,GAAGrE,WAAW,CAAC+I,YAAY,KAAK,IAAI,GAAG5G,SAAS,GAAG4G,YAAY,EAAE;QAAEzC,KAAK,EAAE;MAAK,CAAC,CAAC;MACtF,IAAI,CAACkC,QAAQ,CAACpE,GAAG,EAAEC,KAAK,CAAC;IAC7B;IACA,OAAOA,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;EACI2E,SAASA,CAAC5E,GAAG,EAAE6E,MAAM,EAAE;IACnB,IAAI5E,KAAK,GAAG,IAAI,CAACpB,YAAY,CAACmB,GAAG,CAAC,KAAKjC,SAAS,IAAI,CAAC,IAAI,CAACC,OAAO,GAC3D,IAAI,CAACa,YAAY,CAACmB,GAAG,CAAC,GACtB,IAAI,CAAC8E,sBAAsB,CAAC,IAAI,CAACvH,KAAK,EAAEyC,GAAG,CAAC,IAC1C,IAAI,CAAC+E,qBAAqB,CAAC,IAAI,CAAC/G,OAAO,EAAEgC,GAAG,EAAE,IAAI,CAACpC,OAAO,CAAC;IACnE,IAAIqC,KAAK,KAAKlC,SAAS,IAAIkC,KAAK,KAAK,IAAI,EAAE;MACvC,IAAI,OAAOA,KAAK,KAAK,QAAQ,KACxBhE,iBAAiB,CAACgE,KAAK,CAAC,IAAI/D,iBAAiB,CAAC+D,KAAK,CAAC,CAAC,EAAE;QACxD;QACAA,KAAK,GAAG+E,UAAU,CAAC/E,KAAK,CAAC;MAC7B,CAAC,MACI,IAAI,CAACpE,aAAa,CAACoE,KAAK,CAAC,IAAInE,OAAO,CAACmJ,IAAI,CAACJ,MAAM,CAAC,EAAE;QACpD5E,KAAK,GAAGlE,iBAAiB,CAACiE,GAAG,EAAE6E,MAAM,CAAC;MAC1C;MACA,IAAI,CAACK,aAAa,CAAClF,GAAG,EAAEvE,aAAa,CAACwE,KAAK,CAAC,GAAGA,KAAK,CAACoB,GAAG,EAAE,GAAGpB,KAAK,CAAC;IACvE;IACA,OAAOxE,aAAa,CAACwE,KAAK,CAAC,GAAGA,KAAK,CAACoB,GAAG,EAAE,GAAGpB,KAAK;EACrD;EACA;AACJ;AACA;AACA;EACIiF,aAAaA,CAAClF,GAAG,EAAEC,KAAK,EAAE;IACtB,IAAI,CAACV,UAAU,CAACS,GAAG,CAAC,GAAGC,KAAK;EAChC;EACA;AACJ;AACA;AACA;EACIkF,aAAaA,CAACnF,GAAG,EAAE;IACf,MAAM;MAAEP;IAAQ,CAAC,GAAG,IAAI,CAAClC,KAAK;IAC9B,IAAI6H,gBAAgB;IACpB,IAAI,OAAO3F,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC5D,MAAM4F,OAAO,GAAGxI,uBAAuB,CAAC,IAAI,CAACU,KAAK,EAAEkC,OAAO,EAAE,IAAI,CAACjC,eAAe,EAAE8H,MAAM,CAAC;MAC1F,IAAID,OAAO,EAAE;QACTD,gBAAgB,GAAGC,OAAO,CAACrF,GAAG,CAAC;MACnC;IACJ;IACA;AACR;AACA;IACQ,IAAIP,OAAO,IAAI2F,gBAAgB,KAAKrH,SAAS,EAAE;MAC3C,OAAOqH,gBAAgB;IAC3B;IACA;AACR;AACA;AACA;IACQ,MAAMP,MAAM,GAAG,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAACvH,KAAK,EAAEyC,GAAG,CAAC;IAC3D,IAAI6E,MAAM,KAAK9G,SAAS,IAAI,CAACtC,aAAa,CAACoJ,MAAM,CAAC,EAC9C,OAAOA,MAAM;IACjB;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACrF,aAAa,CAACQ,GAAG,CAAC,KAAKjC,SAAS,IACxCqH,gBAAgB,KAAKrH,SAAS,GAC5BA,SAAS,GACT,IAAI,CAACwB,UAAU,CAACS,GAAG,CAAC;EAC9B;EACAyB,EAAEA,CAAC8D,SAAS,EAAEC,QAAQ,EAAE;IACpB,IAAI,CAAC,IAAI,CAAC/G,MAAM,CAAC8G,SAAS,CAAC,EAAE;MACzB,IAAI,CAAC9G,MAAM,CAAC8G,SAAS,CAAC,GAAG,IAAIpJ,mBAAmB,EAAE;IACtD;IACA,OAAO,IAAI,CAACsC,MAAM,CAAC8G,SAAS,CAAC,CAAC3E,GAAG,CAAC4E,QAAQ,CAAC;EAC/C;EACA5G,MAAMA,CAAC2G,SAAS,EAAW;IACvB,IAAI,IAAI,CAAC9G,MAAM,CAAC8G,SAAS,CAAC,EAAE;MAAA,SAAAE,IAAA,GAAA5H,SAAA,CAAAC,MAAA,EADX4H,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAA/H,SAAA,CAAA+H,IAAA;MAAA;MAEjB,IAAI,CAACnH,MAAM,CAAC8G,SAAS,CAAC,CAAC3G,MAAM,CAAC,GAAG8G,IAAI,CAAC;IAC1C;EACJ;AACJ;AAEA,SAAS3I,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}