{"ast": null, "code": "\"use client\";\n\nimport { jsx } from 'react/jsx-runtime';\nimport { useContext, useMemo } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { loadExternalIsValidProp } from '../../render/dom/utils/filter-props.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * `MotionConfig` is used to set configuration options for all children `motion` components.\n *\n * ```jsx\n * import { motion, MotionConfig } from \"framer-motion\"\n *\n * export function App() {\n *   return (\n *     <MotionConfig transition={{ type: \"spring\" }}>\n *       <motion.div animate={{ x: 100 }} />\n *     </MotionConfig>\n *   )\n * }\n * ```\n *\n * @public\n */\nfunction MotionConfig(_ref) {\n  let {\n    children,\n    isValidProp,\n    ...config\n  } = _ref;\n  isValidProp && loadExternalIsValidProp(isValidProp);\n  /**\n   * Inherit props from any parent MotionConfig components\n   */\n  config = {\n    ...useContext(MotionConfigContext),\n    ...config\n  };\n  /**\n   * Don't allow isStatic to change between renders as it affects how many hooks\n   * motion components fire.\n   */\n  config.isStatic = useConstant(() => config.isStatic);\n  /**\n   * Creating a new config context object will re-render every `motion` component\n   * every time it renders. So we only want to create a new one sparingly.\n   */\n  const context = useMemo(() => config, [JSON.stringify(config.transition), config.transformPagePoint, config.reducedMotion]);\n  return jsx(MotionConfigContext.Provider, {\n    value: context,\n    children: children\n  });\n}\nexport { MotionConfig };", "map": {"version": 3, "names": ["jsx", "useContext", "useMemo", "MotionConfigContext", "loadExternalIsValidProp", "useConstant", "MotionConfig", "_ref", "children", "isValidProp", "config", "isStatic", "context", "JSON", "stringify", "transition", "transformPagePoint", "reducedMotion", "Provider", "value"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/components/MotionConfig/index.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport { useContext, useMemo } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { loadExternalIsValidProp } from '../../render/dom/utils/filter-props.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * `MotionConfig` is used to set configuration options for all children `motion` components.\n *\n * ```jsx\n * import { motion, MotionConfig } from \"framer-motion\"\n *\n * export function App() {\n *   return (\n *     <MotionConfig transition={{ type: \"spring\" }}>\n *       <motion.div animate={{ x: 100 }} />\n *     </MotionConfig>\n *   )\n * }\n * ```\n *\n * @public\n */\nfunction MotionConfig({ children, isValidProp, ...config }) {\n    isValidProp && loadExternalIsValidProp(isValidProp);\n    /**\n     * Inherit props from any parent MotionConfig components\n     */\n    config = { ...useContext(MotionConfigContext), ...config };\n    /**\n     * Don't allow isStatic to change between renders as it affects how many hooks\n     * motion components fire.\n     */\n    config.isStatic = useConstant(() => config.isStatic);\n    /**\n     * Creating a new config context object will re-render every `motion` component\n     * every time it renders. So we only want to create a new one sparingly.\n     */\n    const context = useMemo(() => config, [\n        JSON.stringify(config.transition),\n        config.transformPagePoint,\n        config.reducedMotion,\n    ]);\n    return (jsx(MotionConfigContext.Provider, { value: context, children: children }));\n}\n\nexport { MotionConfig };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,WAAW,QAAQ,8BAA8B;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAAC,IAAA,EAAuC;EAAA,IAAtC;IAAEC,QAAQ;IAAEC,WAAW;IAAE,GAAGC;EAAO,CAAC,GAAAH,IAAA;EACtDE,WAAW,IAAIL,uBAAuB,CAACK,WAAW,CAAC;EACnD;AACJ;AACA;EACIC,MAAM,GAAG;IAAE,GAAGT,UAAU,CAACE,mBAAmB,CAAC;IAAE,GAAGO;EAAO,CAAC;EAC1D;AACJ;AACA;AACA;EACIA,MAAM,CAACC,QAAQ,GAAGN,WAAW,CAAC,MAAMK,MAAM,CAACC,QAAQ,CAAC;EACpD;AACJ;AACA;AACA;EACI,MAAMC,OAAO,GAAGV,OAAO,CAAC,MAAMQ,MAAM,EAAE,CAClCG,IAAI,CAACC,SAAS,CAACJ,MAAM,CAACK,UAAU,CAAC,EACjCL,MAAM,CAACM,kBAAkB,EACzBN,MAAM,CAACO,aAAa,CACvB,CAAC;EACF,OAAQjB,GAAG,CAACG,mBAAmB,CAACe,QAAQ,EAAE;IAAEC,KAAK,EAAEP,OAAO;IAAEJ,QAAQ,EAAEA;EAAS,CAAC,CAAC;AACrF;AAEA,SAASF,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}