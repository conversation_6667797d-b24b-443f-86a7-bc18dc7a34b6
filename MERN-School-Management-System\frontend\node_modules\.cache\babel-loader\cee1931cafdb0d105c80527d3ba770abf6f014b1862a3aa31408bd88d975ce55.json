{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\pages\\\\admin\\\\feeManagement\\\\FeeManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Grid, Paper, Typography, Box, Card, CardContent, Button, TextField, InputAdornment, Tab, Tabs, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, IconButton, Tooltip, LinearProgress, Alert } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Search as SearchIcon, Payment as PaymentIcon, AccountBalance as AccountBalanceIcon, TrendingUp as TrendingUpIcon, Warning as WarningIcon, CheckCircle as CheckCircleIcon, Schedule as ScheduleIcon, Receipt as ReceiptIcon, Print as PrintIcon, Send as SendIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledPaper = styled(Paper)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    padding: theme.spacing(3),\n    borderRadius: '16px',\n    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'\n  };\n});\n_c = StyledPaper;\nconst StatCard = styled(Card)(_ref2 => {\n  let {\n    theme,\n    gradient\n  } = _ref2;\n  return {\n    background: gradient,\n    color: 'white',\n    borderRadius: '16px',\n    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',\n    transition: 'all 0.3s ease',\n    '&:hover': {\n      transform: 'translateY(-4px)',\n      boxShadow: '0 12px 40px rgba(0, 0, 0, 0.2)'\n    }\n  };\n});\n_c2 = StatCard;\nconst TabPanel = _ref3 => {\n  let {\n    children,\n    value,\n    index,\n    ...other\n  } = _ref3;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"tabpanel\",\n    hidden: value !== index,\n    id: `fee-tabpanel-${index}`,\n    \"aria-labelledby\": `fee-tab-${index}`,\n    ...other,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 25\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 3\n  }, this);\n};\n_c3 = TabPanel;\nconst FeeManagement = () => {\n  _s();\n  const [tabValue, setTabValue] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Sample fee data\n  const feeStats = {\n    totalCollection: 125000,\n    pendingAmount: 35000,\n    overdueAmount: 12000,\n    collectionRate: 78\n  };\n  const feeRecords = [{\n    id: 1,\n    studentName: 'John Doe',\n    rollNumber: 'ST001',\n    class: '10A',\n    totalFee: 5000,\n    paidAmount: 3000,\n    pendingAmount: 2000,\n    dueDate: '2024-01-15',\n    status: 'Partial',\n    lastPayment: '2023-12-10'\n  }, {\n    id: 2,\n    studentName: 'Alice Smith',\n    rollNumber: 'ST002',\n    class: '10A',\n    totalFee: 5000,\n    paidAmount: 5000,\n    pendingAmount: 0,\n    dueDate: '2024-01-15',\n    status: 'Paid',\n    lastPayment: '2023-12-05'\n  }, {\n    id: 3,\n    studentName: 'Bob Johnson',\n    rollNumber: 'ST003',\n    class: '9B',\n    totalFee: 4500,\n    paidAmount: 0,\n    pendingAmount: 4500,\n    dueDate: '2023-12-15',\n    status: 'Overdue',\n    lastPayment: null\n  }];\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Paid':\n        return 'success';\n      case 'Partial':\n        return 'warning';\n      case 'Overdue':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const filteredRecords = feeRecords.filter(record => record.studentName.toLowerCase().includes(searchTerm.toLowerCase()) || record.rollNumber.toLowerCase().includes(searchTerm.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      mt: 2,\n      mb: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          fontWeight: \"bold\",\n          color: \"primary\",\n          gutterBottom: true,\n          children: \"\\uD83D\\uDCB0 Fee Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"Comprehensive fee tracking, due management, and payment processing\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            gradient: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    fontWeight: \"bold\",\n                    children: [\"$\", feeStats.totalCollection.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      opacity: 0.8\n                    },\n                    children: \"Total Collection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(AccountBalanceIcon, {\n                  sx: {\n                    fontSize: 48,\n                    opacity: 0.3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3,\n            delay: 0.1\n          },\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            gradient: \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    fontWeight: \"bold\",\n                    children: [\"$\", feeStats.pendingAmount.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      opacity: 0.8\n                    },\n                    children: \"Pending Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ScheduleIcon, {\n                  sx: {\n                    fontSize: 48,\n                    opacity: 0.3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3,\n            delay: 0.2\n          },\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            gradient: \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    fontWeight: \"bold\",\n                    children: [\"$\", feeStats.overdueAmount.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      opacity: 0.8\n                    },\n                    children: \"Overdue Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(WarningIcon, {\n                  sx: {\n                    fontSize: 48,\n                    opacity: 0.3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3,\n            delay: 0.3\n          },\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            gradient: \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    fontWeight: \"bold\",\n                    children: [feeStats.collectionRate, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      opacity: 0.8\n                    },\n                    children: \"Collection Rate\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                    variant: \"determinate\",\n                    value: feeStats.collectionRate,\n                    sx: {\n                      mt: 1,\n                      bgcolor: 'rgba(255,255,255,0.3)',\n                      '& .MuiLinearProgress-bar': {\n                        bgcolor: 'white'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n                  sx: {\n                    fontSize: 48,\n                    opacity: 0.3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StyledPaper, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          placeholder: \"Search students...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)\n          },\n          sx: {\n            minWidth: 300\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            startIcon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 68\n            }, this),\n            children: \"Collect Fee\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"secondary\",\n            startIcon: /*#__PURE__*/_jsxDEV(ReceiptIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 69\n            }, this),\n            children: \"Generate Receipt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"info\",\n            startIcon: /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 64\n            }, this),\n            children: \"Send Reminders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n        value: tabValue,\n        onChange: handleTabChange,\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Fee Records\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Due Payments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Discounts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Payment History\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 0,\n        children: /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          sx: {\n            borderRadius: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  bgcolor: 'primary.main'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: \"Student\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: \"Class\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: \"Total Fee\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: \"Paid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: \"Due Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: filteredRecords.map(record => /*#__PURE__*/_jsxDEV(TableRow, {\n                hover: true,\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"bold\",\n                      children: record.studentName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: record.rollNumber\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: record.class\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [\"$\", record.totalFee]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [\"$\", record.paidAmount]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    color: record.pendingAmount > 0 ? 'error' : 'success',\n                    fontWeight: \"bold\",\n                    children: [\"$\", record.pendingAmount]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: record.dueDate\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: record.status,\n                    color: getStatusColor(record.status),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    gap: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Collect Payment\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"primary\",\n                        children: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 354,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 353,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Print Receipt\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"secondary\",\n                        children: /*#__PURE__*/_jsxDEV(PrintIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 359,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 358,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Send Reminder\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"info\",\n                        children: /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 364,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this)]\n              }, record.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 1,\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Due Payments Alert\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), \"There are \", feeRecords.filter(r => r.status === 'Overdue').length, \" overdue payments requiring immediate attention.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Due Payment Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"text.secondary\",\n          children: \"Overdue payment tracking and reminder system will be displayed here.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Discount Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"text.secondary\",\n          children: \"Student discount schemes, scholarship management, and fee concessions will be displayed here.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Payment History\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"text.secondary\",\n          children: \"Complete payment transaction history and financial reports will be displayed here.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(FeeManagement, \"OO0ySx/D3/Gw0FY5QdcTS8StiD4=\");\n_c4 = FeeManagement;\nexport default FeeManagement;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"StyledPaper\");\n$RefreshReg$(_c2, \"StatCard\");\n$RefreshReg$(_c3, \"TabPanel\");\n$RefreshReg$(_c4, \"FeeManagement\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Grid", "Paper", "Typography", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "TextField", "InputAdornment", "Tab", "Tabs", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "LinearProgress", "<PERSON><PERSON>", "styled", "motion", "Search", "SearchIcon", "Payment", "PaymentIcon", "AccountBalance", "AccountBalanceIcon", "TrendingUp", "TrendingUpIcon", "Warning", "WarningIcon", "CheckCircle", "CheckCircleIcon", "Schedule", "ScheduleIcon", "Receipt", "ReceiptIcon", "Print", "PrintIcon", "Send", "SendIcon", "jsxDEV", "_jsxDEV", "StyledPaper", "_ref", "theme", "padding", "spacing", "borderRadius", "boxShadow", "_c", "StatCard", "_ref2", "gradient", "background", "color", "transition", "transform", "_c2", "TabPanel", "_ref3", "children", "value", "index", "other", "role", "hidden", "id", "sx", "p", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c3", "FeeManagement", "_s", "tabValue", "setTabValue", "searchTerm", "setSearchTerm", "feeStats", "totalCollection", "pendingAmount", "overdueAmount", "collectionRate", "feeRecords", "studentName", "rollNumber", "class", "totalFee", "paidAmount", "dueDate", "status", "lastPayment", "handleTabChange", "event", "newValue", "getStatusColor", "filteredRecords", "filter", "record", "toLowerCase", "includes", "max<PERSON><PERSON><PERSON>", "mt", "mb", "div", "initial", "opacity", "y", "animate", "duration", "variant", "fontWeight", "gutterBottom", "container", "item", "xs", "sm", "md", "display", "alignItems", "justifyContent", "toLocaleString", "fontSize", "delay", "bgcolor", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "min<PERSON><PERSON><PERSON>", "gap", "startIcon", "label", "component", "map", "hover", "size", "title", "severity", "r", "length", "_c4", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/pages/admin/feeManagement/FeeManagement.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Container,\n  Grid,\n  Paper,\n  Typography,\n  Box,\n  Card,\n  CardContent,\n  Button,\n  TextField,\n  InputAdornment,\n  Tab,\n  Tabs,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Chip,\n  IconButton,\n  Tooltip,\n  LinearProgress,\n  Alert\n} from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport {\n  Search as SearchIcon,\n  Payment as PaymentIcon,\n  AccountBalance as AccountBalanceIcon,\n  TrendingUp as TrendingUpIcon,\n  Warning as WarningIcon,\n  CheckCircle as CheckCircleIcon,\n  Schedule as ScheduleIcon,\n  Receipt as ReceiptIcon,\n  Print as PrintIcon,\n  Send as SendIcon\n} from '@mui/icons-material';\n\nconst StyledPaper = styled(Paper)(({ theme }) => ({\n  padding: theme.spacing(3),\n  borderRadius: '16px',\n  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n}));\n\nconst StatCard = styled(Card)(({ theme, gradient }) => ({\n  background: gradient,\n  color: 'white',\n  borderRadius: '16px',\n  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.2)',\n  },\n}));\n\nconst TabPanel = ({ children, value, index, ...other }) => (\n  <div\n    role=\"tabpanel\"\n    hidden={value !== index}\n    id={`fee-tabpanel-${index}`}\n    aria-labelledby={`fee-tab-${index}`}\n    {...other}\n  >\n    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}\n  </div>\n);\n\nconst FeeManagement = () => {\n  const [tabValue, setTabValue] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Sample fee data\n  const feeStats = {\n    totalCollection: 125000,\n    pendingAmount: 35000,\n    overdueAmount: 12000,\n    collectionRate: 78\n  };\n\n  const feeRecords = [\n    {\n      id: 1,\n      studentName: 'John Doe',\n      rollNumber: 'ST001',\n      class: '10A',\n      totalFee: 5000,\n      paidAmount: 3000,\n      pendingAmount: 2000,\n      dueDate: '2024-01-15',\n      status: 'Partial',\n      lastPayment: '2023-12-10'\n    },\n    {\n      id: 2,\n      studentName: 'Alice Smith',\n      rollNumber: 'ST002',\n      class: '10A',\n      totalFee: 5000,\n      paidAmount: 5000,\n      pendingAmount: 0,\n      dueDate: '2024-01-15',\n      status: 'Paid',\n      lastPayment: '2023-12-05'\n    },\n    {\n      id: 3,\n      studentName: 'Bob Johnson',\n      rollNumber: 'ST003',\n      class: '9B',\n      totalFee: 4500,\n      paidAmount: 0,\n      pendingAmount: 4500,\n      dueDate: '2023-12-15',\n      status: 'Overdue',\n      lastPayment: null\n    },\n  ];\n\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Paid': return 'success';\n      case 'Partial': return 'warning';\n      case 'Overdue': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const filteredRecords = feeRecords.filter(record =>\n    record.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    record.rollNumber.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ mt: 2, mb: 4 }}>\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box mb={4}>\n          <Typography variant=\"h4\" fontWeight=\"bold\" color=\"primary\" gutterBottom>\n            💰 Fee Management System\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Comprehensive fee tracking, due management, and payment processing\n          </Typography>\n        </Box>\n      </motion.div>\n\n      {/* Fee Statistics */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <StatCard gradient=\"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\">\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                  <Box>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      ${feeStats.totalCollection.toLocaleString()}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                      Total Collection\n                    </Typography>\n                  </Box>\n                  <AccountBalanceIcon sx={{ fontSize: 48, opacity: 0.3 }} />\n                </Box>\n              </CardContent>\n            </StatCard>\n          </motion.div>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3, delay: 0.1 }}\n          >\n            <StatCard gradient=\"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\">\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                  <Box>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      ${feeStats.pendingAmount.toLocaleString()}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                      Pending Amount\n                    </Typography>\n                  </Box>\n                  <ScheduleIcon sx={{ fontSize: 48, opacity: 0.3 }} />\n                </Box>\n              </CardContent>\n            </StatCard>\n          </motion.div>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3, delay: 0.2 }}\n          >\n            <StatCard gradient=\"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\">\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                  <Box>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      ${feeStats.overdueAmount.toLocaleString()}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                      Overdue Amount\n                    </Typography>\n                  </Box>\n                  <WarningIcon sx={{ fontSize: 48, opacity: 0.3 }} />\n                </Box>\n              </CardContent>\n            </StatCard>\n          </motion.div>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3, delay: 0.3 }}\n          >\n            <StatCard gradient=\"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\">\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                  <Box>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      {feeStats.collectionRate}%\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                      Collection Rate\n                    </Typography>\n                    <LinearProgress \n                      variant=\"determinate\" \n                      value={feeStats.collectionRate} \n                      sx={{ \n                        mt: 1, \n                        bgcolor: 'rgba(255,255,255,0.3)',\n                        '& .MuiLinearProgress-bar': {\n                          bgcolor: 'white'\n                        }\n                      }}\n                    />\n                  </Box>\n                  <TrendingUpIcon sx={{ fontSize: 48, opacity: 0.3 }} />\n                </Box>\n              </CardContent>\n            </StatCard>\n          </motion.div>\n        </Grid>\n      </Grid>\n\n      <StyledPaper>\n        <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n          <TextField\n            placeholder=\"Search students...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              ),\n            }}\n            sx={{ minWidth: 300 }}\n          />\n          <Box display=\"flex\" gap={2}>\n            <Button variant=\"contained\" color=\"primary\" startIcon={<PaymentIcon />}>\n              Collect Fee\n            </Button>\n            <Button variant=\"outlined\" color=\"secondary\" startIcon={<ReceiptIcon />}>\n              Generate Receipt\n            </Button>\n            <Button variant=\"outlined\" color=\"info\" startIcon={<SendIcon />}>\n              Send Reminders\n            </Button>\n          </Box>\n        </Box>\n\n        <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>\n          <Tab label=\"Fee Records\" />\n          <Tab label=\"Due Payments\" />\n          <Tab label=\"Discounts\" />\n          <Tab label=\"Payment History\" />\n        </Tabs>\n\n        <TabPanel value={tabValue} index={0}>\n          <TableContainer component={Paper} sx={{ borderRadius: '16px' }}>\n            <Table>\n              <TableHead>\n                <TableRow sx={{ bgcolor: 'primary.main' }}>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Student</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Class</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Total Fee</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Paid</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Pending</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Due Date</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Status</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Actions</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {filteredRecords.map((record) => (\n                  <TableRow key={record.id} hover>\n                    <TableCell>\n                      <Box>\n                        <Typography variant=\"body2\" fontWeight=\"bold\">\n                          {record.studentName}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {record.rollNumber}\n                        </Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>{record.class}</TableCell>\n                    <TableCell>${record.totalFee}</TableCell>\n                    <TableCell>${record.paidAmount}</TableCell>\n                    <TableCell>\n                      <Typography \n                        color={record.pendingAmount > 0 ? 'error' : 'success'}\n                        fontWeight=\"bold\"\n                      >\n                        ${record.pendingAmount}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{record.dueDate}</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={record.status}\n                        color={getStatusColor(record.status)}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Box display=\"flex\" gap={1}>\n                        <Tooltip title=\"Collect Payment\">\n                          <IconButton size=\"small\" color=\"primary\">\n                            <PaymentIcon />\n                          </IconButton>\n                        </Tooltip>\n                        <Tooltip title=\"Print Receipt\">\n                          <IconButton size=\"small\" color=\"secondary\">\n                            <PrintIcon />\n                          </IconButton>\n                        </Tooltip>\n                        <Tooltip title=\"Send Reminder\">\n                          <IconButton size=\"small\" color=\"info\">\n                            <SendIcon />\n                          </IconButton>\n                        </Tooltip>\n                      </Box>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </TabPanel>\n\n        <TabPanel value={tabValue} index={1}>\n          <Alert severity=\"warning\" sx={{ mb: 2 }}>\n            <Typography variant=\"h6\">Due Payments Alert</Typography>\n            There are {feeRecords.filter(r => r.status === 'Overdue').length} overdue payments requiring immediate attention.\n          </Alert>\n          <Typography variant=\"h6\" gutterBottom>\n            Due Payment Management\n          </Typography>\n          <Typography color=\"text.secondary\">\n            Overdue payment tracking and reminder system will be displayed here.\n          </Typography>\n        </TabPanel>\n\n        <TabPanel value={tabValue} index={2}>\n          <Typography variant=\"h6\" gutterBottom>\n            Discount Management\n          </Typography>\n          <Typography color=\"text.secondary\">\n            Student discount schemes, scholarship management, and fee concessions will be displayed here.\n          </Typography>\n        </TabPanel>\n\n        <TabPanel value={tabValue} index={3}>\n          <Typography variant=\"h6\" gutterBottom>\n            Payment History\n          </Typography>\n          <Typography color=\"text.secondary\">\n            Complete payment transaction history and financial reports will be displayed here.\n          </Typography>\n        </TabPanel>\n      </StyledPaper>\n    </Container>\n  );\n};\n\nexport default FeeManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,cAAc,EACdC,KAAK,QACA,eAAe;AACtB,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,cAAc,IAAIC,kBAAkB,EACpCC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,WAAW,GAAGxB,MAAM,CAACrB,KAAK,CAAC,CAAC8C,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IAChDE,OAAO,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;IACzBC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE;EACb,CAAC;AAAA,CAAC,CAAC;AAACC,EAAA,GAJEP,WAAW;AAMjB,MAAMQ,QAAQ,GAAGhC,MAAM,CAAClB,IAAI,CAAC,CAACmD,KAAA;EAAA,IAAC;IAAEP,KAAK;IAAEQ;EAAS,CAAC,GAAAD,KAAA;EAAA,OAAM;IACtDE,UAAU,EAAED,QAAQ;IACpBE,KAAK,EAAE,OAAO;IACdP,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE,gCAAgC;IAC3CO,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTC,SAAS,EAAE,kBAAkB;MAC7BR,SAAS,EAAE;IACb;EACF,CAAC;AAAA,CAAC,CAAC;AAACS,GAAA,GAVEP,QAAQ;AAYd,MAAMQ,QAAQ,GAAGC,KAAA;EAAA,IAAC;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,KAAK;IAAE,GAAGC;EAAM,CAAC,GAAAJ,KAAA;EAAA,oBACpDlB,OAAA;IACEuB,IAAI,EAAC,UAAU;IACfC,MAAM,EAAEJ,KAAK,KAAKC,KAAM;IACxBI,EAAE,EAAG,gBAAeJ,KAAM,EAAE;IAC5B,mBAAkB,WAAUA,KAAM,EAAE;IAAA,GAChCC,KAAK;IAAAH,QAAA,EAERC,KAAK,KAAKC,KAAK,iBAAIrB,OAAA,CAAC1C,GAAG;MAACoE,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAEA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAO;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACnD;AAAA,CACP;AAACC,GAAA,GAVIf,QAAQ;AAYd,MAAMgB,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnF,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACoF,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAMsF,QAAQ,GAAG;IACfC,eAAe,EAAE,MAAM;IACvBC,aAAa,EAAE,KAAK;IACpBC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE;EAClB,CAAC;EAED,MAAMC,UAAU,GAAG,CACjB;IACEnB,EAAE,EAAE,CAAC;IACLoB,WAAW,EAAE,UAAU;IACvBC,UAAU,EAAE,OAAO;IACnBC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBR,aAAa,EAAE,IAAI;IACnBS,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE;EACf,CAAC,EACD;IACE3B,EAAE,EAAE,CAAC;IACLoB,WAAW,EAAE,aAAa;IAC1BC,UAAU,EAAE,OAAO;IACnBC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBR,aAAa,EAAE,CAAC;IAChBS,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE;EACf,CAAC,EACD;IACE3B,EAAE,EAAE,CAAC;IACLoB,WAAW,EAAE,aAAa;IAC1BC,UAAU,EAAE,OAAO;IACnBC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,CAAC;IACbR,aAAa,EAAE,IAAI;IACnBS,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CnB,WAAW,CAACmB,QAAQ,CAAC;EACvB,CAAC;EAED,MAAMC,cAAc,GAAIL,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,SAAS;IAAC;EAE9B,CAAC;EAED,MAAMM,eAAe,GAAGb,UAAU,CAACc,MAAM,CAACC,MAAM,IAC9CA,MAAM,CAACd,WAAW,CAACe,WAAW,EAAE,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,EAAE,CAAC,IACnED,MAAM,CAACb,UAAU,CAACc,WAAW,EAAE,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,EAAE,CAAC,CACnE;EAED,oBACE5D,OAAA,CAAC9C,SAAS;IAAC4G,QAAQ,EAAC,IAAI;IAACpC,EAAE,EAAE;MAAEqC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAA7C,QAAA,gBAC5CnB,OAAA,CAACtB,MAAM,CAACuF,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BtD,UAAU,EAAE;QAAEwD,QAAQ,EAAE;MAAI,CAAE;MAAAnD,QAAA,eAE9BnB,OAAA,CAAC1C,GAAG;QAAC0G,EAAE,EAAE,CAAE;QAAA7C,QAAA,gBACTnB,OAAA,CAAC3C,UAAU;UAACkH,OAAO,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAAC3D,KAAK,EAAC,SAAS;UAAC4D,YAAY;UAAAtD,QAAA,EAAC;QAExE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACb/B,OAAA,CAAC3C,UAAU;UAACkH,OAAO,EAAC,OAAO;UAAC1D,KAAK,EAAC,gBAAgB;UAAAM,QAAA,EAAC;QAEnD;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACK,eAGb/B,OAAA,CAAC7C,IAAI;MAACuH,SAAS;MAACrE,OAAO,EAAE,CAAE;MAACqB,EAAE,EAAE;QAAEsC,EAAE,EAAE;MAAE,CAAE;MAAA7C,QAAA,gBACxCnB,OAAA,CAAC7C,IAAI;QAACwH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3D,QAAA,eAC9BnB,OAAA,CAACtB,MAAM,CAACuF,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BtD,UAAU,EAAE;YAAEwD,QAAQ,EAAE;UAAI,CAAE;UAAAnD,QAAA,eAE9BnB,OAAA,CAACS,QAAQ;YAACE,QAAQ,EAAC,mDAAmD;YAAAQ,QAAA,eACpEnB,OAAA,CAACxC,WAAW;cAAA2D,QAAA,eACVnB,OAAA,CAAC1C,GAAG;gBAACyH,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAA9D,QAAA,gBACpEnB,OAAA,CAAC1C,GAAG;kBAAA6D,QAAA,gBACFnB,OAAA,CAAC3C,UAAU;oBAACkH,OAAO,EAAC,IAAI;oBAACC,UAAU,EAAC,MAAM;oBAAArD,QAAA,GAAC,GACxC,EAACoB,QAAQ,CAACC,eAAe,CAAC0C,cAAc,EAAE;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAChC,eACb/B,OAAA,CAAC3C,UAAU;oBAACkH,OAAO,EAAC,OAAO;oBAAC7C,EAAE,EAAE;sBAAEyC,OAAO,EAAE;oBAAI,CAAE;oBAAAhD,QAAA,EAAC;kBAElD;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAa;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACT,eACN/B,OAAA,CAAChB,kBAAkB;kBAAC0C,EAAE,EAAE;oBAAEyD,QAAQ,EAAE,EAAE;oBAAEhB,OAAO,EAAE;kBAAI;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACtD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACR,eAEP/B,OAAA,CAAC7C,IAAI;QAACwH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3D,QAAA,eAC9BnB,OAAA,CAACtB,MAAM,CAACuF,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BtD,UAAU,EAAE;YAAEwD,QAAQ,EAAE,GAAG;YAAEc,KAAK,EAAE;UAAI,CAAE;UAAAjE,QAAA,eAE1CnB,OAAA,CAACS,QAAQ;YAACE,QAAQ,EAAC,mDAAmD;YAAAQ,QAAA,eACpEnB,OAAA,CAACxC,WAAW;cAAA2D,QAAA,eACVnB,OAAA,CAAC1C,GAAG;gBAACyH,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAA9D,QAAA,gBACpEnB,OAAA,CAAC1C,GAAG;kBAAA6D,QAAA,gBACFnB,OAAA,CAAC3C,UAAU;oBAACkH,OAAO,EAAC,IAAI;oBAACC,UAAU,EAAC,MAAM;oBAAArD,QAAA,GAAC,GACxC,EAACoB,QAAQ,CAACE,aAAa,CAACyC,cAAc,EAAE;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAC9B,eACb/B,OAAA,CAAC3C,UAAU;oBAACkH,OAAO,EAAC,OAAO;oBAAC7C,EAAE,EAAE;sBAAEyC,OAAO,EAAE;oBAAI,CAAE;oBAAAhD,QAAA,EAAC;kBAElD;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAa;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACT,eACN/B,OAAA,CAACR,YAAY;kBAACkC,EAAE,EAAE;oBAAEyD,QAAQ,EAAE,EAAE;oBAAEhB,OAAO,EAAE;kBAAI;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAChD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACR,eAEP/B,OAAA,CAAC7C,IAAI;QAACwH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3D,QAAA,eAC9BnB,OAAA,CAACtB,MAAM,CAACuF,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BtD,UAAU,EAAE;YAAEwD,QAAQ,EAAE,GAAG;YAAEc,KAAK,EAAE;UAAI,CAAE;UAAAjE,QAAA,eAE1CnB,OAAA,CAACS,QAAQ;YAACE,QAAQ,EAAC,mDAAmD;YAAAQ,QAAA,eACpEnB,OAAA,CAACxC,WAAW;cAAA2D,QAAA,eACVnB,OAAA,CAAC1C,GAAG;gBAACyH,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAA9D,QAAA,gBACpEnB,OAAA,CAAC1C,GAAG;kBAAA6D,QAAA,gBACFnB,OAAA,CAAC3C,UAAU;oBAACkH,OAAO,EAAC,IAAI;oBAACC,UAAU,EAAC,MAAM;oBAAArD,QAAA,GAAC,GACxC,EAACoB,QAAQ,CAACG,aAAa,CAACwC,cAAc,EAAE;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAC9B,eACb/B,OAAA,CAAC3C,UAAU;oBAACkH,OAAO,EAAC,OAAO;oBAAC7C,EAAE,EAAE;sBAAEyC,OAAO,EAAE;oBAAI,CAAE;oBAAAhD,QAAA,EAAC;kBAElD;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAa;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACT,eACN/B,OAAA,CAACZ,WAAW;kBAACsC,EAAE,EAAE;oBAAEyD,QAAQ,EAAE,EAAE;oBAAEhB,OAAO,EAAE;kBAAI;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAC/C;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACR,eAEP/B,OAAA,CAAC7C,IAAI;QAACwH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3D,QAAA,eAC9BnB,OAAA,CAACtB,MAAM,CAACuF,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BtD,UAAU,EAAE;YAAEwD,QAAQ,EAAE,GAAG;YAAEc,KAAK,EAAE;UAAI,CAAE;UAAAjE,QAAA,eAE1CnB,OAAA,CAACS,QAAQ;YAACE,QAAQ,EAAC,mDAAmD;YAAAQ,QAAA,eACpEnB,OAAA,CAACxC,WAAW;cAAA2D,QAAA,eACVnB,OAAA,CAAC1C,GAAG;gBAACyH,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAA9D,QAAA,gBACpEnB,OAAA,CAAC1C,GAAG;kBAAA6D,QAAA,gBACFnB,OAAA,CAAC3C,UAAU;oBAACkH,OAAO,EAAC,IAAI;oBAACC,UAAU,EAAC,MAAM;oBAAArD,QAAA,GACvCoB,QAAQ,CAACI,cAAc,EAAC,GAC3B;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAa,eACb/B,OAAA,CAAC3C,UAAU;oBAACkH,OAAO,EAAC,OAAO;oBAAC7C,EAAE,EAAE;sBAAEyC,OAAO,EAAE;oBAAI,CAAE;oBAAAhD,QAAA,EAAC;kBAElD;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAa,eACb/B,OAAA,CAACzB,cAAc;oBACbgG,OAAO,EAAC,aAAa;oBACrBnD,KAAK,EAAEmB,QAAQ,CAACI,cAAe;oBAC/BjB,EAAE,EAAE;sBACFqC,EAAE,EAAE,CAAC;sBACLsB,OAAO,EAAE,uBAAuB;sBAChC,0BAA0B,EAAE;wBAC1BA,OAAO,EAAE;sBACX;oBACF;kBAAE;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACE,eACN/B,OAAA,CAACd,cAAc;kBAACwC,EAAE,EAAE;oBAAEyD,QAAQ,EAAE,EAAE;oBAAEhB,OAAO,EAAE;kBAAI;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAClD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF,eAEP/B,OAAA,CAACC,WAAW;MAAAkB,QAAA,gBACVnB,OAAA,CAAC1C,GAAG;QAACyH,OAAO,EAAC,MAAM;QAACE,cAAc,EAAC,eAAe;QAACD,UAAU,EAAC,QAAQ;QAAChB,EAAE,EAAE,CAAE;QAAA7C,QAAA,gBAC3EnB,OAAA,CAACtC,SAAS;UACR4H,WAAW,EAAC,oBAAoB;UAChClE,KAAK,EAAEiB,UAAW;UAClBkD,QAAQ,EAAGC,CAAC,IAAKlD,aAAa,CAACkD,CAAC,CAACC,MAAM,CAACrE,KAAK,CAAE;UAC/CsE,UAAU,EAAE;YACVC,cAAc,eACZ3F,OAAA,CAACrC,cAAc;cAACiI,QAAQ,EAAC,OAAO;cAAAzE,QAAA,eAC9BnB,OAAA,CAACpB,UAAU;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAGpB,CAAE;UACFL,EAAE,EAAE;YAAEmE,QAAQ,EAAE;UAAI;QAAE;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACtB,eACF/B,OAAA,CAAC1C,GAAG;UAACyH,OAAO,EAAC,MAAM;UAACe,GAAG,EAAE,CAAE;UAAA3E,QAAA,gBACzBnB,OAAA,CAACvC,MAAM;YAAC8G,OAAO,EAAC,WAAW;YAAC1D,KAAK,EAAC,SAAS;YAACkF,SAAS,eAAE/F,OAAA,CAAClB,WAAW;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAI;YAAAZ,QAAA,EAAC;UAExE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAS,eACT/B,OAAA,CAACvC,MAAM;YAAC8G,OAAO,EAAC,UAAU;YAAC1D,KAAK,EAAC,WAAW;YAACkF,SAAS,eAAE/F,OAAA,CAACN,WAAW;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAI;YAAAZ,QAAA,EAAC;UAEzE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAS,eACT/B,OAAA,CAACvC,MAAM;YAAC8G,OAAO,EAAC,UAAU;YAAC1D,KAAK,EAAC,MAAM;YAACkF,SAAS,eAAE/F,OAAA,CAACF,QAAQ;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAI;YAAAZ,QAAA,EAAC;UAEjE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAS;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACL;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACF,eAEN/B,OAAA,CAACnC,IAAI;QAACuD,KAAK,EAAEe,QAAS;QAACoD,QAAQ,EAAElC,eAAgB;QAAC3B,EAAE,EAAE;UAAEsC,EAAE,EAAE;QAAE,CAAE;QAAA7C,QAAA,gBAC9DnB,OAAA,CAACpC,GAAG;UAACoI,KAAK,EAAC;QAAa;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,eAC3B/B,OAAA,CAACpC,GAAG;UAACoI,KAAK,EAAC;QAAc;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,eAC5B/B,OAAA,CAACpC,GAAG;UAACoI,KAAK,EAAC;QAAW;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,eACzB/B,OAAA,CAACpC,GAAG;UAACoI,KAAK,EAAC;QAAiB;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC1B,eAEP/B,OAAA,CAACiB,QAAQ;QAACG,KAAK,EAAEe,QAAS;QAACd,KAAK,EAAE,CAAE;QAAAF,QAAA,eAClCnB,OAAA,CAAC/B,cAAc;UAACgI,SAAS,EAAE7I,KAAM;UAACsE,EAAE,EAAE;YAAEpB,YAAY,EAAE;UAAO,CAAE;UAAAa,QAAA,eAC7DnB,OAAA,CAAClC,KAAK;YAAAqD,QAAA,gBACJnB,OAAA,CAAC9B,SAAS;cAAAiD,QAAA,eACRnB,OAAA,CAAC7B,QAAQ;gBAACuD,EAAE,EAAE;kBAAE2D,OAAO,EAAE;gBAAe,CAAE;gBAAAlE,QAAA,gBACxCnB,OAAA,CAAChC,SAAS;kBAAC0D,EAAE,EAAE;oBAAEb,KAAK,EAAE,OAAO;oBAAE2D,UAAU,EAAE;kBAAO,CAAE;kBAAArD,QAAA,EAAC;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAY,eAC1E/B,OAAA,CAAChC,SAAS;kBAAC0D,EAAE,EAAE;oBAAEb,KAAK,EAAE,OAAO;oBAAE2D,UAAU,EAAE;kBAAO,CAAE;kBAAArD,QAAA,EAAC;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAY,eACxE/B,OAAA,CAAChC,SAAS;kBAAC0D,EAAE,EAAE;oBAAEb,KAAK,EAAE,OAAO;oBAAE2D,UAAU,EAAE;kBAAO,CAAE;kBAAArD,QAAA,EAAC;gBAAS;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAY,eAC5E/B,OAAA,CAAChC,SAAS;kBAAC0D,EAAE,EAAE;oBAAEb,KAAK,EAAE,OAAO;oBAAE2D,UAAU,EAAE;kBAAO,CAAE;kBAAArD,QAAA,EAAC;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAY,eACvE/B,OAAA,CAAChC,SAAS;kBAAC0D,EAAE,EAAE;oBAAEb,KAAK,EAAE,OAAO;oBAAE2D,UAAU,EAAE;kBAAO,CAAE;kBAAArD,QAAA,EAAC;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAY,eAC1E/B,OAAA,CAAChC,SAAS;kBAAC0D,EAAE,EAAE;oBAAEb,KAAK,EAAE,OAAO;oBAAE2D,UAAU,EAAE;kBAAO,CAAE;kBAAArD,QAAA,EAAC;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAY,eAC3E/B,OAAA,CAAChC,SAAS;kBAAC0D,EAAE,EAAE;oBAAEb,KAAK,EAAE,OAAO;oBAAE2D,UAAU,EAAE;kBAAO,CAAE;kBAAArD,QAAA,EAAC;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAY,eACzE/B,OAAA,CAAChC,SAAS;kBAAC0D,EAAE,EAAE;oBAAEb,KAAK,EAAE,OAAO;oBAAE2D,UAAU,EAAE;kBAAO,CAAE;kBAAArD,QAAA,EAAC;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAY;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACjE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACD,eACZ/B,OAAA,CAACjC,SAAS;cAAAoD,QAAA,EACPsC,eAAe,CAACyC,GAAG,CAAEvC,MAAM,iBAC1B3D,OAAA,CAAC7B,QAAQ;gBAAiBgI,KAAK;gBAAAhF,QAAA,gBAC7BnB,OAAA,CAAChC,SAAS;kBAAAmD,QAAA,eACRnB,OAAA,CAAC1C,GAAG;oBAAA6D,QAAA,gBACFnB,OAAA,CAAC3C,UAAU;sBAACkH,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,MAAM;sBAAArD,QAAA,EAC1CwC,MAAM,CAACd;oBAAW;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACR,eACb/B,OAAA,CAAC3C,UAAU;sBAACkH,OAAO,EAAC,SAAS;sBAAC1D,KAAK,EAAC,gBAAgB;sBAAAM,QAAA,EACjDwC,MAAM,CAACb;oBAAU;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACT;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACI,eACZ/B,OAAA,CAAChC,SAAS;kBAAAmD,QAAA,EAAEwC,MAAM,CAACZ;gBAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAa,eACrC/B,OAAA,CAAChC,SAAS;kBAAAmD,QAAA,GAAC,GAAC,EAACwC,MAAM,CAACX,QAAQ;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAa,eACzC/B,OAAA,CAAChC,SAAS;kBAAAmD,QAAA,GAAC,GAAC,EAACwC,MAAM,CAACV,UAAU;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAa,eAC3C/B,OAAA,CAAChC,SAAS;kBAAAmD,QAAA,eACRnB,OAAA,CAAC3C,UAAU;oBACTwD,KAAK,EAAE8C,MAAM,CAAClB,aAAa,GAAG,CAAC,GAAG,OAAO,GAAG,SAAU;oBACtD+B,UAAU,EAAC,MAAM;oBAAArD,QAAA,GAClB,GACE,EAACwC,MAAM,CAAClB,aAAa;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACX;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACH,eACZ/B,OAAA,CAAChC,SAAS;kBAAAmD,QAAA,EAAEwC,MAAM,CAACT;gBAAO;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAa,eACvC/B,OAAA,CAAChC,SAAS;kBAAAmD,QAAA,eACRnB,OAAA,CAAC5B,IAAI;oBACH4H,KAAK,EAAErC,MAAM,CAACR,MAAO;oBACrBtC,KAAK,EAAE2C,cAAc,CAACG,MAAM,CAACR,MAAM,CAAE;oBACrCiD,IAAI,EAAC;kBAAO;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACZ;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACQ,eACZ/B,OAAA,CAAChC,SAAS;kBAAAmD,QAAA,eACRnB,OAAA,CAAC1C,GAAG;oBAACyH,OAAO,EAAC,MAAM;oBAACe,GAAG,EAAE,CAAE;oBAAA3E,QAAA,gBACzBnB,OAAA,CAAC1B,OAAO;sBAAC+H,KAAK,EAAC,iBAAiB;sBAAAlF,QAAA,eAC9BnB,OAAA,CAAC3B,UAAU;wBAAC+H,IAAI,EAAC,OAAO;wBAACvF,KAAK,EAAC,SAAS;wBAAAM,QAAA,eACtCnB,OAAA,CAAClB,WAAW;0BAAA8C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA;sBAAG;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBACJ;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACL,eACV/B,OAAA,CAAC1B,OAAO;sBAAC+H,KAAK,EAAC,eAAe;sBAAAlF,QAAA,eAC5BnB,OAAA,CAAC3B,UAAU;wBAAC+H,IAAI,EAAC,OAAO;wBAACvF,KAAK,EAAC,WAAW;wBAAAM,QAAA,eACxCnB,OAAA,CAACJ,SAAS;0BAAAgC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA;sBAAG;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBACF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACL,eACV/B,OAAA,CAAC1B,OAAO;sBAAC+H,KAAK,EAAC,eAAe;sBAAAlF,QAAA,eAC5BnB,OAAA,CAAC3B,UAAU;wBAAC+H,IAAI,EAAC,OAAO;wBAACvF,KAAK,EAAC,MAAM;wBAAAM,QAAA,eACnCnB,OAAA,CAACF,QAAQ;0BAAA8B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA;sBAAG;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBACD;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACL;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACI;cAAA,GAhDC4B,MAAM,CAAClC,EAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAkDzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACQ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACO;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACR,eAEX/B,OAAA,CAACiB,QAAQ;QAACG,KAAK,EAAEe,QAAS;QAACd,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCnB,OAAA,CAACxB,KAAK;UAAC8H,QAAQ,EAAC,SAAS;UAAC5E,EAAE,EAAE;YAAEsC,EAAE,EAAE;UAAE,CAAE;UAAA7C,QAAA,gBACtCnB,OAAA,CAAC3C,UAAU;YAACkH,OAAO,EAAC,IAAI;YAAApD,QAAA,EAAC;UAAkB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAa,cAC9C,EAACa,UAAU,CAACc,MAAM,CAAC6C,CAAC,IAAIA,CAAC,CAACpD,MAAM,KAAK,SAAS,CAAC,CAACqD,MAAM,EAAC,kDACnE;QAAA;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAQ,eACR/B,OAAA,CAAC3C,UAAU;UAACkH,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAtD,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACb/B,OAAA,CAAC3C,UAAU;UAACwD,KAAK,EAAC,gBAAgB;UAAAM,QAAA,EAAC;QAEnC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACJ,eAEX/B,OAAA,CAACiB,QAAQ;QAACG,KAAK,EAAEe,QAAS;QAACd,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCnB,OAAA,CAAC3C,UAAU;UAACkH,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAtD,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACb/B,OAAA,CAAC3C,UAAU;UAACwD,KAAK,EAAC,gBAAgB;UAAAM,QAAA,EAAC;QAEnC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACJ,eAEX/B,OAAA,CAACiB,QAAQ;QAACG,KAAK,EAAEe,QAAS;QAACd,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCnB,OAAA,CAAC3C,UAAU;UAACkH,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAtD,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACb/B,OAAA,CAAC3C,UAAU;UAACwD,KAAK,EAAC,gBAAgB;UAAAM,QAAA,EAAC;QAEnC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACJ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACJ;AAEhB,CAAC;AAACG,EAAA,CAjVID,aAAa;AAAAwE,GAAA,GAAbxE,aAAa;AAmVnB,eAAeA,aAAa;AAAC,IAAAzB,EAAA,EAAAQ,GAAA,EAAAgB,GAAA,EAAAyE,GAAA;AAAAC,YAAA,CAAAlG,EAAA;AAAAkG,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}