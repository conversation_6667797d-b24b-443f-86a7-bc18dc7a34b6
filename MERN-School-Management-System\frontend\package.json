{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.131", "@mui/material": "^5.12.1", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.3.6", "dotenv": "^16.1.3", "eslint-config-react-app": "^7.0.1", "react": "^18.2.0", "react-countup": "^6.4.2", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "recharts": "^2.6.2", "redux": "^4.2.1", "styled-components": "^5.3.10"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"webpack": "^5.81.0"}}