{"version": 3, "file": "aggregate.js", "sourceRoot": "", "sources": ["../../src/operations/aggregate.ts"], "names": [], "mappings": ";;;AACA,+DAA0F;AAE1F,oCAAqD;AAKrD,oCAAiE;AACjE,oDAAgD;AAChD,uCAAkG;AAClG,2CAA+D;AAE/D,gBAAgB;AACH,QAAA,uBAAuB,GAAG,CAAU,CAAC;AAClD,MAAM,0CAA0C,GAAG,CAAC,CAAC;AAqCrD,gBAAgB;AAChB,MAAa,kBAAmB,SAAQ,0BAAgC;IAMtE,YAAY,EAAoB,EAAE,QAAoB,EAAE,OAA0B;QAChF,KAAK,CAAC,SAAS,EAAE,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhD,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QAE9B,gGAAgG;QAChG,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,IAAI,+BAAuB,CAAC;QAEvD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,qEAAqE;QACrE,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,OAAO,OAAO,EAAE,GAAG,KAAK,QAAQ,EAAE,CAAC;YACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5D,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC5B,CAAC;aAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACjD,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBACzC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAChC,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtC,MAAM,IAAI,iCAAyB,CACjC,wEAAwE,CACzE,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,EAAE,MAAM,IAAI,IAAI,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAClE,MAAM,IAAI,iCAAyB,CAAC,kCAAkC,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED,IAAa,WAAW;QACtB,OAAO,WAAoB,CAAC;IAC9B,CAAC;IAED,IAAa,YAAY;QACvB,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;IAC7B,CAAC;IAED,aAAa,CAAC,KAAe;QAC3B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAEQ,KAAK,CAAC,OAAO,CACpB,MAAc,EACd,OAAkC,EAClC,cAA8B;QAE9B,MAAM,OAAO,GAAqB,IAAI,CAAC,OAAO,CAAC;QAC/C,MAAM,iBAAiB,GAAG,IAAA,sBAAc,EAAC,MAAM,CAAC,CAAC;QACjD,MAAM,OAAO,GAAa,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9E,IAAI,IAAI,CAAC,aAAa,IAAI,iBAAiB,GAAG,0CAA0C,EAAE,CAAC;YACzF,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;QAC/B,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5C,4BAAY,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,OAAO,CAAC,wBAAwB,KAAK,IAAI,EAAE,CAAC;YAC9C,OAAO,CAAC,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,CAAC;QACtE,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YAC9C,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QAC9C,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC9B,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;YAChB,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QAC5B,CAAC;QAED,iEAAiE;QACjE,gDAAgD;QAChD,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAClC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QACpC,CAAC;QAED,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;QACtC,IAAI,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC7C,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAC/C,CAAC;QAED,OAAO,MAAM,KAAK,CAAC,cAAc,CAC/B,MAAM,EACN,OAAO,EACP,OAAO,EACP,cAAc,EACd,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,mCAAuB,CAAC,CAAC,CAAC,0BAAc,CACxD,CAAC;IACJ,CAAC;CACF;AA7GD,gDA6GC;AAED,IAAA,yBAAa,EAAC,kBAAkB,EAAE;IAChC,kBAAM,CAAC,cAAc;IACrB,kBAAM,CAAC,SAAS;IAChB,kBAAM,CAAC,WAAW;IAClB,kBAAM,CAAC,eAAe;CACvB,CAAC,CAAC"}