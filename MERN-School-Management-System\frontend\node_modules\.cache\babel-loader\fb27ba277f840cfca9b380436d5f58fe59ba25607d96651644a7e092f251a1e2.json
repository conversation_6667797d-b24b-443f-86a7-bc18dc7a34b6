{"ast": null, "code": "import { frame } from 'motion-dom';\nimport { useMotionValueEvent } from '../utils/use-motion-value-event.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\n\n/**\n * Creates a `MotionValue` that updates when the velocity of the provided `MotionValue` changes.\n *\n * ```javascript\n * const x = useMotionValue(0)\n * const xVelocity = useVelocity(x)\n * const xAcceleration = useVelocity(xVelocity)\n * ```\n *\n * @public\n */\nfunction useVelocity(value) {\n  const velocity = useMotionValue(value.getVelocity());\n  const updateVelocity = () => {\n    const latest = value.getVelocity();\n    velocity.set(latest);\n    /**\n     * If we still have velocity, schedule an update for the next frame\n     * to keep checking until it is zero.\n     */\n    if (latest) frame.update(updateVelocity);\n  };\n  useMotionValueEvent(value, \"change\", () => {\n    // Schedule an update to this value at the end of the current frame.\n    frame.update(updateVelocity, false, true);\n  });\n  return velocity;\n}\nexport { useVelocity };", "map": {"version": 3, "names": ["frame", "useMotionValueEvent", "useMotionValue", "useVelocity", "value", "velocity", "getVelocity", "updateVelocity", "latest", "set", "update"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/value/use-velocity.mjs"], "sourcesContent": ["import { frame } from 'motion-dom';\nimport { useMotionValueEvent } from '../utils/use-motion-value-event.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\n\n/**\n * Creates a `MotionValue` that updates when the velocity of the provided `MotionValue` changes.\n *\n * ```javascript\n * const x = useMotionValue(0)\n * const xVelocity = useVelocity(x)\n * const xAcceleration = useVelocity(xVelocity)\n * ```\n *\n * @public\n */\nfunction useVelocity(value) {\n    const velocity = useMotionValue(value.getVelocity());\n    const updateVelocity = () => {\n        const latest = value.getVelocity();\n        velocity.set(latest);\n        /**\n         * If we still have velocity, schedule an update for the next frame\n         * to keep checking until it is zero.\n         */\n        if (latest)\n            frame.update(updateVelocity);\n    };\n    useMotionValueEvent(value, \"change\", () => {\n        // Schedule an update to this value at the end of the current frame.\n        frame.update(updateVelocity, false, true);\n    });\n    return velocity;\n}\n\nexport { useVelocity };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,cAAc,QAAQ,wBAAwB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EACxB,MAAMC,QAAQ,GAAGH,cAAc,CAACE,KAAK,CAACE,WAAW,EAAE,CAAC;EACpD,MAAMC,cAAc,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAGJ,KAAK,CAACE,WAAW,EAAE;IAClCD,QAAQ,CAACI,GAAG,CAACD,MAAM,CAAC;IACpB;AACR;AACA;AACA;IACQ,IAAIA,MAAM,EACNR,KAAK,CAACU,MAAM,CAACH,cAAc,CAAC;EACpC,CAAC;EACDN,mBAAmB,CAACG,KAAK,EAAE,QAAQ,EAAE,MAAM;IACvC;IACAJ,KAAK,CAACU,MAAM,CAACH,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC;EAC7C,CAAC,CAAC;EACF,OAAOF,QAAQ;AACnB;AAEA,SAASF,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}