// Simple in-memory database for development
const fs = require('fs');
const path = require('path');

const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir);
}

const adminFile = path.join(dataDir, 'admin.json');
const classFile = path.join(dataDir, 'classes.json');

// Helper functions
const readFile = (filePath) => {
    try {
        if (fs.existsSync(filePath)) {
            return JSON.parse(fs.readFileSync(filePath, 'utf8'));
        }
        return null;
    } catch (error) {
        return null;
    }
};

const writeFile = (filePath, data) => {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
};

const generateId = () => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

// Initialize with default admin
const initializeDB = () => {
    if (!readFile(adminFile)) {
        const adminId = generateId();
        const admin = {
            _id: adminId,
            name: 'Administrator',
            email: '<EMAIL>',
            password: 'admin123',
            schoolName: 'Amar Vidya Mandir Higher Secondary School',
            phone: '7799505005',
            address: 'Partaj, Anantapuram, AP, India',
            createdAt: new Date().toISOString()
        };
        writeFile(adminFile, admin);
        
        // Create sample classes
        const classes = [
            { _id: generateId(), sclassName: 'Class 1', school: adminId, createdAt: new Date().toISOString() },
            { _id: generateId(), sclassName: 'Class 2', school: adminId, createdAt: new Date().toISOString() },
            { _id: generateId(), sclassName: 'Class 3', school: adminId, createdAt: new Date().toISOString() },
            { _id: generateId(), sclassName: 'Class 4', school: adminId, createdAt: new Date().toISOString() },
            { _id: generateId(), sclassName: 'Class 5', school: adminId, createdAt: new Date().toISOString() }
        ];
        writeFile(classFile, classes);
        
        console.log('✅ Simple database initialized');
        console.log('📧 Admin Email: <EMAIL>');
        console.log('🔑 Admin Password: admin123');
    }
};

// Database operations
const simpleDB = {
    // Admin operations
    findAdmin: (email) => {
        const admin = readFile(adminFile);
        if (admin && admin.email === email) {
            return admin;
        }
        return null;
    },
    
    getAdmin: () => {
        return readFile(adminFile);
    },
    
    // Class operations
    getClasses: (schoolId) => {
        const classes = readFile(classFile) || [];
        return classes.filter(cls => cls.school === schoolId);
    },
    
    getAllClasses: () => {
        return readFile(classFile) || [];
    },
    
    addClass: (classData) => {
        const classes = readFile(classFile) || [];
        const newClass = { ...classData, _id: generateId(), createdAt: new Date().toISOString() };
        classes.push(newClass);
        writeFile(classFile, classes);
        return newClass;
    }
};

module.exports = { simpleDB, initializeDB };
