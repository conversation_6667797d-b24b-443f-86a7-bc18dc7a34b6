{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from \"react-router-dom\";\nimport { useSelector } from 'react-redux';\nimport Homepage from './pages/Homepage';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport StudentDashboard from './pages/student/StudentDashboard';\nimport TeacherDashboard from './pages/teacher/TeacherDashboard';\nimport LoginPage from './pages/LoginPage';\nimport EnhancedLoginPage from './pages/EnhancedLoginPage';\nimport AdminRegisterPage from './pages/admin/AdminRegisterPage';\nimport ChooseUser from './pages/ChooseUser';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst App = () => {\n  _s();\n  const {\n    currentRole\n  } = useSelector(state => state.user);\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: [currentRole === null && /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Homepage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 36\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/choose\",\n        element: /*#__PURE__*/_jsxDEV(ChooseUser, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(EnhancedLoginPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/Adminlogin\",\n        element: /*#__PURE__*/_jsxDEV(EnhancedLoginPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/Studentlogin\",\n        element: /*#__PURE__*/_jsxDEV(EnhancedLoginPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/Teacherlogin\",\n        element: /*#__PURE__*/_jsxDEV(EnhancedLoginPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/Adminregister\",\n        element: /*#__PURE__*/_jsxDEV(AdminRegisterPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 36\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 9\n    }, this), currentRole === \"Admin\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 11\n      }, this)\n    }, void 0, false), currentRole === \"Student\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(StudentDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 11\n      }, this)\n    }, void 0, false), currentRole === \"Teacher\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(TeacherDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this)\n    }, void 0, false)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(App, \"xTn/0MaFqMbmEwlX0ZWaS+4LNDg=\", false, function () {\n  return [useSelector];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "useSelector", "Homepage", "AdminDashboard", "StudentDashboard", "TeacherDashboard", "LoginPage", "EnhancedLoginPage", "AdminRegisterPage", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "currentRole", "state", "user", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/App.js"], "sourcesContent": ["import React from 'react'\r\nimport { BrowserRouter as Router, Routes, Route, Navigate } from \"react-router-dom\";\r\nimport { useSelector } from 'react-redux';\r\nimport Homepage from './pages/Homepage';\r\nimport AdminDashboard from './pages/admin/AdminDashboard';\r\nimport StudentDashboard from './pages/student/StudentDashboard';\r\nimport TeacherDashboard from './pages/teacher/TeacherDashboard';\r\nimport LoginPage from './pages/LoginPage';\r\nimport EnhancedLoginPage from './pages/EnhancedLoginPage';\r\nimport AdminRegisterPage from './pages/admin/AdminRegisterPage';\r\nimport ChooseUser from './pages/ChooseUser';\r\n\r\nconst App = () => {\r\n  const { currentRole } = useSelector(state => state.user);\r\n\r\n  return (\r\n    <Router>\r\n      {currentRole === null &&\r\n        <Routes>\r\n          <Route path=\"/\" element={<Homepage />} />\r\n          <Route path=\"/choose\" element={<ChooseUser />} />\r\n\r\n          <Route path=\"/login\" element={<EnhancedLoginPage />} />\r\n          <Route path=\"/Adminlogin\" element={<EnhancedLoginPage />} />\r\n          <Route path=\"/Studentlogin\" element={<EnhancedLoginPage />} />\r\n          <Route path=\"/Teacherlogin\" element={<EnhancedLoginPage />} />\r\n\r\n          <Route path=\"/Adminregister\" element={<AdminRegisterPage />} />\r\n\r\n          <Route path='*' element={<Navigate to=\"/\" />} />\r\n        </Routes>}\r\n\r\n      {currentRole === \"Admin\" &&\r\n        <>\r\n          <AdminDashboard />\r\n        </>\r\n      }\r\n\r\n      {currentRole === \"Student\" &&\r\n        <>\r\n          <StudentDashboard />\r\n        </>\r\n      }\r\n\r\n      {currentRole === \"Teacher\" &&\r\n        <>\r\n          <TeacherDashboard />\r\n        </>\r\n      }\r\n    </Router>\r\n  )\r\n}\r\n\r\nexport default App"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM;IAAEC;EAAY,CAAC,GAAGf,WAAW,CAACgB,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EAExD,oBACEP,OAAA,CAACd,MAAM;IAAAsB,QAAA,GACJH,WAAW,KAAK,IAAI,iBACnBL,OAAA,CAACb,MAAM;MAAAqB,QAAA,gBACLR,OAAA,CAACZ,KAAK;QAACqB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEV,OAAA,CAACT,QAAQ;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eACzCd,OAAA,CAACZ,KAAK;QAACqB,IAAI,EAAC,SAAS;QAACC,OAAO,eAAEV,OAAA,CAACF,UAAU;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAEjDd,OAAA,CAACZ,KAAK;QAACqB,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEV,OAAA,CAACJ,iBAAiB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eACvDd,OAAA,CAACZ,KAAK;QAACqB,IAAI,EAAC,aAAa;QAACC,OAAO,eAAEV,OAAA,CAACJ,iBAAiB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAC5Dd,OAAA,CAACZ,KAAK;QAACqB,IAAI,EAAC,eAAe;QAACC,OAAO,eAAEV,OAAA,CAACJ,iBAAiB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAC9Dd,OAAA,CAACZ,KAAK;QAACqB,IAAI,EAAC,eAAe;QAACC,OAAO,eAAEV,OAAA,CAACJ,iBAAiB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAE9Dd,OAAA,CAACZ,KAAK;QAACqB,IAAI,EAAC,gBAAgB;QAACC,OAAO,eAAEV,OAAA,CAACH,iBAAiB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAE/Dd,OAAA,CAACZ,KAAK;QAACqB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEV,OAAA,CAACX,QAAQ;UAAC0B,EAAE,EAAC;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACzC,EAEVT,WAAW,KAAK,OAAO,iBACtBL,OAAA,CAAAE,SAAA;MAAAM,QAAA,eACER,OAAA,CAACR,cAAc;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAG,iBACjB,EAGJT,WAAW,KAAK,SAAS,iBACxBL,OAAA,CAAAE,SAAA;MAAAM,QAAA,eACER,OAAA,CAACP,gBAAgB;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAG,iBACnB,EAGJT,WAAW,KAAK,SAAS,iBACxBL,OAAA,CAAAE,SAAA;MAAAM,QAAA,eACER,OAAA,CAACN,gBAAgB;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAG,iBACnB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAEE;AAEb,CAAC;AAAAV,EAAA,CAvCKD,GAAG;EAAA,QACiBb,WAAW;AAAA;AAAA0B,EAAA,GAD/Bb,GAAG;AAyCT,eAAeA,GAAG;AAAA,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}