{"ast": null, "code": "import axios from 'axios';\nimport { getRequest, getSuccess, getFailed, getError, getStudentsSuccess, detailsSuccess, getFailedTwo, getSubjectsSuccess, getSubDetailsSuccess, getSubDetailsRequest } from './sclassSlice';\nconst REACT_APP_BASE_URL = \"http://localhost:5000\";\nexport const getAllSclasses = (id, address) => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}List/${id}`);\n    if (result.data.message) {\n      dispatch(getFailedTwo(result.data.message));\n    } else {\n      dispatch(getSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const getClassStudents = id => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/Sclass/Students/${id}`);\n    if (result.data.message) {\n      dispatch(getFailedTwo(result.data.message));\n    } else {\n      dispatch(getStudentsSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const getClassDetails = (id, address) => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\n    if (result.data) {\n      dispatch(detailsSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const getSubjectList = (id, address) => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\n    if (result.data.message) {\n      dispatch(getFailed(result.data.message));\n    } else {\n      dispatch(getSubjectsSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const getTeacherFreeClassSubjects = id => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/FreeSubjectList/${id}`);\n    if (result.data.message) {\n      dispatch(getFailed(result.data.message));\n    } else {\n      dispatch(getSubjectsSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const getSubjectDetails = (id, address) => async dispatch => {\n  dispatch(getSubDetailsRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\n    if (result.data) {\n      dispatch(getSubDetailsSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};", "map": {"version": 3, "names": ["axios", "getRequest", "getSuccess", "getFailed", "getError", "getStudentsSuccess", "detailsSuccess", "getFailedTwo", "getSubjectsSuccess", "getSubDetailsSuccess", "getSubDetailsRequest", "REACT_APP_BASE_URL", "getAllSclasses", "id", "address", "dispatch", "result", "get", "process", "env", "data", "message", "error", "getClassStudents", "getClassDetails", "getSubjectList", "getTeacherFreeClassSubjects", "getSubjectDetails"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/redux/sclassRelated/sclassHandle.js"], "sourcesContent": ["import axios from 'axios';\r\nimport {\r\n    getRequest,\r\n    getSuccess,\r\n    getFailed,\r\n    getError,\r\n    getStudentsSuccess,\r\n    detailsSuccess,\r\n    getFailedTwo,\r\n    getSubjectsSuccess,\r\n    getSubDetailsSuccess,\r\n    getSubDetailsRequest\r\n} from './sclassSlice';\r\n\r\nconst REACT_APP_BASE_URL = \"http://localhost:5000\";\r\n\r\nexport const getAllSclasses = (id, address) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}List/${id}`);\r\n        if (result.data.message) {\r\n            dispatch(getFailedTwo(result.data.message));\r\n        } else {\r\n            dispatch(getSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const getClassStudents = (id) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/Sclass/Students/${id}`);\r\n        if (result.data.message) {\r\n            dispatch(getFailedTwo(result.data.message));\r\n        } else {\r\n            dispatch(getStudentsSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const getClassDetails = (id, address) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\r\n        if (result.data) {\r\n            dispatch(detailsSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const getSubjectList = (id, address) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\r\n        if (result.data.message) {\r\n            dispatch(getFailed(result.data.message));\r\n        } else {\r\n            dispatch(getSubjectsSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const getTeacherFreeClassSubjects = (id) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/FreeSubjectList/${id}`);\r\n        if (result.data.message) {\r\n            dispatch(getFailed(result.data.message));\r\n        } else {\r\n            dispatch(getSubjectsSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const getSubjectDetails = (id, address) => async (dispatch) => {\r\n    dispatch(getSubDetailsRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\r\n        if (result.data) {\r\n            dispatch(getSubDetailsSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,UAAU,EACVC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,kBAAkB,EAClBC,oBAAoB,EACpBC,oBAAoB,QACjB,eAAe;AAEtB,MAAMC,kBAAkB,GAAG,uBAAuB;AAElD,OAAO,MAAMC,cAAc,GAAGA,CAACC,EAAE,EAAEC,OAAO,KAAK,MAAOC,QAAQ,IAAK;EAC/DA,QAAQ,CAACd,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMe,MAAM,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACR,kBAAmB,IAAGG,OAAQ,QAAOD,EAAG,EAAC,CAAC;IACxF,IAAIG,MAAM,CAACI,IAAI,CAACC,OAAO,EAAE;MACrBN,QAAQ,CAACR,YAAY,CAACS,MAAM,CAACI,IAAI,CAACC,OAAO,CAAC,CAAC;IAC/C,CAAC,MAAM;MACHN,QAAQ,CAACb,UAAU,CAACc,MAAM,CAACI,IAAI,CAAC,CAAC;IACrC;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZP,QAAQ,CAACX,QAAQ,CAACkB,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAIV,EAAE,IAAK,MAAOE,QAAQ,IAAK;EACxDA,QAAQ,CAACd,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMe,MAAM,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACR,kBAAmB,oBAAmBE,EAAG,EAAC,CAAC;IACzF,IAAIG,MAAM,CAACI,IAAI,CAACC,OAAO,EAAE;MACrBN,QAAQ,CAACR,YAAY,CAACS,MAAM,CAACI,IAAI,CAACC,OAAO,CAAC,CAAC;IAC/C,CAAC,MAAM;MACHN,QAAQ,CAACV,kBAAkB,CAACW,MAAM,CAACI,IAAI,CAAC,CAAC;IAC7C;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZP,QAAQ,CAACX,QAAQ,CAACkB,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAME,eAAe,GAAGA,CAACX,EAAE,EAAEC,OAAO,KAAK,MAAOC,QAAQ,IAAK;EAChEA,QAAQ,CAACd,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMe,MAAM,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACR,kBAAmB,IAAGG,OAAQ,IAAGD,EAAG,EAAC,CAAC;IACpF,IAAIG,MAAM,CAACI,IAAI,EAAE;MACbL,QAAQ,CAACT,cAAc,CAACU,MAAM,CAACI,IAAI,CAAC,CAAC;IACzC;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZP,QAAQ,CAACX,QAAQ,CAACkB,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMG,cAAc,GAAGA,CAACZ,EAAE,EAAEC,OAAO,KAAK,MAAOC,QAAQ,IAAK;EAC/DA,QAAQ,CAACd,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMe,MAAM,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACR,kBAAmB,IAAGG,OAAQ,IAAGD,EAAG,EAAC,CAAC;IACpF,IAAIG,MAAM,CAACI,IAAI,CAACC,OAAO,EAAE;MACrBN,QAAQ,CAACZ,SAAS,CAACa,MAAM,CAACI,IAAI,CAACC,OAAO,CAAC,CAAC;IAC5C,CAAC,MAAM;MACHN,QAAQ,CAACP,kBAAkB,CAACQ,MAAM,CAACI,IAAI,CAAC,CAAC;IAC7C;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZP,QAAQ,CAACX,QAAQ,CAACkB,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMI,2BAA2B,GAAIb,EAAE,IAAK,MAAOE,QAAQ,IAAK;EACnEA,QAAQ,CAACd,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMe,MAAM,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACR,kBAAmB,oBAAmBE,EAAG,EAAC,CAAC;IACzF,IAAIG,MAAM,CAACI,IAAI,CAACC,OAAO,EAAE;MACrBN,QAAQ,CAACZ,SAAS,CAACa,MAAM,CAACI,IAAI,CAACC,OAAO,CAAC,CAAC;IAC5C,CAAC,MAAM;MACHN,QAAQ,CAACP,kBAAkB,CAACQ,MAAM,CAACI,IAAI,CAAC,CAAC;IAC7C;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZP,QAAQ,CAACX,QAAQ,CAACkB,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMK,iBAAiB,GAAGA,CAACd,EAAE,EAAEC,OAAO,KAAK,MAAOC,QAAQ,IAAK;EAClEA,QAAQ,CAACL,oBAAoB,EAAE,CAAC;EAEhC,IAAI;IACA,MAAMM,MAAM,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACR,kBAAmB,IAAGG,OAAQ,IAAGD,EAAG,EAAC,CAAC;IACpF,IAAIG,MAAM,CAACI,IAAI,EAAE;MACbL,QAAQ,CAACN,oBAAoB,CAACO,MAAM,CAACI,IAAI,CAAC,CAAC;IAC/C;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZP,QAAQ,CAACX,QAAQ,CAACkB,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}