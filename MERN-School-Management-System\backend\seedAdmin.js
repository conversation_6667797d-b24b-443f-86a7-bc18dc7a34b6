const mongoose = require('mongoose');
const Admin = require('./models/adminSchema.js');
require('dotenv').config();

const seedAdmin = async () => {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URL, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('Connected to MongoDB');

        // Check if admin already exists
        const existingAdmin = await Admin.findOne({ email: '<EMAIL>' });
        
        if (existingAdmin) {
            console.log('Admin user already exists');
            process.exit(0);
        }

        // Create default admin user
        const defaultAdmin = new Admin({
            name: 'Administrator',
            email: '<EMAIL>',
            password: 'admin123', // Plain text password as per current implementation
            schoolName: 'Amar Vidya Mandir',
            phone: '**********',
            address: 'Partaj, Anantapuram, AP, India',
            description: 'Default administrator account for <PERSON>idy<PERSON>',
            schoolSettings: {
                primaryColor: '#667eea',
                secondaryColor: '#764ba2',
                timezone: 'Asia/Kolkata',
                academicYear: '2024-2025',
                currency: 'INR',
                language: 'en'
            }
        });

        await defaultAdmin.save();
        console.log('Default admin user created successfully!');
        console.log('Email: <EMAIL>');
        console.log('Password: admin123');
        
        process.exit(0);
    } catch (error) {
        console.error('Error creating admin user:', error);
        process.exit(1);
    }
};

seedAdmin();
