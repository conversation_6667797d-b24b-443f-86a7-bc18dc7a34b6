{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\components\\\\layout\\\\ResponsiveSidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Drawer, List, ListItemButton, ListItemIcon, ListItemText, Divider, Box, Typography, useTheme, useMediaQuery, Avatar, Chip } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Home as HomeIcon, Class as ClassIcon, Assignment as AssignmentIcon, SupervisorAccount as SupervisorAccountIcon, PersonOutline as PersonOutlineIcon, Announcement as AnnouncementIcon, Report as ReportIcon, AccountCircleOutlined as AccountCircleOutlinedIcon, ExitToApp as ExitToAppIcon, School as SchoolIcon,\n// New icons for additional modules\nInfo as InfoIcon, Payment as PaymentIcon, Description as DescriptionIcon, DirectionsBus as DirectionsBusIcon, People as PeopleIcon, PersonAdd as PersonAddIcon, EventAvailable as EventAvailableIcon, Quiz as QuizIcon, MenuBook as MenuBookIcon, NotificationsActive as NotificationsActiveIcon, Hotel as HotelIcon, Web as WebIcon, Business as BusinessIcon, ExpandLess, ExpandMore } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledDrawer = styled(Drawer)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    '& .MuiDrawer-paper': {\n      background: 'linear-gradient(180deg, #667eea 0%, #764ba2 100%)',\n      color: 'white',\n      borderRight: 'none',\n      boxShadow: '4px 0 20px rgba(0, 0, 0, 0.1)'\n    }\n  };\n});\n_c = StyledDrawer;\nconst SidebarHeader = styled(Box)(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    padding: theme.spacing(3, 2),\n    display: 'flex',\n    alignItems: 'center',\n    gap: theme.spacing(2),\n    borderBottom: '1px solid rgba(255, 255, 255, 0.1)'\n  };\n});\n_c2 = SidebarHeader;\nconst StyledListItemButton = styled(ListItemButton)(_ref3 => {\n  let {\n    theme,\n    active\n  } = _ref3;\n  return {\n    margin: theme.spacing(0.5, 1),\n    borderRadius: '12px',\n    padding: theme.spacing(1.5, 2),\n    backgroundColor: active ? 'rgba(255, 255, 255, 0.15)' : 'transparent',\n    backdropFilter: active ? 'blur(10px)' : 'none',\n    border: active ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid transparent',\n    transition: 'all 0.3s ease',\n    '&:hover': {\n      backgroundColor: 'rgba(255, 255, 255, 0.1)',\n      transform: 'translateX(4px)'\n    },\n    '&:before': {\n      content: '\"\"',\n      position: 'absolute',\n      left: 0,\n      top: '50%',\n      transform: 'translateY(-50%)',\n      width: active ? '4px' : '0px',\n      height: '60%',\n      backgroundColor: '#FFD700',\n      borderRadius: '0 4px 4px 0',\n      transition: 'width 0.3s ease'\n    }\n  };\n});\n_c3 = StyledListItemButton;\nconst MenuSection = styled(Box)(_ref4 => {\n  let {\n    theme\n  } = _ref4;\n  return {\n    padding: theme.spacing(1, 2),\n    marginTop: theme.spacing(2)\n  };\n});\n_c4 = MenuSection;\nconst SectionTitle = styled(Typography)(_ref5 => {\n  let {\n    theme\n  } = _ref5;\n  return {\n    fontSize: '0.75rem',\n    fontWeight: 600,\n    textTransform: 'uppercase',\n    letterSpacing: '0.5px',\n    opacity: 0.7,\n    marginBottom: theme.spacing(1)\n  };\n});\n_c5 = SectionTitle;\nconst ResponsiveSidebar = _ref6 => {\n  _s();\n  let {\n    open,\n    onClose,\n    variant = 'permanent'\n  } = _ref6;\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const location = useLocation();\n  const mainMenuItems = [{\n    text: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/dashboard',\n    badge: null\n  }, {\n    text: 'Classes',\n    icon: /*#__PURE__*/_jsxDEV(ClassIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/classes',\n    badge: null\n  }, {\n    text: 'Subjects',\n    icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/subjects',\n    badge: null\n  }, {\n    text: 'Teachers',\n    icon: /*#__PURE__*/_jsxDEV(SupervisorAccountIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/teachers',\n    badge: null\n  }, {\n    text: 'Students',\n    icon: /*#__PURE__*/_jsxDEV(PersonOutlineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/students',\n    badge: null\n  }, {\n    text: 'Notices',\n    icon: /*#__PURE__*/_jsxDEV(AnnouncementIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/notices',\n    badge: '3'\n  }, {\n    text: 'Complaints',\n    icon: /*#__PURE__*/_jsxDEV(ReportIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/complains',\n    badge: '2'\n  }];\n  const userMenuItems = [{\n    text: 'Profile',\n    icon: /*#__PURE__*/_jsxDEV(AccountCircleOutlinedIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 13\n    }, this),\n    path: '/Admin/profile',\n    badge: null\n  }, {\n    text: 'Logout',\n    icon: /*#__PURE__*/_jsxDEV(ExitToAppIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 13\n    }, this),\n    path: '/logout',\n    badge: null\n  }];\n  const isActive = path => {\n    if (path === '/Admin/dashboard') {\n      return location.pathname === '/' || location.pathname === '/Admin/dashboard';\n    }\n    return location.pathname.startsWith(path);\n  };\n  const drawerContent = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(SidebarHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        sx: {\n          bgcolor: 'rgba(255, 255, 255, 0.2)',\n          width: 48,\n          height: 48\n        },\n        children: /*#__PURE__*/_jsxDEV(SchoolIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"bold\",\n          children: \"School Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            opacity: 0.8\n          },\n          children: \"Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        overflowY: 'auto',\n        py: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(MenuSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"Main Menu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          sx: {\n            padding: 0\n          },\n          children: mainMenuItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(StyledListItemButton, {\n              component: Link,\n              to: item.path,\n              active: isActive(item.path),\n              onClick: isMobile ? onClose : undefined,\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                sx: {\n                  color: 'inherit',\n                  minWidth: 40\n                },\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: item.text,\n                primaryTypographyProps: {\n                  fontSize: '0.9rem',\n                  fontWeight: isActive(item.path) ? 600 : 400\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), item.badge && /*#__PURE__*/_jsxDEV(Chip, {\n                label: item.badge,\n                size: \"small\",\n                sx: {\n                  bgcolor: '#FFD700',\n                  color: '#333',\n                  fontSize: '0.7rem',\n                  height: 20,\n                  minWidth: 20\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mx: 2,\n          bgcolor: 'rgba(255, 255, 255, 0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          sx: {\n            padding: 0\n          },\n          children: userMenuItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: (mainMenuItems.length + index) * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(StyledListItemButton, {\n              component: Link,\n              to: item.path,\n              active: isActive(item.path),\n              onClick: isMobile ? onClose : undefined,\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                sx: {\n                  color: 'inherit',\n                  minWidth: 40\n                },\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: item.text,\n                primaryTypographyProps: {\n                  fontSize: '0.9rem',\n                  fontWeight: isActive(item.path) ? 600 : 400\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this)\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n  if (isMobile) {\n    return /*#__PURE__*/_jsxDEV(StyledDrawer, {\n      variant: \"temporary\",\n      open: open,\n      onClose: onClose,\n      ModalProps: {\n        keepMounted: true // Better open performance on mobile.\n      },\n\n      sx: {\n        '& .MuiDrawer-paper': {\n          width: 280\n        }\n      },\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(StyledDrawer, {\n    variant: variant,\n    open: open,\n    sx: {\n      '& .MuiDrawer-paper': {\n        width: open ? 280 : 70,\n        transition: theme.transitions.create('width', {\n          easing: theme.transitions.easing.sharp,\n          duration: theme.transitions.duration.enteringScreen\n        }),\n        overflowX: 'hidden'\n      }\n    },\n    children: drawerContent\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 303,\n    columnNumber: 5\n  }, this);\n};\n_s(ResponsiveSidebar, \"EDcwBIz4vaW9vHEUm4iGTJ9ZhSk=\", false, function () {\n  return [useTheme, useMediaQuery, useLocation];\n});\n_c6 = ResponsiveSidebar;\nexport default ResponsiveSidebar;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"StyledDrawer\");\n$RefreshReg$(_c2, \"SidebarHeader\");\n$RefreshReg$(_c3, \"StyledListItemButton\");\n$RefreshReg$(_c4, \"MenuSection\");\n$RefreshReg$(_c5, \"SectionTitle\");\n$RefreshReg$(_c6, \"ResponsiveSidebar\");", "map": {"version": 3, "names": ["React", "Drawer", "List", "ListItemButton", "ListItemIcon", "ListItemText", "Divider", "Box", "Typography", "useTheme", "useMediaQuery", "Avatar", "Chip", "styled", "motion", "Link", "useLocation", "Home", "HomeIcon", "Class", "ClassIcon", "Assignment", "AssignmentIcon", "SupervisorAccount", "SupervisorAccountIcon", "PersonOutline", "PersonOutlineIcon", "Announcement", "AnnouncementIcon", "Report", "ReportIcon", "AccountCircleOutlined", "AccountCircleOutlinedIcon", "ExitToApp", "ExitToAppIcon", "School", "SchoolIcon", "Info", "InfoIcon", "Payment", "PaymentIcon", "Description", "DescriptionIcon", "DirectionsBus", "DirectionsBusIcon", "People", "PeopleIcon", "PersonAdd", "PersonAddIcon", "EventAvailable", "EventAvailableIcon", "Quiz", "QuizIcon", "MenuBook", "MenuBookIcon", "NotificationsActive", "NotificationsActiveIcon", "Hotel", "HotelIcon", "Web", "WebIcon", "Business", "BusinessIcon", "ExpandLess", "ExpandMore", "jsxDEV", "_jsxDEV", "StyledDrawer", "_ref", "theme", "background", "color", "borderRight", "boxShadow", "_c", "SidebarHeader", "_ref2", "padding", "spacing", "display", "alignItems", "gap", "borderBottom", "_c2", "StyledListItemButton", "_ref3", "active", "margin", "borderRadius", "backgroundColor", "<PERSON><PERSON>ilter", "border", "transition", "transform", "content", "position", "left", "top", "width", "height", "_c3", "MenuSection", "_ref4", "marginTop", "_c4", "SectionTitle", "_ref5", "fontSize", "fontWeight", "textTransform", "letterSpacing", "opacity", "marginBottom", "_c5", "ResponsiveSidebar", "_ref6", "_s", "open", "onClose", "variant", "isMobile", "breakpoints", "down", "location", "mainMenuItems", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "badge", "userMenuItems", "isActive", "pathname", "startsWith", "drawerContent", "sx", "flexDirection", "children", "bgcolor", "flex", "overflowY", "py", "component", "map", "item", "index", "div", "initial", "x", "animate", "delay", "to", "onClick", "undefined", "min<PERSON><PERSON><PERSON>", "primary", "primaryTypographyProps", "label", "size", "mx", "length", "ModalProps", "keepMounted", "transitions", "create", "easing", "sharp", "duration", "enteringScreen", "overflowX", "_c6", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/components/layout/ResponsiveSidebar.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Drawer,\n  List,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Box,\n  Typography,\n  useTheme,\n  useMediaQuery,\n  Avatar,\n  Chip\n} from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport {\n  Home as HomeIcon,\n  Class as ClassIcon,\n  Assignment as AssignmentIcon,\n  SupervisorAccount as SupervisorAccountIcon,\n  PersonOutline as PersonOutlineIcon,\n  Announcement as AnnouncementIcon,\n  Report as ReportIcon,\n  AccountCircleOutlined as AccountCircleOutlinedIcon,\n  ExitToApp as ExitToAppIcon,\n  School as SchoolIcon,\n  // New icons for additional modules\n  Info as InfoIcon,\n  Payment as PaymentIcon,\n  Description as DescriptionIcon,\n  DirectionsBus as DirectionsBusIcon,\n  People as PeopleIcon,\n  PersonAdd as PersonAddIcon,\n  EventAvailable as EventAvailableIcon,\n  Quiz as QuizIcon,\n  MenuBook as MenuBookIcon,\n  NotificationsActive as NotificationsActiveIcon,\n  Hotel as HotelIcon,\n  Web as WebIcon,\n  Business as BusinessIcon,\n  ExpandLess,\n  ExpandMore,\n} from '@mui/icons-material';\n\nconst StyledDrawer = styled(Drawer)(({ theme }) => ({\n  '& .MuiDrawer-paper': {\n    background: 'linear-gradient(180deg, #667eea 0%, #764ba2 100%)',\n    color: 'white',\n    borderRight: 'none',\n    boxShadow: '4px 0 20px rgba(0, 0, 0, 0.1)',\n  },\n}));\n\nconst SidebarHeader = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(3, 2),\n  display: 'flex',\n  alignItems: 'center',\n  gap: theme.spacing(2),\n  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\n}));\n\nconst StyledListItemButton = styled(ListItemButton)(({ theme, active }) => ({\n  margin: theme.spacing(0.5, 1),\n  borderRadius: '12px',\n  padding: theme.spacing(1.5, 2),\n  backgroundColor: active ? 'rgba(255, 255, 255, 0.15)' : 'transparent',\n  backdropFilter: active ? 'blur(10px)' : 'none',\n  border: active ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid transparent',\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    backgroundColor: 'rgba(255, 255, 255, 0.1)',\n    transform: 'translateX(4px)',\n  },\n  '&:before': {\n    content: '\"\"',\n    position: 'absolute',\n    left: 0,\n    top: '50%',\n    transform: 'translateY(-50%)',\n    width: active ? '4px' : '0px',\n    height: '60%',\n    backgroundColor: '#FFD700',\n    borderRadius: '0 4px 4px 0',\n    transition: 'width 0.3s ease',\n  },\n}));\n\nconst MenuSection = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(1, 2),\n  marginTop: theme.spacing(2),\n}));\n\nconst SectionTitle = styled(Typography)(({ theme }) => ({\n  fontSize: '0.75rem',\n  fontWeight: 600,\n  textTransform: 'uppercase',\n  letterSpacing: '0.5px',\n  opacity: 0.7,\n  marginBottom: theme.spacing(1),\n}));\n\nconst ResponsiveSidebar = ({ open, onClose, variant = 'permanent' }) => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const location = useLocation();\n\n  const mainMenuItems = [\n    {\n      text: 'Dashboard',\n      icon: <HomeIcon />,\n      path: '/Admin/dashboard',\n      badge: null\n    },\n    {\n      text: 'Classes',\n      icon: <ClassIcon />,\n      path: '/Admin/classes',\n      badge: null\n    },\n    {\n      text: 'Subjects',\n      icon: <AssignmentIcon />,\n      path: '/Admin/subjects',\n      badge: null\n    },\n    {\n      text: 'Teachers',\n      icon: <SupervisorAccountIcon />,\n      path: '/Admin/teachers',\n      badge: null\n    },\n    {\n      text: 'Students',\n      icon: <PersonOutlineIcon />,\n      path: '/Admin/students',\n      badge: null\n    },\n    {\n      text: 'Notices',\n      icon: <AnnouncementIcon />,\n      path: '/Admin/notices',\n      badge: '3'\n    },\n    {\n      text: 'Complaints',\n      icon: <ReportIcon />,\n      path: '/Admin/complains',\n      badge: '2'\n    },\n  ];\n\n  const userMenuItems = [\n    {\n      text: 'Profile',\n      icon: <AccountCircleOutlinedIcon />,\n      path: '/Admin/profile',\n      badge: null\n    },\n    {\n      text: 'Logout',\n      icon: <ExitToAppIcon />,\n      path: '/logout',\n      badge: null\n    },\n  ];\n\n  const isActive = (path) => {\n    if (path === '/Admin/dashboard') {\n      return location.pathname === '/' || location.pathname === '/Admin/dashboard';\n    }\n    return location.pathname.startsWith(path);\n  };\n\n  const drawerContent = (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <SidebarHeader>\n        <Avatar\n          sx={{\n            bgcolor: 'rgba(255, 255, 255, 0.2)',\n            width: 48,\n            height: 48,\n          }}\n        >\n          <SchoolIcon />\n        </Avatar>\n        <Box>\n          <Typography variant=\"h6\" fontWeight=\"bold\">\n            School Admin\n          </Typography>\n          <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n            Management System\n          </Typography>\n        </Box>\n      </SidebarHeader>\n\n      <Box sx={{ flex: 1, overflowY: 'auto', py: 1 }}>\n        <MenuSection>\n          <SectionTitle>Main Menu</SectionTitle>\n          <List component=\"nav\" sx={{ padding: 0 }}>\n            {mainMenuItems.map((item, index) => (\n              <motion.div\n                key={item.text}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.1 }}\n              >\n                <StyledListItemButton\n                  component={Link}\n                  to={item.path}\n                  active={isActive(item.path)}\n                  onClick={isMobile ? onClose : undefined}\n                >\n                  <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>\n                    {item.icon}\n                  </ListItemIcon>\n                  <ListItemText \n                    primary={item.text}\n                    primaryTypographyProps={{\n                      fontSize: '0.9rem',\n                      fontWeight: isActive(item.path) ? 600 : 400,\n                    }}\n                  />\n                  {item.badge && (\n                    <Chip\n                      label={item.badge}\n                      size=\"small\"\n                      sx={{\n                        bgcolor: '#FFD700',\n                        color: '#333',\n                        fontSize: '0.7rem',\n                        height: 20,\n                        minWidth: 20,\n                      }}\n                    />\n                  )}\n                </StyledListItemButton>\n              </motion.div>\n            ))}\n          </List>\n        </MenuSection>\n\n        <Divider sx={{ mx: 2, bgcolor: 'rgba(255, 255, 255, 0.1)' }} />\n\n        <MenuSection>\n          <SectionTitle>Account</SectionTitle>\n          <List component=\"nav\" sx={{ padding: 0 }}>\n            {userMenuItems.map((item, index) => (\n              <motion.div\n                key={item.text}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: (mainMenuItems.length + index) * 0.1 }}\n              >\n                <StyledListItemButton\n                  component={Link}\n                  to={item.path}\n                  active={isActive(item.path)}\n                  onClick={isMobile ? onClose : undefined}\n                >\n                  <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>\n                    {item.icon}\n                  </ListItemIcon>\n                  <ListItemText \n                    primary={item.text}\n                    primaryTypographyProps={{\n                      fontSize: '0.9rem',\n                      fontWeight: isActive(item.path) ? 600 : 400,\n                    }}\n                  />\n                </StyledListItemButton>\n              </motion.div>\n            ))}\n          </List>\n        </MenuSection>\n      </Box>\n    </Box>\n  );\n\n  if (isMobile) {\n    return (\n      <StyledDrawer\n        variant=\"temporary\"\n        open={open}\n        onClose={onClose}\n        ModalProps={{\n          keepMounted: true, // Better open performance on mobile.\n        }}\n        sx={{\n          '& .MuiDrawer-paper': {\n            width: 280,\n          },\n        }}\n      >\n        {drawerContent}\n      </StyledDrawer>\n    );\n  }\n\n  return (\n    <StyledDrawer\n      variant={variant}\n      open={open}\n      sx={{\n        '& .MuiDrawer-paper': {\n          width: open ? 280 : 70,\n          transition: theme.transitions.create('width', {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.enteringScreen,\n          }),\n          overflowX: 'hidden',\n        },\n      }}\n    >\n      {drawerContent}\n    </StyledDrawer>\n  );\n};\n\nexport default ResponsiveSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,IAAI,EACJC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,aAAa,EACbC,MAAM,EACNC,IAAI,QACC,eAAe;AACtB,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SACEC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,iBAAiB,IAAIC,qBAAqB,EAC1CC,aAAa,IAAIC,iBAAiB,EAClCC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,qBAAqB,IAAIC,yBAAyB,EAClDC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU;AACpB;AACAC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,eAAe,EAC9BC,aAAa,IAAIC,iBAAiB,EAClCC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,cAAc,IAAIC,kBAAkB,EACpCC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,mBAAmB,IAAIC,uBAAuB,EAC9CC,KAAK,IAAIC,SAAS,EAClBC,GAAG,IAAIC,OAAO,EACdC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,EACVC,UAAU,QACL,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,YAAY,GAAGtD,MAAM,CAACZ,MAAM,CAAC,CAACmE,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IAClD,oBAAoB,EAAE;MACpBE,UAAU,EAAE,mDAAmD;MAC/DC,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE;IACb;EACF,CAAC;AAAA,CAAC,CAAC;AAACC,EAAA,GAPEP,YAAY;AASlB,MAAMQ,aAAa,GAAG9D,MAAM,CAACN,GAAG,CAAC,CAACqE,KAAA;EAAA,IAAC;IAAEP;EAAM,CAAC,GAAAO,KAAA;EAAA,OAAM;IAChDC,OAAO,EAAER,KAAK,CAACS,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5BC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAEZ,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC;IACrBI,YAAY,EAAE;EAChB,CAAC;AAAA,CAAC,CAAC;AAACC,GAAA,GANER,aAAa;AAQnB,MAAMS,oBAAoB,GAAGvE,MAAM,CAACV,cAAc,CAAC,CAACkF,KAAA;EAAA,IAAC;IAAEhB,KAAK;IAAEiB;EAAO,CAAC,GAAAD,KAAA;EAAA,OAAM;IAC1EE,MAAM,EAAElB,KAAK,CAACS,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IAC7BU,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAER,KAAK,CAACS,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IAC9BW,eAAe,EAAEH,MAAM,GAAG,2BAA2B,GAAG,aAAa;IACrEI,cAAc,EAAEJ,MAAM,GAAG,YAAY,GAAG,MAAM;IAC9CK,MAAM,EAAEL,MAAM,GAAG,oCAAoC,GAAG,uBAAuB;IAC/EM,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTH,eAAe,EAAE,0BAA0B;MAC3CI,SAAS,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACVC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE,KAAK;MACVJ,SAAS,EAAE,kBAAkB;MAC7BK,KAAK,EAAEZ,MAAM,GAAG,KAAK,GAAG,KAAK;MAC7Ba,MAAM,EAAE,KAAK;MACbV,eAAe,EAAE,SAAS;MAC1BD,YAAY,EAAE,aAAa;MAC3BI,UAAU,EAAE;IACd;EACF,CAAC;AAAA,CAAC,CAAC;AAACQ,GAAA,GAxBEhB,oBAAoB;AA0B1B,MAAMiB,WAAW,GAAGxF,MAAM,CAACN,GAAG,CAAC,CAAC+F,KAAA;EAAA,IAAC;IAAEjC;EAAM,CAAC,GAAAiC,KAAA;EAAA,OAAM;IAC9CzB,OAAO,EAAER,KAAK,CAACS,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5ByB,SAAS,EAAElC,KAAK,CAACS,OAAO,CAAC,CAAC;EAC5B,CAAC;AAAA,CAAC,CAAC;AAAC0B,GAAA,GAHEH,WAAW;AAKjB,MAAMI,YAAY,GAAG5F,MAAM,CAACL,UAAU,CAAC,CAACkG,KAAA;EAAA,IAAC;IAAErC;EAAM,CAAC,GAAAqC,KAAA;EAAA,OAAM;IACtDC,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfC,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE,OAAO;IACtBC,OAAO,EAAE,GAAG;IACZC,YAAY,EAAE3C,KAAK,CAACS,OAAO,CAAC,CAAC;EAC/B,CAAC;AAAA,CAAC,CAAC;AAACmC,GAAA,GAPER,YAAY;AASlB,MAAMS,iBAAiB,GAAGC,KAAA,IAA8C;EAAAC,EAAA;EAAA,IAA7C;IAAEC,IAAI;IAAEC,OAAO;IAAEC,OAAO,GAAG;EAAY,CAAC,GAAAJ,KAAA;EACjE,MAAM9C,KAAK,GAAG5D,QAAQ,EAAE;EACxB,MAAM+G,QAAQ,GAAG9G,aAAa,CAAC2D,KAAK,CAACoD,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAMC,QAAQ,GAAG3G,WAAW,EAAE;EAE9B,MAAM4G,aAAa,GAAG,CACpB;IACEC,IAAI,EAAE,WAAW;IACjBC,IAAI,eAAE5D,OAAA,CAAChD,QAAQ;MAAA6G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAClBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,eAAE5D,OAAA,CAAC9C,SAAS;MAAA2G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnBC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAE5D,OAAA,CAAC5C,cAAc;MAAAyG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACxBC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAE5D,OAAA,CAAC1C,qBAAqB;MAAAuG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC/BC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,eAAE5D,OAAA,CAACxC,iBAAiB;MAAAqG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC3BC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,eAAE5D,OAAA,CAACtC,gBAAgB;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IAC1BC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,eAAE5D,OAAA,CAACpC,UAAU;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACpBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,aAAa,GAAG,CACpB;IACER,IAAI,EAAE,SAAS;IACfC,IAAI,eAAE5D,OAAA,CAAClC,yBAAyB;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACnCC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdC,IAAI,eAAE5D,OAAA,CAAChC,aAAa;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAME,QAAQ,GAAIH,IAAI,IAAK;IACzB,IAAIA,IAAI,KAAK,kBAAkB,EAAE;MAC/B,OAAOR,QAAQ,CAACY,QAAQ,KAAK,GAAG,IAAIZ,QAAQ,CAACY,QAAQ,KAAK,kBAAkB;IAC9E;IACA,OAAOZ,QAAQ,CAACY,QAAQ,CAACC,UAAU,CAACL,IAAI,CAAC;EAC3C,CAAC;EAED,MAAMM,aAAa,gBACjBvE,OAAA,CAAC3D,GAAG;IAACmI,EAAE,EAAE;MAAEvC,MAAM,EAAE,MAAM;MAAEpB,OAAO,EAAE,MAAM;MAAE4D,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACpE1E,OAAA,CAACS,aAAa;MAAAiE,QAAA,gBACZ1E,OAAA,CAACvD,MAAM;QACL+H,EAAE,EAAE;UACFG,OAAO,EAAE,0BAA0B;UACnC3C,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE;QACV,CAAE;QAAAyC,QAAA,eAEF1E,OAAA,CAAC9B,UAAU;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACP,eACThE,OAAA,CAAC3D,GAAG;QAAAqI,QAAA,gBACF1E,OAAA,CAAC1D,UAAU;UAAC+G,OAAO,EAAC,IAAI;UAACX,UAAU,EAAC,MAAM;UAAAgC,QAAA,EAAC;QAE3C;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACbhE,OAAA,CAAC1D,UAAU;UAAC+G,OAAO,EAAC,SAAS;UAACmB,EAAE,EAAE;YAAE3B,OAAO,EAAE;UAAI,CAAE;UAAA6B,QAAA,EAAC;QAEpD;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACQ,eAEhBhE,OAAA,CAAC3D,GAAG;MAACmI,EAAE,EAAE;QAAEI,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE,MAAM;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBAC7C1E,OAAA,CAACmC,WAAW;QAAAuC,QAAA,gBACV1E,OAAA,CAACuC,YAAY;UAAAmC,QAAA,EAAC;QAAS;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAe,eACtChE,OAAA,CAAChE,IAAI;UAAC+I,SAAS,EAAC,KAAK;UAACP,EAAE,EAAE;YAAE7D,OAAO,EAAE;UAAE,CAAE;UAAA+D,QAAA,EACtChB,aAAa,CAACsB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7BlF,OAAA,CAACpD,MAAM,CAACuI,GAAG;YAETC,OAAO,EAAE;cAAEvC,OAAO,EAAE,CAAC;cAAEwC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEzC,OAAO,EAAE,CAAC;cAAEwC,CAAC,EAAE;YAAE,CAAE;YAC9B3D,UAAU,EAAE;cAAE6D,KAAK,EAAEL,KAAK,GAAG;YAAI,CAAE;YAAAR,QAAA,eAEnC1E,OAAA,CAACkB,oBAAoB;cACnB6D,SAAS,EAAElI,IAAK;cAChB2I,EAAE,EAAEP,IAAI,CAAChB,IAAK;cACd7C,MAAM,EAAEgD,QAAQ,CAACa,IAAI,CAAChB,IAAI,CAAE;cAC5BwB,OAAO,EAAEnC,QAAQ,GAAGF,OAAO,GAAGsC,SAAU;cAAAhB,QAAA,gBAExC1E,OAAA,CAAC9D,YAAY;gBAACsI,EAAE,EAAE;kBAAEnE,KAAK,EAAE,SAAS;kBAAEsF,QAAQ,EAAE;gBAAG,CAAE;gBAAAjB,QAAA,EAClDO,IAAI,CAACrB;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACG,eACfhE,OAAA,CAAC7D,YAAY;gBACXyJ,OAAO,EAAEX,IAAI,CAACtB,IAAK;gBACnBkC,sBAAsB,EAAE;kBACtBpD,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE0B,QAAQ,CAACa,IAAI,CAAChB,IAAI,CAAC,GAAG,GAAG,GAAG;gBAC1C;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACF,EACDiB,IAAI,CAACf,KAAK,iBACTlE,OAAA,CAACtD,IAAI;gBACHoJ,KAAK,EAAEb,IAAI,CAACf,KAAM;gBAClB6B,IAAI,EAAC,OAAO;gBACZvB,EAAE,EAAE;kBACFG,OAAO,EAAE,SAAS;kBAClBtE,KAAK,EAAE,MAAM;kBACboC,QAAQ,EAAE,QAAQ;kBAClBR,MAAM,EAAE,EAAE;kBACV0D,QAAQ,EAAE;gBACZ;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAEL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACoB,GAlClBiB,IAAI,CAACtB,IAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAoCjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACK,eAEdhE,OAAA,CAAC5D,OAAO;QAACoI,EAAE,EAAE;UAAEwB,EAAE,EAAE,CAAC;UAAErB,OAAO,EAAE;QAA2B;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAE/DhE,OAAA,CAACmC,WAAW;QAAAuC,QAAA,gBACV1E,OAAA,CAACuC,YAAY;UAAAmC,QAAA,EAAC;QAAO;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAe,eACpChE,OAAA,CAAChE,IAAI;UAAC+I,SAAS,EAAC,KAAK;UAACP,EAAE,EAAE;YAAE7D,OAAO,EAAE;UAAE,CAAE;UAAA+D,QAAA,EACtCP,aAAa,CAACa,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7BlF,OAAA,CAACpD,MAAM,CAACuI,GAAG;YAETC,OAAO,EAAE;cAAEvC,OAAO,EAAE,CAAC;cAAEwC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEzC,OAAO,EAAE,CAAC;cAAEwC,CAAC,EAAE;YAAE,CAAE;YAC9B3D,UAAU,EAAE;cAAE6D,KAAK,EAAE,CAAC7B,aAAa,CAACuC,MAAM,GAAGf,KAAK,IAAI;YAAI,CAAE;YAAAR,QAAA,eAE5D1E,OAAA,CAACkB,oBAAoB;cACnB6D,SAAS,EAAElI,IAAK;cAChB2I,EAAE,EAAEP,IAAI,CAAChB,IAAK;cACd7C,MAAM,EAAEgD,QAAQ,CAACa,IAAI,CAAChB,IAAI,CAAE;cAC5BwB,OAAO,EAAEnC,QAAQ,GAAGF,OAAO,GAAGsC,SAAU;cAAAhB,QAAA,gBAExC1E,OAAA,CAAC9D,YAAY;gBAACsI,EAAE,EAAE;kBAAEnE,KAAK,EAAE,SAAS;kBAAEsF,QAAQ,EAAE;gBAAG,CAAE;gBAAAjB,QAAA,EAClDO,IAAI,CAACrB;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACG,eACfhE,OAAA,CAAC7D,YAAY;gBACXyJ,OAAO,EAAEX,IAAI,CAACtB,IAAK;gBACnBkC,sBAAsB,EAAE;kBACtBpD,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE0B,QAAQ,CAACa,IAAI,CAAChB,IAAI,CAAC,GAAG,GAAG,GAAG;gBAC1C;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACmB,GArBlBiB,IAAI,CAACtB,IAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAuBjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACK;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACV;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAET;EAED,IAAIV,QAAQ,EAAE;IACZ,oBACEtD,OAAA,CAACC,YAAY;MACXoD,OAAO,EAAC,WAAW;MACnBF,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAEA,OAAQ;MACjB8C,UAAU,EAAE;QACVC,WAAW,EAAE,IAAI,CAAE;MACrB,CAAE;;MACF3B,EAAE,EAAE;QACF,oBAAoB,EAAE;UACpBxC,KAAK,EAAE;QACT;MACF,CAAE;MAAA0C,QAAA,EAEDH;IAAa;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD;EAEnB;EAEA,oBACEhE,OAAA,CAACC,YAAY;IACXoD,OAAO,EAAEA,OAAQ;IACjBF,IAAI,EAAEA,IAAK;IACXqB,EAAE,EAAE;MACF,oBAAoB,EAAE;QACpBxC,KAAK,EAAEmB,IAAI,GAAG,GAAG,GAAG,EAAE;QACtBzB,UAAU,EAAEvB,KAAK,CAACiG,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;UAC5CC,MAAM,EAAEnG,KAAK,CAACiG,WAAW,CAACE,MAAM,CAACC,KAAK;UACtCC,QAAQ,EAAErG,KAAK,CAACiG,WAAW,CAACI,QAAQ,CAACC;QACvC,CAAC,CAAC;QACFC,SAAS,EAAE;MACb;IACF,CAAE;IAAAhC,QAAA,EAEDH;EAAa;IAAAV,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACD;AAEnB,CAAC;AAACd,EAAA,CAvNIF,iBAAiB;EAAA,QACPzG,QAAQ,EACLC,aAAa,EACbM,WAAW;AAAA;AAAA6J,GAAA,GAHxB3D,iBAAiB;AAyNvB,eAAeA,iBAAiB;AAAC,IAAAxC,EAAA,EAAAS,GAAA,EAAAiB,GAAA,EAAAI,GAAA,EAAAS,GAAA,EAAA4D,GAAA;AAAAC,YAAA,CAAApG,EAAA;AAAAoG,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAtE,GAAA;AAAAsE,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}