{"ast": null, "code": "import { useState, useEffect } from 'react';\nfunction usePageInView() {\n  const [isInView, setIsInView] = useState(true);\n  useEffect(() => {\n    const handleVisibilityChange = () => setIsInView(!document.hidden);\n    if (document.hidden) {\n      handleVisibilityChange();\n    }\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n    return () => {\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n    };\n  }, []);\n  return isInView;\n}\nexport { usePageInView };", "map": {"version": 3, "names": ["useState", "useEffect", "usePageInView", "isInView", "setIsInView", "handleVisibilityChange", "document", "hidden", "addEventListener", "removeEventListener"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/utils/use-page-in-view.mjs"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\nfunction usePageInView() {\n    const [isInView, setIsInView] = useState(true);\n    useEffect(() => {\n        const handleVisibilityChange = () => setIsInView(!document.hidden);\n        if (document.hidden) {\n            handleVisibilityChange();\n        }\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        return () => {\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n        };\n    }, []);\n    return isInView;\n}\n\nexport { usePageInView };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAE3C,SAASC,aAAaA,CAAA,EAAG;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGJ,QAAQ,CAAC,IAAI,CAAC;EAC9CC,SAAS,CAAC,MAAM;IACZ,MAAMI,sBAAsB,GAAGA,CAAA,KAAMD,WAAW,CAAC,CAACE,QAAQ,CAACC,MAAM,CAAC;IAClE,IAAID,QAAQ,CAACC,MAAM,EAAE;MACjBF,sBAAsB,EAAE;IAC5B;IACAC,QAAQ,CAACE,gBAAgB,CAAC,kBAAkB,EAAEH,sBAAsB,CAAC;IACrE,OAAO,MAAM;MACTC,QAAQ,CAACG,mBAAmB,CAAC,kBAAkB,EAAEJ,sBAAsB,CAAC;IAC5E,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EACN,OAAOF,QAAQ;AACnB;AAEA,SAASD,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}