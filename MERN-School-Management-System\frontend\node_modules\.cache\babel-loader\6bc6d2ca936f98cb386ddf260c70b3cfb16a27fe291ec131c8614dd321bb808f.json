{"ast": null, "code": "import { animations } from '../../motion/features/animations.mjs';\nimport { createDomVisualElement } from './create-visual-element.mjs';\n\n/**\n * @public\n */\nconst domMin = {\n  renderer: createDomVisualElement,\n  ...animations\n};\nexport { domMin };", "map": {"version": 3, "names": ["animations", "createDomVisualElement", "dom<PERSON>in", "renderer"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/render/dom/features-min.mjs"], "sourcesContent": ["import { animations } from '../../motion/features/animations.mjs';\nimport { createDomVisualElement } from './create-visual-element.mjs';\n\n/**\n * @public\n */\nconst domMin = {\n    renderer: createDomVisualElement,\n    ...animations,\n};\n\nexport { domMin };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,sCAAsC;AACjE,SAASC,sBAAsB,QAAQ,6BAA6B;;AAEpE;AACA;AACA;AACA,MAAMC,MAAM,GAAG;EACXC,QAAQ,EAAEF,sBAAsB;EAChC,GAAGD;AACP,CAAC;AAED,SAASE,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}