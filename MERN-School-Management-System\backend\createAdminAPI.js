const axios = require('axios');

const createAdminAndClasses = async () => {
    try {
        console.log('🔧 Creating admin user and sample data...');
        
        // Create admin user
        const adminData = {
            name: 'Administrator',
            email: '<EMAIL>',
            password: 'admin123',
            schoolName: 'Amar Vidya Mandir Higher Secondary School',
            phone: '7799505005',
            address: 'Partaj, Anantapuram, AP, India'
        };
        
        try {
            const adminResponse = await axios.post('http://localhost:5000/AdminReg', adminData);
            console.log('✅ Admin user created:', adminResponse.data);
        } catch (error) {
            if (error.response && error.response.data.message === 'Email already exists') {
                console.log('ℹ️ Admin user already exists');
            } else {
                console.log('❌ Error creating admin:', error.response?.data || error.message);
                return;
            }
        }
        
        // Login to get admin ID
        const loginResponse = await axios.post('http://localhost:5000/AdminLogin', {
            email: '<EMAIL>',
            password: 'admin123'
        });
        
        if (loginResponse.data.message) {
            console.log('❌ Login failed:', loginResponse.data.message);
            return;
        }
        
        const adminId = loginResponse.data._id;
        console.log('✅ Admin login successful, ID:', adminId);
        
        // Create sample classes
        const classes = ['Class 1', 'Class 2', 'Class 3', 'Class 4', 'Class 5'];
        
        for (const className of classes) {
            try {
                const classResponse = await axios.post('http://localhost:5000/SclassCreate', {
                    sclassName: className,
                    adminID: adminId
                });
                
                if (classResponse.data.message) {
                    console.log(`ℹ️ ${className}: ${classResponse.data.message}`);
                } else {
                    console.log(`✅ Created class: ${className}`);
                }
            } catch (error) {
                console.log(`❌ Error creating ${className}:`, error.response?.data || error.message);
            }
        }
        
        console.log('\n🎉 Setup complete!');
        console.log('📧 Login Email: <EMAIL>');
        console.log('🔑 Login Password: admin123');
        console.log('🌐 Frontend URL: http://localhost:3001');
        
    } catch (error) {
        console.log('❌ Setup failed:', error.message);
    }
};

createAdminAndClasses();
