{"ast": null, "code": "import { mixNumber } from 'motion-dom';\nimport { hasTransform } from '../utils/has-transform.mjs';\n\n/**\n * Scales a point based on a factor and an originPoint\n */\nfunction scalePoint(point, scale, originPoint) {\n  const distanceFromOrigin = point - originPoint;\n  const scaled = scale * distanceFromOrigin;\n  return originPoint + scaled;\n}\n/**\n * Applies a translate/scale delta to a point\n */\nfunction applyPointDelta(point, translate, scale, originPoint, boxScale) {\n  if (boxScale !== undefined) {\n    point = scalePoint(point, boxScale, originPoint);\n  }\n  return scalePoint(point, scale, originPoint) + translate;\n}\n/**\n * Applies a translate/scale delta to an axis\n */\nfunction applyAxisDelta(axis) {\n  let translate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  let scale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  let originPoint = arguments.length > 3 ? arguments[3] : undefined;\n  let boxScale = arguments.length > 4 ? arguments[4] : undefined;\n  axis.min = applyPointDelta(axis.min, translate, scale, originPoint, boxScale);\n  axis.max = applyPointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Applies a translate/scale delta to a box\n */\nfunction applyBoxDelta(box, _ref) {\n  let {\n    x,\n    y\n  } = _ref;\n  applyAxisDelta(box.x, x.translate, x.scale, x.originPoint);\n  applyAxisDelta(box.y, y.translate, y.scale, y.originPoint);\n}\nconst TREE_SCALE_SNAP_MIN = 0.999999999999;\nconst TREE_SCALE_SNAP_MAX = 1.0000000000001;\n/**\n * Apply a tree of deltas to a box. We do this to calculate the effect of all the transforms\n * in a tree upon our box before then calculating how to project it into our desired viewport-relative box\n *\n * This is the final nested loop within updateLayoutDelta for future refactoring\n */\nfunction applyTreeDeltas(box, treeScale, treePath) {\n  let isSharedTransition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  const treeLength = treePath.length;\n  if (!treeLength) return;\n  // Reset the treeScale\n  treeScale.x = treeScale.y = 1;\n  let node;\n  let delta;\n  for (let i = 0; i < treeLength; i++) {\n    node = treePath[i];\n    delta = node.projectionDelta;\n    /**\n     * TODO: Prefer to remove this, but currently we have motion components with\n     * display: contents in Framer.\n     */\n    const {\n      visualElement\n    } = node.options;\n    if (visualElement && visualElement.props.style && visualElement.props.style.display === \"contents\") {\n      continue;\n    }\n    if (isSharedTransition && node.options.layoutScroll && node.scroll && node !== node.root) {\n      transformBox(box, {\n        x: -node.scroll.offset.x,\n        y: -node.scroll.offset.y\n      });\n    }\n    if (delta) {\n      // Incoporate each ancestor's scale into a culmulative treeScale for this component\n      treeScale.x *= delta.x.scale;\n      treeScale.y *= delta.y.scale;\n      // Apply each ancestor's calculated delta into this component's recorded layout box\n      applyBoxDelta(box, delta);\n    }\n    if (isSharedTransition && hasTransform(node.latestValues)) {\n      transformBox(box, node.latestValues);\n    }\n  }\n  /**\n   * Snap tree scale back to 1 if it's within a non-perceivable threshold.\n   * This will help reduce useless scales getting rendered.\n   */\n  if (treeScale.x < TREE_SCALE_SNAP_MAX && treeScale.x > TREE_SCALE_SNAP_MIN) {\n    treeScale.x = 1.0;\n  }\n  if (treeScale.y < TREE_SCALE_SNAP_MAX && treeScale.y > TREE_SCALE_SNAP_MIN) {\n    treeScale.y = 1.0;\n  }\n}\nfunction translateAxis(axis, distance) {\n  axis.min = axis.min + distance;\n  axis.max = axis.max + distance;\n}\n/**\n * Apply a transform to an axis from the latest resolved motion values.\n * This function basically acts as a bridge between a flat motion value map\n * and applyAxisDelta\n */\nfunction transformAxis(axis, axisTranslate, axisScale, boxScale) {\n  let axisOrigin = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0.5;\n  const originPoint = mixNumber(axis.min, axis.max, axisOrigin);\n  // Apply the axis delta to the final axis\n  applyAxisDelta(axis, axisTranslate, axisScale, originPoint, boxScale);\n}\n/**\n * Apply a transform to a box from the latest resolved motion values.\n */\nfunction transformBox(box, transform) {\n  transformAxis(box.x, transform.x, transform.scaleX, transform.scale, transform.originX);\n  transformAxis(box.y, transform.y, transform.scaleY, transform.scale, transform.originY);\n}\nexport { applyAxisDelta, applyBoxDelta, applyPointDelta, applyTreeDeltas, scalePoint, transformAxis, transformBox, translateAxis };", "map": {"version": 3, "names": ["mixNumber", "hasTransform", "scalePoint", "point", "scale", "originPoint", "distanceFromOrigin", "scaled", "applyPointDelta", "translate", "boxScale", "undefined", "applyAxis<PERSON><PERSON><PERSON>", "axis", "arguments", "length", "min", "max", "applyBoxDelta", "box", "_ref", "x", "y", "TREE_SCALE_SNAP_MIN", "TREE_SCALE_SNAP_MAX", "applyTreeDeltas", "treeScale", "treePath", "isSharedTransition", "tree<PERSON>ength", "node", "delta", "i", "projectionDel<PERSON>", "visualElement", "options", "props", "style", "display", "layoutScroll", "scroll", "root", "transformBox", "offset", "latestValues", "translateAxis", "distance", "transformAxis", "axisTranslate", "axisScale", "axisOrigin", "transform", "scaleX", "originX", "scaleY", "originY"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs"], "sourcesContent": ["import { mixNumber } from 'motion-dom';\nimport { hasTransform } from '../utils/has-transform.mjs';\n\n/**\n * Scales a point based on a factor and an originPoint\n */\nfunction scalePoint(point, scale, originPoint) {\n    const distanceFromOrigin = point - originPoint;\n    const scaled = scale * distanceFromOrigin;\n    return originPoint + scaled;\n}\n/**\n * Applies a translate/scale delta to a point\n */\nfunction applyPointDelta(point, translate, scale, originPoint, boxScale) {\n    if (boxScale !== undefined) {\n        point = scalePoint(point, boxScale, originPoint);\n    }\n    return scalePoint(point, scale, originPoint) + translate;\n}\n/**\n * Applies a translate/scale delta to an axis\n */\nfunction applyAxisDelta(axis, translate = 0, scale = 1, originPoint, boxScale) {\n    axis.min = applyPointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = applyPointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Applies a translate/scale delta to a box\n */\nfunction applyBoxDelta(box, { x, y }) {\n    applyAxisDelta(box.x, x.translate, x.scale, x.originPoint);\n    applyAxisDelta(box.y, y.translate, y.scale, y.originPoint);\n}\nconst TREE_SCALE_SNAP_MIN = 0.999999999999;\nconst TREE_SCALE_SNAP_MAX = 1.0000000000001;\n/**\n * Apply a tree of deltas to a box. We do this to calculate the effect of all the transforms\n * in a tree upon our box before then calculating how to project it into our desired viewport-relative box\n *\n * This is the final nested loop within updateLayoutDelta for future refactoring\n */\nfunction applyTreeDeltas(box, treeScale, treePath, isSharedTransition = false) {\n    const treeLength = treePath.length;\n    if (!treeLength)\n        return;\n    // Reset the treeScale\n    treeScale.x = treeScale.y = 1;\n    let node;\n    let delta;\n    for (let i = 0; i < treeLength; i++) {\n        node = treePath[i];\n        delta = node.projectionDelta;\n        /**\n         * TODO: Prefer to remove this, but currently we have motion components with\n         * display: contents in Framer.\n         */\n        const { visualElement } = node.options;\n        if (visualElement &&\n            visualElement.props.style &&\n            visualElement.props.style.display === \"contents\") {\n            continue;\n        }\n        if (isSharedTransition &&\n            node.options.layoutScroll &&\n            node.scroll &&\n            node !== node.root) {\n            transformBox(box, {\n                x: -node.scroll.offset.x,\n                y: -node.scroll.offset.y,\n            });\n        }\n        if (delta) {\n            // Incoporate each ancestor's scale into a culmulative treeScale for this component\n            treeScale.x *= delta.x.scale;\n            treeScale.y *= delta.y.scale;\n            // Apply each ancestor's calculated delta into this component's recorded layout box\n            applyBoxDelta(box, delta);\n        }\n        if (isSharedTransition && hasTransform(node.latestValues)) {\n            transformBox(box, node.latestValues);\n        }\n    }\n    /**\n     * Snap tree scale back to 1 if it's within a non-perceivable threshold.\n     * This will help reduce useless scales getting rendered.\n     */\n    if (treeScale.x < TREE_SCALE_SNAP_MAX &&\n        treeScale.x > TREE_SCALE_SNAP_MIN) {\n        treeScale.x = 1.0;\n    }\n    if (treeScale.y < TREE_SCALE_SNAP_MAX &&\n        treeScale.y > TREE_SCALE_SNAP_MIN) {\n        treeScale.y = 1.0;\n    }\n}\nfunction translateAxis(axis, distance) {\n    axis.min = axis.min + distance;\n    axis.max = axis.max + distance;\n}\n/**\n * Apply a transform to an axis from the latest resolved motion values.\n * This function basically acts as a bridge between a flat motion value map\n * and applyAxisDelta\n */\nfunction transformAxis(axis, axisTranslate, axisScale, boxScale, axisOrigin = 0.5) {\n    const originPoint = mixNumber(axis.min, axis.max, axisOrigin);\n    // Apply the axis delta to the final axis\n    applyAxisDelta(axis, axisTranslate, axisScale, originPoint, boxScale);\n}\n/**\n * Apply a transform to a box from the latest resolved motion values.\n */\nfunction transformBox(box, transform) {\n    transformAxis(box.x, transform.x, transform.scaleX, transform.scale, transform.originX);\n    transformAxis(box.y, transform.y, transform.scaleY, transform.scale, transform.originY);\n}\n\nexport { applyAxisDelta, applyBoxDelta, applyPointDelta, applyTreeDeltas, scalePoint, transformAxis, transformBox, translateAxis };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,SAASC,YAAY,QAAQ,4BAA4B;;AAEzD;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAEC,KAAK,EAAEC,WAAW,EAAE;EAC3C,MAAMC,kBAAkB,GAAGH,KAAK,GAAGE,WAAW;EAC9C,MAAME,MAAM,GAAGH,KAAK,GAAGE,kBAAkB;EACzC,OAAOD,WAAW,GAAGE,MAAM;AAC/B;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACL,KAAK,EAAEM,SAAS,EAAEL,KAAK,EAAEC,WAAW,EAAEK,QAAQ,EAAE;EACrE,IAAIA,QAAQ,KAAKC,SAAS,EAAE;IACxBR,KAAK,GAAGD,UAAU,CAACC,KAAK,EAAEO,QAAQ,EAAEL,WAAW,CAAC;EACpD;EACA,OAAOH,UAAU,CAACC,KAAK,EAAEC,KAAK,EAAEC,WAAW,CAAC,GAAGI,SAAS;AAC5D;AACA;AACA;AACA;AACA,SAASG,cAAcA,CAACC,IAAI,EAAmD;EAAA,IAAjDJ,SAAS,GAAAK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAH,SAAA,GAAAG,SAAA,MAAG,CAAC;EAAA,IAAEV,KAAK,GAAAU,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAH,SAAA,GAAAG,SAAA,MAAG,CAAC;EAAA,IAAET,WAAW,GAAAS,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAH,SAAA;EAAA,IAAED,QAAQ,GAAAI,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAH,SAAA;EACzEE,IAAI,CAACG,GAAG,GAAGR,eAAe,CAACK,IAAI,CAACG,GAAG,EAAEP,SAAS,EAAEL,KAAK,EAAEC,WAAW,EAAEK,QAAQ,CAAC;EAC7EG,IAAI,CAACI,GAAG,GAAGT,eAAe,CAACK,IAAI,CAACI,GAAG,EAAER,SAAS,EAAEL,KAAK,EAAEC,WAAW,EAAEK,QAAQ,CAAC;AACjF;AACA;AACA;AACA;AACA,SAASQ,aAAaA,CAACC,GAAG,EAAAC,IAAA,EAAY;EAAA,IAAV;IAAEC,CAAC;IAAEC;EAAE,CAAC,GAAAF,IAAA;EAChCR,cAAc,CAACO,GAAG,CAACE,CAAC,EAAEA,CAAC,CAACZ,SAAS,EAAEY,CAAC,CAACjB,KAAK,EAAEiB,CAAC,CAAChB,WAAW,CAAC;EAC1DO,cAAc,CAACO,GAAG,CAACG,CAAC,EAAEA,CAAC,CAACb,SAAS,EAAEa,CAAC,CAAClB,KAAK,EAAEkB,CAAC,CAACjB,WAAW,CAAC;AAC9D;AACA,MAAMkB,mBAAmB,GAAG,cAAc;AAC1C,MAAMC,mBAAmB,GAAG,eAAe;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACN,GAAG,EAAEO,SAAS,EAAEC,QAAQ,EAA8B;EAAA,IAA5BC,kBAAkB,GAAAd,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAH,SAAA,GAAAG,SAAA,MAAG,KAAK;EACzE,MAAMe,UAAU,GAAGF,QAAQ,CAACZ,MAAM;EAClC,IAAI,CAACc,UAAU,EACX;EACJ;EACAH,SAAS,CAACL,CAAC,GAAGK,SAAS,CAACJ,CAAC,GAAG,CAAC;EAC7B,IAAIQ,IAAI;EACR,IAAIC,KAAK;EACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,EAAEG,CAAC,EAAE,EAAE;IACjCF,IAAI,GAAGH,QAAQ,CAACK,CAAC,CAAC;IAClBD,KAAK,GAAGD,IAAI,CAACG,eAAe;IAC5B;AACR;AACA;AACA;IACQ,MAAM;MAAEC;IAAc,CAAC,GAAGJ,IAAI,CAACK,OAAO;IACtC,IAAID,aAAa,IACbA,aAAa,CAACE,KAAK,CAACC,KAAK,IACzBH,aAAa,CAACE,KAAK,CAACC,KAAK,CAACC,OAAO,KAAK,UAAU,EAAE;MAClD;IACJ;IACA,IAAIV,kBAAkB,IAClBE,IAAI,CAACK,OAAO,CAACI,YAAY,IACzBT,IAAI,CAACU,MAAM,IACXV,IAAI,KAAKA,IAAI,CAACW,IAAI,EAAE;MACpBC,YAAY,CAACvB,GAAG,EAAE;QACdE,CAAC,EAAE,CAACS,IAAI,CAACU,MAAM,CAACG,MAAM,CAACtB,CAAC;QACxBC,CAAC,EAAE,CAACQ,IAAI,CAACU,MAAM,CAACG,MAAM,CAACrB;MAC3B,CAAC,CAAC;IACN;IACA,IAAIS,KAAK,EAAE;MACP;MACAL,SAAS,CAACL,CAAC,IAAIU,KAAK,CAACV,CAAC,CAACjB,KAAK;MAC5BsB,SAAS,CAACJ,CAAC,IAAIS,KAAK,CAACT,CAAC,CAAClB,KAAK;MAC5B;MACAc,aAAa,CAACC,GAAG,EAAEY,KAAK,CAAC;IAC7B;IACA,IAAIH,kBAAkB,IAAI3B,YAAY,CAAC6B,IAAI,CAACc,YAAY,CAAC,EAAE;MACvDF,YAAY,CAACvB,GAAG,EAAEW,IAAI,CAACc,YAAY,CAAC;IACxC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIlB,SAAS,CAACL,CAAC,GAAGG,mBAAmB,IACjCE,SAAS,CAACL,CAAC,GAAGE,mBAAmB,EAAE;IACnCG,SAAS,CAACL,CAAC,GAAG,GAAG;EACrB;EACA,IAAIK,SAAS,CAACJ,CAAC,GAAGE,mBAAmB,IACjCE,SAAS,CAACJ,CAAC,GAAGC,mBAAmB,EAAE;IACnCG,SAAS,CAACJ,CAAC,GAAG,GAAG;EACrB;AACJ;AACA,SAASuB,aAAaA,CAAChC,IAAI,EAAEiC,QAAQ,EAAE;EACnCjC,IAAI,CAACG,GAAG,GAAGH,IAAI,CAACG,GAAG,GAAG8B,QAAQ;EAC9BjC,IAAI,CAACI,GAAG,GAAGJ,IAAI,CAACI,GAAG,GAAG6B,QAAQ;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAClC,IAAI,EAAEmC,aAAa,EAAEC,SAAS,EAAEvC,QAAQ,EAAoB;EAAA,IAAlBwC,UAAU,GAAApC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAH,SAAA,GAAAG,SAAA,MAAG,GAAG;EAC7E,MAAMT,WAAW,GAAGL,SAAS,CAACa,IAAI,CAACG,GAAG,EAAEH,IAAI,CAACI,GAAG,EAAEiC,UAAU,CAAC;EAC7D;EACAtC,cAAc,CAACC,IAAI,EAAEmC,aAAa,EAAEC,SAAS,EAAE5C,WAAW,EAAEK,QAAQ,CAAC;AACzE;AACA;AACA;AACA;AACA,SAASgC,YAAYA,CAACvB,GAAG,EAAEgC,SAAS,EAAE;EAClCJ,aAAa,CAAC5B,GAAG,CAACE,CAAC,EAAE8B,SAAS,CAAC9B,CAAC,EAAE8B,SAAS,CAACC,MAAM,EAAED,SAAS,CAAC/C,KAAK,EAAE+C,SAAS,CAACE,OAAO,CAAC;EACvFN,aAAa,CAAC5B,GAAG,CAACG,CAAC,EAAE6B,SAAS,CAAC7B,CAAC,EAAE6B,SAAS,CAACG,MAAM,EAAEH,SAAS,CAAC/C,KAAK,EAAE+C,SAAS,CAACI,OAAO,CAAC;AAC3F;AAEA,SAAS3C,cAAc,EAAEM,aAAa,EAAEV,eAAe,EAAEiB,eAAe,EAAEvB,UAAU,EAAE6C,aAAa,EAAEL,YAAY,EAAEG,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}