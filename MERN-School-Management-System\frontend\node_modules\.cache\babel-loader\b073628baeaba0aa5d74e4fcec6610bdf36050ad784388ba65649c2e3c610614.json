{"ast": null, "code": "import { memoSupports } from './memo.mjs';\nconst supportsLinearEasing = /*@__PURE__*/memoSupports(() => {\n  try {\n    document.createElement(\"div\").animate({\n      opacity: 0\n    }, {\n      easing: \"linear(0, 1)\"\n    });\n  } catch (e) {\n    return false;\n  }\n  return true;\n}, \"linearEasing\");\nexport { supportsLinearEasing };", "map": {"version": 3, "names": ["memoSupports", "supportsLinearEasing", "document", "createElement", "animate", "opacity", "easing", "e"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs"], "sourcesContent": ["import { memoSupports } from './memo.mjs';\n\nconst supportsLinearEasing = /*@__PURE__*/ memoSupports(() => {\n    try {\n        document\n            .createElement(\"div\")\n            .animate({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n}, \"linearEasing\");\n\nexport { supportsLinearEasing };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,YAAY;AAEzC,MAAMC,oBAAoB,GAAG,aAAcD,YAAY,CAAC,MAAM;EAC1D,IAAI;IACAE,QAAQ,CACHC,aAAa,CAAC,KAAK,CAAC,CACpBC,OAAO,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAC,EAAE;MAAEC,MAAM,EAAE;IAAe,CAAC,CAAC;EAC5D,CAAC,CACD,OAAOC,CAAC,EAAE;IACN,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC,EAAE,cAAc,CAAC;AAElB,SAASN,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}