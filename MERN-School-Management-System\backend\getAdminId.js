const mongoose = require('mongoose');
const Admin = require('./models/adminSchema.js');
const Sclass = require('./models/sclassSchema.js');
require('dotenv').config();

const getAdminInfo = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URL);
        console.log('Connected to MongoDB');

        const admin = await Admin.findOne({ email: '<EMAIL>' });
        if (admin) {
            console.log('✅ Admin found:');
            console.log('   ID:', admin._id.toString());
            console.log('   Email:', admin.email);
            console.log('   Name:', admin.name);
            
            const classes = await Sclass.find({ school: admin._id });
            console.log(`\n📚 Classes (${classes.length} found):`);
            classes.forEach((cls, index) => {
                console.log(`   ${index + 1}. ${cls.sclassName}`);
            });
            
            console.log('\n🔗 API URL to test:');
            console.log(`   http://localhost:5000/SclassList/${admin._id.toString()}`);
        } else {
            console.log('❌ Admin not found');
        }
        
        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
};

getAdminInfo();
