const mongoose = require('mongoose');

const inventorySchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    category: {
        type: String,
        required: true,
        enum: ['Electronics', 'Books', 'Sports', 'Laboratory', 'Furniture', 'Maintenance']
    },
    quantity: {
        type: Number,
        required: true,
        min: 0,
        default: 0
    },
    minQuantity: {
        type: Number,
        default: 5,
        min: 0
    },
    location: {
        type: String,
        required: true,
        trim: true
    },
    status: {
        type: String,
        enum: ['Available', 'Low Stock', 'Out of Stock', 'Maintenance', 'Damaged'],
        default: function() {
            if (this.quantity === 0) return 'Out of Stock';
            if (this.quantity <= this.minQuantity) return 'Low Stock';
            return 'Available';
        }
    },
    condition: {
        type: String,
        enum: ['Excellent', 'Good', 'Fair', 'Poor'],
        default: 'Good'
    },
    cost: {
        type: Number,
        min: 0,
        default: 0
    },
    totalValue: {
        type: Number,
        default: function() {
            return this.quantity * this.cost;
        }
    },
    supplier: {
        type: String,
        trim: true
    },
    serialNumber: {
        type: String,
        trim: true,
        unique: true,
        sparse: true // Allows multiple null values
    },
    description: {
        type: String,
        trim: true
    },
    purchaseDate: {
        type: Date,
        default: Date.now
    },
    warrantyExpiry: {
        type: Date
    },
    image: {
        type: String, // Base64 encoded image or URL
        default: null
    },
    
    // Tracking fields
    lastMaintenanceDate: {
        type: Date
    },
    nextMaintenanceDate: {
        type: Date
    },
    maintenanceHistory: [{
        date: {
            type: Date,
            default: Date.now
        },
        description: String,
        cost: {
            type: Number,
            default: 0
        },
        performedBy: String
    }],
    
    // Usage tracking
    usageHistory: [{
        date: {
            type: Date,
            default: Date.now
        },
        usedBy: String,
        purpose: String,
        returnDate: Date,
        condition: {
            type: String,
            enum: ['Excellent', 'Good', 'Fair', 'Poor']
        }
    }],
    
    // Stock movement tracking
    stockMovements: [{
        date: {
            type: Date,
            default: Date.now
        },
        type: {
            type: String,
            enum: ['Purchase', 'Usage', 'Return', 'Damage', 'Loss', 'Transfer'],
            required: true
        },
        quantity: {
            type: Number,
            required: true
        },
        previousQuantity: Number,
        newQuantity: Number,
        reason: String,
        performedBy: String
    }],
    
    // School reference
    school: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'admin',
        required: true
    },
    
    // QR Code for tracking
    qrCode: {
        type: String
    },
    
    // Tags for better organization
    tags: [{
        type: String,
        trim: true
    }],
    
    // Depreciation tracking
    depreciationRate: {
        type: Number,
        default: 0, // Percentage per year
        min: 0,
        max: 100
    },
    currentValue: {
        type: Number,
        default: function() {
            return this.cost; // Initial value equals cost
        }
    },
    
    // Alerts and notifications
    alerts: [{
        type: {
            type: String,
            enum: ['Low Stock', 'Maintenance Due', 'Warranty Expiring', 'Overdue Return']
        },
        message: String,
        date: {
            type: Date,
            default: Date.now
        },
        acknowledged: {
            type: Boolean,
            default: false
        }
    }],
    
    // Additional metadata
    isActive: {
        type: Boolean,
        default: true
    },
    notes: {
        type: String,
        trim: true
    }
}, {
    timestamps: true
});

// Indexes for better performance
inventorySchema.index({ school: 1, category: 1 });
inventorySchema.index({ school: 1, status: 1 });
inventorySchema.index({ school: 1, location: 1 });
inventorySchema.index({ serialNumber: 1 });
inventorySchema.index({ name: 'text', description: 'text' });

// Pre-save middleware to update status and total value
inventorySchema.pre('save', function(next) {
    // Update status based on quantity
    if (this.quantity === 0) {
        this.status = 'Out of Stock';
    } else if (this.quantity <= this.minQuantity) {
        this.status = 'Low Stock';
    } else if (this.status === 'Low Stock' || this.status === 'Out of Stock') {
        this.status = 'Available';
    }
    
    // Update total value
    this.totalValue = this.quantity * this.cost;
    
    // Calculate current value with depreciation
    if (this.depreciationRate > 0) {
        const yearsOld = (Date.now() - this.purchaseDate) / (365.25 * 24 * 60 * 60 * 1000);
        const depreciationAmount = this.cost * (this.depreciationRate / 100) * yearsOld;
        this.currentValue = Math.max(0, this.cost - depreciationAmount);
    } else {
        this.currentValue = this.cost;
    }
    
    next();
});

// Instance methods
inventorySchema.methods.addStockMovement = function(type, quantity, reason, performedBy) {
    const previousQuantity = this.quantity;
    
    if (type === 'Purchase' || type === 'Return') {
        this.quantity += quantity;
    } else if (type === 'Usage' || type === 'Damage' || type === 'Loss') {
        this.quantity = Math.max(0, this.quantity - quantity);
    }
    
    this.stockMovements.push({
        type,
        quantity,
        previousQuantity,
        newQuantity: this.quantity,
        reason,
        performedBy
    });
    
    return this.save();
};

inventorySchema.methods.addMaintenanceRecord = function(description, cost, performedBy) {
    this.maintenanceHistory.push({
        description,
        cost,
        performedBy
    });
    
    this.lastMaintenanceDate = new Date();
    
    return this.save();
};

inventorySchema.methods.generateQRCode = function() {
    // Generate QR code data
    this.qrCode = `INV-${this.school}-${this._id}`;
    return this.save();
};

// Static methods
inventorySchema.statics.getLowStockItems = function(schoolId) {
    return this.find({
        school: schoolId,
        $expr: { $lte: ['$quantity', '$minQuantity'] },
        isActive: true
    });
};

inventorySchema.statics.getOutOfStockItems = function(schoolId) {
    return this.find({
        school: schoolId,
        quantity: 0,
        isActive: true
    });
};

inventorySchema.statics.getInventoryByCategory = function(schoolId, category) {
    return this.find({
        school: schoolId,
        category: category,
        isActive: true
    });
};

inventorySchema.statics.getTotalInventoryValue = function(schoolId) {
    return this.aggregate([
        { $match: { school: mongoose.Types.ObjectId(schoolId), isActive: true } },
        { $group: { _id: null, totalValue: { $sum: '$totalValue' } } }
    ]);
};

module.exports = mongoose.model('Inventory', inventorySchema);
