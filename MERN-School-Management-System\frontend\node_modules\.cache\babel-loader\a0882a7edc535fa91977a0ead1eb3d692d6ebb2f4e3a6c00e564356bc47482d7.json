{"ast": null, "code": "const warned = new Set();\nfunction hasWarned(message) {\n  return warned.has(message);\n}\nfunction warnOnce(condition, message, element) {\n  if (condition || warned.has(message)) return;\n  console.warn(message);\n  if (element) console.warn(element);\n  warned.add(message);\n}\nexport { hasWarned, warnOnce };", "map": {"version": 3, "names": ["warned", "Set", "hasWarned", "message", "has", "warnOnce", "condition", "element", "console", "warn", "add"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-utils/dist/es/warn-once.mjs"], "sourcesContent": ["const warned = new Set();\nfunction hasWarned(message) {\n    return warned.has(message);\n}\nfunction warnOnce(condition, message, element) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(message);\n    if (element)\n        console.warn(element);\n    warned.add(message);\n}\n\nexport { hasWarned, warnOnce };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG,IAAIC,GAAG,EAAE;AACxB,SAASC,SAASA,CAACC,OAAO,EAAE;EACxB,OAAOH,MAAM,CAACI,GAAG,CAACD,OAAO,CAAC;AAC9B;AACA,SAASE,QAAQA,CAACC,SAAS,EAAEH,OAAO,EAAEI,OAAO,EAAE;EAC3C,IAAID,SAAS,IAAIN,MAAM,CAACI,GAAG,CAACD,OAAO,CAAC,EAChC;EACJK,OAAO,CAACC,IAAI,CAACN,OAAO,CAAC;EACrB,IAAII,OAAO,EACPC,OAAO,CAACC,IAAI,CAACF,OAAO,CAAC;EACzBP,MAAM,CAACU,GAAG,CAACP,OAAO,CAAC;AACvB;AAEA,SAASD,SAAS,EAAEG,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}