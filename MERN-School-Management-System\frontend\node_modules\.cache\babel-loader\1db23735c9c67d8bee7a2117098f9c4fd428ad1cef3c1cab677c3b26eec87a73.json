{"ast": null, "code": "let warning = () => {};\nlet invariant = () => {};\nif (process.env.NODE_ENV !== \"production\") {\n  warning = (check, message) => {\n    if (!check && typeof console !== \"undefined\") {\n      console.warn(message);\n    }\n  };\n  invariant = (check, message) => {\n    if (!check) {\n      throw new Error(message);\n    }\n  };\n}\nexport { invariant, warning };", "map": {"version": 3, "names": ["warning", "invariant", "process", "env", "NODE_ENV", "check", "message", "console", "warn", "Error"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-utils/dist/es/errors.mjs"], "sourcesContent": ["let warning = () => { };\nlet invariant = () => { };\nif (process.env.NODE_ENV !== \"production\") {\n    warning = (check, message) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(message);\n        }\n    };\n    invariant = (check, message) => {\n        if (!check) {\n            throw new Error(message);\n        }\n    };\n}\n\nexport { invariant, warning };\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGA,CAAA,KAAM,CAAE,CAAC;AACvB,IAAIC,SAAS,GAAGA,CAAA,KAAM,CAAE,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACvCJ,OAAO,GAAGA,CAACK,KAAK,EAAEC,OAAO,KAAK;IAC1B,IAAI,CAACD,KAAK,IAAI,OAAOE,OAAO,KAAK,WAAW,EAAE;MAC1CA,OAAO,CAACC,IAAI,CAACF,OAAO,CAAC;IACzB;EACJ,CAAC;EACDL,SAAS,GAAGA,CAACI,KAAK,EAAEC,OAAO,KAAK;IAC5B,IAAI,CAACD,KAAK,EAAE;MACR,MAAM,IAAII,KAAK,CAACH,OAAO,CAAC;IAC5B;EACJ,CAAC;AACL;AAEA,SAASL,SAAS,EAAED,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}