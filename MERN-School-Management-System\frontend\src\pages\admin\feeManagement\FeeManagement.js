import React, { useState } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
  Alert
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  Search as SearchIcon,
  Payment as PaymentIcon,
  AccountBalance as AccountBalanceIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Receipt as ReceiptIcon,
  Print as PrintIcon,
  Send as SendIcon
} from '@mui/icons-material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
}));

const StatCard = styled(Card)(({ theme, gradient }) => ({
  background: gradient,
  color: 'white',
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.2)',
  },
}));

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`fee-tabpanel-${index}`}
    aria-labelledby={`fee-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const FeeManagement = () => {
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');

  // Sample fee data
  const feeStats = {
    totalCollection: 125000,
    pendingAmount: 35000,
    overdueAmount: 12000,
    collectionRate: 78
  };

  const feeRecords = [
    {
      id: 1,
      studentName: 'John Doe',
      rollNumber: 'ST001',
      class: '10A',
      totalFee: 5000,
      paidAmount: 3000,
      pendingAmount: 2000,
      dueDate: '2024-01-15',
      status: 'Partial',
      lastPayment: '2023-12-10'
    },
    {
      id: 2,
      studentName: 'Alice Smith',
      rollNumber: 'ST002',
      class: '10A',
      totalFee: 5000,
      paidAmount: 5000,
      pendingAmount: 0,
      dueDate: '2024-01-15',
      status: 'Paid',
      lastPayment: '2023-12-05'
    },
    {
      id: 3,
      studentName: 'Bob Johnson',
      rollNumber: 'ST003',
      class: '9B',
      totalFee: 4500,
      paidAmount: 0,
      pendingAmount: 4500,
      dueDate: '2023-12-15',
      status: 'Overdue',
      lastPayment: null
    },
  ];

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Paid': return 'success';
      case 'Partial': return 'warning';
      case 'Overdue': return 'error';
      default: return 'default';
    }
  };

  const filteredRecords = feeRecords.filter(record =>
    record.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.rollNumber.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box mb={4}>
          <Typography variant="h4" fontWeight="bold" color="primary" gutterBottom>
            💰 Fee Management System
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Comprehensive fee tracking, due management, and payment processing
          </Typography>
        </Box>
      </motion.div>

      {/* Fee Statistics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <StatCard gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)">
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      ${feeStats.totalCollection.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      Total Collection
                    </Typography>
                  </Box>
                  <AccountBalanceIcon sx={{ fontSize: 48, opacity: 0.3 }} />
                </Box>
              </CardContent>
            </StatCard>
          </motion.div>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <StatCard gradient="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)">
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      ${feeStats.pendingAmount.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      Pending Amount
                    </Typography>
                  </Box>
                  <ScheduleIcon sx={{ fontSize: 48, opacity: 0.3 }} />
                </Box>
              </CardContent>
            </StatCard>
          </motion.div>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <StatCard gradient="linear-gradient(135deg, #fa709a 0%, #fee140 100%)">
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      ${feeStats.overdueAmount.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      Overdue Amount
                    </Typography>
                  </Box>
                  <WarningIcon sx={{ fontSize: 48, opacity: 0.3 }} />
                </Box>
              </CardContent>
            </StatCard>
          </motion.div>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <StatCard gradient="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)">
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {feeStats.collectionRate}%
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      Collection Rate
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={feeStats.collectionRate} 
                      sx={{ 
                        mt: 1, 
                        bgcolor: 'rgba(255,255,255,0.3)',
                        '& .MuiLinearProgress-bar': {
                          bgcolor: 'white'
                        }
                      }}
                    />
                  </Box>
                  <TrendingUpIcon sx={{ fontSize: 48, opacity: 0.3 }} />
                </Box>
              </CardContent>
            </StatCard>
          </motion.div>
        </Grid>
      </Grid>

      <StyledPaper>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <TextField
            placeholder="Search students..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300 }}
          />
          <Box display="flex" gap={2}>
            <Button variant="contained" color="primary" startIcon={<PaymentIcon />}>
              Collect Fee
            </Button>
            <Button variant="outlined" color="secondary" startIcon={<ReceiptIcon />}>
              Generate Receipt
            </Button>
            <Button variant="outlined" color="info" startIcon={<SendIcon />}>
              Send Reminders
            </Button>
          </Box>
        </Box>

        <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="Fee Records" />
          <Tab label="Due Payments" />
          <Tab label="Discounts" />
          <Tab label="Payment History" />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <TableContainer component={Paper} sx={{ borderRadius: '16px' }}>
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: 'primary.main' }}>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Student</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Class</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Total Fee</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Paid</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Pending</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Due Date</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Status</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredRecords.map((record) => (
                  <TableRow key={record.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {record.studentName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {record.rollNumber}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{record.class}</TableCell>
                    <TableCell>${record.totalFee}</TableCell>
                    <TableCell>${record.paidAmount}</TableCell>
                    <TableCell>
                      <Typography 
                        color={record.pendingAmount > 0 ? 'error' : 'success'}
                        fontWeight="bold"
                      >
                        ${record.pendingAmount}
                      </Typography>
                    </TableCell>
                    <TableCell>{record.dueDate}</TableCell>
                    <TableCell>
                      <Chip
                        label={record.status}
                        color={getStatusColor(record.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <Tooltip title="Collect Payment">
                          <IconButton size="small" color="primary">
                            <PaymentIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Print Receipt">
                          <IconButton size="small" color="secondary">
                            <PrintIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Send Reminder">
                          <IconButton size="small" color="info">
                            <SendIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="h6">Due Payments Alert</Typography>
            There are {feeRecords.filter(r => r.status === 'Overdue').length} overdue payments requiring immediate attention.
          </Alert>
          <Typography variant="h6" gutterBottom>
            Due Payment Management
          </Typography>
          <Typography color="text.secondary">
            Overdue payment tracking and reminder system will be displayed here.
          </Typography>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Discount Management
          </Typography>
          <Typography color="text.secondary">
            Student discount schemes, scholarship management, and fee concessions will be displayed here.
          </Typography>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>
            Payment History
          </Typography>
          <Typography color="text.secondary">
            Complete payment transaction history and financial reports will be displayed here.
          </Typography>
        </TabPanel>
      </StyledPaper>
    </Container>
  );
};

export default FeeManagement;
