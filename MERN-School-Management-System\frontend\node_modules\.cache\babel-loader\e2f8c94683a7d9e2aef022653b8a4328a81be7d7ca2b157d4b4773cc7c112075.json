{"ast": null, "code": "import { frame, cancelFrame } from 'motion-dom';\nimport { useRef, useContext, useEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nfunction useAnimationFrame(callback) {\n  const initialTimestamp = useRef(0);\n  const {\n    isStatic\n  } = useContext(MotionConfigContext);\n  useEffect(() => {\n    if (isStatic) return;\n    const provideTimeSinceStart = _ref => {\n      let {\n        timestamp,\n        delta\n      } = _ref;\n      if (!initialTimestamp.current) initialTimestamp.current = timestamp;\n      callback(timestamp - initialTimestamp.current, delta);\n    };\n    frame.update(provideTimeSinceStart, true);\n    return () => cancelFrame(provideTimeSinceStart);\n  }, [callback]);\n}\nexport { useAnimationFrame };", "map": {"version": 3, "names": ["frame", "cancelFrame", "useRef", "useContext", "useEffect", "MotionConfigContext", "useAnimationFrame", "callback", "initialTimestamp", "isStatic", "provideTimeSinceStart", "_ref", "timestamp", "delta", "current", "update"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/utils/use-animation-frame.mjs"], "sourcesContent": ["import { frame, cancelFrame } from 'motion-dom';\nimport { useRef, useContext, useEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\n\nfunction useAnimationFrame(callback) {\n    const initialTimestamp = useRef(0);\n    const { isStatic } = useContext(MotionConfigContext);\n    useEffect(() => {\n        if (isStatic)\n            return;\n        const provideTimeSinceStart = ({ timestamp, delta }) => {\n            if (!initialTimestamp.current)\n                initialTimestamp.current = timestamp;\n            callback(timestamp - initialTimestamp.current, delta);\n        };\n        frame.update(provideTimeSinceStart, true);\n        return () => cancelFrame(provideTimeSinceStart);\n    }, [callback]);\n}\n\nexport { useAnimationFrame };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,QAAQ,YAAY;AAC/C,SAASC,MAAM,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACrD,SAASC,mBAAmB,QAAQ,oCAAoC;AAExE,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EACjC,MAAMC,gBAAgB,GAAGN,MAAM,CAAC,CAAC,CAAC;EAClC,MAAM;IAAEO;EAAS,CAAC,GAAGN,UAAU,CAACE,mBAAmB,CAAC;EACpDD,SAAS,CAAC,MAAM;IACZ,IAAIK,QAAQ,EACR;IACJ,MAAMC,qBAAqB,GAAGC,IAAA,IAA0B;MAAA,IAAzB;QAAEC,SAAS;QAAEC;MAAM,CAAC,GAAAF,IAAA;MAC/C,IAAI,CAACH,gBAAgB,CAACM,OAAO,EACzBN,gBAAgB,CAACM,OAAO,GAAGF,SAAS;MACxCL,QAAQ,CAACK,SAAS,GAAGJ,gBAAgB,CAACM,OAAO,EAAED,KAAK,CAAC;IACzD,CAAC;IACDb,KAAK,CAACe,MAAM,CAACL,qBAAqB,EAAE,IAAI,CAAC;IACzC,OAAO,MAAMT,WAAW,CAACS,qBAAqB,CAAC;EACnD,CAAC,EAAE,CAACH,QAAQ,CAAC,CAAC;AAClB;AAEA,SAASD,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}