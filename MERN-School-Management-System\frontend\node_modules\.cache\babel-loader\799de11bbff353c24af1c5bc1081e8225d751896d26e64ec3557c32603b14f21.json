{"ast": null, "code": "import { jsx } from 'react/jsx-runtime';\nimport { invariant } from 'motion-utils';\nimport * as React from 'react';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { LayoutGroup } from './LayoutGroup/index.mjs';\nlet id = 0;\nconst AnimateSharedLayout = _ref => {\n  let {\n    children\n  } = _ref;\n  React.useEffect(() => {\n    invariant(false, \"AnimateSharedLayout is deprecated: https://www.framer.com/docs/guide-upgrade/##shared-layout-animations\");\n  }, []);\n  return jsx(LayoutGroup, {\n    id: useConstant(() => `asl-${id++}`),\n    children: children\n  });\n};\nexport { AnimateSharedLayout };", "map": {"version": 3, "names": ["jsx", "invariant", "React", "useConstant", "LayoutGroup", "id", "AnimateSharedLayout", "_ref", "children", "useEffect"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/components/AnimateSharedLayout.mjs"], "sourcesContent": ["import { jsx } from 'react/jsx-runtime';\nimport { invariant } from 'motion-utils';\nimport * as React from 'react';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { LayoutGroup } from './LayoutGroup/index.mjs';\n\nlet id = 0;\nconst AnimateSharedLayout = ({ children }) => {\n    React.useEffect(() => {\n        invariant(false, \"AnimateSharedLayout is deprecated: https://www.framer.com/docs/guide-upgrade/##shared-layout-animations\");\n    }, []);\n    return (jsx(LayoutGroup, { id: useConstant(() => `asl-${id++}`), children: children }));\n};\n\nexport { AnimateSharedLayout };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,WAAW,QAAQ,yBAAyB;AAErD,IAAIC,EAAE,GAAG,CAAC;AACV,MAAMC,mBAAmB,GAAGC,IAAA,IAAkB;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAD,IAAA;EACrCL,KAAK,CAACO,SAAS,CAAC,MAAM;IAClBR,SAAS,CAAC,KAAK,EAAE,yGAAyG,CAAC;EAC/H,CAAC,EAAE,EAAE,CAAC;EACN,OAAQD,GAAG,CAACI,WAAW,EAAE;IAAEC,EAAE,EAAEF,WAAW,CAAC,MAAO,OAAME,EAAE,EAAG,EAAC,CAAC;IAAEG,QAAQ,EAAEA;EAAS,CAAC,CAAC;AAC1F,CAAC;AAED,SAASF,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}