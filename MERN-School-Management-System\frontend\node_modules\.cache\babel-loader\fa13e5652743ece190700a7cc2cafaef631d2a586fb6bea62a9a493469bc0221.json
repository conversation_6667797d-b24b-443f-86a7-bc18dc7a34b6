{"ast": null, "code": "import axios from 'axios';\nimport { getRequest, getSuccess, getFailed, getError, stuffDone } from './studentSlice';\nconst REACT_APP_BASE_URL = \"http://localhost:5000\";\nexport const getAllStudents = id => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/Students/${id}`);\n    if (result.data.message) {\n      dispatch(getFailed(result.data.message));\n    } else {\n      dispatch(getSuccess(result.data));\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const updateStudentFields = (id, fields, address) => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.put(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`, fields, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    if (result.data.message) {\n      dispatch(getFailed(result.data.message));\n    } else {\n      dispatch(stuffDone());\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};\nexport const removeStuff = (id, address) => async dispatch => {\n  dispatch(getRequest());\n  try {\n    const result = await axios.put(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\n    if (result.data.message) {\n      dispatch(getFailed(result.data.message));\n    } else {\n      dispatch(stuffDone());\n    }\n  } catch (error) {\n    dispatch(getError(error));\n  }\n};", "map": {"version": 3, "names": ["axios", "getRequest", "getSuccess", "getFailed", "getError", "stuffDone", "REACT_APP_BASE_URL", "getAllStudents", "id", "dispatch", "result", "get", "process", "env", "data", "message", "error", "updateStudentFields", "fields", "address", "put", "headers", "removeStuff"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/redux/studentRelated/studentHandle.js"], "sourcesContent": ["import axios from 'axios';\r\nimport {\r\n    getRequest,\r\n    getSuccess,\r\n    getFailed,\r\n    getError,\r\n    stuffDone\r\n} from './studentSlice';\r\n\r\nconst REACT_APP_BASE_URL = \"http://localhost:5000\";\r\n\r\nexport const getAllStudents = (id) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.get(`${process.env.REACT_APP_BASE_URL}/Students/${id}`);\r\n        if (result.data.message) {\r\n            dispatch(getFailed(result.data.message));\r\n        } else {\r\n            dispatch(getSuccess(result.data));\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const updateStudentFields = (id, fields, address) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.put(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`, fields, {\r\n            headers: { 'Content-Type': 'application/json' },\r\n        });\r\n        if (result.data.message) {\r\n            dispatch(getFailed(result.data.message));\r\n        } else {\r\n            dispatch(stuffDone());\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}\r\n\r\nexport const removeStuff = (id, address) => async (dispatch) => {\r\n    dispatch(getRequest());\r\n\r\n    try {\r\n        const result = await axios.put(`${process.env.REACT_APP_BASE_URL}/${address}/${id}`);\r\n        if (result.data.message) {\r\n            dispatch(getFailed(result.data.message));\r\n        } else {\r\n            dispatch(stuffDone());\r\n        }\r\n    } catch (error) {\r\n        dispatch(getError(error));\r\n    }\r\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,UAAU,EACVC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,SAAS,QACN,gBAAgB;AAEvB,MAAMC,kBAAkB,GAAG,uBAAuB;AAElD,OAAO,MAAMC,cAAc,GAAIC,EAAE,IAAK,MAAOC,QAAQ,IAAK;EACtDA,QAAQ,CAACR,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMS,MAAM,GAAG,MAAMV,KAAK,CAACW,GAAG,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACP,kBAAmB,aAAYE,EAAG,EAAC,CAAC;IAClF,IAAIE,MAAM,CAACI,IAAI,CAACC,OAAO,EAAE;MACrBN,QAAQ,CAACN,SAAS,CAACO,MAAM,CAACI,IAAI,CAACC,OAAO,CAAC,CAAC;IAC5C,CAAC,MAAM;MACHN,QAAQ,CAACP,UAAU,CAACQ,MAAM,CAACI,IAAI,CAAC,CAAC;IACrC;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZP,QAAQ,CAACL,QAAQ,CAACY,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAGA,CAACT,EAAE,EAAEU,MAAM,EAAEC,OAAO,KAAK,MAAOV,QAAQ,IAAK;EAC5EA,QAAQ,CAACR,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMS,MAAM,GAAG,MAAMV,KAAK,CAACoB,GAAG,CAAE,GAAER,OAAO,CAACC,GAAG,CAACP,kBAAmB,IAAGa,OAAQ,IAAGX,EAAG,EAAC,EAAEU,MAAM,EAAE;MACzFG,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB;IAClD,CAAC,CAAC;IACF,IAAIX,MAAM,CAACI,IAAI,CAACC,OAAO,EAAE;MACrBN,QAAQ,CAACN,SAAS,CAACO,MAAM,CAACI,IAAI,CAACC,OAAO,CAAC,CAAC;IAC5C,CAAC,MAAM;MACHN,QAAQ,CAACJ,SAAS,EAAE,CAAC;IACzB;EACJ,CAAC,CAAC,OAAOW,KAAK,EAAE;IACZP,QAAQ,CAACL,QAAQ,CAACY,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC;AAED,OAAO,MAAMM,WAAW,GAAGA,CAACd,EAAE,EAAEW,OAAO,KAAK,MAAOV,QAAQ,IAAK;EAC5DA,QAAQ,CAACR,UAAU,EAAE,CAAC;EAEtB,IAAI;IACA,MAAMS,MAAM,GAAG,MAAMV,KAAK,CAACoB,GAAG,CAAE,GAAER,OAAO,CAACC,GAAG,CAACP,kBAAmB,IAAGa,OAAQ,IAAGX,EAAG,EAAC,CAAC;IACpF,IAAIE,MAAM,CAACI,IAAI,CAACC,OAAO,EAAE;MACrBN,QAAQ,CAACN,SAAS,CAACO,MAAM,CAACI,IAAI,CAACC,OAAO,CAAC,CAAC;IAC5C,CAAC,MAAM;MACHN,QAAQ,CAACJ,SAAS,EAAE,CAAC;IACzB;EACJ,CAAC,CAAC,OAAOW,KAAK,EAAE;IACZP,QAAQ,CAACL,QAAQ,CAACY,KAAK,CAAC,CAAC;EAC7B;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}