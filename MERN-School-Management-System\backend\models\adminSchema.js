const mongoose = require("mongoose")

const adminSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    email: {
        type: String,
        unique: true,
        required: true,
    },
    password: {
        type: String,
        required: true,
    },
    role: {
        type: String,
        default: "Admin"
    },
    schoolName: {
        type: String,
        unique: true,
        required: true
    },
    profilePicture: {
        type: String, // Base64 encoded image or URL
        default: null
    },
    phone: {
        type: String,
        default: null
    },
    address: {
        type: String,
        default: null
    },
    website: {
        type: String,
        default: null
    },
    description: {
        type: String,
        default: null
    },
    schoolLogo: {
        type: String, // Base64 encoded image or URL
        default: null
    },
    schoolSettings: {
        primaryColor: {
            type: String,
            default: '#667eea'
        },
        secondaryColor: {
            type: String,
            default: '#764ba2'
        },
        timezone: {
            type: String,
            default: 'UTC'
        },
        academicYear: {
            type: String,
            default: '2024-2025'
        },
        currency: {
            type: String,
            default: 'USD'
        },
        language: {
            type: String,
            default: 'en'
        }
    },
    systemSettings: {
        emailNotifications: {
            type: Boolean,
            default: true
        },
        smsNotifications: {
            type: Boolean,
            default: false
        },
        darkMode: {
            type: Boolean,
            default: false
        },
        autoBackup: {
            type: Boolean,
            default: true
        },
        maintenanceMode: {
            type: Boolean,
            default: false
        }
    }
});

module.exports = mongoose.model("admin", adminSchema)