import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { Paper, Typography, Box } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  height: '400px',
  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
  color: 'white',
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
}));

const PerformanceChart = ({ data }) => {
  // Sample data if none provided
  const defaultData = [
    { subject: 'Math', average: 78, highest: 95, lowest: 45 },
    { subject: 'Science', average: 82, highest: 98, lowest: 52 },
    { subject: 'English', average: 75, highest: 92, lowest: 48 },
    { subject: 'History', average: 80, highest: 94, lowest: 55 },
    { subject: 'Geography', average: 77, highest: 89, lowest: 42 },
  ];

  const chartData = data || defaultData;

  return (
    <StyledPaper elevation={3}>
      <Box mb={2}>
        <Typography variant="h6" component="h3" fontWeight="bold">
          📈 Academic Performance
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.8 }}>
          Subject-wise performance analysis
        </Typography>
      </Box>
      <ResponsiveContainer width="100%" height="85%">
        <BarChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.2)" />
          <XAxis 
            dataKey="subject" 
            stroke="white"
            fontSize={12}
          />
          <YAxis 
            stroke="white"
            fontSize={12}
            domain={[0, 100]}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: 'rgba(0,0,0,0.8)',
              border: 'none',
              borderRadius: '8px',
              color: 'white'
            }}
          />
          <Legend />
          <Bar 
            dataKey="average" 
            fill="#FFD700" 
            name="Average Score"
            radius={[4, 4, 0, 0]}
          />
          <Bar 
            dataKey="highest" 
            fill="#4ECDC4" 
            name="Highest Score"
            radius={[4, 4, 0, 0]}
          />
          <Bar 
            dataKey="lowest" 
            fill="#FF6B6B" 
            name="Lowest Score"
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </StyledPaper>
  );
};

export default PerformanceChart;
