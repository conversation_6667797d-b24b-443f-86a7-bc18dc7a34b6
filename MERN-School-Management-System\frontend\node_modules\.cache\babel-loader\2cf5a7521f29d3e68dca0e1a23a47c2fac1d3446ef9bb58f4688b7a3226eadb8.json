{"ast": null, "code": "// Fixes https://github.com/motiondivision/motion/issues/2270\nconst getContextWindow = _ref => {\n  let {\n    current\n  } = _ref;\n  return current ? current.ownerDocument.defaultView : null;\n};\nexport { getContextWindow };", "map": {"version": 3, "names": ["getContextWindow", "_ref", "current", "ownerDocument", "defaultView"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/utils/get-context-window.mjs"], "sourcesContent": ["// Fixes https://github.com/motiondivision/motion/issues/2270\nconst getContextWindow = ({ current }) => {\n    return current ? current.ownerDocument.defaultView : null;\n};\n\nexport { getContextWindow };\n"], "mappings": "AAAA;AACA,MAAMA,gBAAgB,GAAGC,IAAA,IAAiB;EAAA,IAAhB;IAAEC;EAAQ,CAAC,GAAAD,IAAA;EACjC,OAAOC,OAAO,GAAGA,OAAO,CAACC,aAAa,CAACC,WAAW,GAAG,IAAI;AAC7D,CAAC;AAED,SAASJ,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}