{"ast": null, "code": "const cubicBezierAsString = _ref => {\n  let [a, b, c, d] = _ref;\n  return `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n};\nexport { cubicBezierAsString };", "map": {"version": 3, "names": ["cubicBezierAsString", "_ref", "a", "b", "c", "d"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs"], "sourcesContent": ["const cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n\nexport { cubicBezierAsString };\n"], "mappings": "AAAA,MAAMA,mBAAmB,GAAGC,IAAA;EAAA,IAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAAJ,IAAA;EAAA,OAAM,gBAAeC,CAAE,KAAIC,CAAE,KAAIC,CAAE,KAAIC,CAAE,GAAE;AAAA;AAEpF,SAASL,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}