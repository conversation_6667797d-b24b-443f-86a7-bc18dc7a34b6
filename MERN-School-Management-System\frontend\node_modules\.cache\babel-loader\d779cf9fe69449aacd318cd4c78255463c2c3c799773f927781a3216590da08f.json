{"ast": null, "code": "import { frame } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { Feature } from '../../motion/features/Feature.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { PanSession } from './PanSession.mjs';\nconst asyncHandler = handler => (event, info) => {\n  if (handler) {\n    frame.postRender(() => handler(event, info));\n  }\n};\nclass PanGesture extends Feature {\n  constructor() {\n    super(...arguments);\n    this.removePointerDownListener = noop;\n  }\n  onPointerDown(pointerDownEvent) {\n    this.session = new PanSession(pointerDownEvent, this.createPanHandlers(), {\n      transformPagePoint: this.node.getTransformPagePoint(),\n      contextWindow: getContextWindow(this.node)\n    });\n  }\n  createPanHandlers() {\n    const {\n      onPanSessionStart,\n      onPanStart,\n      onPan,\n      onPanEnd\n    } = this.node.getProps();\n    return {\n      onSessionStart: asyncHandler(onPanSessionStart),\n      onStart: asyncHandler(onPanStart),\n      onMove: onPan,\n      onEnd: (event, info) => {\n        delete this.session;\n        if (onPanEnd) {\n          frame.postRender(() => onPanEnd(event, info));\n        }\n      }\n    };\n  }\n  mount() {\n    this.removePointerDownListener = addPointerEvent(this.node.current, \"pointerdown\", event => this.onPointerDown(event));\n  }\n  update() {\n    this.session && this.session.updateHandlers(this.createPanHandlers());\n  }\n  unmount() {\n    this.removePointerDownListener();\n    this.session && this.session.end();\n  }\n}\nexport { PanGesture };", "map": {"version": 3, "names": ["frame", "noop", "addPointerEvent", "Feature", "getContextWindow", "PanSession", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handler", "event", "info", "postRender", "PanGesture", "constructor", "arguments", "removePointerDownListener", "onPointerDown", "pointerDownEvent", "session", "createPanHandlers", "transformPagePoint", "node", "getTransformPagePoint", "contextWindow", "onPanSessionStart", "onPanStart", "onPan", "onPanEnd", "getProps", "onSessionStart", "onStart", "onMove", "onEnd", "mount", "current", "update", "updateHandlers", "unmount", "end"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/gestures/pan/index.mjs"], "sourcesContent": ["import { frame } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { Feature } from '../../motion/features/Feature.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { PanSession } from './PanSession.mjs';\n\nconst asyncHandler = (handler) => (event, info) => {\n    if (handler) {\n        frame.postRender(() => handler(event, info));\n    }\n};\nclass PanGesture extends Feature {\n    constructor() {\n        super(...arguments);\n        this.removePointerDownListener = noop;\n    }\n    onPointerDown(pointerDownEvent) {\n        this.session = new PanSession(pointerDownEvent, this.createPanHandlers(), {\n            transformPagePoint: this.node.getTransformPagePoint(),\n            contextWindow: getContextWindow(this.node),\n        });\n    }\n    createPanHandlers() {\n        const { onPanSessionStart, onPanStart, onPan, onPanEnd } = this.node.getProps();\n        return {\n            onSessionStart: asyncHandler(onPanSessionStart),\n            onStart: asyncHandler(onPanStart),\n            onMove: onPan,\n            onEnd: (event, info) => {\n                delete this.session;\n                if (onPanEnd) {\n                    frame.postRender(() => onPanEnd(event, info));\n                }\n            },\n        };\n    }\n    mount() {\n        this.removePointerDownListener = addPointerEvent(this.node.current, \"pointerdown\", (event) => this.onPointerDown(event));\n    }\n    update() {\n        this.session && this.session.updateHandlers(this.createPanHandlers());\n    }\n    unmount() {\n        this.removePointerDownListener();\n        this.session && this.session.end();\n    }\n}\n\nexport { PanGesture };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,UAAU,QAAQ,kBAAkB;AAE7C,MAAMC,YAAY,GAAIC,OAAO,IAAK,CAACC,KAAK,EAAEC,IAAI,KAAK;EAC/C,IAAIF,OAAO,EAAE;IACTP,KAAK,CAACU,UAAU,CAAC,MAAMH,OAAO,CAACC,KAAK,EAAEC,IAAI,CAAC,CAAC;EAChD;AACJ,CAAC;AACD,MAAME,UAAU,SAASR,OAAO,CAAC;EAC7BS,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,yBAAyB,GAAGb,IAAI;EACzC;EACAc,aAAaA,CAACC,gBAAgB,EAAE;IAC5B,IAAI,CAACC,OAAO,GAAG,IAAIZ,UAAU,CAACW,gBAAgB,EAAE,IAAI,CAACE,iBAAiB,EAAE,EAAE;MACtEC,kBAAkB,EAAE,IAAI,CAACC,IAAI,CAACC,qBAAqB,EAAE;MACrDC,aAAa,EAAElB,gBAAgB,CAAC,IAAI,CAACgB,IAAI;IAC7C,CAAC,CAAC;EACN;EACAF,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEK,iBAAiB;MAAEC,UAAU;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACN,IAAI,CAACO,QAAQ,EAAE;IAC/E,OAAO;MACHC,cAAc,EAAEtB,YAAY,CAACiB,iBAAiB,CAAC;MAC/CM,OAAO,EAAEvB,YAAY,CAACkB,UAAU,CAAC;MACjCM,MAAM,EAAEL,KAAK;MACbM,KAAK,EAAEA,CAACvB,KAAK,EAAEC,IAAI,KAAK;QACpB,OAAO,IAAI,CAACQ,OAAO;QACnB,IAAIS,QAAQ,EAAE;UACV1B,KAAK,CAACU,UAAU,CAAC,MAAMgB,QAAQ,CAAClB,KAAK,EAAEC,IAAI,CAAC,CAAC;QACjD;MACJ;IACJ,CAAC;EACL;EACAuB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAClB,yBAAyB,GAAGZ,eAAe,CAAC,IAAI,CAACkB,IAAI,CAACa,OAAO,EAAE,aAAa,EAAGzB,KAAK,IAAK,IAAI,CAACO,aAAa,CAACP,KAAK,CAAC,CAAC;EAC5H;EACA0B,MAAMA,CAAA,EAAG;IACL,IAAI,CAACjB,OAAO,IAAI,IAAI,CAACA,OAAO,CAACkB,cAAc,CAAC,IAAI,CAACjB,iBAAiB,EAAE,CAAC;EACzE;EACAkB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACtB,yBAAyB,EAAE;IAChC,IAAI,CAACG,OAAO,IAAI,IAAI,CAACA,OAAO,CAACoB,GAAG,EAAE;EACtC;AACJ;AAEA,SAAS1B,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}