import React from 'react';
import {
  Drawer,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  Typography,
  useTheme,
  useMediaQuery,
  Avatar,
  Chip,
  Collapse
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { Link, useLocation } from 'react-router-dom';
import {
  Home as HomeIcon,
  Class as ClassIcon,
  Assignment as AssignmentIcon,
  SupervisorAccount as SupervisorAccountIcon,
  PersonOutline as PersonOutlineIcon,
  Announcement as AnnouncementIcon,
  Report as ReportIcon,
  AccountCircleOutlined as AccountCircleOutlinedIcon,
  ExitToApp as ExitToAppIcon,
  School as SchoolIcon,
  // New icons for additional modules
  Info as InfoIcon,
  Payment as PaymentIcon,
  Description as DescriptionIcon,
  DirectionsBus as DirectionsBusIcon,
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  EventAvailable as EventAvailableIcon,
  Quiz as QuizIcon,
  MenuBook as MenuBookIcon,
  NotificationsActive as NotificationsActiveIcon,
  Hotel as HotelIcon,
  Web as WebIcon,
  Business as BusinessIcon,
  ExpandLess,
  ExpandMore,
} from '@mui/icons-material';

const StyledDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    background: 'linear-gradient(180deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    borderRight: 'none',
    boxShadow: '4px 0 20px rgba(0, 0, 0, 0.1)',
  },
}));

const SidebarHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3, 2),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
}));

const StyledListItemButton = styled(ListItemButton)(({ theme, active }) => ({
  margin: theme.spacing(0.5, 1),
  borderRadius: '12px',
  padding: theme.spacing(1.5, 2),
  backgroundColor: active ? 'rgba(255, 255, 255, 0.15)' : 'transparent',
  backdropFilter: active ? 'blur(10px)' : 'none',
  border: active ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid transparent',
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    transform: 'translateX(4px)',
  },
  '&:before': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    width: active ? '4px' : '0px',
    height: '60%',
    backgroundColor: '#FFD700',
    borderRadius: '0 4px 4px 0',
    transition: 'width 0.3s ease',
  },
}));

const MenuSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1, 2),
  marginTop: theme.spacing(2),
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  fontWeight: 600,
  textTransform: 'uppercase',
  letterSpacing: '0.5px',
  opacity: 0.7,
  marginBottom: theme.spacing(1),
}));

const ResponsiveSidebar = ({ open, onClose, variant = 'permanent' }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const location = useLocation();
  const [expandedSections, setExpandedSections] = React.useState({});

  const handleSectionToggle = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Main Dashboard
  const dashboardItems = [
    {
      text: 'Dashboard',
      icon: <HomeIcon />,
      path: '/Admin/dashboard',
      badge: null
    },
  ];

  // Student Management
  const studentManagementItems = [
    {
      text: 'Student Info',
      icon: <InfoIcon />,
      path: '/Admin/student-info',
      badge: null
    },
    {
      text: 'Students',
      icon: <PersonOutlineIcon />,
      path: '/Admin/students',
      badge: null
    },
    {
      text: 'Admission',
      icon: <PersonAddIcon />,
      path: '/Admin/admission',
      badge: '5'
    },
  ];

  // Academic Management
  const academicItems = [
    {
      text: 'Classes',
      icon: <ClassIcon />,
      path: '/Admin/classes',
      badge: null
    },
    {
      text: 'Subjects',
      icon: <AssignmentIcon />,
      path: '/Admin/subjects',
      badge: null
    },
    {
      text: 'Academics',
      icon: <MenuBookIcon />,
      path: '/Admin/academics',
      badge: null
    },
    {
      text: 'Examination',
      icon: <QuizIcon />,
      path: '/Admin/examination',
      badge: '2'
    },
  ];

  // Staff Management
  const staffItems = [
    {
      text: 'Teachers',
      icon: <SupervisorAccountIcon />,
      path: '/Admin/teachers',
      badge: null
    },
    {
      text: 'Attendance',
      icon: <EventAvailableIcon />,
      path: '/Admin/attendance',
      badge: null
    },
  ];

  // Financial Management
  const financialItems = [
    {
      text: 'Fee Management',
      icon: <PaymentIcon />,
      path: '/Admin/fee-management',
      badge: '12'
    },
    {
      text: 'Fee Due',
      icon: <PaymentIcon />,
      path: '/Admin/fee-due',
      badge: '8'
    },
  ];

  // Operations
  const operationsItems = [
    {
      text: 'Transport',
      icon: <DirectionsBusIcon />,
      path: '/Admin/transport',
      badge: null
    },
    {
      text: 'Hostel',
      icon: <HotelIcon />,
      path: '/Admin/hostel',
      badge: '3'
    },
    {
      text: 'Front Office',
      icon: <BusinessIcon />,
      path: '/Admin/front-office',
      badge: null
    },
  ];

  // Communication
  const communicationItems = [
    {
      text: 'Notifications',
      icon: <NotificationsActiveIcon />,
      path: '/Admin/notifications',
      badge: '15'
    },
    {
      text: 'Notices',
      icon: <AnnouncementIcon />,
      path: '/Admin/notices',
      badge: '3'
    },
    {
      text: 'CMS',
      icon: <WebIcon />,
      path: '/Admin/cms',
      badge: null
    },
  ];

  // Documents & Reports
  const documentsItems = [
    {
      text: 'Documents',
      icon: <DescriptionIcon />,
      path: '/Admin/documents',
      badge: null
    },
    {
      text: 'Complaints',
      icon: <ReportIcon />,
      path: '/Admin/complains',
      badge: '2'
    },
  ];

  const userMenuItems = [
    {
      text: 'Profile',
      icon: <AccountCircleOutlinedIcon />,
      path: '/Admin/profile',
      badge: null
    },
    {
      text: 'Logout',
      icon: <ExitToAppIcon />,
      path: '/logout',
      badge: null
    },
  ];

  const isActive = (path) => {
    if (path === '/Admin/dashboard') {
      return location.pathname === '/' || location.pathname === '/Admin/dashboard';
    }
    return location.pathname.startsWith(path);
  };

  const renderMenuSection = (title, items, sectionKey) => (
    <MenuSection key={sectionKey}>
      <StyledListItemButton
        onClick={() => handleSectionToggle(sectionKey)}
        sx={{
          justifyContent: 'space-between',
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          mb: 1,
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
          }
        }}
      >
        <SectionTitle>{title}</SectionTitle>
        {expandedSections[sectionKey] ? <ExpandLess /> : <ExpandMore />}
      </StyledListItemButton>

      <Collapse in={expandedSections[sectionKey]} timeout="auto" unmountOnExit>
        <List component="nav" sx={{ padding: 0, pl: 1 }}>
          {items.map((item, index) => (
            <motion.div
              key={item.text}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <StyledListItemButton
                component={Link}
                to={item.path}
                active={isActive(item.path)}
                onClick={isMobile ? onClose : undefined}
                sx={{ pl: 2 }}
              >
                <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{
                    fontSize: '0.85rem',
                    fontWeight: isActive(item.path) ? 600 : 400,
                  }}
                />
                {item.badge && (
                  <Chip
                    label={item.badge}
                    size="small"
                    sx={{
                      bgcolor: '#FFD700',
                      color: '#333',
                      fontSize: '0.7rem',
                      height: 18,
                      minWidth: 18,
                    }}
                  />
                )}
              </StyledListItemButton>
            </motion.div>
          ))}
        </List>
      </Collapse>
    </MenuSection>
  );

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <SidebarHeader>
        <Avatar
          sx={{
            bgcolor: 'rgba(255, 255, 255, 0.2)',
            width: 48,
            height: 48,
          }}
        >
          <SchoolIcon />
        </Avatar>
        <Box>
          <Typography variant="h6" fontWeight="bold">
            School Admin
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.8 }}>
            Management System
          </Typography>
        </Box>
      </SidebarHeader>

      <Box sx={{ flex: 1, overflowY: 'auto', py: 1 }}>
        {/* Dashboard */}
        <MenuSection>
          <List component="nav" sx={{ padding: 0 }}>
            {dashboardItems.map((item, index) => (
              <motion.div
                key={item.text}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <StyledListItemButton
                  component={Link}
                  to={item.path}
                  active={isActive(item.path)}
                  onClick={isMobile ? onClose : undefined}
                >
                  <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={item.text}
                    primaryTypographyProps={{
                      fontSize: '0.9rem',
                      fontWeight: isActive(item.path) ? 600 : 400,
                    }}
                  />
                </StyledListItemButton>
              </motion.div>
            ))}
          </List>
        </MenuSection>

        <Divider sx={{ mx: 2, my: 1, bgcolor: 'rgba(255, 255, 255, 0.1)' }} />

        {/* Student Management */}
        {renderMenuSection('👥 Student Management', studentManagementItems, 'students')}

        {/* Academic Management */}
        {renderMenuSection('📚 Academic Management', academicItems, 'academics')}

        {/* Staff Management */}
        {renderMenuSection('👨‍🏫 Staff Management', staffItems, 'staff')}

        {/* Financial Management */}
        {renderMenuSection('💰 Financial Management', financialItems, 'finance')}

        {/* Operations */}
        {renderMenuSection('🚌 Operations', operationsItems, 'operations')}

        {/* Communication */}
        {renderMenuSection('📢 Communication', communicationItems, 'communication')}

        {/* Documents & Reports */}
        {renderMenuSection('📄 Documents & Reports', documentsItems, 'documents')}

        <Divider sx={{ mx: 2, my: 1, bgcolor: 'rgba(255, 255, 255, 0.1)' }} />

        {/* User Account */}
        <MenuSection>
          <SectionTitle>👤 Account</SectionTitle>
          <List component="nav" sx={{ padding: 0 }}>
            {userMenuItems.map((item, index) => (
              <motion.div
                key={item.text}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <StyledListItemButton
                  component={Link}
                  to={item.path}
                  active={isActive(item.path)}
                  onClick={isMobile ? onClose : undefined}
                >
                  <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={item.text}
                    primaryTypographyProps={{
                      fontSize: '0.9rem',
                      fontWeight: isActive(item.path) ? 600 : 400,
                    }}
                  />
                </StyledListItemButton>
              </motion.div>
            ))}
          </List>
        </MenuSection>
      </Box>
    </Box>
  );

  if (isMobile) {
    return (
      <StyledDrawer
        variant="temporary"
        open={open}
        onClose={onClose}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
          },
        }}
      >
        {drawerContent}
      </StyledDrawer>
    );
  }

  return (
    <StyledDrawer
      variant={variant}
      open={open}
      sx={{
        '& .MuiDrawer-paper': {
          width: open ? 280 : 70,
          transition: theme.transitions.create('width', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
          overflowX: 'hidden',
        },
      }}
    >
      {drawerContent}
    </StyledDrawer>
  );
};

export default ResponsiveSidebar;
