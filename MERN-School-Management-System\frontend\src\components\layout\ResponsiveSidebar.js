import React from 'react';
import {
  Drawer,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  Typography,
  useTheme,
  useMediaQuery,
  Avatar,
  Chip,
  Collapse
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { Link, useLocation } from 'react-router-dom';
import {
  Home as HomeIcon,
  Class as ClassIcon,
  Assignment as AssignmentIcon,
  SupervisorAccount as SupervisorAccountIcon,
  PersonOutline as PersonOutlineIcon,
  Announcement as AnnouncementIcon,
  Report as ReportIcon,
  AccountCircleOutlined as AccountCircleOutlinedIcon,
  ExitToApp as ExitToAppIcon,
  School as SchoolIcon,
  // New icons for additional modules
  Info as InfoIcon,
  Payment as PaymentIcon,
  Description as DescriptionIcon,
  DirectionsBus as DirectionsBusIcon,
  Inventory as InventoryIcon,
  Web as WebIcon,
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  EventAvailable as EventAvailableIcon,
  Quiz as QuizIcon,
  MenuBook as MenuBookIcon,
  NotificationsActive as NotificationsActiveIcon,
  Hotel as HotelIcon,
  Business as BusinessIcon,
  Dashboard as CMSIcon,
  ExpandLess,
  ExpandMore,
} from '@mui/icons-material';

const StyledDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%)',
    color: 'white',
    borderRight: 'none',
    boxShadow: '8px 0 32px rgba(0, 0, 0, 0.15)',
    backdropFilter: 'blur(10px)',
    position: 'fixed',
    top: theme.mixins.toolbar.minHeight || '64px', // Use theme toolbar height
    height: `calc(100vh - ${theme.mixins.toolbar.minHeight || '64px'}px)`, // Full height minus AppBar
    zIndex: theme.zIndex.drawer,
    [theme.breakpoints.up('sm')]: {
      top: '64px',
      height: 'calc(100vh - 64px)',
    },
    [theme.breakpoints.up('md')]: {
      top: '64px',
      height: 'calc(100vh - 64px)',
    },
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
      pointerEvents: 'none',
    }
  },
}));

const SidebarHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 2),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  borderBottom: '2px solid rgba(255, 255, 255, 0.15)',
  background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
  backdropFilter: 'blur(10px)',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: '50%',
    transform: 'translateX(-50%)',
    width: '60%',
    height: '2px',
    background: 'linear-gradient(90deg, transparent, #FFD700, transparent)',
  }
}));

const StyledListItemButton = styled(ListItemButton)(({ theme, active }) => ({
  margin: theme.spacing(0.5, 1.5),
  borderRadius: '16px',
  padding: theme.spacing(1.5, 2),
  backgroundColor: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
  backdropFilter: active ? 'blur(15px)' : 'none',
  border: active ? '1px solid rgba(255, 255, 255, 0.3)' : '1px solid transparent',
  boxShadow: active ? '0 8px 32px rgba(0, 0, 0, 0.1)' : 'none',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    transform: 'translateX(8px) scale(1.02)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
  },
  '&:before': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    width: active ? '5px' : '0px',
    height: '70%',
    background: 'linear-gradient(135deg, #FFD700, #FFA500)',
    borderRadius: '0 8px 8px 0',
    transition: 'width 0.3s ease',
    boxShadow: active ? '0 0 20px rgba(255, 215, 0, 0.5)' : 'none',
  },
  '&:after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: active ? 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))' : 'transparent',
    pointerEvents: 'none',
    borderRadius: '16px',
  },
}));

const MenuSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1, 1.5),
  marginTop: theme.spacing(1.5),
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '0.8rem',
  fontWeight: 700,
  textTransform: 'uppercase',
  letterSpacing: '1px',
  opacity: 0.9,
  marginBottom: theme.spacing(1.5),
  marginLeft: theme.spacing(1),
  background: 'linear-gradient(135deg, #FFD700, #FFA500)',
  backgroundClip: 'text',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  textShadow: '0 2px 4px rgba(0,0,0,0.1)',
}));

const ResponsiveSidebar = ({ open, onClose, variant = 'permanent' }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const location = useLocation();
  // Simplified menu without collapsible sections

  // Simplified main menu items
  const mainMenuItems = [
    {
      text: 'Dashboard',
      icon: <HomeIcon />,
      path: '/Admin/dashboard',
      badge: null
    },
    {
      text: 'Student Info',
      icon: <InfoIcon />,
      path: '/Admin/student-info',
      badge: null
    },
    {
      text: 'Students',
      icon: <PersonOutlineIcon />,
      path: '/Admin/students',
      badge: null
    },
    {
      text: 'Classes',
      icon: <ClassIcon />,
      path: '/Admin/classes',
      badge: null
    },
    {
      text: 'Teachers',
      icon: <SupervisorAccountIcon />,
      path: '/Admin/teachers',
      badge: null
    },
    {
      text: 'Subjects',
      icon: <AssignmentIcon />,
      path: '/Admin/subjects',
      badge: null
    },
    {
      text: 'Fee Management',
      icon: <PaymentIcon />,
      path: '/Admin/fee-management',
      badge: '12'
    },
    {
      text: 'Transport',
      icon: <DirectionsBusIcon />,
      path: '/Admin/transport',
      badge: null
    },
    {
      text: 'Inventory',
      icon: <InventoryIcon />,
      path: '/Admin/inventory',
      badge: '5'
    },
    {
      text: 'Documents',
      icon: <DescriptionIcon />,
      path: '/Admin/documents',
      badge: null
    },
    {
      text: 'CMS',
      icon: <WebIcon />,
      path: '/Admin/cms',
      badge: 'New'
    },
    {
      text: 'Notifications',
      icon: <NotificationsActiveIcon />,
      path: '/Admin/notifications',
      badge: '15'
    },
    {
      text: 'Notices',
      icon: <AnnouncementIcon />,
      path: '/Admin/notices',
      badge: '3'
    },
    {
      text: 'CMS',
      icon: <CMSIcon />,
      path: '/Admin/cms',
      badge: null
    },
  ];

  // User account menu items
  const userMenuItems = [
    {
      text: 'Profile',
      icon: <AccountCircleOutlinedIcon />,
      path: '/Admin/profile',
      badge: null
    },
    {
      text: 'Logout',
      icon: <ExitToAppIcon />,
      path: '/logout',
      badge: null
    },
  ];

  const isActive = (path) => {
    if (path === '/Admin/dashboard') {
      return location.pathname === '/' || location.pathname === '/Admin/dashboard';
    }
    return location.pathname.startsWith(path);
  };

  const renderMenuItem = (item, index) => (
    <motion.div
      key={item.text}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: index * 0.05 }}
    >
      <StyledListItemButton
        component={Link}
        to={item.path}
        active={isActive(item.path)}
        onClick={isMobile ? onClose : undefined}
      >
        <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>
          {item.icon}
        </ListItemIcon>
        <ListItemText
          primary={item.text}
          primaryTypographyProps={{
            fontSize: '0.9rem',
            fontWeight: isActive(item.path) ? 600 : 400,
          }}
        />
        {item.badge && (
          <Chip
            label={item.badge}
            size="small"
            sx={{
              bgcolor: '#FFD700',
              color: '#333',
              fontSize: '0.7rem',
              height: 20,
              minWidth: 20,
              fontWeight: 600,
            }}
          />
        )}
      </StyledListItemButton>
    </motion.div>
  );

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <SidebarHeader>
        <Avatar
          sx={{
            bgcolor: 'rgba(255, 255, 255, 0.3)',
            width: 56,
            height: 56,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          }}
        >
          <SchoolIcon sx={{ fontSize: 32 }} />
        </Avatar>
        <Box>
          <Typography variant="h6" fontWeight="bold" sx={{ fontSize: '1.1rem' }}>
            🎓 School Admin
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.9, fontSize: '0.8rem' }}>
            Management System
          </Typography>
        </Box>
      </SidebarHeader>

      <Box sx={{ flex: 1, overflowY: 'auto', py: 2 }}>
        {/* Main Menu */}
        <MenuSection>
          <SectionTitle>📋 Main Menu</SectionTitle>
          <List component="nav" sx={{ padding: 0 }}>
            {mainMenuItems.map((item, index) => renderMenuItem(item, index))}
          </List>
        </MenuSection>

        <Divider sx={{ mx: 3, my: 2, bgcolor: 'rgba(255, 255, 255, 0.2)' }} />

        {/* User Account */}
        <MenuSection>
          <SectionTitle>👤 Account</SectionTitle>
          <List component="nav" sx={{ padding: 0 }}>
            {userMenuItems.map((item, index) => renderMenuItem(item, index))}
          </List>
        </MenuSection>
      </Box>
    </Box>
  );

  if (isMobile) {
    return (
      <StyledDrawer
        variant="temporary"
        open={open}
        onClose={onClose}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
            top: { xs: '72px', sm: '80px', md: '88px' },
            height: { xs: 'calc(100vh - 72px)', sm: 'calc(100vh - 80px)', md: 'calc(100vh - 88px)' },
          },
        }}
      >
        {drawerContent}
      </StyledDrawer>
    );
  }

  return (
    <StyledDrawer
      variant={variant}
      open={open}
      sx={{
        '& .MuiDrawer-paper': {
          width: open ? 280 : 70,
          transition: theme.transitions.create('width', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
          overflowX: 'hidden',
          top: { xs: '72px', sm: '80px', md: '88px' },
          height: { xs: 'calc(100vh - 72px)', sm: 'calc(100vh - 80px)', md: 'calc(100vh - 88px)' },
        },
      }}
    >
      {drawerContent}
    </StyledDrawer>
  );
};

export default ResponsiveSidebar;
