[{"A:\\11\\MERN-School-Management-System\\frontend\\src\\index.js": "1", "A:\\11\\MERN-School-Management-System\\frontend\\src\\App.js": "2", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\store.js": "3", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Homepage.js": "4", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\LoginPage.js": "5", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\ChooseUser.js": "6", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminDashboard.js": "7", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminRegisterPage.js": "8", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherDashboard.js": "9", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentDashboard.js": "10", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userSlice.js": "11", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentSlice.js": "12", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeSlice.js": "13", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassSlice.js": "14", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherSlice.js": "15", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainSlice.js": "16", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Logout.js": "17", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userHandle.js": "18", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminProfile.js": "19", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\SideBar.js": "20", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminHomePage.js": "21", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\Popup.js": "22", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\styles.js": "23", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\AccountMenu.js": "24", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\buttonStyles.js": "25", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentAttendance.js": "26", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentExamMarks.js": "27", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\SeeComplains.js": "28", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\AddStudent.js": "29", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ShowStudents.js": "30", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ViewStudent.js": "31", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\AddNotice.js": "32", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\ShowNotices.js": "33", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ViewSubject.js": "34", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ShowSubjects.js": "35", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherSideBar.js": "36", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherClassDetails.js": "37", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\SubjectForm.js": "38", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseClass.js": "39", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ShowTeachers.js": "40", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseSubject.js": "41", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\AddTeacher.js": "42", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherComplain.js": "43", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherHomePage.js": "44", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\TeacherDetails.js": "45", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentHomePage.js": "46", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherProfile.js": "47", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherViewStudent.js": "48", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSideBar.js": "49", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\AddClass.js": "50", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ShowClasses.js": "51", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSubjects.js": "52", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ClassDetails.js": "53", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentProfile.js": "54", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentComplain.js": "55", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\ViewStdAttendance.js": "56", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SeeNotice.js": "57", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentHandle.js": "58", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassHandle.js": "59", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherHandle.js": "60", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableTemplate.js": "61", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SpeedDialTemplate.js": "62", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainHandle.js": "63", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\attendanceCalculator.js": "64", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomBarChart.js": "65", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomPieChart.js": "66", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeHandle.js": "67", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableViewTemplate.js": "68", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\EnhancedStatCard.js": "69", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\AttendanceChart.js": "70", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\PerformanceChart.js": "71", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\RecentActivity.js": "72", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\QuickActions.js": "73", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\CalendarWidget.js": "74", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\FinancialChart.js": "75", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\NotificationCenter.js": "76", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeToggle.js": "77", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeProvider.js": "78", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\layout\\ResponsiveSidebar.js": "79", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\feeManagement\\FeeManagement.js": "80", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\transport\\TransportManagement.js": "81", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\documents\\DocumentGeneration.js": "82", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentInfo\\StudentInfo.js": "83", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminSettings.js": "84", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\EnhancedViewStudent.js": "85", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\inventory\\InventoryManagement.js": "86", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\inventory\\AddInventoryItem.js": "87", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\cms\\PageBuilder.js": "88", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\cms\\CMSManagement.js": "89", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\cms\\MediaManager.js": "90"}, {"size": 388, "mtime": 1751091352643, "results": "91", "hashOfConfig": "92"}, {"size": 1577, "mtime": 1751100721027, "results": "93", "hashOfConfig": "92"}, {"size": 713, "mtime": 1751091352677, "results": "94", "hashOfConfig": "92"}, {"size": 2910, "mtime": 1751100774610, "results": "95", "hashOfConfig": "92"}, {"size": 11299, "mtime": 1751100761015, "results": "96", "hashOfConfig": "92"}, {"size": 2833, "mtime": 1751100705093, "results": "97", "hashOfConfig": "92"}, {"size": 11765, "mtime": 1751113601306, "results": "98", "hashOfConfig": "92"}, {"size": 9826, "mtime": 1751091352650, "results": "99", "hashOfConfig": "92"}, {"size": 4699, "mtime": 1751091352670, "results": "100", "hashOfConfig": "92"}, {"size": 4230, "mtime": 1751091352662, "results": "101", "hashOfConfig": "92"}, {"size": 2909, "mtime": 1751091352677, "results": "102", "hashOfConfig": "92"}, {"size": 1483, "mtime": 1751091352677, "results": "103", "hashOfConfig": "92"}, {"size": 1014, "mtime": 1751091352677, "results": "104", "hashOfConfig": "92"}, {"size": 2643, "mtime": 1751091352677, "results": "105", "hashOfConfig": "92"}, {"size": 1440, "mtime": 1751091352677, "results": "106", "hashOfConfig": "92"}, {"size": 1028, "mtime": 1751091352672, "results": "107", "hashOfConfig": "92"}, {"size": 1800, "mtime": 1751091352643, "results": "108", "hashOfConfig": "92"}, {"size": 3835, "mtime": 1751111833332, "results": "109", "hashOfConfig": "92"}, {"size": 4480, "mtime": 1751091352648, "results": "110", "hashOfConfig": "92"}, {"size": 4467, "mtime": 1751091352650, "results": "111", "hashOfConfig": "112"}, {"size": 7554, "mtime": 1751104361797, "results": "113", "hashOfConfig": "92"}, {"size": 1562, "mtime": 1751091352637, "results": "114", "hashOfConfig": "92"}, {"size": 2382, "mtime": 1751091352643, "results": "115", "hashOfConfig": "92"}, {"size": 3501, "mtime": 1751111367409, "results": "116", "hashOfConfig": "92"}, {"size": 1933, "mtime": 1751091352639, "results": "117", "hashOfConfig": "92"}, {"size": 8909, "mtime": 1751091352656, "results": "118", "hashOfConfig": "92"}, {"size": 7905, "mtime": 1751091352656, "results": "119", "hashOfConfig": "92"}, {"size": 2165, "mtime": 1751091352656, "results": "120", "hashOfConfig": "92"}, {"size": 23300, "mtime": 1751111313379, "results": "121", "hashOfConfig": "92"}, {"size": 8747, "mtime": 1751091352656, "results": "122", "hashOfConfig": "92"}, {"size": 22050, "mtime": 1751091352656, "results": "123", "hashOfConfig": "92"}, {"size": 2792, "mtime": 1751091352653, "results": "124", "hashOfConfig": "92"}, {"size": 3697, "mtime": 1751091352653, "results": "125", "hashOfConfig": "92"}, {"size": 7183, "mtime": 1751091352659, "results": "126", "hashOfConfig": "92"}, {"size": 4297, "mtime": 1751091352659, "results": "127", "hashOfConfig": "92"}, {"size": 2939, "mtime": 1751091352672, "results": "128", "hashOfConfig": "92"}, {"size": 7137, "mtime": 1751091352669, "results": "129", "hashOfConfig": "92"}, {"size": 6961, "mtime": 1751091352659, "results": "130", "hashOfConfig": "92"}, {"size": 2823, "mtime": 1751091352662, "results": "131", "hashOfConfig": "92"}, {"size": 7878, "mtime": 1751091352662, "results": "132", "hashOfConfig": "92"}, {"size": 5134, "mtime": 1751091352662, "results": "133", "hashOfConfig": "92"}, {"size": 3772, "mtime": 1751091352662, "results": "134", "hashOfConfig": "92"}, {"size": 146, "mtime": 1751091352669, "results": "135", "hashOfConfig": "92"}, {"size": 3747, "mtime": 1751091352670, "results": "136", "hashOfConfig": "92"}, {"size": 2405, "mtime": 1751091352662, "results": "137", "hashOfConfig": "92"}, {"size": 5332, "mtime": 1751091352662, "results": "138", "hashOfConfig": "92"}, {"size": 1317, "mtime": 1751091352670, "results": "139", "hashOfConfig": "92"}, {"size": 12169, "mtime": 1751091352672, "results": "140", "hashOfConfig": "92"}, {"size": 3195, "mtime": 1751091352662, "results": "141", "hashOfConfig": "92"}, {"size": 4208, "mtime": 1751091352651, "results": "142", "hashOfConfig": "92"}, {"size": 6277, "mtime": 1751091352652, "results": "143", "hashOfConfig": "92"}, {"size": 6014, "mtime": 1751091352662, "results": "144", "hashOfConfig": "92"}, {"size": 10926, "mtime": 1751091352652, "results": "145", "hashOfConfig": "92"}, {"size": 3931, "mtime": 1751091352662, "results": "146", "hashOfConfig": "92"}, {"size": 4228, "mtime": 1751091352662, "results": "147", "hashOfConfig": "92"}, {"size": 9767, "mtime": 1751091352662, "results": "148", "hashOfConfig": "92"}, {"size": 2233, "mtime": 1751091352639, "results": "149", "hashOfConfig": "92"}, {"size": 1558, "mtime": 1751093107198, "results": "150", "hashOfConfig": "92"}, {"size": 2895, "mtime": 1751093149585, "results": "151", "hashOfConfig": "92"}, {"size": 1383, "mtime": 1751093059721, "results": "152", "hashOfConfig": "92"}, {"size": 3386, "mtime": 1751091352639, "results": "153", "hashOfConfig": "92"}, {"size": 887, "mtime": 1751091352639, "results": "154", "hashOfConfig": "92"}, {"size": 621, "mtime": 1751093204280, "results": "155", "hashOfConfig": "92"}, {"size": 1996, "mtime": 1751091352639, "results": "156", "hashOfConfig": "92"}, {"size": 6469, "mtime": 1751091352629, "results": "157", "hashOfConfig": "92"}, {"size": 3161, "mtime": 1751091352629, "results": "158", "hashOfConfig": "92"}, {"size": 617, "mtime": 1751093177974, "results": "159", "hashOfConfig": "92"}, {"size": 3018, "mtime": 1751091352639, "results": "160", "hashOfConfig": "92"}, {"size": 3781, "mtime": 1751104338304, "results": "161", "hashOfConfig": "92"}, {"size": 2540, "mtime": 1751101038420, "results": "162", "hashOfConfig": "92"}, {"size": 2516, "mtime": 1751101053307, "results": "163", "hashOfConfig": "92"}, {"size": 4394, "mtime": 1751101121649, "results": "164", "hashOfConfig": "92"}, {"size": 3664, "mtime": 1751104279550, "results": "165", "hashOfConfig": "92"}, {"size": 5436, "mtime": 1751101223218, "results": "166", "hashOfConfig": "92"}, {"size": 3811, "mtime": 1751101073787, "results": "167", "hashOfConfig": "92"}, {"size": 6826, "mtime": 1751101255344, "results": "168", "hashOfConfig": "92"}, {"size": 908, "mtime": 1751101186045, "results": "169", "hashOfConfig": "92"}, {"size": 3711, "mtime": 1751101175499, "results": "170", "hashOfConfig": "92"}, {"size": 10952, "mtime": 1751114011437, "results": "171", "hashOfConfig": "92"}, {"size": 14046, "mtime": 1751102262286, "results": "172", "hashOfConfig": "92"}, {"size": 12806, "mtime": 1751102695142, "results": "173", "hashOfConfig": "92"}, {"size": 11821, "mtime": 1751102469493, "results": "174", "hashOfConfig": "92"}, {"size": 19126, "mtime": 1751111818968, "results": "175", "hashOfConfig": "92"}, {"size": 25082, "mtime": 1751111169508, "results": "176", "hashOfConfig": "92"}, {"size": 17065, "mtime": 1751112046968, "results": "177", "hashOfConfig": "92"}, {"size": 28930, "mtime": 1751113017450, "results": "178", "hashOfConfig": "92"}, {"size": 16940, "mtime": 1751112579256, "results": "179", "hashOfConfig": "92"}, {"size": 18318, "mtime": 1751113362593, "results": "180", "hashOfConfig": "92"}, {"size": 23884, "mtime": 1751113651089, "results": "181", "hashOfConfig": "92"}, {"size": 15610, "mtime": 1751113489441, "results": "182", "hashOfConfig": "92"}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kq195y", {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "243"}, "1pf3lve", {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "421"}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "A:\\11\\MERN-School-Management-System\\frontend\\src\\index.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\App.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\store.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Homepage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\LoginPage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\ChooseUser.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminDashboard.js", ["455"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminRegisterPage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Logout.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\SideBar.js", [], [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminHomePage.js", ["456", "457", "458", "459", "460"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\Popup.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\styles.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\AccountMenu.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\buttonStyles.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentAttendance.js", ["461", "462"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentExamMarks.js", ["463", "464"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\SeeComplains.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\AddStudent.js", ["465", "466", "467", "468"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ShowStudents.js", ["469"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ViewStudent.js", ["470", "471", "472", "473", "474"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\AddNotice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\ShowNotices.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ViewSubject.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ShowSubjects.js", ["475"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherSideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherClassDetails.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\SubjectForm.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseClass.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ShowTeachers.js", ["476"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseSubject.js", ["477"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\AddTeacher.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherComplain.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\TeacherDetails.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherViewStudent.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\AddClass.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ShowClasses.js", ["478"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSubjects.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ClassDetails.js", ["479", "480"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentComplain.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\ViewStdAttendance.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SeeNotice.js", ["481"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SpeedDialTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\attendanceCalculator.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomBarChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomPieChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableViewTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\EnhancedStatCard.js", ["482"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\AttendanceChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\PerformanceChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\RecentActivity.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\QuickActions.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\CalendarWidget.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\FinancialChart.js", ["483"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\NotificationCenter.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeToggle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeProvider.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\layout\\ResponsiveSidebar.js", ["484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494"], [], "import React from 'react';\nimport {\n  Drawer,\n  List,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Box,\n  Typography,\n  useTheme,\n  useMediaQuery,\n  Avatar,\n  Chip,\n  Collapse\n} from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport {\n  Home as HomeIcon,\n  Class as ClassIcon,\n  Assignment as AssignmentIcon,\n  SupervisorAccount as SupervisorAccountIcon,\n  PersonOutline as PersonOutlineIcon,\n  Announcement as AnnouncementIcon,\n  Report as ReportIcon,\n  AccountCircleOutlined as AccountCircleOutlinedIcon,\n  ExitToApp as ExitToAppIcon,\n  School as SchoolIcon,\n  // New icons for additional modules\n  Info as InfoIcon,\n  Payment as PaymentIcon,\n  Description as DescriptionIcon,\n  DirectionsBus as DirectionsBusIcon,\n  Inventory as InventoryIcon,\n  Web as WebIcon,\n  People as PeopleIcon,\n  PersonAdd as PersonAddIcon,\n  EventAvailable as EventAvailableIcon,\n  Quiz as QuizIcon,\n  MenuBook as MenuBookIcon,\n  NotificationsActive as NotificationsActiveIcon,\n  Hotel as HotelIcon,\n  Business as BusinessIcon,\n  Dashboard as CMSIcon,\n  ExpandLess,\n  ExpandMore,\n} from '@mui/icons-material';\n\nconst StyledDrawer = styled(Drawer)(({ theme }) => ({\n  '& .MuiDrawer-paper': {\n    background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%)',\n    color: 'white',\n    borderRight: 'none',\n    boxShadow: '8px 0 32px rgba(0, 0, 0, 0.15)',\n    backdropFilter: 'blur(10px)',\n    position: 'fixed',\n    top: theme.mixins.toolbar.minHeight || '64px', // Use theme toolbar height\n    height: `calc(100vh - ${theme.mixins.toolbar.minHeight || '64px'}px)`, // Full height minus AppBar\n    zIndex: theme.zIndex.drawer,\n    [theme.breakpoints.up('sm')]: {\n      top: '64px',\n      height: 'calc(100vh - 64px)',\n    },\n    [theme.breakpoints.up('md')]: {\n      top: '64px',\n      height: 'calc(100vh - 64px)',\n    },\n    '&::before': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n      pointerEvents: 'none',\n    }\n  },\n}));\n\nconst SidebarHeader = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(2, 2),\n  display: 'flex',\n  alignItems: 'center',\n  gap: theme.spacing(2),\n  borderBottom: '2px solid rgba(255, 255, 255, 0.15)',\n  background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',\n  backdropFilter: 'blur(10px)',\n  position: 'relative',\n  '&::after': {\n    content: '\"\"',\n    position: 'absolute',\n    bottom: 0,\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '60%',\n    height: '2px',\n    background: 'linear-gradient(90deg, transparent, #FFD700, transparent)',\n  }\n}));\n\nconst StyledListItemButton = styled(ListItemButton)(({ theme, active }) => ({\n  margin: theme.spacing(0.5, 1.5),\n  borderRadius: '16px',\n  padding: theme.spacing(1.5, 2),\n  backgroundColor: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',\n  backdropFilter: active ? 'blur(15px)' : 'none',\n  border: active ? '1px solid rgba(255, 255, 255, 0.3)' : '1px solid transparent',\n  boxShadow: active ? '0 8px 32px rgba(0, 0, 0, 0.1)' : 'none',\n  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n  position: 'relative',\n  overflow: 'hidden',\n  '&:hover': {\n    backgroundColor: 'rgba(255, 255, 255, 0.15)',\n    transform: 'translateX(8px) scale(1.02)',\n    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',\n  },\n  '&:before': {\n    content: '\"\"',\n    position: 'absolute',\n    left: 0,\n    top: '50%',\n    transform: 'translateY(-50%)',\n    width: active ? '5px' : '0px',\n    height: '70%',\n    background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n    borderRadius: '0 8px 8px 0',\n    transition: 'width 0.3s ease',\n    boxShadow: active ? '0 0 20px rgba(255, 215, 0, 0.5)' : 'none',\n  },\n  '&:after': {\n    content: '\"\"',\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: active ? 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))' : 'transparent',\n    pointerEvents: 'none',\n    borderRadius: '16px',\n  },\n}));\n\nconst MenuSection = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(1, 1.5),\n  marginTop: theme.spacing(1.5),\n}));\n\nconst SectionTitle = styled(Typography)(({ theme }) => ({\n  fontSize: '0.8rem',\n  fontWeight: 700,\n  textTransform: 'uppercase',\n  letterSpacing: '1px',\n  opacity: 0.9,\n  marginBottom: theme.spacing(1.5),\n  marginLeft: theme.spacing(1),\n  background: 'linear-gradient(135deg, #FFD700, #FFA500)',\n  backgroundClip: 'text',\n  WebkitBackgroundClip: 'text',\n  WebkitTextFillColor: 'transparent',\n  textShadow: '0 2px 4px rgba(0,0,0,0.1)',\n}));\n\nconst ResponsiveSidebar = ({ open, onClose, variant = 'permanent' }) => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const location = useLocation();\n  // Simplified menu without collapsible sections\n\n  // Simplified main menu items\n  const mainMenuItems = [\n    {\n      text: 'Dashboard',\n      icon: <HomeIcon />,\n      path: '/Admin/dashboard',\n      badge: null\n    },\n    {\n      text: 'Student Info',\n      icon: <InfoIcon />,\n      path: '/Admin/student-info',\n      badge: null\n    },\n    {\n      text: 'Students',\n      icon: <PersonOutlineIcon />,\n      path: '/Admin/students',\n      badge: null\n    },\n    {\n      text: 'Classes',\n      icon: <ClassIcon />,\n      path: '/Admin/classes',\n      badge: null\n    },\n    {\n      text: 'Teachers',\n      icon: <SupervisorAccountIcon />,\n      path: '/Admin/teachers',\n      badge: null\n    },\n    {\n      text: 'Subjects',\n      icon: <AssignmentIcon />,\n      path: '/Admin/subjects',\n      badge: null\n    },\n    {\n      text: 'Fee Management',\n      icon: <PaymentIcon />,\n      path: '/Admin/fee-management',\n      badge: '12'\n    },\n    {\n      text: 'Transport',\n      icon: <DirectionsBusIcon />,\n      path: '/Admin/transport',\n      badge: null\n    },\n    {\n      text: 'Inventory',\n      icon: <InventoryIcon />,\n      path: '/Admin/inventory',\n      badge: '5'\n    },\n    {\n      text: 'Documents',\n      icon: <DescriptionIcon />,\n      path: '/Admin/documents',\n      badge: null\n    },\n    {\n      text: 'CMS',\n      icon: <WebIcon />,\n      path: '/Admin/cms',\n      badge: 'New'\n    },\n    {\n      text: 'Notifications',\n      icon: <NotificationsActiveIcon />,\n      path: '/Admin/notifications',\n      badge: '15'\n    },\n    {\n      text: 'Notices',\n      icon: <AnnouncementIcon />,\n      path: '/Admin/notices',\n      badge: '3'\n    },\n    {\n      text: 'CMS',\n      icon: <CMSIcon />,\n      path: '/Admin/cms',\n      badge: null\n    },\n  ];\n\n  // User account menu items\n  const userMenuItems = [\n    {\n      text: 'Profile',\n      icon: <AccountCircleOutlinedIcon />,\n      path: '/Admin/profile',\n      badge: null\n    },\n    {\n      text: 'Logout',\n      icon: <ExitToAppIcon />,\n      path: '/logout',\n      badge: null\n    },\n  ];\n\n  const isActive = (path) => {\n    if (path === '/Admin/dashboard') {\n      return location.pathname === '/' || location.pathname === '/Admin/dashboard';\n    }\n    return location.pathname.startsWith(path);\n  };\n\n  const renderMenuItem = (item, index) => (\n    <motion.div\n      key={item.text}\n      initial={{ opacity: 0, x: -20 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ delay: index * 0.05 }}\n    >\n      <StyledListItemButton\n        component={Link}\n        to={item.path}\n        active={isActive(item.path)}\n        onClick={isMobile ? onClose : undefined}\n      >\n        <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>\n          {item.icon}\n        </ListItemIcon>\n        <ListItemText\n          primary={item.text}\n          primaryTypographyProps={{\n            fontSize: '0.9rem',\n            fontWeight: isActive(item.path) ? 600 : 400,\n          }}\n        />\n        {item.badge && (\n          <Chip\n            label={item.badge}\n            size=\"small\"\n            sx={{\n              bgcolor: '#FFD700',\n              color: '#333',\n              fontSize: '0.7rem',\n              height: 20,\n              minWidth: 20,\n              fontWeight: 600,\n            }}\n          />\n        )}\n      </StyledListItemButton>\n    </motion.div>\n  );\n\n  const drawerContent = (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <SidebarHeader>\n        <Avatar\n          sx={{\n            bgcolor: 'rgba(255, 255, 255, 0.3)',\n            width: 56,\n            height: 56,\n            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n          }}\n        >\n          <SchoolIcon sx={{ fontSize: 32 }} />\n        </Avatar>\n        <Box>\n          <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ fontSize: '1.1rem' }}>\n            🎓 School Admin\n          </Typography>\n          <Typography variant=\"caption\" sx={{ opacity: 0.9, fontSize: '0.8rem' }}>\n            Management System\n          </Typography>\n        </Box>\n      </SidebarHeader>\n\n      <Box sx={{ flex: 1, overflowY: 'auto', py: 2 }}>\n        {/* Main Menu */}\n        <MenuSection>\n          <SectionTitle>📋 Main Menu</SectionTitle>\n          <List component=\"nav\" sx={{ padding: 0 }}>\n            {mainMenuItems.map((item, index) => renderMenuItem(item, index))}\n          </List>\n        </MenuSection>\n\n        <Divider sx={{ mx: 3, my: 2, bgcolor: 'rgba(255, 255, 255, 0.2)' }} />\n\n        {/* User Account */}\n        <MenuSection>\n          <SectionTitle>👤 Account</SectionTitle>\n          <List component=\"nav\" sx={{ padding: 0 }}>\n            {userMenuItems.map((item, index) => renderMenuItem(item, index))}\n          </List>\n        </MenuSection>\n      </Box>\n    </Box>\n  );\n\n  if (isMobile) {\n    return (\n      <StyledDrawer\n        variant=\"temporary\"\n        open={open}\n        onClose={onClose}\n        ModalProps={{\n          keepMounted: true, // Better open performance on mobile.\n        }}\n        sx={{\n          '& .MuiDrawer-paper': {\n            width: 280,\n            top: { xs: '72px', sm: '80px', md: '88px' },\n            height: { xs: 'calc(100vh - 72px)', sm: 'calc(100vh - 80px)', md: 'calc(100vh - 88px)' },\n          },\n        }}\n      >\n        {drawerContent}\n      </StyledDrawer>\n    );\n  }\n\n  return (\n    <StyledDrawer\n      variant={variant}\n      open={open}\n      sx={{\n        '& .MuiDrawer-paper': {\n          width: open ? 280 : 70,\n          transition: theme.transitions.create('width', {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.enteringScreen,\n          }),\n          overflowX: 'hidden',\n          top: { xs: '72px', sm: '80px', md: '88px' },\n          height: { xs: 'calc(100vh - 72px)', sm: 'calc(100vh - 80px)', md: 'calc(100vh - 88px)' },\n        },\n      }}\n    >\n      {drawerContent}\n    </StyledDrawer>\n  );\n};\n\nexport default ResponsiveSidebar;\n", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\feeManagement\\FeeManagement.js", ["495"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\transport\\TransportManagement.js", ["496", "497", "498"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\documents\\DocumentGeneration.js", ["499"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentInfo\\StudentInfo.js", ["500", "501", "502", "503"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminSettings.js", ["504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\EnhancedViewStudent.js", ["516", "517", "518", "519", "520", "521"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\inventory\\InventoryManagement.js", ["522", "523", "524", "525", "526"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\inventory\\AddInventoryItem.js", ["527"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\cms\\PageBuilder.js", ["528", "529", "530", "531"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\cms\\CMSManagement.js", ["532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\cms\\MediaManager.js", ["552"], [], {"ruleId": "553", "severity": 1, "message": "554", "line": 44, "column": 8, "nodeType": "555", "messageId": "556", "endLine": 44, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "557", "line": 31, "column": 17, "nodeType": "555", "messageId": "556", "endLine": 31, "endColumn": 31}, {"ruleId": "553", "severity": 1, "message": "558", "line": 32, "column": 13, "nodeType": "555", "messageId": "556", "endLine": 32, "endColumn": 23}, {"ruleId": "553", "severity": 1, "message": "559", "line": 39, "column": 11, "nodeType": "555", "messageId": "556", "endLine": 39, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "560", "line": 60, "column": 11, "nodeType": "555", "messageId": "556", "endLine": 60, "endColumn": 25}, {"ruleId": "553", "severity": 1, "message": "561", "line": 61, "column": 11, "nodeType": "555", "messageId": "556", "endLine": 61, "endColumn": 23}, {"ruleId": "562", "severity": 1, "message": "563", "line": 46, "column": 8, "nodeType": "564", "endLine": 46, "endColumn": 19, "suggestions": "565"}, {"ruleId": "562", "severity": 1, "message": "566", "line": 52, "column": 8, "nodeType": "564", "endLine": 52, "endColumn": 31, "suggestions": "567"}, {"ruleId": "562", "severity": 1, "message": "563", "line": 45, "column": 8, "nodeType": "564", "endLine": 45, "endColumn": 19, "suggestions": "568"}, {"ruleId": "562", "severity": 1, "message": "566", "line": 51, "column": 8, "nodeType": "564", "endLine": 51, "endColumn": 31, "suggestions": "569"}, {"ruleId": "553", "severity": 1, "message": "570", "line": 23, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 23, "endColumn": 10}, {"ruleId": "553", "severity": 1, "message": "571", "line": 26, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 26, "endColumn": 8}, {"ruleId": "553", "severity": 1, "message": "572", "line": 33, "column": 18, "nodeType": "555", "messageId": "556", "endLine": 33, "endColumn": 33}, {"ruleId": "553", "severity": 1, "message": "573", "line": 98, "column": 12, "nodeType": "555", "messageId": "556", "endLine": 98, "endColumn": 26}, {"ruleId": "553", "severity": 1, "message": "574", "line": 5, "column": 10, "nodeType": "555", "messageId": "556", "endLine": 5, "endColumn": 20}, {"ruleId": "553", "severity": 1, "message": "574", "line": 3, "column": 10, "nodeType": "555", "messageId": "556", "endLine": 3, "endColumn": 20}, {"ruleId": "553", "severity": 1, "message": "575", "line": 24, "column": 12, "nodeType": "555", "messageId": "556", "endLine": 24, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "576", "line": 24, "column": 21, "nodeType": "555", "messageId": "556", "endLine": 24, "endColumn": 31}, {"ruleId": "553", "severity": 1, "message": "577", "line": 49, "column": 22, "nodeType": "555", "messageId": "556", "endLine": 49, "endColumn": 33}, {"ruleId": "553", "severity": 1, "message": "578", "line": 93, "column": 11, "nodeType": "555", "messageId": "556", "endLine": 93, "endColumn": 24}, {"ruleId": "553", "severity": 1, "message": "574", "line": 5, "column": 10, "nodeType": "555", "messageId": "556", "endLine": 5, "endColumn": 20}, {"ruleId": "553", "severity": 1, "message": "574", "line": 9, "column": 10, "nodeType": "555", "messageId": "556", "endLine": 9, "endColumn": 20}, {"ruleId": "562", "severity": 1, "message": "563", "line": 33, "column": 8, "nodeType": "564", "endLine": 33, "endColumn": 19, "suggestions": "579"}, {"ruleId": "553", "severity": 1, "message": "574", "line": 6, "column": 10, "nodeType": "555", "messageId": "556", "endLine": 6, "endColumn": 20}, {"ruleId": "553", "severity": 1, "message": "574", "line": 5, "column": 10, "nodeType": "555", "messageId": "556", "endLine": 5, "endColumn": 20}, {"ruleId": "553", "severity": 1, "message": "580", "line": 12, "column": 10, "nodeType": "555", "messageId": "556", "endLine": 12, "endColumn": 23}, {"ruleId": "562", "severity": 1, "message": "581", "line": 20, "column": 8, "nodeType": "564", "endLine": 20, "endColumn": 18, "suggestions": "582"}, {"ruleId": "553", "severity": 1, "message": "583", "line": 2, "column": 34, "nodeType": "555", "messageId": "556", "endLine": 2, "endColumn": 44}, {"ruleId": "553", "severity": 1, "message": "584", "line": 2, "column": 61, "nodeType": "555", "messageId": "556", "endLine": 2, "endColumn": 67}, {"ruleId": "553", "severity": 1, "message": "585", "line": 15, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 15, "endColumn": 11}, {"ruleId": "553", "severity": 1, "message": "586", "line": 27, "column": 13, "nodeType": "555", "messageId": "556", "endLine": 27, "endColumn": 23}, {"ruleId": "553", "severity": 1, "message": "558", "line": 38, "column": 13, "nodeType": "555", "messageId": "556", "endLine": 38, "endColumn": 23}, {"ruleId": "553", "severity": 1, "message": "587", "line": 39, "column": 16, "nodeType": "555", "messageId": "556", "endLine": 39, "endColumn": 29}, {"ruleId": "553", "severity": 1, "message": "588", "line": 40, "column": 21, "nodeType": "555", "messageId": "556", "endLine": 40, "endColumn": 39}, {"ruleId": "553", "severity": 1, "message": "589", "line": 41, "column": 11, "nodeType": "555", "messageId": "556", "endLine": 41, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "590", "line": 42, "column": 15, "nodeType": "555", "messageId": "556", "endLine": 42, "endColumn": 27}, {"ruleId": "553", "severity": 1, "message": "591", "line": 44, "column": 12, "nodeType": "555", "messageId": "556", "endLine": 44, "endColumn": 21}, {"ruleId": "553", "severity": 1, "message": "592", "line": 45, "column": 15, "nodeType": "555", "messageId": "556", "endLine": 45, "endColumn": 27}, {"ruleId": "553", "severity": 1, "message": "593", "line": 47, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 47, "endColumn": 13}, {"ruleId": "553", "severity": 1, "message": "594", "line": 48, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 48, "endColumn": 13}, {"ruleId": "553", "severity": 1, "message": "595", "line": 35, "column": 18, "nodeType": "555", "messageId": "556", "endLine": 35, "endColumn": 33}, {"ruleId": "553", "severity": 1, "message": "596", "line": 32, "column": 15, "nodeType": "555", "messageId": "556", "endLine": 32, "endColumn": 27}, {"ruleId": "553", "severity": 1, "message": "597", "line": 34, "column": 14, "nodeType": "555", "messageId": "556", "endLine": 34, "endColumn": 25}, {"ruleId": "553", "severity": 1, "message": "595", "line": 35, "column": 18, "nodeType": "555", "messageId": "556", "endLine": 35, "endColumn": 33}, {"ruleId": "553", "severity": 1, "message": "598", "line": 11, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 11, "endColumn": 12}, {"ruleId": "553", "severity": 1, "message": "599", "line": 43, "column": 11, "nodeType": "555", "messageId": "556", "endLine": 43, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "600", "line": 52, "column": 22, "nodeType": "555", "messageId": "556", "endLine": 52, "endColumn": 36}, {"ruleId": "553", "severity": 1, "message": "601", "line": 94, "column": 34, "nodeType": "555", "messageId": "556", "endLine": 94, "endColumn": 39}, {"ruleId": "553", "severity": 1, "message": "602", "line": 94, "column": 41, "nodeType": "555", "messageId": "556", "endLine": 94, "endColumn": 49}, {"ruleId": "553", "severity": 1, "message": "603", "line": 1, "column": 27, "nodeType": "555", "messageId": "556", "endLine": 1, "endColumn": 36}, {"ruleId": "553", "severity": 1, "message": "570", "line": 14, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 14, "endColumn": 10}, {"ruleId": "553", "severity": 1, "message": "583", "line": 15, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 15, "endColumn": 13}, {"ruleId": "553", "severity": 1, "message": "604", "line": 26, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 26, "endColumn": 9}, {"ruleId": "553", "severity": 1, "message": "605", "line": 27, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 27, "endColumn": 14}, {"ruleId": "553", "severity": 1, "message": "606", "line": 28, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 28, "endColumn": 16}, {"ruleId": "553", "severity": 1, "message": "607", "line": 29, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 29, "endColumn": 16}, {"ruleId": "553", "severity": 1, "message": "608", "line": 39, "column": 11, "nodeType": "555", "messageId": "556", "endLine": 39, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "609", "line": 43, "column": 15, "nodeType": "555", "messageId": "556", "endLine": 43, "endColumn": 27}, {"ruleId": "553", "severity": 1, "message": "610", "line": 114, "column": 9, "nodeType": "555", "messageId": "556", "endLine": 114, "endColumn": 17}, {"ruleId": "553", "severity": 1, "message": "611", "line": 121, "column": 10, "nodeType": "555", "messageId": "556", "endLine": 121, "endColumn": 20}, {"ruleId": "553", "severity": 1, "message": "612", "line": 121, "column": 22, "nodeType": "555", "messageId": "556", "endLine": 121, "endColumn": 35}, {"ruleId": "553", "severity": 1, "message": "570", "line": 19, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 19, "endColumn": 10}, {"ruleId": "553", "severity": 1, "message": "613", "line": 32, "column": 12, "nodeType": "555", "messageId": "556", "endLine": 32, "endColumn": 21}, {"ruleId": "553", "severity": 1, "message": "614", "line": 33, "column": 12, "nodeType": "555", "messageId": "556", "endLine": 33, "endColumn": 21}, {"ruleId": "553", "severity": 1, "message": "615", "line": 35, "column": 11, "nodeType": "555", "messageId": "556", "endLine": 35, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "616", "line": 36, "column": 16, "nodeType": "555", "messageId": "556", "endLine": 36, "endColumn": 29}, {"ruleId": "553", "severity": 1, "message": "602", "line": 83, "column": 24, "nodeType": "555", "messageId": "556", "endLine": 83, "endColumn": 32}, {"ruleId": "553", "severity": 1, "message": "617", "line": 26, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 26, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "571", "line": 37, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 37, "endColumn": 8}, {"ruleId": "553", "severity": 1, "message": "595", "line": 50, "column": 18, "nodeType": "555", "messageId": "556", "endLine": 50, "endColumn": 33}, {"ruleId": "553", "severity": 1, "message": "618", "line": 59, "column": 15, "nodeType": "555", "messageId": "556", "endLine": 59, "endColumn": 27}, {"ruleId": "553", "severity": 1, "message": "619", "line": 62, "column": 19, "nodeType": "555", "messageId": "556", "endLine": 62, "endColumn": 35}, {"ruleId": "553", "severity": 1, "message": "570", "line": 19, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 19, "endColumn": 10}, {"ruleId": "553", "severity": 1, "message": "620", "line": 29, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 29, "endColumn": 9}, {"ruleId": "553", "severity": 1, "message": "621", "line": 30, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 30, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "622", "line": 78, "column": 10, "nodeType": "555", "messageId": "556", "endLine": 78, "endColumn": 22}, {"ruleId": "553", "severity": 1, "message": "623", "line": 79, "column": 10, "nodeType": "555", "messageId": "556", "endLine": 79, "endColumn": 20}, {"ruleId": "553", "severity": 1, "message": "603", "line": 1, "column": 27, "nodeType": "555", "messageId": "556", "endLine": 1, "endColumn": 36}, {"ruleId": "553", "severity": 1, "message": "583", "line": 16, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 16, "endColumn": 13}, {"ruleId": "553", "severity": 1, "message": "624", "line": 18, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 18, "endColumn": 9}, {"ruleId": "553", "severity": 1, "message": "570", "line": 19, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 19, "endColumn": 10}, {"ruleId": "553", "severity": 1, "message": "625", "line": 29, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 29, "endColumn": 12}, {"ruleId": "553", "severity": 1, "message": "626", "line": 30, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 30, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "627", "line": 31, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 31, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "628", "line": 32, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 32, "endColumn": 7}, {"ruleId": "553", "severity": 1, "message": "629", "line": 33, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 33, "endColumn": 11}, {"ruleId": "553", "severity": 1, "message": "630", "line": 34, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 34, "endColumn": 15}, {"ruleId": "553", "severity": 1, "message": "631", "line": 35, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 35, "endColumn": 15}, {"ruleId": "553", "severity": 1, "message": "632", "line": 36, "column": 3, "nodeType": "555", "messageId": "556", "endLine": 36, "endColumn": 26}, {"ruleId": "553", "severity": 1, "message": "633", "line": 42, "column": 10, "nodeType": "555", "messageId": "556", "endLine": 42, "endColumn": 17}, {"ruleId": "553", "severity": 1, "message": "608", "line": 43, "column": 11, "nodeType": "555", "messageId": "556", "endLine": 43, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "634", "line": 49, "column": 17, "nodeType": "555", "messageId": "556", "endLine": 49, "endColumn": 31}, {"ruleId": "553", "severity": 1, "message": "635", "line": 50, "column": 20, "nodeType": "555", "messageId": "556", "endLine": 50, "endColumn": 37}, {"ruleId": "553", "severity": 1, "message": "636", "line": 51, "column": 10, "nodeType": "555", "messageId": "556", "endLine": 51, "endColumn": 17}, {"ruleId": "553", "severity": 1, "message": "637", "line": 52, "column": 13, "nodeType": "555", "messageId": "556", "endLine": 52, "endColumn": 23}, {"ruleId": "553", "severity": 1, "message": "638", "line": 53, "column": 17, "nodeType": "555", "messageId": "556", "endLine": 53, "endColumn": 31}, {"ruleId": "553", "severity": 1, "message": "639", "line": 58, "column": 20, "nodeType": "555", "messageId": "556", "endLine": 58, "endColumn": 37}, {"ruleId": "553", "severity": 1, "message": "640", "line": 218, "column": 11, "nodeType": "555", "messageId": "556", "endLine": 218, "endColumn": 16}, "no-unused-vars", "'ViewStudent' is defined but never used.", "Identifier", "unusedVar", "'TrendingUpIcon' is defined but never used.", "'PeopleIcon' is defined but never used.", "'isMobile' is assigned a value but never used.", "'attendanceRate' is assigned a value but never used.", "'averageGrade' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch' and 'params'. Either include them or remove the dependency array.", "ArrayExpression", ["641"], "React Hook useEffect has a missing dependency: 'situation'. Either include it or remove the dependency array.", ["642"], ["643"], ["644"], "'Divider' is defined but never used.", "'Badge' is defined but never used.", "'PhotoCameraIcon' is defined but never used.", "'profilePicture' is assigned a value but never used.", "'deleteUser' is defined but never used.", "'showTab' is assigned a value but never used.", "'setShowTab' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'submitHandler' is assigned a value but never used.", ["645"], "'resetSubjects' is defined but never used.", "React Hook useEffect has missing dependencies: 'currentRole', 'currentUser._id', and 'currentUser.school._id'. Either include them or remove the dependency array.", ["646"], "'IconButton' is defined but never used.", "'Legend' is defined but never used.", "'Collapse' is defined but never used.", "'ReportIcon' is defined but never used.", "'PersonAddIcon' is defined but never used.", "'EventAvailableIcon' is defined but never used.", "'QuizIcon' is defined but never used.", "'MenuBookIcon' is defined but never used.", "'HotelIcon' is defined but never used.", "'BusinessIcon' is defined but never used.", "'ExpandLess' is defined but never used.", "'ExpandMore' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'TextField' is defined but never used.", "'HomeIcon' is defined but never used.", "'getUserDetails' is defined but never used.", "'error' is assigned a value but never used.", "'response' is assigned a value but never used.", "'useEffect' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'EditIcon' is defined but never used.", "'LanguageIcon' is defined but never used.", "'dispatch' is assigned a value but never used.", "'logoDialog' is assigned a value but never used.", "'setLogoDialog' is assigned a value but never used.", "'PhoneIcon' is defined but never used.", "'EmailIcon' is defined but never used.", "'CakeIcon' is defined but never used.", "'BloodtypeIcon' is defined but never used.", "'CircularProgress' is defined but never used.", "'DownloadIcon' is defined but never used.", "'TrendingDownIcon' is defined but never used.", "'Switch' is defined but never used.", "'FormControlLabel' is defined but never used.", "'selectedPage' is assigned a value but never used.", "'editDialog' is assigned a value but never used.", "'Avatar' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'ListItemIcon' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'WebIcon' is defined but never used.", "'VisibilityIcon' is defined but never used.", "'VisibilityOffIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'NotificationsIcon' is defined but never used.", "'files' is assigned a value but never used.", {"desc": "647", "fix": "648"}, {"desc": "649", "fix": "650"}, {"desc": "647", "fix": "651"}, {"desc": "649", "fix": "652"}, {"desc": "647", "fix": "653"}, {"desc": "654", "fix": "655"}, "Update the dependencies array to be: [dispatch, params, situation]", {"range": "656", "text": "657"}, "Update the dependencies array to be: [dispatch, situation, userDetails]", {"range": "658", "text": "659"}, {"range": "660", "text": "657"}, {"range": "661", "text": "659"}, {"range": "662", "text": "657"}, "Update the dependencies array to be: [currentRole, currentUser._id, currentUser.school._id, dispatch]", {"range": "663", "text": "664"}, [1899, 1910], "[dispatch, params, situation]", [2122, 2145], "[dispatch, situation, userDetails]", [1867, 1878], [2090, 2113], [1458, 1469], [747, 757], "[currentR<PERSON>, currentUser._id, currentUser.school._id, dispatch]"]