[{"A:\\11\\MERN-School-Management-System\\frontend\\src\\index.js": "1", "A:\\11\\MERN-School-Management-System\\frontend\\src\\App.js": "2", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\store.js": "3", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Homepage.js": "4", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\LoginPage.js": "5", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\ChooseUser.js": "6", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminDashboard.js": "7", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminRegisterPage.js": "8", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherDashboard.js": "9", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentDashboard.js": "10", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userSlice.js": "11", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentSlice.js": "12", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeSlice.js": "13", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassSlice.js": "14", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherSlice.js": "15", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainSlice.js": "16", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Logout.js": "17", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userHandle.js": "18", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminProfile.js": "19", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\SideBar.js": "20", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminHomePage.js": "21", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\Popup.js": "22", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\styles.js": "23", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\AccountMenu.js": "24", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\buttonStyles.js": "25", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentAttendance.js": "26", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentExamMarks.js": "27", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\SeeComplains.js": "28", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\AddStudent.js": "29", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ShowStudents.js": "30", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ViewStudent.js": "31", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\AddNotice.js": "32", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\ShowNotices.js": "33", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ViewSubject.js": "34", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ShowSubjects.js": "35", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherSideBar.js": "36", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherClassDetails.js": "37", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\SubjectForm.js": "38", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseClass.js": "39", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ShowTeachers.js": "40", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseSubject.js": "41", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\AddTeacher.js": "42", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherComplain.js": "43", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherHomePage.js": "44", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\TeacherDetails.js": "45", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentHomePage.js": "46", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherProfile.js": "47", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherViewStudent.js": "48", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSideBar.js": "49", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\AddClass.js": "50", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ShowClasses.js": "51", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSubjects.js": "52", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ClassDetails.js": "53", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentProfile.js": "54", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentComplain.js": "55", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\ViewStdAttendance.js": "56", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SeeNotice.js": "57", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentHandle.js": "58", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassHandle.js": "59", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherHandle.js": "60", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableTemplate.js": "61", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SpeedDialTemplate.js": "62", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainHandle.js": "63", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\attendanceCalculator.js": "64", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomBarChart.js": "65", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomPieChart.js": "66", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeHandle.js": "67", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableViewTemplate.js": "68", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\EnhancedStatCard.js": "69", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\AttendanceChart.js": "70", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\PerformanceChart.js": "71", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\RecentActivity.js": "72", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\QuickActions.js": "73", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\CalendarWidget.js": "74", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\FinancialChart.js": "75", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\NotificationCenter.js": "76", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeToggle.js": "77", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeProvider.js": "78", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\layout\\ResponsiveSidebar.js": "79", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\feeManagement\\FeeManagement.js": "80", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\transport\\TransportManagement.js": "81", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\documents\\DocumentGeneration.js": "82", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentInfo\\StudentInfo.js": "83", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminSettings.js": "84", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\EnhancedViewStudent.js": "85", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\inventory\\InventoryManagement.js": "86", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\inventory\\AddInventoryItem.js": "87"}, {"size": 388, "mtime": 1751091352643, "results": "88", "hashOfConfig": "89"}, {"size": 1577, "mtime": 1751100721027, "results": "90", "hashOfConfig": "89"}, {"size": 713, "mtime": 1751091352677, "results": "91", "hashOfConfig": "89"}, {"size": 2910, "mtime": 1751100774610, "results": "92", "hashOfConfig": "89"}, {"size": 11299, "mtime": 1751100761015, "results": "93", "hashOfConfig": "89"}, {"size": 2833, "mtime": 1751100705093, "results": "94", "hashOfConfig": "89"}, {"size": 11449, "mtime": 1751112673651, "results": "95", "hashOfConfig": "89"}, {"size": 9826, "mtime": 1751091352650, "results": "96", "hashOfConfig": "89"}, {"size": 4699, "mtime": 1751091352670, "results": "97", "hashOfConfig": "89"}, {"size": 4230, "mtime": 1751091352662, "results": "98", "hashOfConfig": "89"}, {"size": 2909, "mtime": 1751091352677, "results": "99", "hashOfConfig": "89"}, {"size": 1483, "mtime": 1751091352677, "results": "100", "hashOfConfig": "89"}, {"size": 1014, "mtime": 1751091352677, "results": "101", "hashOfConfig": "89"}, {"size": 2643, "mtime": 1751091352677, "results": "102", "hashOfConfig": "89"}, {"size": 1440, "mtime": 1751091352677, "results": "103", "hashOfConfig": "89"}, {"size": 1028, "mtime": 1751091352672, "results": "104", "hashOfConfig": "89"}, {"size": 1800, "mtime": 1751091352643, "results": "105", "hashOfConfig": "89"}, {"size": 3835, "mtime": 1751111833332, "results": "106", "hashOfConfig": "89"}, {"size": 4480, "mtime": 1751091352648, "results": "107", "hashOfConfig": "89"}, {"size": 4467, "mtime": 1751091352650, "results": "108", "hashOfConfig": "109"}, {"size": 7554, "mtime": 1751104361797, "results": "110", "hashOfConfig": "89"}, {"size": 1562, "mtime": 1751091352637, "results": "111", "hashOfConfig": "89"}, {"size": 2382, "mtime": 1751091352643, "results": "112", "hashOfConfig": "89"}, {"size": 3501, "mtime": 1751111367409, "results": "113", "hashOfConfig": "89"}, {"size": 1933, "mtime": 1751091352639, "results": "114", "hashOfConfig": "89"}, {"size": 8909, "mtime": 1751091352656, "results": "115", "hashOfConfig": "89"}, {"size": 7905, "mtime": 1751091352656, "results": "116", "hashOfConfig": "89"}, {"size": 2165, "mtime": 1751091352656, "results": "117", "hashOfConfig": "89"}, {"size": 23300, "mtime": 1751111313379, "results": "118", "hashOfConfig": "89"}, {"size": 8747, "mtime": 1751091352656, "results": "119", "hashOfConfig": "89"}, {"size": 22050, "mtime": 1751091352656, "results": "120", "hashOfConfig": "89"}, {"size": 2792, "mtime": 1751091352653, "results": "121", "hashOfConfig": "89"}, {"size": 3697, "mtime": 1751091352653, "results": "122", "hashOfConfig": "89"}, {"size": 7183, "mtime": 1751091352659, "results": "123", "hashOfConfig": "89"}, {"size": 4297, "mtime": 1751091352659, "results": "124", "hashOfConfig": "89"}, {"size": 2939, "mtime": 1751091352672, "results": "125", "hashOfConfig": "89"}, {"size": 7137, "mtime": 1751091352669, "results": "126", "hashOfConfig": "89"}, {"size": 6961, "mtime": 1751091352659, "results": "127", "hashOfConfig": "89"}, {"size": 2823, "mtime": 1751091352662, "results": "128", "hashOfConfig": "89"}, {"size": 7878, "mtime": 1751091352662, "results": "129", "hashOfConfig": "89"}, {"size": 5134, "mtime": 1751091352662, "results": "130", "hashOfConfig": "89"}, {"size": 3772, "mtime": 1751091352662, "results": "131", "hashOfConfig": "89"}, {"size": 146, "mtime": 1751091352669, "results": "132", "hashOfConfig": "89"}, {"size": 3747, "mtime": 1751091352670, "results": "133", "hashOfConfig": "89"}, {"size": 2405, "mtime": 1751091352662, "results": "134", "hashOfConfig": "89"}, {"size": 5332, "mtime": 1751091352662, "results": "135", "hashOfConfig": "89"}, {"size": 1317, "mtime": 1751091352670, "results": "136", "hashOfConfig": "89"}, {"size": 12169, "mtime": 1751091352672, "results": "137", "hashOfConfig": "89"}, {"size": 3195, "mtime": 1751091352662, "results": "138", "hashOfConfig": "89"}, {"size": 4208, "mtime": 1751091352651, "results": "139", "hashOfConfig": "89"}, {"size": 6277, "mtime": 1751091352652, "results": "140", "hashOfConfig": "89"}, {"size": 6014, "mtime": 1751091352662, "results": "141", "hashOfConfig": "89"}, {"size": 10926, "mtime": 1751091352652, "results": "142", "hashOfConfig": "89"}, {"size": 3931, "mtime": 1751091352662, "results": "143", "hashOfConfig": "89"}, {"size": 4228, "mtime": 1751091352662, "results": "144", "hashOfConfig": "89"}, {"size": 9767, "mtime": 1751091352662, "results": "145", "hashOfConfig": "89"}, {"size": 2233, "mtime": 1751091352639, "results": "146", "hashOfConfig": "89"}, {"size": 1558, "mtime": 1751093107198, "results": "147", "hashOfConfig": "89"}, {"size": 2895, "mtime": 1751093149585, "results": "148", "hashOfConfig": "89"}, {"size": 1383, "mtime": 1751093059721, "results": "149", "hashOfConfig": "89"}, {"size": 3386, "mtime": 1751091352639, "results": "150", "hashOfConfig": "89"}, {"size": 887, "mtime": 1751091352639, "results": "151", "hashOfConfig": "89"}, {"size": 621, "mtime": 1751093204280, "results": "152", "hashOfConfig": "89"}, {"size": 1996, "mtime": 1751091352639, "results": "153", "hashOfConfig": "89"}, {"size": 6469, "mtime": 1751091352629, "results": "154", "hashOfConfig": "89"}, {"size": 3161, "mtime": 1751091352629, "results": "155", "hashOfConfig": "89"}, {"size": 617, "mtime": 1751093177974, "results": "156", "hashOfConfig": "89"}, {"size": 3018, "mtime": 1751091352639, "results": "157", "hashOfConfig": "89"}, {"size": 3781, "mtime": 1751104338304, "results": "158", "hashOfConfig": "89"}, {"size": 2540, "mtime": 1751101038420, "results": "159", "hashOfConfig": "89"}, {"size": 2516, "mtime": 1751101053307, "results": "160", "hashOfConfig": "89"}, {"size": 4394, "mtime": 1751101121649, "results": "161", "hashOfConfig": "89"}, {"size": 3664, "mtime": 1751104279550, "results": "162", "hashOfConfig": "89"}, {"size": 5436, "mtime": 1751101223218, "results": "163", "hashOfConfig": "89"}, {"size": 3811, "mtime": 1751101073787, "results": "164", "hashOfConfig": "89"}, {"size": 6826, "mtime": 1751101255344, "results": "165", "hashOfConfig": "89"}, {"size": 908, "mtime": 1751101186045, "results": "166", "hashOfConfig": "89"}, {"size": 3711, "mtime": 1751101175499, "results": "167", "hashOfConfig": "89"}, {"size": 10725, "mtime": 1751112728727, "results": "168", "hashOfConfig": "89"}, {"size": 14046, "mtime": 1751102262286, "results": "169", "hashOfConfig": "89"}, {"size": 12806, "mtime": 1751102695142, "results": "170", "hashOfConfig": "89"}, {"size": 11821, "mtime": 1751102469493, "results": "171", "hashOfConfig": "89"}, {"size": 19126, "mtime": 1751111818968, "results": "172", "hashOfConfig": "89"}, {"size": 25082, "mtime": 1751111169508, "results": "173", "hashOfConfig": "89"}, {"size": 17065, "mtime": 1751112046968, "results": "174", "hashOfConfig": "89"}, {"size": 29090, "mtime": 1751112799082, "results": "175", "hashOfConfig": "89"}, {"size": 16940, "mtime": 1751112579256, "results": "176", "hashOfConfig": "89"}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kq195y", {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "237"}, "1pf3lve", {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "A:\\11\\MERN-School-Management-System\\frontend\\src\\index.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\App.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\store.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Homepage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\LoginPage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\ChooseUser.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminDashboard.js", ["439"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminRegisterPage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Logout.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\SideBar.js", [], [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminHomePage.js", ["440", "441", "442", "443", "444"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\Popup.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\styles.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\AccountMenu.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\buttonStyles.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentAttendance.js", ["445", "446"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentExamMarks.js", ["447", "448"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\SeeComplains.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\AddStudent.js", ["449", "450", "451", "452"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ShowStudents.js", ["453"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ViewStudent.js", ["454", "455", "456", "457", "458"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\AddNotice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\ShowNotices.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ViewSubject.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ShowSubjects.js", ["459"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherSideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherClassDetails.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\SubjectForm.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseClass.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ShowTeachers.js", ["460"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseSubject.js", ["461"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\AddTeacher.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherComplain.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\TeacherDetails.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherViewStudent.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\AddClass.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ShowClasses.js", ["462"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSubjects.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ClassDetails.js", ["463", "464"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentComplain.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\ViewStdAttendance.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SeeNotice.js", ["465"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SpeedDialTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\attendanceCalculator.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomBarChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomPieChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableViewTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\EnhancedStatCard.js", ["466"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\AttendanceChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\PerformanceChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\RecentActivity.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\QuickActions.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\CalendarWidget.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\FinancialChart.js", ["467"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\NotificationCenter.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeToggle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeProvider.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\layout\\ResponsiveSidebar.js", ["468", "469", "470", "471", "472", "473", "474", "475", "476", "477", "478", "479"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\feeManagement\\FeeManagement.js", ["480"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\transport\\TransportManagement.js", ["481", "482", "483"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\documents\\DocumentGeneration.js", ["484"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentInfo\\StudentInfo.js", ["485", "486", "487", "488"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminSettings.js", ["489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\EnhancedViewStudent.js", ["501", "502", "503", "504", "505", "506"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\inventory\\InventoryManagement.js", ["507", "508", "509", "510", "511", "512", "513", "514"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\inventory\\AddInventoryItem.js", ["515"], [], {"ruleId": "516", "severity": 1, "message": "517", "line": 41, "column": 8, "nodeType": "518", "messageId": "519", "endLine": 41, "endColumn": 19}, {"ruleId": "516", "severity": 1, "message": "520", "line": 31, "column": 17, "nodeType": "518", "messageId": "519", "endLine": 31, "endColumn": 31}, {"ruleId": "516", "severity": 1, "message": "521", "line": 32, "column": 13, "nodeType": "518", "messageId": "519", "endLine": 32, "endColumn": 23}, {"ruleId": "516", "severity": 1, "message": "522", "line": 39, "column": 11, "nodeType": "518", "messageId": "519", "endLine": 39, "endColumn": 19}, {"ruleId": "516", "severity": 1, "message": "523", "line": 60, "column": 11, "nodeType": "518", "messageId": "519", "endLine": 60, "endColumn": 25}, {"ruleId": "516", "severity": 1, "message": "524", "line": 61, "column": 11, "nodeType": "518", "messageId": "519", "endLine": 61, "endColumn": 23}, {"ruleId": "525", "severity": 1, "message": "526", "line": 46, "column": 8, "nodeType": "527", "endLine": 46, "endColumn": 19, "suggestions": "528"}, {"ruleId": "525", "severity": 1, "message": "529", "line": 52, "column": 8, "nodeType": "527", "endLine": 52, "endColumn": 31, "suggestions": "530"}, {"ruleId": "525", "severity": 1, "message": "526", "line": 45, "column": 8, "nodeType": "527", "endLine": 45, "endColumn": 19, "suggestions": "531"}, {"ruleId": "525", "severity": 1, "message": "529", "line": 51, "column": 8, "nodeType": "527", "endLine": 51, "endColumn": 31, "suggestions": "532"}, {"ruleId": "516", "severity": 1, "message": "533", "line": 23, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 23, "endColumn": 10}, {"ruleId": "516", "severity": 1, "message": "534", "line": 26, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 26, "endColumn": 8}, {"ruleId": "516", "severity": 1, "message": "535", "line": 33, "column": 18, "nodeType": "518", "messageId": "519", "endLine": 33, "endColumn": 33}, {"ruleId": "516", "severity": 1, "message": "536", "line": 98, "column": 12, "nodeType": "518", "messageId": "519", "endLine": 98, "endColumn": 26}, {"ruleId": "516", "severity": 1, "message": "537", "line": 5, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 5, "endColumn": 20}, {"ruleId": "516", "severity": 1, "message": "537", "line": 3, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 3, "endColumn": 20}, {"ruleId": "516", "severity": 1, "message": "538", "line": 24, "column": 12, "nodeType": "518", "messageId": "519", "endLine": 24, "endColumn": 19}, {"ruleId": "516", "severity": 1, "message": "539", "line": 24, "column": 21, "nodeType": "518", "messageId": "519", "endLine": 24, "endColumn": 31}, {"ruleId": "516", "severity": 1, "message": "540", "line": 49, "column": 22, "nodeType": "518", "messageId": "519", "endLine": 49, "endColumn": 33}, {"ruleId": "516", "severity": 1, "message": "541", "line": 93, "column": 11, "nodeType": "518", "messageId": "519", "endLine": 93, "endColumn": 24}, {"ruleId": "516", "severity": 1, "message": "537", "line": 5, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 5, "endColumn": 20}, {"ruleId": "516", "severity": 1, "message": "537", "line": 9, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 9, "endColumn": 20}, {"ruleId": "525", "severity": 1, "message": "526", "line": 33, "column": 8, "nodeType": "527", "endLine": 33, "endColumn": 19, "suggestions": "542"}, {"ruleId": "516", "severity": 1, "message": "537", "line": 6, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 6, "endColumn": 20}, {"ruleId": "516", "severity": 1, "message": "537", "line": 5, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 5, "endColumn": 20}, {"ruleId": "516", "severity": 1, "message": "543", "line": 12, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 12, "endColumn": 23}, {"ruleId": "525", "severity": 1, "message": "544", "line": 20, "column": 8, "nodeType": "527", "endLine": 20, "endColumn": 18, "suggestions": "545"}, {"ruleId": "516", "severity": 1, "message": "546", "line": 2, "column": 34, "nodeType": "518", "messageId": "519", "endLine": 2, "endColumn": 44}, {"ruleId": "516", "severity": 1, "message": "547", "line": 2, "column": 61, "nodeType": "518", "messageId": "519", "endLine": 2, "endColumn": 67}, {"ruleId": "516", "severity": 1, "message": "548", "line": 15, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 15, "endColumn": 11}, {"ruleId": "516", "severity": 1, "message": "549", "line": 27, "column": 13, "nodeType": "518", "messageId": "519", "endLine": 27, "endColumn": 23}, {"ruleId": "516", "severity": 1, "message": "521", "line": 37, "column": 13, "nodeType": "518", "messageId": "519", "endLine": 37, "endColumn": 23}, {"ruleId": "516", "severity": 1, "message": "550", "line": 38, "column": 16, "nodeType": "518", "messageId": "519", "endLine": 38, "endColumn": 29}, {"ruleId": "516", "severity": 1, "message": "551", "line": 39, "column": 21, "nodeType": "518", "messageId": "519", "endLine": 39, "endColumn": 39}, {"ruleId": "516", "severity": 1, "message": "552", "line": 40, "column": 11, "nodeType": "518", "messageId": "519", "endLine": 40, "endColumn": 19}, {"ruleId": "516", "severity": 1, "message": "553", "line": 41, "column": 15, "nodeType": "518", "messageId": "519", "endLine": 41, "endColumn": 27}, {"ruleId": "516", "severity": 1, "message": "554", "line": 43, "column": 12, "nodeType": "518", "messageId": "519", "endLine": 43, "endColumn": 21}, {"ruleId": "516", "severity": 1, "message": "555", "line": 44, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 44, "endColumn": 17}, {"ruleId": "516", "severity": 1, "message": "556", "line": 45, "column": 15, "nodeType": "518", "messageId": "519", "endLine": 45, "endColumn": 27}, {"ruleId": "516", "severity": 1, "message": "557", "line": 46, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 46, "endColumn": 13}, {"ruleId": "516", "severity": 1, "message": "558", "line": 47, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 47, "endColumn": 13}, {"ruleId": "516", "severity": 1, "message": "559", "line": 35, "column": 18, "nodeType": "518", "messageId": "519", "endLine": 35, "endColumn": 33}, {"ruleId": "516", "severity": 1, "message": "560", "line": 32, "column": 15, "nodeType": "518", "messageId": "519", "endLine": 32, "endColumn": 27}, {"ruleId": "516", "severity": 1, "message": "561", "line": 34, "column": 14, "nodeType": "518", "messageId": "519", "endLine": 34, "endColumn": 25}, {"ruleId": "516", "severity": 1, "message": "559", "line": 35, "column": 18, "nodeType": "518", "messageId": "519", "endLine": 35, "endColumn": 33}, {"ruleId": "516", "severity": 1, "message": "562", "line": 11, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 11, "endColumn": 12}, {"ruleId": "516", "severity": 1, "message": "563", "line": 43, "column": 11, "nodeType": "518", "messageId": "519", "endLine": 43, "endColumn": 19}, {"ruleId": "516", "severity": 1, "message": "564", "line": 52, "column": 22, "nodeType": "518", "messageId": "519", "endLine": 52, "endColumn": 36}, {"ruleId": "516", "severity": 1, "message": "565", "line": 94, "column": 34, "nodeType": "518", "messageId": "519", "endLine": 94, "endColumn": 39}, {"ruleId": "516", "severity": 1, "message": "566", "line": 94, "column": 41, "nodeType": "518", "messageId": "519", "endLine": 94, "endColumn": 49}, {"ruleId": "516", "severity": 1, "message": "567", "line": 1, "column": 27, "nodeType": "518", "messageId": "519", "endLine": 1, "endColumn": 36}, {"ruleId": "516", "severity": 1, "message": "533", "line": 14, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 14, "endColumn": 10}, {"ruleId": "516", "severity": 1, "message": "546", "line": 15, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 15, "endColumn": 13}, {"ruleId": "516", "severity": 1, "message": "568", "line": 26, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 26, "endColumn": 9}, {"ruleId": "516", "severity": 1, "message": "569", "line": 27, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 27, "endColumn": 14}, {"ruleId": "516", "severity": 1, "message": "570", "line": 28, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 28, "endColumn": 16}, {"ruleId": "516", "severity": 1, "message": "571", "line": 29, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 29, "endColumn": 16}, {"ruleId": "516", "severity": 1, "message": "572", "line": 39, "column": 11, "nodeType": "518", "messageId": "519", "endLine": 39, "endColumn": 19}, {"ruleId": "516", "severity": 1, "message": "573", "line": 43, "column": 15, "nodeType": "518", "messageId": "519", "endLine": 43, "endColumn": 27}, {"ruleId": "516", "severity": 1, "message": "574", "line": 114, "column": 9, "nodeType": "518", "messageId": "519", "endLine": 114, "endColumn": 17}, {"ruleId": "516", "severity": 1, "message": "575", "line": 121, "column": 10, "nodeType": "518", "messageId": "519", "endLine": 121, "endColumn": 20}, {"ruleId": "516", "severity": 1, "message": "576", "line": 121, "column": 22, "nodeType": "518", "messageId": "519", "endLine": 121, "endColumn": 35}, {"ruleId": "516", "severity": 1, "message": "533", "line": 19, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 19, "endColumn": 10}, {"ruleId": "516", "severity": 1, "message": "577", "line": 32, "column": 12, "nodeType": "518", "messageId": "519", "endLine": 32, "endColumn": 21}, {"ruleId": "516", "severity": 1, "message": "578", "line": 33, "column": 12, "nodeType": "518", "messageId": "519", "endLine": 33, "endColumn": 21}, {"ruleId": "516", "severity": 1, "message": "579", "line": 35, "column": 11, "nodeType": "518", "messageId": "519", "endLine": 35, "endColumn": 19}, {"ruleId": "516", "severity": 1, "message": "580", "line": 36, "column": 16, "nodeType": "518", "messageId": "519", "endLine": 36, "endColumn": 29}, {"ruleId": "516", "severity": 1, "message": "566", "line": 83, "column": 24, "nodeType": "518", "messageId": "519", "endLine": 83, "endColumn": 32}, {"ruleId": "516", "severity": 1, "message": "567", "line": 1, "column": 27, "nodeType": "518", "messageId": "519", "endLine": 1, "endColumn": 36}, {"ruleId": "516", "severity": 1, "message": "581", "line": 27, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 27, "endColumn": 19}, {"ruleId": "516", "severity": 1, "message": "534", "line": 38, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 38, "endColumn": 8}, {"ruleId": "516", "severity": 1, "message": "559", "line": 51, "column": 18, "nodeType": "518", "messageId": "519", "endLine": 51, "endColumn": 33}, {"ruleId": "516", "severity": 1, "message": "582", "line": 60, "column": 15, "nodeType": "518", "messageId": "519", "endLine": 60, "endColumn": 27}, {"ruleId": "516", "severity": 1, "message": "583", "line": 63, "column": 19, "nodeType": "518", "messageId": "519", "endLine": 63, "endColumn": 35}, {"ruleId": "516", "severity": 1, "message": "574", "line": 139, "column": 9, "nodeType": "518", "messageId": "519", "endLine": 139, "endColumn": 17}, {"ruleId": "516", "severity": 1, "message": "584", "line": 141, "column": 11, "nodeType": "518", "messageId": "519", "endLine": 141, "endColumn": 22}, {"ruleId": "516", "severity": 1, "message": "533", "line": 19, "column": 3, "nodeType": "518", "messageId": "519", "endLine": 19, "endColumn": 10}, "no-unused-vars", "'ViewStudent' is defined but never used.", "Identifier", "unusedVar", "'TrendingUpIcon' is defined but never used.", "'PeopleIcon' is defined but never used.", "'isMobile' is assigned a value but never used.", "'attendanceRate' is assigned a value but never used.", "'averageGrade' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch' and 'params'. Either include them or remove the dependency array.", "ArrayExpression", ["585"], "React Hook useEffect has a missing dependency: 'situation'. Either include it or remove the dependency array.", ["586"], ["587"], ["588"], "'Divider' is defined but never used.", "'Badge' is defined but never used.", "'PhotoCameraIcon' is defined but never used.", "'profilePicture' is assigned a value but never used.", "'deleteUser' is defined but never used.", "'showTab' is assigned a value but never used.", "'setShowTab' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'submitHandler' is assigned a value but never used.", ["589"], "'resetSubjects' is defined but never used.", "React Hook useEffect has missing dependencies: 'currentRole', 'currentUser._id', and 'currentUser.school._id'. Either include them or remove the dependency array.", ["590"], "'IconButton' is defined but never used.", "'Legend' is defined but never used.", "'Collapse' is defined but never used.", "'ReportIcon' is defined but never used.", "'PersonAddIcon' is defined but never used.", "'EventAvailableIcon' is defined but never used.", "'QuizIcon' is defined but never used.", "'MenuBookIcon' is defined but never used.", "'HotelIcon' is defined but never used.", "'WebIcon' is defined but never used.", "'BusinessIcon' is defined but never used.", "'ExpandLess' is defined but never used.", "'ExpandMore' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'TextField' is defined but never used.", "'HomeIcon' is defined but never used.", "'getUserDetails' is defined but never used.", "'error' is assigned a value but never used.", "'response' is assigned a value but never used.", "'useEffect' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'EditIcon' is defined but never used.", "'LanguageIcon' is defined but never used.", "'dispatch' is assigned a value but never used.", "'logoDialog' is assigned a value but never used.", "'setLogoDialog' is assigned a value but never used.", "'PhoneIcon' is defined but never used.", "'EmailIcon' is defined but never used.", "'CakeIcon' is defined but never used.", "'BloodtypeIcon' is defined but never used.", "'CircularProgress' is defined but never used.", "'DownloadIcon' is defined but never used.", "'TrendingDownIcon' is defined but never used.", "'currentUser' is assigned a value but never used.", {"desc": "591", "fix": "592"}, {"desc": "593", "fix": "594"}, {"desc": "591", "fix": "595"}, {"desc": "593", "fix": "596"}, {"desc": "591", "fix": "597"}, {"desc": "598", "fix": "599"}, "Update the dependencies array to be: [dispatch, params, situation]", {"range": "600", "text": "601"}, "Update the dependencies array to be: [dispatch, situation, userDetails]", {"range": "602", "text": "603"}, {"range": "604", "text": "601"}, {"range": "605", "text": "603"}, {"range": "606", "text": "601"}, "Update the dependencies array to be: [currentRole, currentUser._id, currentUser.school._id, dispatch]", {"range": "607", "text": "608"}, [1899, 1910], "[dispatch, params, situation]", [2122, 2145], "[dispatch, situation, userDetails]", [1867, 1878], [2090, 2113], [1458, 1469], [747, 757], "[currentR<PERSON>, currentUser._id, currentUser.school._id, dispatch]"]