[{"A:\\11\\MERN-School-Management-System\\frontend\\src\\index.js": "1", "A:\\11\\MERN-School-Management-System\\frontend\\src\\App.js": "2", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\store.js": "3", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Homepage.js": "4", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\LoginPage.js": "5", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\ChooseUser.js": "6", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminDashboard.js": "7", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminRegisterPage.js": "8", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherDashboard.js": "9", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentDashboard.js": "10", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userSlice.js": "11", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentSlice.js": "12", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeSlice.js": "13", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassSlice.js": "14", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherSlice.js": "15", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainSlice.js": "16", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Logout.js": "17", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userHandle.js": "18", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminProfile.js": "19", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\SideBar.js": "20", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminHomePage.js": "21", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\Popup.js": "22", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\styles.js": "23", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\AccountMenu.js": "24", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\buttonStyles.js": "25", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentAttendance.js": "26", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentExamMarks.js": "27", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\SeeComplains.js": "28", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\AddStudent.js": "29", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ShowStudents.js": "30", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ViewStudent.js": "31", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\AddNotice.js": "32", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\ShowNotices.js": "33", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ViewSubject.js": "34", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ShowSubjects.js": "35", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherSideBar.js": "36", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherClassDetails.js": "37", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\SubjectForm.js": "38", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseClass.js": "39", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ShowTeachers.js": "40", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseSubject.js": "41", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\AddTeacher.js": "42", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherComplain.js": "43", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherHomePage.js": "44", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\TeacherDetails.js": "45", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentHomePage.js": "46", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherProfile.js": "47", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherViewStudent.js": "48", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSideBar.js": "49", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\AddClass.js": "50", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ShowClasses.js": "51", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSubjects.js": "52", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ClassDetails.js": "53", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentProfile.js": "54", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentComplain.js": "55", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\ViewStdAttendance.js": "56", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SeeNotice.js": "57", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentHandle.js": "58", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassHandle.js": "59", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherHandle.js": "60", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableTemplate.js": "61", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SpeedDialTemplate.js": "62", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainHandle.js": "63", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\attendanceCalculator.js": "64", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomBarChart.js": "65", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomPieChart.js": "66", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeHandle.js": "67", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableViewTemplate.js": "68"}, {"size": 388, "mtime": 1751091352643, "results": "69", "hashOfConfig": "70"}, {"size": 1577, "mtime": 1751100721027, "results": "71", "hashOfConfig": "70"}, {"size": 713, "mtime": 1751091352677, "results": "72", "hashOfConfig": "70"}, {"size": 2910, "mtime": 1751100774610, "results": "73", "hashOfConfig": "70"}, {"size": 11299, "mtime": 1751100761015, "results": "74", "hashOfConfig": "70"}, {"size": 2833, "mtime": 1751100705093, "results": "75", "hashOfConfig": "70"}, {"size": 7722, "mtime": 1751091352643, "results": "76", "hashOfConfig": "70"}, {"size": 9826, "mtime": 1751091352650, "results": "77", "hashOfConfig": "70"}, {"size": 4699, "mtime": 1751091352670, "results": "78", "hashOfConfig": "70"}, {"size": 4230, "mtime": 1751091352662, "results": "79", "hashOfConfig": "70"}, {"size": 2909, "mtime": 1751091352677, "results": "80", "hashOfConfig": "70"}, {"size": 1483, "mtime": 1751091352677, "results": "81", "hashOfConfig": "70"}, {"size": 1014, "mtime": 1751091352677, "results": "82", "hashOfConfig": "70"}, {"size": 2643, "mtime": 1751091352677, "results": "83", "hashOfConfig": "70"}, {"size": 1440, "mtime": 1751091352677, "results": "84", "hashOfConfig": "70"}, {"size": 1028, "mtime": 1751091352672, "results": "85", "hashOfConfig": "70"}, {"size": 1800, "mtime": 1751091352643, "results": "86", "hashOfConfig": "70"}, {"size": 3589, "mtime": 1751092999549, "results": "87", "hashOfConfig": "70"}, {"size": 4480, "mtime": 1751091352648, "results": "88", "hashOfConfig": "70"}, {"size": 4467, "mtime": 1751091352650, "results": "89", "hashOfConfig": "70"}, {"size": 4052, "mtime": 1751091352648, "results": "90", "hashOfConfig": "70"}, {"size": 1562, "mtime": 1751091352637, "results": "91", "hashOfConfig": "70"}, {"size": 2382, "mtime": 1751091352643, "results": "92", "hashOfConfig": "70"}, {"size": 3408, "mtime": 1751091352629, "results": "93", "hashOfConfig": "70"}, {"size": 1933, "mtime": 1751091352639, "results": "94", "hashOfConfig": "70"}, {"size": 8909, "mtime": 1751091352656, "results": "95", "hashOfConfig": "70"}, {"size": 7905, "mtime": 1751091352656, "results": "96", "hashOfConfig": "70"}, {"size": 2165, "mtime": 1751091352656, "results": "97", "hashOfConfig": "70"}, {"size": 5496, "mtime": 1751091352653, "results": "98", "hashOfConfig": "70"}, {"size": 8747, "mtime": 1751091352656, "results": "99", "hashOfConfig": "70"}, {"size": 22050, "mtime": 1751091352656, "results": "100", "hashOfConfig": "70"}, {"size": 2792, "mtime": 1751091352653, "results": "101", "hashOfConfig": "70"}, {"size": 3697, "mtime": 1751091352653, "results": "102", "hashOfConfig": "70"}, {"size": 7183, "mtime": 1751091352659, "results": "103", "hashOfConfig": "70"}, {"size": 4297, "mtime": 1751091352659, "results": "104", "hashOfConfig": "70"}, {"size": 2939, "mtime": 1751091352672, "results": "105", "hashOfConfig": "70"}, {"size": 7137, "mtime": 1751091352669, "results": "106", "hashOfConfig": "70"}, {"size": 6961, "mtime": 1751091352659, "results": "107", "hashOfConfig": "70"}, {"size": 2823, "mtime": 1751091352662, "results": "108", "hashOfConfig": "70"}, {"size": 7878, "mtime": 1751091352662, "results": "109", "hashOfConfig": "70"}, {"size": 5134, "mtime": 1751091352662, "results": "110", "hashOfConfig": "70"}, {"size": 3772, "mtime": 1751091352662, "results": "111", "hashOfConfig": "70"}, {"size": 146, "mtime": 1751091352669, "results": "112", "hashOfConfig": "70"}, {"size": 3747, "mtime": 1751091352670, "results": "113", "hashOfConfig": "70"}, {"size": 2405, "mtime": 1751091352662, "results": "114", "hashOfConfig": "70"}, {"size": 5332, "mtime": 1751091352662, "results": "115", "hashOfConfig": "70"}, {"size": 1317, "mtime": 1751091352670, "results": "116", "hashOfConfig": "70"}, {"size": 12169, "mtime": 1751091352672, "results": "117", "hashOfConfig": "70"}, {"size": 3195, "mtime": 1751091352662, "results": "118", "hashOfConfig": "70"}, {"size": 4208, "mtime": 1751091352651, "results": "119", "hashOfConfig": "70"}, {"size": 6277, "mtime": 1751091352652, "results": "120", "hashOfConfig": "70"}, {"size": 6014, "mtime": 1751091352662, "results": "121", "hashOfConfig": "70"}, {"size": 10926, "mtime": 1751091352652, "results": "122", "hashOfConfig": "70"}, {"size": 3931, "mtime": 1751091352662, "results": "123", "hashOfConfig": "70"}, {"size": 4228, "mtime": 1751091352662, "results": "124", "hashOfConfig": "70"}, {"size": 9767, "mtime": 1751091352662, "results": "125", "hashOfConfig": "70"}, {"size": 2233, "mtime": 1751091352639, "results": "126", "hashOfConfig": "70"}, {"size": 1558, "mtime": 1751093107198, "results": "127", "hashOfConfig": "70"}, {"size": 2895, "mtime": 1751093149585, "results": "128", "hashOfConfig": "70"}, {"size": 1383, "mtime": 1751093059721, "results": "129", "hashOfConfig": "70"}, {"size": 3386, "mtime": 1751091352639, "results": "130", "hashOfConfig": "70"}, {"size": 887, "mtime": 1751091352639, "results": "131", "hashOfConfig": "70"}, {"size": 621, "mtime": 1751093204280, "results": "132", "hashOfConfig": "70"}, {"size": 1996, "mtime": 1751091352639, "results": "133", "hashOfConfig": "70"}, {"size": 6469, "mtime": 1751091352629, "results": "134", "hashOfConfig": "70"}, {"size": 3161, "mtime": 1751091352629, "results": "135", "hashOfConfig": "70"}, {"size": 617, "mtime": 1751093177974, "results": "136", "hashOfConfig": "70"}, {"size": 3018, "mtime": 1751091352639, "results": "137", "hashOfConfig": "70"}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, "1pf3lve", {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "217", "usedDeprecatedRules": "141"}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "221", "usedDeprecatedRules": "141"}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "231", "usedDeprecatedRules": "141"}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "235", "usedDeprecatedRules": "141"}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "248", "usedDeprecatedRules": "141"}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "264", "usedDeprecatedRules": "141"}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "268", "usedDeprecatedRules": "141"}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "299", "usedDeprecatedRules": "141"}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "306", "usedDeprecatedRules": "141"}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "319", "usedDeprecatedRules": "141"}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "141"}, "A:\\11\\MERN-School-Management-System\\frontend\\src\\index.js", [], [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\App.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\store.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Homepage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\LoginPage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\ChooseUser.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminRegisterPage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Logout.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userHandle.js", ["353"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\SideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\Popup.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\styles.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\AccountMenu.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\buttonStyles.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentAttendance.js", ["354", "355"], [], "import React, { useEffect, useState } from 'react'\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { useParams } from 'react-router-dom';\r\nimport { getUserDetails } from '../../../redux/userRelated/userHandle';\r\nimport { getSubjectList } from '../../../redux/sclassRelated/sclassHandle';\r\nimport { updateStudentFields } from '../../../redux/studentRelated/studentHandle';\r\n\r\nimport {\r\n    Box, InputLabel,\r\n    MenuItem, Select,\r\n    Typography, Stack,\r\n    TextField, CircularProgress, FormControl\r\n} from '@mui/material';\r\nimport { PurpleButton } from '../../../components/buttonStyles';\r\nimport Popup from '../../../components/Popup';\r\n\r\nconst StudentAttendance = ({ situation }) => {\r\n    const dispatch = useDispatch();\r\n    const { currentUser, userDetails, loading } = useSelector((state) => state.user);\r\n    const { subjectsList } = useSelector((state) => state.sclass);\r\n    const { response, error, statestatus } = useSelector((state) => state.student);\r\n    const params = useParams()\r\n\r\n    const [studentID, setStudentID] = useState(\"\");\r\n    const [subjectName, setSubjectName] = useState(\"\");\r\n    const [chosenSubName, setChosenSubName] = useState(\"\");\r\n    const [status, setStatus] = useState('');\r\n    const [date, setDate] = useState('');\r\n\r\n    const [showPopup, setShowPopup] = useState(false);\r\n    const [message, setMessage] = useState(\"\");\r\n    const [loader, setLoader] = useState(false)\r\n\r\n    useEffect(() => {\r\n        if (situation === \"Student\") {\r\n            setStudentID(params.id);\r\n            const stdID = params.id\r\n            dispatch(getUserDetails(stdID, \"Student\"));\r\n        }\r\n        else if (situation === \"Subject\") {\r\n            const { studentID, subjectID } = params\r\n            setStudentID(studentID);\r\n            dispatch(getUserDetails(studentID, \"Student\"));\r\n            setChosenSubName(subjectID);\r\n        }\r\n    }, [situation]);\r\n\r\n    useEffect(() => {\r\n        if (userDetails && userDetails.sclassName && situation === \"Student\") {\r\n            dispatch(getSubjectList(userDetails.sclassName._id, \"ClassSubjects\"));\r\n        }\r\n    }, [dispatch, userDetails]);\r\n\r\n    const changeHandler = (event) => {\r\n        const selectedSubject = subjectsList.find(\r\n            (subject) => subject.subName === event.target.value\r\n        );\r\n        setSubjectName(selectedSubject.subName);\r\n        setChosenSubName(selectedSubject._id);\r\n    }\r\n\r\n    const fields = { subName: chosenSubName, status, date }\r\n\r\n    const submitHandler = (event) => {\r\n        event.preventDefault()\r\n        setLoader(true)\r\n        dispatch(updateStudentFields(studentID, fields, \"StudentAttendance\"))\r\n    }\r\n\r\n    useEffect(() => {\r\n        if (response) {\r\n            setLoader(false)\r\n            setShowPopup(true)\r\n            setMessage(response)\r\n        }\r\n        else if (error) {\r\n            setLoader(false)\r\n            setShowPopup(true)\r\n            setMessage(\"error\")\r\n        }\r\n        else if (statestatus === \"added\") {\r\n            setLoader(false)\r\n            setShowPopup(true)\r\n            setMessage(\"Done Successfully\")\r\n        }\r\n    }, [response, statestatus, error])\r\n\r\n    return (\r\n        <>\r\n            {loading\r\n                ?\r\n                <>\r\n                    <div>Loading...</div>\r\n                </>\r\n                :\r\n                <>\r\n                    <Box\r\n                        sx={{\r\n                            flex: '1 1 auto',\r\n                            alignItems: 'center',\r\n                            display: 'flex',\r\n                            justifyContent: 'center'\r\n                        }}\r\n                    >\r\n                        <Box\r\n                            sx={{\r\n                                maxWidth: 550,\r\n                                px: 3,\r\n                                py: '100px',\r\n                                width: '100%'\r\n                            }}\r\n                        >\r\n                            <Stack spacing={1} sx={{ mb: 3 }}>\r\n                                <Typography variant=\"h4\">\r\n                                    Student Name: {userDetails.name}\r\n                                </Typography>\r\n                                {currentUser.teachSubject &&\r\n                                    <Typography variant=\"h4\">\r\n                                        Subject Name: {currentUser.teachSubject?.subName}\r\n                                    </Typography>\r\n                                }\r\n                            </Stack>\r\n                            <form onSubmit={submitHandler}>\r\n                                <Stack spacing={3}>\r\n                                    {\r\n                                        situation === \"Student\" &&\r\n                                        <FormControl fullWidth>\r\n                                            <InputLabel id=\"demo-simple-select-label\">Select Subject</InputLabel>\r\n                                            <Select\r\n                                                labelId=\"demo-simple-select-label\"\r\n                                                id=\"demo-simple-select\"\r\n                                                value={subjectName}\r\n                                                label=\"Choose an option\"\r\n                                                onChange={changeHandler} required\r\n                                            >\r\n                                                {subjectsList ?\r\n                                                    subjectsList.map((subject, index) => (\r\n                                                        <MenuItem key={index} value={subject.subName}>\r\n                                                            {subject.subName}\r\n                                                        </MenuItem>\r\n                                                    ))\r\n                                                    :\r\n                                                    <MenuItem value=\"Select Subject\">\r\n                                                        Add Subjects For Attendance\r\n                                                    </MenuItem>\r\n                                                }\r\n                                            </Select>\r\n                                        </FormControl>\r\n                                    }\r\n                                    <FormControl fullWidth>\r\n                                        <InputLabel id=\"demo-simple-select-label\">Attendance Status</InputLabel>\r\n                                        <Select\r\n                                            labelId=\"demo-simple-select-label\"\r\n                                            id=\"demo-simple-select\"\r\n                                            value={status}\r\n                                            label=\"Choose an option\"\r\n                                            onChange={(event) => setStatus(event.target.value)}\r\n                                            required\r\n                                        >\r\n                                            <MenuItem value=\"Present\">Present</MenuItem>\r\n                                            <MenuItem value=\"Absent\">Absent</MenuItem>\r\n                                        </Select>\r\n                                    </FormControl>\r\n                                    <FormControl>\r\n                                        <TextField\r\n                                            label=\"Select Date\"\r\n                                            type=\"date\"\r\n                                            value={date}\r\n                                            onChange={(event) => setDate(event.target.value)} required\r\n                                            InputLabelProps={{\r\n                                                shrink: true,\r\n                                            }}\r\n                                        />\r\n                                    </FormControl>\r\n                                </Stack>\r\n\r\n                                <PurpleButton\r\n                                    fullWidth\r\n                                    size=\"large\"\r\n                                    sx={{ mt: 3 }}\r\n                                    variant=\"contained\"\r\n                                    type=\"submit\"\r\n                                    disabled={loader}\r\n                                >\r\n                                    {loader ? <CircularProgress size={24} color=\"inherit\" /> : \"Submit\"}\r\n                                </PurpleButton>\r\n                            </form>\r\n                        </Box>\r\n                    </Box>\r\n                    <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n                </>\r\n            }\r\n        </>\r\n    )\r\n}\r\n\r\nexport default StudentAttendance", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentExamMarks.js", ["356", "357"], [], "import React, { useEffect, useState } from 'react'\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { useParams } from 'react-router-dom';\r\nimport { getUserDetails } from '../../../redux/userRelated/userHandle';\r\nimport { getSubjectList } from '../../../redux/sclassRelated/sclassHandle';\r\nimport { updateStudentFields } from '../../../redux/studentRelated/studentHandle';\r\n\r\nimport Popup from '../../../components/Popup';\r\nimport { BlueButton } from '../../../components/buttonStyles';\r\nimport {\r\n    Box, InputLabel,\r\n    MenuItem, Select,\r\n    Typography, Stack,\r\n    TextField, CircularProgress, FormControl\r\n} from '@mui/material';\r\n\r\nconst StudentExamMarks = ({ situation }) => {\r\n    const dispatch = useDispatch();\r\n    const { currentUser, userDetails, loading } = useSelector((state) => state.user);\r\n    const { subjectsList } = useSelector((state) => state.sclass);\r\n    const { response, error, statestatus } = useSelector((state) => state.student);\r\n    const params = useParams()\r\n\r\n    const [studentID, setStudentID] = useState(\"\");\r\n    const [subjectName, setSubjectName] = useState(\"\");\r\n    const [chosenSubName, setChosenSubName] = useState(\"\");\r\n    const [marksObtained, setMarksObtained] = useState(\"\");\r\n\r\n    const [showPopup, setShowPopup] = useState(false);\r\n    const [message, setMessage] = useState(\"\");\r\n    const [loader, setLoader] = useState(false)\r\n\r\n    useEffect(() => {\r\n        if (situation === \"Student\") {\r\n            setStudentID(params.id);\r\n            const stdID = params.id\r\n            dispatch(getUserDetails(stdID, \"Student\"));\r\n        }\r\n        else if (situation === \"Subject\") {\r\n            const { studentID, subjectID } = params\r\n            setStudentID(studentID);\r\n            dispatch(getUserDetails(studentID, \"Student\"));\r\n            setChosenSubName(subjectID);\r\n        }\r\n    }, [situation]);\r\n\r\n    useEffect(() => {\r\n        if (userDetails && userDetails.sclassName && situation === \"Student\") {\r\n            dispatch(getSubjectList(userDetails.sclassName._id, \"ClassSubjects\"));\r\n        }\r\n    }, [dispatch, userDetails]);\r\n\r\n    const changeHandler = (event) => {\r\n        const selectedSubject = subjectsList.find(\r\n            (subject) => subject.subName === event.target.value\r\n        );\r\n        setSubjectName(selectedSubject.subName);\r\n        setChosenSubName(selectedSubject._id);\r\n    }\r\n\r\n    const fields = { subName: chosenSubName, marksObtained }\r\n\r\n    const submitHandler = (event) => {\r\n        event.preventDefault()\r\n        setLoader(true)\r\n        dispatch(updateStudentFields(studentID, fields, \"UpdateExamResult\"))\r\n    }\r\n\r\n    useEffect(() => {\r\n        if (response) {\r\n            setLoader(false)\r\n            setShowPopup(true)\r\n            setMessage(response)\r\n        }\r\n        else if (error) {\r\n            setLoader(false)\r\n            setShowPopup(true)\r\n            setMessage(\"error\")\r\n        }\r\n        else if (statestatus === \"added\") {\r\n            setLoader(false)\r\n            setShowPopup(true)\r\n            setMessage(\"Done Successfully\")\r\n        }\r\n    }, [response, statestatus, error])\r\n\r\n    return (\r\n        <>\r\n            {loading\r\n                ?\r\n                <>\r\n                    <div>Loading...</div>\r\n                </>\r\n                :\r\n                <>\r\n                    <Box\r\n                        sx={{\r\n                            flex: '1 1 auto',\r\n                            alignItems: 'center',\r\n                            display: 'flex',\r\n                            justifyContent: 'center'\r\n                        }}\r\n                    >\r\n                        <Box\r\n                            sx={{\r\n                                maxWidth: 550,\r\n                                px: 3,\r\n                                py: '100px',\r\n                                width: '100%'\r\n                            }}\r\n                        >\r\n                            <Stack spacing={1} sx={{ mb: 3 }}>\r\n                                <Typography variant=\"h4\">\r\n                                    Student Name: {userDetails.name}\r\n                                </Typography>\r\n                                {currentUser.teachSubject &&\r\n                                    <Typography variant=\"h4\">\r\n                                        Subject Name: {currentUser.teachSubject?.subName}\r\n                                    </Typography>\r\n                                }\r\n                            </Stack>\r\n                            <form onSubmit={submitHandler}>\r\n                                <Stack spacing={3}>\r\n                                    {\r\n                                        situation === \"Student\" &&\r\n                                        <FormControl fullWidth>\r\n                                            <InputLabel id=\"demo-simple-select-label\">\r\n                                                Select Subject\r\n                                            </InputLabel>\r\n                                            <Select\r\n                                                labelId=\"demo-simple-select-label\"\r\n                                                id=\"demo-simple-select\"\r\n                                                value={subjectName}\r\n                                                label=\"Choose an option\"\r\n                                                onChange={changeHandler} required\r\n                                            >\r\n                                                {subjectsList ?\r\n                                                    subjectsList.map((subject, index) => (\r\n                                                        <MenuItem key={index} value={subject.subName}>\r\n                                                            {subject.subName}\r\n                                                        </MenuItem>\r\n                                                    ))\r\n                                                    :\r\n                                                    <MenuItem value=\"Select Subject\">\r\n                                                        Add Subjects For Marks\r\n                                                    </MenuItem>\r\n                                                }\r\n                                            </Select>\r\n                                        </FormControl>\r\n                                    }\r\n                                    <FormControl>\r\n                                        <TextField type=\"number\" label='Enter marks'\r\n                                            value={marksObtained} required\r\n                                            onChange={(e) => setMarksObtained(e.target.value)}\r\n                                            InputLabelProps={{\r\n                                                shrink: true,\r\n                                            }}\r\n                                        />\r\n                                    </FormControl>\r\n                                </Stack>\r\n                                <BlueButton\r\n                                    fullWidth\r\n                                    size=\"large\"\r\n                                    sx={{ mt: 3 }}\r\n                                    variant=\"contained\"\r\n                                    type=\"submit\"\r\n                                    disabled={loader}\r\n                                >\r\n                                    {loader ? <CircularProgress size={24} color=\"inherit\" /> : \"Submit\"}\r\n                                </BlueButton>\r\n                            </form>\r\n                        </Box>\r\n                    </Box>\r\n                    <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n                </>\r\n            }\r\n        </>\r\n    )\r\n}\r\n\r\nexport default StudentExamMarks", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\SeeComplains.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\AddStudent.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ShowStudents.js", ["358"], [], "import { useEffect } from 'react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { getAllStudents } from '../../../redux/studentRelated/studentHandle';\r\nimport { deleteUser } from '../../../redux/userRelated/userHandle';\r\nimport {\r\n    Paper, Box, IconButton\r\n} from '@mui/material';\r\nimport PersonRemoveIcon from '@mui/icons-material/PersonRemove';\r\nimport { BlackButton, BlueButton, GreenButton } from '../../../components/buttonStyles';\r\nimport TableTemplate from '../../../components/TableTemplate';\r\nimport PersonAddAlt1Icon from '@mui/icons-material/PersonAddAlt1';\r\nimport SpeedDialTemplate from '../../../components/SpeedDialTemplate';\r\n\r\nimport * as React from 'react';\r\nimport Button from '@mui/material/Button';\r\nimport ButtonGroup from '@mui/material/ButtonGroup';\r\n// import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';\r\nimport { KeyboardArrowUp, KeyboardArrowDown } from '@mui/icons-material';\r\nimport ClickAwayListener from '@mui/material/ClickAwayListener';\r\nimport Grow from '@mui/material/Grow';\r\nimport Popper from '@mui/material/Popper';\r\nimport MenuItem from '@mui/material/MenuItem';\r\nimport MenuList from '@mui/material/MenuList';\r\nimport Popup from '../../../components/Popup';\r\n\r\nconst ShowStudents = () => {\r\n\r\n    const navigate = useNavigate()\r\n    const dispatch = useDispatch();\r\n    const { studentsList, loading, error, response } = useSelector((state) => state.student);\r\n    const { currentUser } = useSelector(state => state.user)\r\n\r\n    useEffect(() => {\r\n        dispatch(getAllStudents(currentUser._id));\r\n    }, [currentUser._id, dispatch]);\r\n\r\n    if (error) {\r\n        console.log(error);\r\n    }\r\n\r\n    const [showPopup, setShowPopup] = React.useState(false);\r\n    const [message, setMessage] = React.useState(\"\");\r\n\r\n    const deleteHandler = (deleteID, address) => {\r\n        console.log(deleteID);\r\n        console.log(address);\r\n        setMessage(\"Sorry the delete function has been disabled for now.\")\r\n        setShowPopup(true)\r\n\r\n        // dispatch(deleteUser(deleteID, address))\r\n        //     .then(() => {\r\n        //         dispatch(getAllStudents(currentUser._id));\r\n        //     })\r\n    }\r\n\r\n    const studentColumns = [\r\n        { id: 'name', label: 'Name', minWidth: 170 },\r\n        { id: 'rollNum', label: 'Roll Number', minWidth: 100 },\r\n        { id: 'sclassName', label: 'Class', minWidth: 170 },\r\n    ]\r\n\r\n    const studentRows = studentsList && studentsList.length > 0 && studentsList.map((student) => {\r\n        return {\r\n            name: student.name,\r\n            rollNum: student.rollNum,\r\n            sclassName: student.sclassName.sclassName,\r\n            id: student._id,\r\n        };\r\n    })\r\n\r\n    const StudentButtonHaver = ({ row }) => {\r\n        const options = ['Take Attendance', 'Provide Marks'];\r\n\r\n        const [open, setOpen] = React.useState(false);\r\n        const anchorRef = React.useRef(null);\r\n        const [selectedIndex, setSelectedIndex] = React.useState(0);\r\n\r\n        const handleClick = () => {\r\n            console.info(`You clicked ${options[selectedIndex]}`);\r\n            if (selectedIndex === 0) {\r\n                handleAttendance();\r\n            } else if (selectedIndex === 1) {\r\n                handleMarks();\r\n            }\r\n        };\r\n\r\n        const handleAttendance = () => {\r\n            navigate(\"/Admin/students/student/attendance/\" + row.id)\r\n        }\r\n        const handleMarks = () => {\r\n            navigate(\"/Admin/students/student/marks/\" + row.id)\r\n        };\r\n\r\n        const handleMenuItemClick = (event, index) => {\r\n            setSelectedIndex(index);\r\n            setOpen(false);\r\n        };\r\n\r\n        const handleToggle = () => {\r\n            setOpen((prevOpen) => !prevOpen);\r\n        };\r\n\r\n        const handleClose = (event) => {\r\n            if (anchorRef.current && anchorRef.current.contains(event.target)) {\r\n                return;\r\n            }\r\n\r\n            setOpen(false);\r\n        };\r\n        return (\r\n            <>\r\n                <IconButton onClick={() => deleteHandler(row.id, \"Student\")}>\r\n                    <PersonRemoveIcon color=\"error\" />\r\n                </IconButton>\r\n                <BlueButton variant=\"contained\"\r\n                    onClick={() => navigate(\"/Admin/students/student/\" + row.id)}>\r\n                    View\r\n                </BlueButton>\r\n                <React.Fragment>\r\n                    <ButtonGroup variant=\"contained\" ref={anchorRef} aria-label=\"split button\">\r\n                        <Button onClick={handleClick}>{options[selectedIndex]}</Button>\r\n                        <BlackButton\r\n                            size=\"small\"\r\n                            aria-controls={open ? 'split-button-menu' : undefined}\r\n                            aria-expanded={open ? 'true' : undefined}\r\n                            aria-label=\"select merge strategy\"\r\n                            aria-haspopup=\"menu\"\r\n                            onClick={handleToggle}\r\n                        >\r\n                            {open ? <KeyboardArrowUp /> : <KeyboardArrowDown />}\r\n                        </BlackButton>\r\n                    </ButtonGroup>\r\n                    <Popper\r\n                        sx={{\r\n                            zIndex: 1,\r\n                        }}\r\n                        open={open}\r\n                        anchorEl={anchorRef.current}\r\n                        role={undefined}\r\n                        transition\r\n                        disablePortal\r\n                    >\r\n                        {({ TransitionProps, placement }) => (\r\n                            <Grow\r\n                                {...TransitionProps}\r\n                                style={{\r\n                                    transformOrigin:\r\n                                        placement === 'bottom' ? 'center top' : 'center bottom',\r\n                                }}\r\n                            >\r\n                                <Paper>\r\n                                    <ClickAwayListener onClickAway={handleClose}>\r\n                                        <MenuList id=\"split-button-menu\" autoFocusItem>\r\n                                            {options.map((option, index) => (\r\n                                                <MenuItem\r\n                                                    key={option}\r\n                                                    disabled={index === 2}\r\n                                                    selected={index === selectedIndex}\r\n                                                    onClick={(event) => handleMenuItemClick(event, index)}\r\n                                                >\r\n                                                    {option}\r\n                                                </MenuItem>\r\n                                            ))}\r\n                                        </MenuList>\r\n                                    </ClickAwayListener>\r\n                                </Paper>\r\n                            </Grow>\r\n                        )}\r\n                    </Popper>\r\n                </React.Fragment>\r\n            </>\r\n        );\r\n    };\r\n\r\n    const actions = [\r\n        {\r\n            icon: <PersonAddAlt1Icon color=\"primary\" />, name: 'Add New Student',\r\n            action: () => navigate(\"/Admin/addstudents\")\r\n        },\r\n        {\r\n            icon: <PersonRemoveIcon color=\"error\" />, name: 'Delete All Students',\r\n            action: () => deleteHandler(currentUser._id, \"Students\")\r\n        },\r\n    ];\r\n\r\n    return (\r\n        <>\r\n            {loading ?\r\n                <div>Loading...</div>\r\n                :\r\n                <>\r\n                    {response ?\r\n                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>\r\n                            <GreenButton variant=\"contained\" onClick={() => navigate(\"/Admin/addstudents\")}>\r\n                                Add Students\r\n                            </GreenButton>\r\n                        </Box>\r\n                        :\r\n                        <Paper sx={{ width: '100%', overflow: 'hidden' }}>\r\n                            {Array.isArray(studentsList) && studentsList.length > 0 &&\r\n                                <TableTemplate buttonHaver={StudentButtonHaver} columns={studentColumns} rows={studentRows} />\r\n                            }\r\n                            <SpeedDialTemplate actions={actions} />\r\n                        </Paper>\r\n                    }\r\n                </>\r\n            }\r\n            <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n        </>\r\n    );\r\n};\r\n\r\nexport default ShowStudents;", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ViewStudent.js", ["359", "360", "361", "362", "363"], [], "import React, { useEffect, useState } from 'react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { deleteUser, getUserDetails, updateUser } from '../../../redux/userRelated/userHandle';\r\nimport { useNavigate, useParams } from 'react-router-dom'\r\nimport { getSubjectList } from '../../../redux/sclassRelated/sclassHandle';\r\nimport { Box, Button, Collapse, IconButton, Table, TableBody, TableHead, Typography, Tab, Paper, BottomNavigation, BottomNavigationAction, Container } from '@mui/material';\r\nimport TabContext from '@mui/lab/TabContext';\r\nimport TabList from '@mui/lab/TabList';\r\nimport TabPanel from '@mui/lab/TabPanel';\r\nimport { KeyboardArrowUp, KeyboardArrowDown, Delete as DeleteIcon } from '@mui/icons-material';\r\nimport { removeStuff, updateStudentFields } from '../../../redux/studentRelated/studentHandle';\r\nimport { calculateOverallAttendancePercentage, calculateSubjectAttendancePercentage, groupAttendanceBySubject } from '../../../components/attendanceCalculator';\r\nimport CustomBarChart from '../../../components/CustomBarChart'\r\nimport CustomPieChart from '../../../components/CustomPieChart'\r\nimport { StyledTableCell, StyledTableRow } from '../../../components/styles';\r\n\r\nimport InsertChartIcon from '@mui/icons-material/InsertChart';\r\nimport InsertChartOutlinedIcon from '@mui/icons-material/InsertChartOutlined';\r\nimport TableChartIcon from '@mui/icons-material/TableChart';\r\nimport TableChartOutlinedIcon from '@mui/icons-material/TableChartOutlined';\r\nimport Popup from '../../../components/Popup';\r\n\r\nconst ViewStudent = () => {\r\n    const [showTab, setShowTab] = useState(false);\r\n\r\n    const navigate = useNavigate()\r\n    const params = useParams()\r\n    const dispatch = useDispatch()\r\n    const { userDetails, response, loading, error } = useSelector((state) => state.user);\r\n\r\n    const studentID = params.id\r\n    const address = \"Student\"\r\n\r\n    useEffect(() => {\r\n        dispatch(getUserDetails(studentID, address));\r\n    }, [dispatch, studentID])\r\n\r\n    useEffect(() => {\r\n        if (userDetails && userDetails.sclassName && userDetails.sclassName._id !== undefined) {\r\n            dispatch(getSubjectList(userDetails.sclassName._id, \"ClassSubjects\"));\r\n        }\r\n    }, [dispatch, userDetails]);\r\n\r\n    if (response) { console.log(response) }\r\n    else if (error) { console.log(error) }\r\n\r\n    const [name, setName] = useState('');\r\n    const [rollNum, setRollNum] = useState('');\r\n    const [password, setPassword] = useState('');\r\n    const [sclassName, setSclassName] = useState('');\r\n    const [studentSchool, setStudentSchool] = useState('');\r\n    const [subjectMarks, setSubjectMarks] = useState('');\r\n    const [subjectAttendance, setSubjectAttendance] = useState([]);\r\n\r\n    const [openStates, setOpenStates] = useState({});\r\n\r\n    const [showPopup, setShowPopup] = useState(false);\r\n    const [message, setMessage] = useState(\"\");\r\n\r\n    const handleOpen = (subId) => {\r\n        setOpenStates((prevState) => ({\r\n            ...prevState,\r\n            [subId]: !prevState[subId],\r\n        }));\r\n    };\r\n\r\n    const [value, setValue] = useState('1');\r\n\r\n    const handleChange = (event, newValue) => {\r\n        setValue(newValue);\r\n    };\r\n\r\n    const [selectedSection, setSelectedSection] = useState('table');\r\n    const handleSectionChange = (event, newSection) => {\r\n        setSelectedSection(newSection);\r\n    };\r\n\r\n    const fields = password === \"\"\r\n        ? { name, rollNum }\r\n        : { name, rollNum, password }\r\n\r\n    useEffect(() => {\r\n        if (userDetails) {\r\n            setName(userDetails.name || '');\r\n            setRollNum(userDetails.rollNum || '');\r\n            setSclassName(userDetails.sclassName || '');\r\n            setStudentSchool(userDetails.school || '');\r\n            setSubjectMarks(userDetails.examResult || '');\r\n            setSubjectAttendance(userDetails.attendance || []);\r\n        }\r\n    }, [userDetails]);\r\n\r\n    const submitHandler = (event) => {\r\n        event.preventDefault()\r\n        dispatch(updateUser(fields, studentID, address))\r\n            .then(() => {\r\n                dispatch(getUserDetails(studentID, address));\r\n            })\r\n            .catch((error) => {\r\n                console.error(error)\r\n            })\r\n    }\r\n\r\n    const deleteHandler = () => {\r\n        setMessage(\"Sorry the delete function has been disabled for now.\")\r\n        setShowPopup(true)\r\n\r\n        // dispatch(deleteUser(studentID, address))\r\n        //     .then(() => {\r\n        //         navigate(-1)\r\n        //     })\r\n    }\r\n\r\n    const removeHandler = (id, deladdress) => {\r\n        dispatch(removeStuff(id, deladdress))\r\n            .then(() => {\r\n                dispatch(getUserDetails(studentID, address));\r\n            })\r\n    }\r\n\r\n    const removeSubAttendance = (subId) => {\r\n        dispatch(updateStudentFields(studentID, { subId }, \"RemoveStudentSubAtten\"))\r\n            .then(() => {\r\n                dispatch(getUserDetails(studentID, address));\r\n            })\r\n    }\r\n\r\n    const overallAttendancePercentage = calculateOverallAttendancePercentage(subjectAttendance);\r\n    const overallAbsentPercentage = 100 - overallAttendancePercentage;\r\n\r\n    const chartData = [\r\n        { name: 'Present', value: overallAttendancePercentage },\r\n        { name: 'Absent', value: overallAbsentPercentage }\r\n    ];\r\n\r\n    const subjectData = Object.entries(groupAttendanceBySubject(subjectAttendance)).map(([subName, { subCode, present, sessions }]) => {\r\n        const subjectAttendancePercentage = calculateSubjectAttendancePercentage(present, sessions);\r\n        return {\r\n            subject: subName,\r\n            attendancePercentage: subjectAttendancePercentage,\r\n            totalClasses: sessions,\r\n            attendedClasses: present\r\n        };\r\n    });\r\n\r\n    const StudentAttendanceSection = () => {\r\n        const renderTableSection = () => {\r\n            return (\r\n                <>\r\n                    <h3>Attendance:</h3>\r\n                    <Table>\r\n                        <TableHead>\r\n                            <StyledTableRow>\r\n                                <StyledTableCell>Subject</StyledTableCell>\r\n                                <StyledTableCell>Present</StyledTableCell>\r\n                                <StyledTableCell>Total Sessions</StyledTableCell>\r\n                                <StyledTableCell>Attendance Percentage</StyledTableCell>\r\n                                <StyledTableCell align=\"center\">Actions</StyledTableCell>\r\n                            </StyledTableRow>\r\n                        </TableHead>\r\n                        {Object.entries(groupAttendanceBySubject(subjectAttendance)).map(([subName, { present, allData, subId, sessions }], index) => {\r\n                            const subjectAttendancePercentage = calculateSubjectAttendancePercentage(present, sessions);\r\n                            return (\r\n                                <TableBody key={index}>\r\n                                    <StyledTableRow>\r\n                                        <StyledTableCell>{subName}</StyledTableCell>\r\n                                        <StyledTableCell>{present}</StyledTableCell>\r\n                                        <StyledTableCell>{sessions}</StyledTableCell>\r\n                                        <StyledTableCell>{subjectAttendancePercentage}%</StyledTableCell>\r\n                                        <StyledTableCell align=\"center\">\r\n                                            <Button variant=\"contained\"\r\n                                                onClick={() => handleOpen(subId)}>\r\n                                                {openStates[subId] ? <KeyboardArrowUp /> : <KeyboardArrowDown />}Details\r\n                                            </Button>\r\n                                            <IconButton onClick={() => removeSubAttendance(subId)}>\r\n                                                <DeleteIcon color=\"error\" />\r\n                                            </IconButton>\r\n                                            <Button variant=\"contained\" sx={styles.attendanceButton}\r\n                                                onClick={() => navigate(`/Admin/subject/student/attendance/${studentID}/${subId}`)}>\r\n                                                Change\r\n                                            </Button>\r\n                                        </StyledTableCell>\r\n                                    </StyledTableRow>\r\n                                    <StyledTableRow>\r\n                                        <StyledTableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>\r\n                                            <Collapse in={openStates[subId]} timeout=\"auto\" unmountOnExit>\r\n                                                <Box sx={{ margin: 1 }}>\r\n                                                    <Typography variant=\"h6\" gutterBottom component=\"div\">\r\n                                                        Attendance Details\r\n                                                    </Typography>\r\n                                                    <Table size=\"small\" aria-label=\"purchases\">\r\n                                                        <TableHead>\r\n                                                            <StyledTableRow>\r\n                                                                <StyledTableCell>Date</StyledTableCell>\r\n                                                                <StyledTableCell align=\"right\">Status</StyledTableCell>\r\n                                                            </StyledTableRow>\r\n                                                        </TableHead>\r\n                                                        <TableBody>\r\n                                                            {allData.map((data, index) => {\r\n                                                                const date = new Date(data.date);\r\n                                                                const dateString = date.toString() !== \"Invalid Date\" ? date.toISOString().substring(0, 10) : \"Invalid Date\";\r\n                                                                return (\r\n                                                                    <StyledTableRow key={index}>\r\n                                                                        <StyledTableCell component=\"th\" scope=\"row\">\r\n                                                                            {dateString}\r\n                                                                        </StyledTableCell>\r\n                                                                        <StyledTableCell align=\"right\">{data.status}</StyledTableCell>\r\n                                                                    </StyledTableRow>\r\n                                                                )\r\n                                                            })}\r\n                                                        </TableBody>\r\n                                                    </Table>\r\n                                                </Box>\r\n                                            </Collapse>\r\n                                        </StyledTableCell>\r\n                                    </StyledTableRow>\r\n                                </TableBody>\r\n                            )\r\n                        }\r\n                        )}\r\n                    </Table>\r\n                    <div>\r\n                        Overall Attendance Percentage: {overallAttendancePercentage.toFixed(2)}%\r\n                    </div>\r\n                    <Button variant=\"contained\" color=\"error\" startIcon={<DeleteIcon />} onClick={() => removeHandler(studentID, \"RemoveStudentAtten\")}>Delete All</Button>\r\n                    <Button variant=\"contained\" sx={styles.styledButton} onClick={() => navigate(\"/Admin/students/student/attendance/\" + studentID)}>\r\n                        Add Attendance\r\n                    </Button>\r\n                </>\r\n            )\r\n        }\r\n        const renderChartSection = () => {\r\n            return (\r\n                <>\r\n                    <CustomBarChart chartData={subjectData} dataKey=\"attendancePercentage\" />\r\n                </>\r\n            )\r\n        }\r\n        return (\r\n            <>\r\n                {subjectAttendance && Array.isArray(subjectAttendance) && subjectAttendance.length > 0\r\n                    ?\r\n                    <>\r\n                        {selectedSection === 'table' && renderTableSection()}\r\n                        {selectedSection === 'chart' && renderChartSection()}\r\n\r\n                        <Paper sx={{ position: 'fixed', bottom: 0, left: 0, right: 0 }} elevation={3}>\r\n                            <BottomNavigation value={selectedSection} onChange={handleSectionChange} showLabels>\r\n                                <BottomNavigationAction\r\n                                    label=\"Table\"\r\n                                    value=\"table\"\r\n                                    icon={selectedSection === 'table' ? <TableChartIcon /> : <TableChartOutlinedIcon />}\r\n                                />\r\n                                <BottomNavigationAction\r\n                                    label=\"Chart\"\r\n                                    value=\"chart\"\r\n                                    icon={selectedSection === 'chart' ? <InsertChartIcon /> : <InsertChartOutlinedIcon />}\r\n                                />\r\n                            </BottomNavigation>\r\n                        </Paper>\r\n                    </>\r\n                    :\r\n                    <Button variant=\"contained\" sx={styles.styledButton} onClick={() => navigate(\"/Admin/students/student/attendance/\" + studentID)}>\r\n                        Add Attendance\r\n                    </Button>\r\n                }\r\n            </>\r\n        )\r\n    }\r\n\r\n    const StudentMarksSection = () => {\r\n        const renderTableSection = () => {\r\n            return (\r\n                <>\r\n                    <h3>Subject Marks:</h3>\r\n                    <Table>\r\n                        <TableHead>\r\n                            <StyledTableRow>\r\n                                <StyledTableCell>Subject</StyledTableCell>\r\n                                <StyledTableCell>Marks</StyledTableCell>\r\n                            </StyledTableRow>\r\n                        </TableHead>\r\n                        <TableBody>\r\n                            {subjectMarks.map((result, index) => {\r\n                                if (!result.subName || !result.marksObtained) {\r\n                                    return null;\r\n                                }\r\n                                return (\r\n                                    <StyledTableRow key={index}>\r\n                                        <StyledTableCell>{result.subName.subName}</StyledTableCell>\r\n                                        <StyledTableCell>{result.marksObtained}</StyledTableCell>\r\n                                    </StyledTableRow>\r\n                                );\r\n                            })}\r\n                        </TableBody>\r\n                    </Table>\r\n                    <Button variant=\"contained\" sx={styles.styledButton} onClick={() => navigate(\"/Admin/students/student/marks/\" + studentID)}>\r\n                        Add Marks\r\n                    </Button>\r\n                </>\r\n            )\r\n        }\r\n        const renderChartSection = () => {\r\n            return (\r\n                <>\r\n                    <CustomBarChart chartData={subjectMarks} dataKey=\"marksObtained\" />\r\n                </>\r\n            )\r\n        }\r\n        return (\r\n            <>\r\n                {subjectMarks && Array.isArray(subjectMarks) && subjectMarks.length > 0\r\n                    ?\r\n                    <>\r\n                        {selectedSection === 'table' && renderTableSection()}\r\n                        {selectedSection === 'chart' && renderChartSection()}\r\n\r\n                        <Paper sx={{ position: 'fixed', bottom: 0, left: 0, right: 0 }} elevation={3}>\r\n                            <BottomNavigation value={selectedSection} onChange={handleSectionChange} showLabels>\r\n                                <BottomNavigationAction\r\n                                    label=\"Table\"\r\n                                    value=\"table\"\r\n                                    icon={selectedSection === 'table' ? <TableChartIcon /> : <TableChartOutlinedIcon />}\r\n                                />\r\n                                <BottomNavigationAction\r\n                                    label=\"Chart\"\r\n                                    value=\"chart\"\r\n                                    icon={selectedSection === 'chart' ? <InsertChartIcon /> : <InsertChartOutlinedIcon />}\r\n                                />\r\n                            </BottomNavigation>\r\n                        </Paper>\r\n                    </>\r\n                    :\r\n                    <Button variant=\"contained\" sx={styles.styledButton} onClick={() => navigate(\"/Admin/students/student/marks/\" + studentID)}>\r\n                        Add Marks\r\n                    </Button>\r\n                }\r\n            </>\r\n        )\r\n    }\r\n\r\n    const StudentDetailsSection = () => {\r\n        return (\r\n            <div>\r\n                Name: {userDetails.name}\r\n                <br />\r\n                Roll Number: {userDetails.rollNum}\r\n                <br />\r\n                Class: {sclassName.sclassName}\r\n                <br />\r\n                School: {studentSchool.schoolName}\r\n                {\r\n                    subjectAttendance && Array.isArray(subjectAttendance) && subjectAttendance.length > 0 && (\r\n                        <CustomPieChart data={chartData} />\r\n                    )\r\n                }\r\n                <Button variant=\"contained\" sx={styles.styledButton} onClick={deleteHandler}>\r\n                    Delete\r\n                </Button>\r\n                <br />\r\n                {/* <Button variant=\"contained\" sx={styles.styledButton} className=\"show-tab\" onClick={() => { setShowTab(!showTab) }}>\r\n                    {\r\n                        showTab\r\n                            ? <KeyboardArrowUp />\r\n                            : <KeyboardArrowDown />\r\n                    }\r\n                    Edit Student\r\n                </Button>\r\n                <Collapse in={showTab} timeout=\"auto\" unmountOnExit>\r\n                    <div className=\"register\">\r\n                        <form className=\"registerForm\" onSubmit={submitHandler}>\r\n                            <span className=\"registerTitle\">Edit Details</span>\r\n                            <label>Name</label>\r\n                            <input className=\"registerInput\" type=\"text\" placeholder=\"Enter user's name...\"\r\n                                value={name}\r\n                                onChange={(event) => setName(event.target.value)}\r\n                                autoComplete=\"name\" required />\r\n\r\n                            <label>Roll Number</label>\r\n                            <input className=\"registerInput\" type=\"number\" placeholder=\"Enter user's Roll Number...\"\r\n                                value={rollNum}\r\n                                onChange={(event) => setRollNum(event.target.value)}\r\n                                required />\r\n\r\n                            <label>Password</label>\r\n                            <input className=\"registerInput\" type=\"password\" placeholder=\"Enter user's password...\"\r\n                                value={password}\r\n                                onChange={(event) => setPassword(event.target.value)}\r\n                                autoComplete=\"new-password\" />\r\n\r\n                            <button className=\"registerButton\" type=\"submit\" >Update</button>\r\n                        </form>\r\n                    </div>\r\n                </Collapse> */}\r\n            </div>\r\n        )\r\n    }\r\n\r\n    return (\r\n        <>\r\n            {loading\r\n                ?\r\n                <>\r\n                    <div>Loading...</div>\r\n                </>\r\n                :\r\n                <>\r\n                    <Box sx={{ width: '100%', typography: 'body1', }} >\r\n                        <TabContext value={value}>\r\n                            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\r\n                                <TabList onChange={handleChange} sx={{ position: 'fixed', width: '100%', bgcolor: 'background.paper', zIndex: 1 }}>\r\n                                    <Tab label=\"Details\" value=\"1\" />\r\n                                    <Tab label=\"Attendance\" value=\"2\" />\r\n                                    <Tab label=\"Marks\" value=\"3\" />\r\n                                </TabList>\r\n                            </Box>\r\n                            <Container sx={{ marginTop: \"3rem\", marginBottom: \"4rem\" }}>\r\n                                <TabPanel value=\"1\">\r\n                                    <StudentDetailsSection />\r\n                                </TabPanel>\r\n                                <TabPanel value=\"2\">\r\n                                    <StudentAttendanceSection />\r\n                                </TabPanel>\r\n                                <TabPanel value=\"3\">\r\n                                    <StudentMarksSection />\r\n                                </TabPanel>\r\n                            </Container>\r\n                        </TabContext>\r\n                    </Box>\r\n                </>\r\n            }\r\n            <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n\r\n        </>\r\n    )\r\n}\r\n\r\nexport default ViewStudent\r\n\r\nconst styles = {\r\n    attendanceButton: {\r\n        marginLeft: \"20px\",\r\n        backgroundColor: \"#270843\",\r\n        \"&:hover\": {\r\n            backgroundColor: \"#3f1068\",\r\n        }\r\n    },\r\n    styledButton: {\r\n        margin: \"20px\",\r\n        backgroundColor: \"#02250b\",\r\n        \"&:hover\": {\r\n            backgroundColor: \"#106312\",\r\n        }\r\n    }\r\n}", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\AddNotice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\ShowNotices.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ViewSubject.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ShowSubjects.js", ["364"], [], "import React, { useEffect, useState } from 'react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { getSubjectList } from '../../../redux/sclassRelated/sclassHandle';\r\nimport { deleteUser } from '../../../redux/userRelated/userHandle';\r\nimport PostAddIcon from '@mui/icons-material/PostAdd';\r\nimport {\r\n    Paper, Box, IconButton,\r\n} from '@mui/material';\r\nimport DeleteIcon from \"@mui/icons-material/Delete\";\r\nimport TableTemplate from '../../../components/TableTemplate';\r\nimport { BlueButton, GreenButton } from '../../../components/buttonStyles';\r\nimport SpeedDialTemplate from '../../../components/SpeedDialTemplate';\r\nimport Popup from '../../../components/Popup';\r\n\r\nconst ShowSubjects = () => {\r\n    const navigate = useNavigate()\r\n    const dispatch = useDispatch();\r\n    const { subjectsList, loading, error, response } = useSelector((state) => state.sclass);\r\n    const { currentUser } = useSelector(state => state.user)\r\n\r\n    useEffect(() => {\r\n        dispatch(getSubjectList(currentUser._id, \"AllSubjects\"));\r\n    }, [currentUser._id, dispatch]);\r\n\r\n    if (error) {\r\n        console.log(error);\r\n    }\r\n\r\n    const [showPopup, setShowPopup] = useState(false);\r\n    const [message, setMessage] = useState(\"\");\r\n\r\n    const deleteHandler = (deleteID, address) => {\r\n        console.log(deleteID);\r\n        console.log(address);\r\n        setMessage(\"Sorry the delete function has been disabled for now.\")\r\n        setShowPopup(true)\r\n\r\n        // dispatch(deleteUser(deleteID, address))\r\n        //     .then(() => {\r\n        //         dispatch(getSubjectList(currentUser._id, \"AllSubjects\"));\r\n        //     })\r\n    }\r\n\r\n    const subjectColumns = [\r\n        { id: 'subName', label: 'Sub Name', minWidth: 170 },\r\n        { id: 'sessions', label: 'Sessions', minWidth: 170 },\r\n        { id: 'sclassName', label: 'Class', minWidth: 170 },\r\n    ]\r\n\r\n    const subjectRows = subjectsList.map((subject) => {\r\n        return {\r\n            subName: subject.subName,\r\n            sessions: subject.sessions,\r\n            sclassName: subject.sclassName.sclassName,\r\n            sclassID: subject.sclassName._id,\r\n            id: subject._id,\r\n        };\r\n    })\r\n\r\n    const SubjectsButtonHaver = ({ row }) => {\r\n        return (\r\n            <>\r\n                <IconButton onClick={() => deleteHandler(row.id, \"Subject\")}>\r\n                    <DeleteIcon color=\"error\" />\r\n                </IconButton>\r\n                <BlueButton variant=\"contained\"\r\n                    onClick={() => navigate(`/Admin/subjects/subject/${row.sclassID}/${row.id}`)}>\r\n                    View\r\n                </BlueButton>\r\n            </>\r\n        );\r\n    };\r\n\r\n    const actions = [\r\n        {\r\n            icon: <PostAddIcon color=\"primary\" />, name: 'Add New Subject',\r\n            action: () => navigate(\"/Admin/subjects/chooseclass\")\r\n        },\r\n        {\r\n            icon: <DeleteIcon color=\"error\" />, name: 'Delete All Subjects',\r\n            action: () => deleteHandler(currentUser._id, \"Subjects\")\r\n        }\r\n    ];\r\n\r\n    return (\r\n        <>\r\n            {loading ?\r\n                <div>Loading...</div>\r\n                :\r\n                <>\r\n                    {response ?\r\n                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>\r\n                            <GreenButton variant=\"contained\"\r\n                                onClick={() => navigate(\"/Admin/subjects/chooseclass\")}>\r\n                                Add Subjects\r\n                            </GreenButton>\r\n                        </Box>\r\n                        :\r\n                        <Paper sx={{ width: '100%', overflow: 'hidden' }}>\r\n                            {Array.isArray(subjectsList) && subjectsList.length > 0 &&\r\n                                <TableTemplate buttonHaver={SubjectsButtonHaver} columns={subjectColumns} rows={subjectRows} />\r\n                            }\r\n                            <SpeedDialTemplate actions={actions} />\r\n                        </Paper>\r\n                    }\r\n                </>\r\n            }\r\n            <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n\r\n        </>\r\n    );\r\n};\r\n\r\nexport default ShowSubjects;", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherSideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherClassDetails.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\SubjectForm.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseClass.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ShowTeachers.js", ["365"], [], "import React, { useEffect, useState } from 'react'\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { useNavigate } from 'react-router-dom'\r\nimport { getAllTeachers } from '../../../redux/teacherRelated/teacherHandle';\r\nimport {\r\n    Paper, Table, TableBody, TableContainer,\r\n    TableHead, TablePagination, Button, Box, IconButton,\r\n} from '@mui/material';\r\nimport { deleteUser } from '../../../redux/userRelated/userHandle';\r\nimport PersonRemoveIcon from '@mui/icons-material/PersonRemove';\r\nimport { StyledTableCell, StyledTableRow } from '../../../components/styles';\r\nimport { BlueButton, GreenButton } from '../../../components/buttonStyles';\r\nimport PersonAddAlt1Icon from '@mui/icons-material/PersonAddAlt1';\r\nimport SpeedDialTemplate from '../../../components/SpeedDialTemplate';\r\nimport Popup from '../../../components/Popup';\r\n\r\nconst ShowTeachers = () => {\r\n    const [page, setPage] = useState(0);\r\n    const [rowsPerPage, setRowsPerPage] = useState(5);\r\n\r\n    const navigate = useNavigate();\r\n    const dispatch = useDispatch();\r\n    const { teachersList, loading, error, response } = useSelector((state) => state.teacher);\r\n    const { currentUser } = useSelector((state) => state.user);\r\n\r\n    useEffect(() => {\r\n        dispatch(getAllTeachers(currentUser._id));\r\n    }, [currentUser._id, dispatch]);\r\n\r\n    const [showPopup, setShowPopup] = useState(false);\r\n    const [message, setMessage] = useState(\"\");\r\n\r\n    if (loading) {\r\n        return <div>Loading...</div>;\r\n    } else if (response) {\r\n        return (\r\n            <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>\r\n                <GreenButton variant=\"contained\" onClick={() => navigate(\"/Admin/teachers/chooseclass\")}>\r\n                    Add Teacher\r\n                </GreenButton>\r\n            </Box>\r\n        );\r\n    } else if (error) {\r\n        console.log(error);\r\n    }\r\n\r\n    const deleteHandler = (deleteID, address) => {\r\n        console.log(deleteID);\r\n        console.log(address);\r\n        setMessage(\"Sorry the delete function has been disabled for now.\")\r\n        setShowPopup(true)\r\n\r\n        // dispatch(deleteUser(deleteID, address)).then(() => {\r\n        //     dispatch(getAllTeachers(currentUser._id));\r\n        // });\r\n    };\r\n\r\n    const columns = [\r\n        { id: 'name', label: 'Name', minWidth: 170 },\r\n        { id: 'teachSubject', label: 'Subject', minWidth: 100 },\r\n        { id: 'teachSclass', label: 'Class', minWidth: 170 },\r\n    ];\r\n\r\n    const rows = teachersList.map((teacher) => {\r\n        return {\r\n            name: teacher.name,\r\n            teachSubject: teacher.teachSubject?.subName || null,\r\n            teachSclass: teacher.teachSclass.sclassName,\r\n            teachSclassID: teacher.teachSclass._id,\r\n            id: teacher._id,\r\n        };\r\n    });\r\n\r\n    const actions = [\r\n        {\r\n            icon: <PersonAddAlt1Icon color=\"primary\" />, name: 'Add New Teacher',\r\n            action: () => navigate(\"/Admin/teachers/chooseclass\")\r\n        },\r\n        {\r\n            icon: <PersonRemoveIcon color=\"error\" />, name: 'Delete All Teachers',\r\n            action: () => deleteHandler(currentUser._id, \"Teachers\")\r\n        },\r\n    ];\r\n\r\n    return (\r\n        <Paper sx={{ width: '100%', overflow: 'hidden' }}>\r\n            <TableContainer>\r\n                <Table stickyHeader aria-label=\"sticky table\">\r\n                    <TableHead>\r\n                        <StyledTableRow>\r\n                            {columns.map((column) => (\r\n                                <StyledTableCell\r\n                                    key={column.id}\r\n                                    align={column.align}\r\n                                    style={{ minWidth: column.minWidth }}\r\n                                >\r\n                                    {column.label}\r\n                                </StyledTableCell>\r\n                            ))}\r\n                            <StyledTableCell align=\"center\">\r\n                                Actions\r\n                            </StyledTableCell>\r\n                        </StyledTableRow>\r\n                    </TableHead>\r\n                    <TableBody>\r\n                        {rows\r\n                            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)\r\n                            .map((row) => {\r\n                                return (\r\n                                    <StyledTableRow hover role=\"checkbox\" tabIndex={-1} key={row.id}>\r\n                                        {columns.map((column) => {\r\n                                            const value = row[column.id];\r\n                                            if (column.id === 'teachSubject') {\r\n                                                return (\r\n                                                    <StyledTableCell key={column.id} align={column.align}>\r\n                                                        {value ? (\r\n                                                            value\r\n                                                        ) : (\r\n                                                            <Button variant=\"contained\"\r\n                                                                onClick={() => {\r\n                                                                    navigate(`/Admin/teachers/choosesubject/${row.teachSclassID}/${row.id}`)\r\n                                                                }}>\r\n                                                                Add Subject\r\n                                                            </Button>\r\n                                                        )}\r\n                                                    </StyledTableCell>\r\n                                                );\r\n                                            }\r\n                                            return (\r\n                                                <StyledTableCell key={column.id} align={column.align}>\r\n                                                    {column.format && typeof value === 'number' ? column.format(value) : value}\r\n                                                </StyledTableCell>\r\n                                            );\r\n                                        })}\r\n                                        <StyledTableCell align=\"center\">\r\n                                            <IconButton onClick={() => deleteHandler(row.id, \"Teacher\")}>\r\n                                                <PersonRemoveIcon color=\"error\" />\r\n                                            </IconButton>\r\n                                            <BlueButton variant=\"contained\"\r\n                                                onClick={() => navigate(\"/Admin/teachers/teacher/\" + row.id)}>\r\n                                                View\r\n                                            </BlueButton>\r\n                                        </StyledTableCell>\r\n                                    </StyledTableRow>\r\n                                );\r\n                            })}\r\n                    </TableBody>\r\n                </Table>\r\n            </TableContainer>\r\n            <TablePagination\r\n                rowsPerPageOptions={[5, 10, 25, 100]}\r\n                component=\"div\"\r\n                count={rows.length}\r\n                rowsPerPage={rowsPerPage}\r\n                page={page}\r\n                onPageChange={(event, newPage) => setPage(newPage)}\r\n                onRowsPerPageChange={(event) => {\r\n                    setRowsPerPage(parseInt(event.target.value, 5));\r\n                    setPage(0);\r\n                }}\r\n            />\r\n\r\n            <SpeedDialTemplate actions={actions} />\r\n            <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n        </Paper >\r\n    );\r\n};\r\n\r\nexport default ShowTeachers", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseSubject.js", ["366"], [], "import React, { useEffect, useState } from 'react'\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { Box, Table, TableBody, TableContainer, TableHead, Typography, Paper } from '@mui/material'\r\nimport { useNavigate, useParams } from 'react-router-dom';\r\nimport { getTeacherFreeClassSubjects } from '../../../redux/sclassRelated/sclassHandle';\r\nimport { updateTeachSubject } from '../../../redux/teacherRelated/teacherHandle';\r\nimport { GreenButton, PurpleButton } from '../../../components/buttonStyles';\r\nimport { StyledTableCell, StyledTableRow } from '../../../components/styles';\r\n\r\nconst ChooseSubject = ({ situation }) => {\r\n    const params = useParams();\r\n    const navigate = useNavigate()\r\n    const dispatch = useDispatch();\r\n\r\n    const [classID, setClassID] = useState(\"\");\r\n    const [teacherID, setTeacherID] = useState(\"\");\r\n    const [loader, setLoader] = useState(false)\r\n\r\n    const { subjectsList, loading, error, response } = useSelector((state) => state.sclass);\r\n\r\n    useEffect(() => {\r\n        if (situation === \"Norm\") {\r\n            setClassID(params.id);\r\n            const classID = params.id\r\n            dispatch(getTeacherFreeClassSubjects(classID));\r\n        }\r\n        else if (situation === \"Teacher\") {\r\n            const { classID, teacherID } = params\r\n            setClassID(classID);\r\n            setTeacherID(teacherID);\r\n            dispatch(getTeacherFreeClassSubjects(classID));\r\n        }\r\n    }, [situation]);\r\n\r\n    if (loading) {\r\n        return <div>Loading...</div>;\r\n    } else if (response) {\r\n        return <div>\r\n            <h1>Sorry all subjects have teachers assigned already</h1>\r\n            <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>\r\n                <PurpleButton variant=\"contained\"\r\n                    onClick={() => navigate(\"/Admin/addsubject/\" + classID)}>\r\n                    Add Subjects\r\n                </PurpleButton>\r\n            </Box>\r\n        </div>;\r\n    } else if (error) {\r\n        console.log(error)\r\n    }\r\n\r\n    const updateSubjectHandler = (teacherId, teachSubject) => {\r\n        setLoader(true)\r\n        dispatch(updateTeachSubject(teacherId, teachSubject))\r\n        navigate(\"/Admin/teachers\")\r\n    }\r\n\r\n    return (\r\n        <Paper sx={{ width: '100%', overflow: 'hidden' }}>\r\n            <Typography variant=\"h6\" gutterBottom component=\"div\">\r\n                Choose a subject\r\n            </Typography>\r\n            <>\r\n                <TableContainer>\r\n                    <Table aria-label=\"sclasses table\">\r\n                        <TableHead>\r\n                            <StyledTableRow>\r\n                                <StyledTableCell></StyledTableCell>\r\n                                <StyledTableCell align=\"center\">Subject Name</StyledTableCell>\r\n                                <StyledTableCell align=\"center\">Subject Code</StyledTableCell>\r\n                                <StyledTableCell align=\"center\">Actions</StyledTableCell>\r\n                            </StyledTableRow>\r\n                        </TableHead>\r\n                        <TableBody>\r\n                            {Array.isArray(subjectsList) && subjectsList.length > 0 && subjectsList.map((subject, index) => (\r\n                                <StyledTableRow key={subject._id}>\r\n                                    <StyledTableCell component=\"th\" scope=\"row\" style={{ color: \"white\" }}>\r\n                                        {index + 1}\r\n                                    </StyledTableCell>\r\n                                    <StyledTableCell align=\"center\">{subject.subName}</StyledTableCell>\r\n                                    <StyledTableCell align=\"center\">{subject.subCode}</StyledTableCell>\r\n                                    <StyledTableCell align=\"center\">\r\n                                        {situation === \"Norm\" ?\r\n                                            <GreenButton variant=\"contained\"\r\n                                                onClick={() => navigate(\"/Admin/teachers/addteacher/\" + subject._id)}>\r\n                                                Choose\r\n                                            </GreenButton>\r\n                                            :\r\n                                            <GreenButton variant=\"contained\" disabled={loader}\r\n                                                onClick={() => updateSubjectHandler(teacherID, subject._id)}>\r\n                                                {loader ? (\r\n                                                    <div className=\"load\"></div>\r\n                                                ) : (\r\n                                                    'Choose Sub'\r\n                                                )}\r\n                                            </GreenButton>}\r\n                                    </StyledTableCell>\r\n                                </StyledTableRow>\r\n                            ))}\r\n                        </TableBody>\r\n                    </Table>\r\n                </TableContainer>\r\n            </>\r\n        </Paper >\r\n    );\r\n};\r\n\r\nexport default ChooseSubject;", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\AddTeacher.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherComplain.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\TeacherDetails.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherViewStudent.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\AddClass.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ShowClasses.js", ["367"], [], "import { useEffect, useState } from 'react';\r\nimport { IconButton, Box, Menu, MenuItem, ListItemIcon, Tooltip } from '@mui/material';\r\nimport DeleteIcon from \"@mui/icons-material/Delete\";\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { deleteUser } from '../../../redux/userRelated/userHandle';\r\nimport { getAllSclasses } from '../../../redux/sclassRelated/sclassHandle';\r\nimport { BlueButton, GreenButton } from '../../../components/buttonStyles';\r\nimport TableTemplate from '../../../components/TableTemplate';\r\n\r\nimport SpeedDialIcon from '@mui/material/SpeedDialIcon';\r\nimport PostAddIcon from '@mui/icons-material/PostAdd';\r\nimport PersonAddAlt1Icon from '@mui/icons-material/PersonAddAlt1';\r\nimport AddCardIcon from '@mui/icons-material/AddCard';\r\nimport styled from 'styled-components';\r\nimport SpeedDialTemplate from '../../../components/SpeedDialTemplate';\r\nimport Popup from '../../../components/Popup';\r\n\r\nconst ShowClasses = () => {\r\n  const navigate = useNavigate()\r\n  const dispatch = useDispatch();\r\n\r\n  const { sclassesList, loading, error, getresponse } = useSelector((state) => state.sclass);\r\n  const { currentUser } = useSelector(state => state.user)\r\n\r\n  const adminID = currentUser._id\r\n\r\n  useEffect(() => {\r\n    dispatch(getAllSclasses(adminID, \"Sclass\"));\r\n  }, [adminID, dispatch]);\r\n\r\n  if (error) {\r\n    console.log(error)\r\n  }\r\n\r\n  const [showPopup, setShowPopup] = useState(false);\r\n  const [message, setMessage] = useState(\"\");\r\n\r\n  const deleteHandler = (deleteID, address) => {\r\n    console.log(deleteID);\r\n    console.log(address);\r\n    setMessage(\"Sorry the delete function has been disabled for now.\")\r\n    setShowPopup(true)\r\n    // dispatch(deleteUser(deleteID, address))\r\n    //   .then(() => {\r\n    //     dispatch(getAllSclasses(adminID, \"Sclass\"));\r\n    //   })\r\n  }\r\n\r\n  const sclassColumns = [\r\n    { id: 'name', label: 'Class Name', minWidth: 170 },\r\n  ]\r\n\r\n  const sclassRows = sclassesList && sclassesList.length > 0 && sclassesList.map((sclass) => {\r\n    return {\r\n      name: sclass.sclassName,\r\n      id: sclass._id,\r\n    };\r\n  })\r\n\r\n  const SclassButtonHaver = ({ row }) => {\r\n    const actions = [\r\n      { icon: <PostAddIcon />, name: 'Add Subjects', action: () => navigate(\"/Admin/addsubject/\" + row.id) },\r\n      { icon: <PersonAddAlt1Icon />, name: 'Add Student', action: () => navigate(\"/Admin/class/addstudents/\" + row.id) },\r\n    ];\r\n    return (\r\n      <ButtonContainer>\r\n        <IconButton onClick={() => deleteHandler(row.id, \"Sclass\")} color=\"secondary\">\r\n          <DeleteIcon color=\"error\" />\r\n        </IconButton>\r\n        <BlueButton variant=\"contained\"\r\n          onClick={() => navigate(\"/Admin/classes/class/\" + row.id)}>\r\n          View\r\n        </BlueButton>\r\n        <ActionMenu actions={actions} />\r\n      </ButtonContainer>\r\n    );\r\n  };\r\n\r\n  const ActionMenu = ({ actions }) => {\r\n    const [anchorEl, setAnchorEl] = useState(null);\r\n\r\n    const open = Boolean(anchorEl);\r\n\r\n    const handleClick = (event) => {\r\n      setAnchorEl(event.currentTarget);\r\n    };\r\n    const handleClose = () => {\r\n      setAnchorEl(null);\r\n    };\r\n    return (\r\n      <>\r\n        <Box sx={{ display: 'flex', alignItems: 'center', textAlign: 'center' }}>\r\n          <Tooltip title=\"Add Students & Subjects\">\r\n            <IconButton\r\n              onClick={handleClick}\r\n              size=\"small\"\r\n              sx={{ ml: 2 }}\r\n              aria-controls={open ? 'account-menu' : undefined}\r\n              aria-haspopup=\"true\"\r\n              aria-expanded={open ? 'true' : undefined}\r\n            >\r\n              <h5>Add</h5>\r\n              <SpeedDialIcon />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Box>\r\n        <Menu\r\n          anchorEl={anchorEl}\r\n          id=\"account-menu\"\r\n          open={open}\r\n          onClose={handleClose}\r\n          onClick={handleClose}\r\n          PaperProps={{\r\n            elevation: 0,\r\n            sx: styles.styledPaper,\r\n          }}\r\n          transformOrigin={{ horizontal: 'right', vertical: 'top' }}\r\n          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}\r\n        >\r\n          {actions.map((action) => (\r\n            <MenuItem onClick={action.action}>\r\n              <ListItemIcon fontSize=\"small\">\r\n                {action.icon}\r\n              </ListItemIcon>\r\n              {action.name}\r\n            </MenuItem>\r\n          ))}\r\n        </Menu>\r\n      </>\r\n    );\r\n  }\r\n\r\n  const actions = [\r\n    {\r\n      icon: <AddCardIcon color=\"primary\" />, name: 'Add New Class',\r\n      action: () => navigate(\"/Admin/addclass\")\r\n    },\r\n    {\r\n      icon: <DeleteIcon color=\"error\" />, name: 'Delete All Classes',\r\n      action: () => deleteHandler(adminID, \"Sclasses\")\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      {loading ?\r\n        <div>Loading...</div>\r\n        :\r\n        <>\r\n          {getresponse ?\r\n            <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>\r\n              <GreenButton variant=\"contained\" onClick={() => navigate(\"/Admin/addclass\")}>\r\n                Add Class\r\n              </GreenButton>\r\n            </Box>\r\n            :\r\n            <>\r\n              {Array.isArray(sclassesList) && sclassesList.length > 0 &&\r\n                <TableTemplate buttonHaver={SclassButtonHaver} columns={sclassColumns} rows={sclassRows} />\r\n              }\r\n              <SpeedDialTemplate actions={actions} />\r\n            </>}\r\n        </>\r\n      }\r\n      <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ShowClasses;\r\n\r\nconst styles = {\r\n  styledPaper: {\r\n    overflow: 'visible',\r\n    filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',\r\n    mt: 1.5,\r\n    '& .MuiAvatar-root': {\r\n      width: 32,\r\n      height: 32,\r\n      ml: -0.5,\r\n      mr: 1,\r\n    },\r\n    '&:before': {\r\n      content: '\"\"',\r\n      display: 'block',\r\n      position: 'absolute',\r\n      top: 0,\r\n      right: 14,\r\n      width: 10,\r\n      height: 10,\r\n      bgcolor: 'background.paper',\r\n      transform: 'translateY(-50%) rotate(45deg)',\r\n      zIndex: 0,\r\n    },\r\n  }\r\n}\r\n\r\nconst ButtonContainer = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 1rem;\r\n`;", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSubjects.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ClassDetails.js", ["368", "369"], [], "import { useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { useNavigate, useParams } from 'react-router-dom'\r\nimport { getClassDetails, getClassStudents, getSubjectList } from \"../../../redux/sclassRelated/sclassHandle\";\r\nimport { deleteUser } from '../../../redux/userRelated/userHandle';\r\nimport {\r\n    Box, Container, Typography, Tab, IconButton\r\n} from '@mui/material';\r\nimport TabContext from '@mui/lab/TabContext';\r\nimport TabList from '@mui/lab/TabList';\r\nimport TabPanel from '@mui/lab/TabPanel';\r\nimport { resetSubjects } from \"../../../redux/sclassRelated/sclassSlice\";\r\nimport { BlueButton, GreenButton, PurpleButton } from \"../../../components/buttonStyles\";\r\nimport TableTemplate from \"../../../components/TableTemplate\";\r\nimport PersonAddAlt1Icon from '@mui/icons-material/PersonAddAlt1';\r\nimport PersonRemoveIcon from '@mui/icons-material/PersonRemove';\r\nimport SpeedDialTemplate from \"../../../components/SpeedDialTemplate\";\r\nimport Popup from \"../../../components/Popup\";\r\nimport DeleteIcon from \"@mui/icons-material/Delete\";\r\nimport PostAddIcon from '@mui/icons-material/PostAdd';\r\n\r\nconst ClassDetails = () => {\r\n    const params = useParams()\r\n    const navigate = useNavigate()\r\n    const dispatch = useDispatch();\r\n    const { subjectsList, sclassStudents, sclassDetails, loading, error, response, getresponse } = useSelector((state) => state.sclass);\r\n\r\n    const classID = params.id\r\n\r\n    useEffect(() => {\r\n        dispatch(getClassDetails(classID, \"Sclass\"));\r\n        dispatch(getSubjectList(classID, \"ClassSubjects\"))\r\n        dispatch(getClassStudents(classID));\r\n    }, [dispatch, classID])\r\n\r\n    if (error) {\r\n        console.log(error)\r\n    }\r\n\r\n    const [value, setValue] = useState('1');\r\n\r\n    const handleChange = (event, newValue) => {\r\n        setValue(newValue);\r\n    };\r\n\r\n    const [showPopup, setShowPopup] = useState(false);\r\n    const [message, setMessage] = useState(\"\");\r\n\r\n    const deleteHandler = (deleteID, address) => {\r\n        console.log(deleteID);\r\n        console.log(address);\r\n        setMessage(\"Sorry the delete function has been disabled for now.\")\r\n        setShowPopup(true)\r\n        // dispatch(deleteUser(deleteID, address))\r\n        //     .then(() => {\r\n        //         dispatch(getClassStudents(classID));\r\n        //         dispatch(resetSubjects())\r\n        //         dispatch(getSubjectList(classID, \"ClassSubjects\"))\r\n        //     })\r\n    }\r\n\r\n    const subjectColumns = [\r\n        { id: 'name', label: 'Subject Name', minWidth: 170 },\r\n        { id: 'code', label: 'Subject Code', minWidth: 100 },\r\n    ]\r\n\r\n    const subjectRows = subjectsList && subjectsList.length > 0 && subjectsList.map((subject) => {\r\n        return {\r\n            name: subject.subName,\r\n            code: subject.subCode,\r\n            id: subject._id,\r\n        };\r\n    })\r\n\r\n    const SubjectsButtonHaver = ({ row }) => {\r\n        return (\r\n            <>\r\n                <IconButton onClick={() => deleteHandler(row.id, \"Subject\")}>\r\n                    <DeleteIcon color=\"error\" />\r\n                </IconButton>\r\n                <BlueButton\r\n                    variant=\"contained\"\r\n                    onClick={() => {\r\n                        navigate(`/Admin/class/subject/${classID}/${row.id}`)\r\n                    }}\r\n                >\r\n                    View\r\n                </BlueButton >\r\n            </>\r\n        );\r\n    };\r\n\r\n    const subjectActions = [\r\n        {\r\n            icon: <PostAddIcon color=\"primary\" />, name: 'Add New Subject',\r\n            action: () => navigate(\"/Admin/addsubject/\" + classID)\r\n        },\r\n        {\r\n            icon: <DeleteIcon color=\"error\" />, name: 'Delete All Subjects',\r\n            action: () => deleteHandler(classID, \"SubjectsClass\")\r\n        }\r\n    ];\r\n\r\n    const ClassSubjectsSection = () => {\r\n        return (\r\n            <>\r\n                {response ?\r\n                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>\r\n                        <GreenButton\r\n                            variant=\"contained\"\r\n                            onClick={() => navigate(\"/Admin/addsubject/\" + classID)}\r\n                        >\r\n                            Add Subjects\r\n                        </GreenButton>\r\n                    </Box>\r\n                    :\r\n                    <>\r\n                        <Typography variant=\"h5\" gutterBottom>\r\n                            Subjects List:\r\n                        </Typography>\r\n\r\n                        <TableTemplate buttonHaver={SubjectsButtonHaver} columns={subjectColumns} rows={subjectRows} />\r\n                        <SpeedDialTemplate actions={subjectActions} />\r\n                    </>\r\n                }\r\n            </>\r\n        )\r\n    }\r\n\r\n    const studentColumns = [\r\n        { id: 'name', label: 'Name', minWidth: 170 },\r\n        { id: 'rollNum', label: 'Roll Number', minWidth: 100 },\r\n    ]\r\n\r\n    const studentRows = sclassStudents.map((student) => {\r\n        return {\r\n            name: student.name,\r\n            rollNum: student.rollNum,\r\n            id: student._id,\r\n        };\r\n    })\r\n\r\n    const StudentsButtonHaver = ({ row }) => {\r\n        return (\r\n            <>\r\n                <IconButton onClick={() => deleteHandler(row.id, \"Student\")}>\r\n                    <PersonRemoveIcon color=\"error\" />\r\n                </IconButton>\r\n                <BlueButton\r\n                    variant=\"contained\"\r\n                    onClick={() => navigate(\"/Admin/students/student/\" + row.id)}\r\n                >\r\n                    View\r\n                </BlueButton>\r\n                <PurpleButton\r\n                    variant=\"contained\"\r\n                    onClick={() =>\r\n                        navigate(\"/Admin/students/student/attendance/\" + row.id)\r\n                    }\r\n                >\r\n                    Attendance\r\n                </PurpleButton>\r\n            </>\r\n        );\r\n    };\r\n\r\n    const studentActions = [\r\n        {\r\n            icon: <PersonAddAlt1Icon color=\"primary\" />, name: 'Add New Student',\r\n            action: () => navigate(\"/Admin/class/addstudents/\" + classID)\r\n        },\r\n        {\r\n            icon: <PersonRemoveIcon color=\"error\" />, name: 'Delete All Students',\r\n            action: () => deleteHandler(classID, \"StudentsClass\")\r\n        },\r\n    ];\r\n\r\n    const ClassStudentsSection = () => {\r\n        return (\r\n            <>\r\n                {getresponse ? (\r\n                    <>\r\n                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>\r\n                            <GreenButton\r\n                                variant=\"contained\"\r\n                                onClick={() => navigate(\"/Admin/class/addstudents/\" + classID)}\r\n                            >\r\n                                Add Students\r\n                            </GreenButton>\r\n                        </Box>\r\n                    </>\r\n                ) : (\r\n                    <>\r\n                        <Typography variant=\"h5\" gutterBottom>\r\n                            Students List:\r\n                        </Typography>\r\n\r\n                        <TableTemplate buttonHaver={StudentsButtonHaver} columns={studentColumns} rows={studentRows} />\r\n                        <SpeedDialTemplate actions={studentActions} />\r\n                    </>\r\n                )}\r\n            </>\r\n        )\r\n    }\r\n\r\n    const ClassTeachersSection = () => {\r\n        return (\r\n            <>\r\n                Teachers\r\n            </>\r\n        )\r\n    }\r\n\r\n    const ClassDetailsSection = () => {\r\n        const numberOfSubjects = subjectsList.length;\r\n        const numberOfStudents = sclassStudents.length;\r\n\r\n        return (\r\n            <>\r\n                <Typography variant=\"h4\" align=\"center\" gutterBottom>\r\n                    Class Details\r\n                </Typography>\r\n                <Typography variant=\"h5\" gutterBottom>\r\n                    This is Class {sclassDetails && sclassDetails.sclassName}\r\n                </Typography>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                    Number of Subjects: {numberOfSubjects}\r\n                </Typography>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                    Number of Students: {numberOfStudents}\r\n                </Typography>\r\n                {getresponse &&\r\n                    <GreenButton\r\n                        variant=\"contained\"\r\n                        onClick={() => navigate(\"/Admin/class/addstudents/\" + classID)}\r\n                    >\r\n                        Add Students\r\n                    </GreenButton>\r\n                }\r\n                {response &&\r\n                    <GreenButton\r\n                        variant=\"contained\"\r\n                        onClick={() => navigate(\"/Admin/addsubject/\" + classID)}\r\n                    >\r\n                        Add Subjects\r\n                    </GreenButton>\r\n                }\r\n            </>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <>\r\n            {loading ? (\r\n                <div>Loading...</div>\r\n            ) : (\r\n                <>\r\n                    <Box sx={{ width: '100%', typography: 'body1', }} >\r\n                        <TabContext value={value}>\r\n                            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\r\n                                <TabList onChange={handleChange} sx={{ position: 'fixed', width: '100%', bgcolor: 'background.paper', zIndex: 1 }}>\r\n                                    <Tab label=\"Details\" value=\"1\" />\r\n                                    <Tab label=\"Subjects\" value=\"2\" />\r\n                                    <Tab label=\"Students\" value=\"3\" />\r\n                                    <Tab label=\"Teachers\" value=\"4\" />\r\n                                </TabList>\r\n                            </Box>\r\n                            <Container sx={{ marginTop: \"3rem\", marginBottom: \"4rem\" }}>\r\n                                <TabPanel value=\"1\">\r\n                                    <ClassDetailsSection />\r\n                                </TabPanel>\r\n                                <TabPanel value=\"2\">\r\n                                    <ClassSubjectsSection />\r\n                                </TabPanel>\r\n                                <TabPanel value=\"3\">\r\n                                    <ClassStudentsSection />\r\n                                </TabPanel>\r\n                                <TabPanel value=\"4\">\r\n                                    <ClassTeachersSection />\r\n                                </TabPanel>\r\n                            </Container>\r\n                        </TabContext>\r\n                    </Box>\r\n                </>\r\n            )}\r\n            <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n        </>\r\n    );\r\n};\r\n\r\nexport default ClassDetails;", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentComplain.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\ViewStdAttendance.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SeeNotice.js", ["370"], [], "import React, { useEffect } from 'react'\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { getAllNotices } from '../redux/noticeRelated/noticeHandle';\r\nimport { Paper } from '@mui/material';\r\nimport TableViewTemplate from './TableViewTemplate';\r\n\r\nconst SeeNotice = () => {\r\n    const dispatch = useDispatch();\r\n\r\n    const { currentUser, currentRole } = useSelector(state => state.user);\r\n    const { noticesList, loading, error, response } = useSelector((state) => state.notice);\r\n\r\n    useEffect(() => {\r\n        if (currentRole === \"Admin\") {\r\n            dispatch(getAllNotices(currentUser._id, \"Notice\"));\r\n        }\r\n        else {\r\n            dispatch(getAllNotices(currentUser.school._id, \"Notice\"));\r\n        }\r\n    }, [dispatch]);\r\n\r\n    if (error) {\r\n        console.log(error);\r\n    }\r\n\r\n    const noticeColumns = [\r\n        { id: 'title', label: 'Title', minWidth: 170 },\r\n        { id: 'details', label: 'Details', minWidth: 100 },\r\n        { id: 'date', label: 'Date', minWidth: 170 },\r\n    ];\r\n\r\n    const noticeRows = noticesList.map((notice) => {\r\n        const date = new Date(notice.date);\r\n        const dateString = date.toString() !== \"Invalid Date\" ? date.toISOString().substring(0, 10) : \"Invalid Date\";\r\n        return {\r\n            title: notice.title,\r\n            details: notice.details,\r\n            date: dateString,\r\n            id: notice._id,\r\n        };\r\n    });\r\n    return (\r\n        <div style={{ marginTop: '50px', marginRight: '20px' }}>\r\n            {loading ? (\r\n                <div style={{ fontSize: '20px' }}>Loading...</div>\r\n            ) : response ? (\r\n                <div style={{ fontSize: '20px' }}>No Notices to Show Right Now</div>\r\n            ) : (\r\n                <>\r\n                    <h3 style={{ fontSize: '30px', marginBottom: '40px' }}>Notices</h3>\r\n                    <Paper sx={{ width: '100%', overflow: 'hidden' }}>\r\n                        {Array.isArray(noticesList) && noticesList.length > 0 &&\r\n                            <TableViewTemplate columns={noticeColumns} rows={noticeRows} />\r\n                        }\r\n                    </Paper>\r\n                </>\r\n            )}\r\n        </div>\r\n\r\n    )\r\n}\r\n\r\nexport default SeeNotice", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SpeedDialTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\attendanceCalculator.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomBarChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomPieChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableViewTemplate.js", [], [], {"ruleId": "371", "severity": 1, "message": "372", "line": 10, "column": 5, "nodeType": "373", "messageId": "374", "endLine": 10, "endColumn": 21}, {"ruleId": "375", "severity": 1, "message": "376", "line": 46, "column": 8, "nodeType": "377", "endLine": 46, "endColumn": 19, "suggestions": "378"}, {"ruleId": "375", "severity": 1, "message": "379", "line": 52, "column": 8, "nodeType": "377", "endLine": 52, "endColumn": 31, "suggestions": "380"}, {"ruleId": "375", "severity": 1, "message": "376", "line": 45, "column": 8, "nodeType": "377", "endLine": 45, "endColumn": 19, "suggestions": "381"}, {"ruleId": "375", "severity": 1, "message": "379", "line": 51, "column": 8, "nodeType": "377", "endLine": 51, "endColumn": 31, "suggestions": "382"}, {"ruleId": "371", "severity": 1, "message": "383", "line": 5, "column": 10, "nodeType": "373", "messageId": "374", "endLine": 5, "endColumn": 20}, {"ruleId": "371", "severity": 1, "message": "383", "line": 3, "column": 10, "nodeType": "373", "messageId": "374", "endLine": 3, "endColumn": 20}, {"ruleId": "371", "severity": 1, "message": "384", "line": 24, "column": 12, "nodeType": "373", "messageId": "374", "endLine": 24, "endColumn": 19}, {"ruleId": "371", "severity": 1, "message": "385", "line": 24, "column": 21, "nodeType": "373", "messageId": "374", "endLine": 24, "endColumn": 31}, {"ruleId": "371", "severity": 1, "message": "386", "line": 49, "column": 22, "nodeType": "373", "messageId": "374", "endLine": 49, "endColumn": 33}, {"ruleId": "371", "severity": 1, "message": "387", "line": 93, "column": 11, "nodeType": "373", "messageId": "374", "endLine": 93, "endColumn": 24}, {"ruleId": "371", "severity": 1, "message": "383", "line": 5, "column": 10, "nodeType": "373", "messageId": "374", "endLine": 5, "endColumn": 20}, {"ruleId": "371", "severity": 1, "message": "383", "line": 9, "column": 10, "nodeType": "373", "messageId": "374", "endLine": 9, "endColumn": 20}, {"ruleId": "375", "severity": 1, "message": "376", "line": 33, "column": 8, "nodeType": "377", "endLine": 33, "endColumn": 19, "suggestions": "388"}, {"ruleId": "371", "severity": 1, "message": "383", "line": 6, "column": 10, "nodeType": "373", "messageId": "374", "endLine": 6, "endColumn": 20}, {"ruleId": "371", "severity": 1, "message": "383", "line": 5, "column": 10, "nodeType": "373", "messageId": "374", "endLine": 5, "endColumn": 20}, {"ruleId": "371", "severity": 1, "message": "389", "line": 12, "column": 10, "nodeType": "373", "messageId": "374", "endLine": 12, "endColumn": 23}, {"ruleId": "375", "severity": 1, "message": "390", "line": 20, "column": 8, "nodeType": "377", "endLine": 20, "endColumn": 18, "suggestions": "391"}, "no-unused-vars", "'getDeleteSuccess' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch' and 'params'. Either include them or remove the dependency array.", "ArrayExpression", ["392"], "React Hook useEffect has a missing dependency: 'situation'. Either include it or remove the dependency array.", ["393"], ["394"], ["395"], "'deleteUser' is defined but never used.", "'showTab' is assigned a value but never used.", "'setShowTab' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'submitHandler' is assigned a value but never used.", ["396"], "'resetSubjects' is defined but never used.", "React Hook useEffect has missing dependencies: 'currentRole', 'currentUser._id', and 'currentUser.school._id'. Either include them or remove the dependency array.", ["397"], {"desc": "398", "fix": "399"}, {"desc": "400", "fix": "401"}, {"desc": "398", "fix": "402"}, {"desc": "400", "fix": "403"}, {"desc": "398", "fix": "404"}, {"desc": "405", "fix": "406"}, "Update the dependencies array to be: [dispatch, params, situation]", {"range": "407", "text": "408"}, "Update the dependencies array to be: [dispatch, situation, userDetails]", {"range": "409", "text": "410"}, {"range": "411", "text": "408"}, {"range": "412", "text": "410"}, {"range": "413", "text": "408"}, "Update the dependencies array to be: [currentRole, currentUser._id, currentUser.school._id, dispatch]", {"range": "414", "text": "415"}, [1899, 1910], "[dispatch, params, situation]", [2122, 2145], "[dispatch, situation, userDetails]", [1867, 1878], [2090, 2113], [1458, 1469], [747, 757], "[currentR<PERSON>, currentUser._id, currentUser.school._id, dispatch]"]