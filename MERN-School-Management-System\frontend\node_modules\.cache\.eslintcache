[{"A:\\11\\MERN-School-Management-System\\frontend\\src\\index.js": "1", "A:\\11\\MERN-School-Management-System\\frontend\\src\\App.js": "2", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\store.js": "3", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Homepage.js": "4", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\LoginPage.js": "5", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\ChooseUser.js": "6", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminDashboard.js": "7", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminRegisterPage.js": "8", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherDashboard.js": "9", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentDashboard.js": "10", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userSlice.js": "11", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentSlice.js": "12", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeSlice.js": "13", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassSlice.js": "14", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherSlice.js": "15", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainSlice.js": "16", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Logout.js": "17", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userHandle.js": "18", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminProfile.js": "19", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\SideBar.js": "20", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminHomePage.js": "21", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\Popup.js": "22", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\styles.js": "23", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\AccountMenu.js": "24", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\buttonStyles.js": "25", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentAttendance.js": "26", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentExamMarks.js": "27", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\SeeComplains.js": "28", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\AddStudent.js": "29", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ShowStudents.js": "30", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ViewStudent.js": "31", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\AddNotice.js": "32", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\ShowNotices.js": "33", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ViewSubject.js": "34", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ShowSubjects.js": "35", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherSideBar.js": "36", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherClassDetails.js": "37", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\SubjectForm.js": "38", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseClass.js": "39", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ShowTeachers.js": "40", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseSubject.js": "41", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\AddTeacher.js": "42", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherComplain.js": "43", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherHomePage.js": "44", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\TeacherDetails.js": "45", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentHomePage.js": "46", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherProfile.js": "47", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherViewStudent.js": "48", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSideBar.js": "49", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\AddClass.js": "50", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ShowClasses.js": "51", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSubjects.js": "52", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ClassDetails.js": "53", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentProfile.js": "54", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentComplain.js": "55", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\ViewStdAttendance.js": "56", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SeeNotice.js": "57", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentHandle.js": "58", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassHandle.js": "59", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherHandle.js": "60", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableTemplate.js": "61", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SpeedDialTemplate.js": "62", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainHandle.js": "63", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\attendanceCalculator.js": "64", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomBarChart.js": "65", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomPieChart.js": "66", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeHandle.js": "67", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableViewTemplate.js": "68", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\EnhancedStatCard.js": "69", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\AttendanceChart.js": "70", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\PerformanceChart.js": "71", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\RecentActivity.js": "72", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\QuickActions.js": "73", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\CalendarWidget.js": "74", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\FinancialChart.js": "75", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\NotificationCenter.js": "76", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeToggle.js": "77", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeProvider.js": "78", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\layout\\ResponsiveSidebar.js": "79", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\feeManagement\\FeeManagement.js": "80", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\transport\\TransportManagement.js": "81", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\documents\\DocumentGeneration.js": "82", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentInfo\\StudentInfo.js": "83", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminSettings.js": "84", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\EnhancedViewStudent.js": "85", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\inventory\\InventoryManagement.js": "86", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\inventory\\AddInventoryItem.js": "87", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\cms\\PageBuilder.js": "88", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\cms\\CMSManagement.js": "89", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\cms\\MediaManager.js": "90", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\EnhancedLoginPage.js": "91"}, {"size": 388, "mtime": 1751091352643, "results": "92", "hashOfConfig": "93"}, {"size": 1685, "mtime": 1751115179157, "results": "94", "hashOfConfig": "93"}, {"size": 713, "mtime": 1751091352677, "results": "95", "hashOfConfig": "93"}, {"size": 2591, "mtime": 1751115192286, "results": "96", "hashOfConfig": "93"}, {"size": 10685, "mtime": 1751114270323, "results": "97", "hashOfConfig": "93"}, {"size": 2833, "mtime": 1751100705093, "results": "98", "hashOfConfig": "93"}, {"size": 11765, "mtime": 1751113601306, "results": "99", "hashOfConfig": "93"}, {"size": 9826, "mtime": 1751091352650, "results": "100", "hashOfConfig": "93"}, {"size": 4699, "mtime": 1751091352670, "results": "101", "hashOfConfig": "93"}, {"size": 4230, "mtime": 1751091352662, "results": "102", "hashOfConfig": "93"}, {"size": 2909, "mtime": 1751091352677, "results": "103", "hashOfConfig": "93"}, {"size": 1483, "mtime": 1751091352677, "results": "104", "hashOfConfig": "93"}, {"size": 1014, "mtime": 1751091352677, "results": "105", "hashOfConfig": "93"}, {"size": 2643, "mtime": 1751091352677, "results": "106", "hashOfConfig": "93"}, {"size": 1440, "mtime": 1751091352677, "results": "107", "hashOfConfig": "93"}, {"size": 1028, "mtime": 1751091352672, "results": "108", "hashOfConfig": "93"}, {"size": 1800, "mtime": 1751091352643, "results": "109", "hashOfConfig": "93"}, {"size": 3835, "mtime": 1751111833332, "results": "110", "hashOfConfig": "93"}, {"size": 4480, "mtime": 1751091352648, "results": "111", "hashOfConfig": "93"}, {"size": 4467, "mtime": 1751091352650, "results": "112", "hashOfConfig": "113"}, {"size": 7554, "mtime": 1751104361797, "results": "114", "hashOfConfig": "93"}, {"size": 1562, "mtime": 1751091352637, "results": "115", "hashOfConfig": "93"}, {"size": 2382, "mtime": 1751091352643, "results": "116", "hashOfConfig": "93"}, {"size": 3501, "mtime": 1751111367409, "results": "117", "hashOfConfig": "93"}, {"size": 1933, "mtime": 1751091352639, "results": "118", "hashOfConfig": "93"}, {"size": 8909, "mtime": 1751091352656, "results": "119", "hashOfConfig": "93"}, {"size": 7905, "mtime": 1751091352656, "results": "120", "hashOfConfig": "93"}, {"size": 2165, "mtime": 1751091352656, "results": "121", "hashOfConfig": "93"}, {"size": 23300, "mtime": 1751111313379, "results": "122", "hashOfConfig": "93"}, {"size": 8747, "mtime": 1751091352656, "results": "123", "hashOfConfig": "93"}, {"size": 22050, "mtime": 1751091352656, "results": "124", "hashOfConfig": "93"}, {"size": 2792, "mtime": 1751091352653, "results": "125", "hashOfConfig": "93"}, {"size": 3697, "mtime": 1751091352653, "results": "126", "hashOfConfig": "93"}, {"size": 7183, "mtime": 1751091352659, "results": "127", "hashOfConfig": "93"}, {"size": 4297, "mtime": 1751091352659, "results": "128", "hashOfConfig": "93"}, {"size": 2939, "mtime": 1751091352672, "results": "129", "hashOfConfig": "93"}, {"size": 7137, "mtime": 1751091352669, "results": "130", "hashOfConfig": "93"}, {"size": 6961, "mtime": 1751091352659, "results": "131", "hashOfConfig": "93"}, {"size": 2823, "mtime": 1751091352662, "results": "132", "hashOfConfig": "93"}, {"size": 7878, "mtime": 1751091352662, "results": "133", "hashOfConfig": "93"}, {"size": 5134, "mtime": 1751091352662, "results": "134", "hashOfConfig": "93"}, {"size": 3772, "mtime": 1751091352662, "results": "135", "hashOfConfig": "93"}, {"size": 146, "mtime": 1751091352669, "results": "136", "hashOfConfig": "93"}, {"size": 3747, "mtime": 1751091352670, "results": "137", "hashOfConfig": "93"}, {"size": 2405, "mtime": 1751091352662, "results": "138", "hashOfConfig": "93"}, {"size": 5332, "mtime": 1751091352662, "results": "139", "hashOfConfig": "93"}, {"size": 1317, "mtime": 1751091352670, "results": "140", "hashOfConfig": "93"}, {"size": 12169, "mtime": 1751091352672, "results": "141", "hashOfConfig": "93"}, {"size": 3195, "mtime": 1751091352662, "results": "142", "hashOfConfig": "93"}, {"size": 11812, "mtime": 1751115935523, "results": "143", "hashOfConfig": "93"}, {"size": 16863, "mtime": 1751121584724, "results": "144", "hashOfConfig": "93"}, {"size": 6014, "mtime": 1751091352662, "results": "145", "hashOfConfig": "93"}, {"size": 11441, "mtime": 1751115845314, "results": "146", "hashOfConfig": "93"}, {"size": 3931, "mtime": 1751091352662, "results": "147", "hashOfConfig": "93"}, {"size": 4228, "mtime": 1751091352662, "results": "148", "hashOfConfig": "93"}, {"size": 9767, "mtime": 1751091352662, "results": "149", "hashOfConfig": "93"}, {"size": 2233, "mtime": 1751091352639, "results": "150", "hashOfConfig": "93"}, {"size": 1558, "mtime": 1751093107198, "results": "151", "hashOfConfig": "93"}, {"size": 2895, "mtime": 1751121606777, "results": "152", "hashOfConfig": "93"}, {"size": 1383, "mtime": 1751093059721, "results": "153", "hashOfConfig": "93"}, {"size": 3386, "mtime": 1751091352639, "results": "154", "hashOfConfig": "93"}, {"size": 887, "mtime": 1751091352639, "results": "155", "hashOfConfig": "93"}, {"size": 621, "mtime": 1751093204280, "results": "156", "hashOfConfig": "93"}, {"size": 1996, "mtime": 1751091352639, "results": "157", "hashOfConfig": "93"}, {"size": 6469, "mtime": 1751091352629, "results": "158", "hashOfConfig": "93"}, {"size": 3161, "mtime": 1751091352629, "results": "159", "hashOfConfig": "93"}, {"size": 617, "mtime": 1751093177974, "results": "160", "hashOfConfig": "93"}, {"size": 3018, "mtime": 1751091352639, "results": "161", "hashOfConfig": "93"}, {"size": 3781, "mtime": 1751104338304, "results": "162", "hashOfConfig": "93"}, {"size": 2540, "mtime": 1751101038420, "results": "163", "hashOfConfig": "93"}, {"size": 2516, "mtime": 1751101053307, "results": "164", "hashOfConfig": "93"}, {"size": 4394, "mtime": 1751101121649, "results": "165", "hashOfConfig": "93"}, {"size": 3664, "mtime": 1751104279550, "results": "166", "hashOfConfig": "93"}, {"size": 5436, "mtime": 1751101223218, "results": "167", "hashOfConfig": "93"}, {"size": 3811, "mtime": 1751101073787, "results": "168", "hashOfConfig": "93"}, {"size": 6826, "mtime": 1751101255344, "results": "169", "hashOfConfig": "93"}, {"size": 908, "mtime": 1751101186045, "results": "170", "hashOfConfig": "93"}, {"size": 3711, "mtime": 1751101175499, "results": "171", "hashOfConfig": "93"}, {"size": 10827, "mtime": 1751120751125, "results": "172", "hashOfConfig": "93"}, {"size": 14046, "mtime": 1751102262286, "results": "173", "hashOfConfig": "93"}, {"size": 12806, "mtime": 1751102695142, "results": "174", "hashOfConfig": "93"}, {"size": 11821, "mtime": 1751102469493, "results": "175", "hashOfConfig": "93"}, {"size": 19126, "mtime": 1751111818968, "results": "176", "hashOfConfig": "93"}, {"size": 25082, "mtime": 1751111169508, "results": "177", "hashOfConfig": "93"}, {"size": 17065, "mtime": 1751112046968, "results": "178", "hashOfConfig": "93"}, {"size": 28930, "mtime": 1751113017450, "results": "179", "hashOfConfig": "93"}, {"size": 16940, "mtime": 1751112579256, "results": "180", "hashOfConfig": "93"}, {"size": 18318, "mtime": 1751113362593, "results": "181", "hashOfConfig": "93"}, {"size": 46965, "mtime": 1751114879099, "results": "182", "hashOfConfig": "93"}, {"size": 15610, "mtime": 1751113489441, "results": "183", "hashOfConfig": "93"}, {"size": 38815, "mtime": 1751115161750, "results": "184", "hashOfConfig": "93"}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kq195y", {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, "1pf3lve", {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "A:\\11\\MERN-School-Management-System\\frontend\\src\\index.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\App.js", ["459"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\store.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Homepage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\LoginPage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\ChooseUser.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminDashboard.js", ["460"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminRegisterPage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Logout.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\SideBar.js", [], [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminHomePage.js", ["461", "462", "463", "464", "465"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\Popup.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\styles.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\AccountMenu.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\buttonStyles.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentAttendance.js", ["466", "467"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentExamMarks.js", ["468", "469"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\SeeComplains.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\AddStudent.js", ["470", "471", "472", "473"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ShowStudents.js", ["474"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ViewStudent.js", ["475", "476", "477", "478", "479"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\AddNotice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\ShowNotices.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ViewSubject.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ShowSubjects.js", ["480"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherSideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherClassDetails.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\SubjectForm.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseClass.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ShowTeachers.js", ["481"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseSubject.js", ["482"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\AddTeacher.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherComplain.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\TeacherDetails.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherViewStudent.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\AddClass.js", ["483"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ShowClasses.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSubjects.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ClassDetails.js", ["484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentComplain.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\ViewStdAttendance.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SeeNotice.js", ["504"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SpeedDialTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\attendanceCalculator.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomBarChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomPieChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableViewTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\EnhancedStatCard.js", ["505"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\AttendanceChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\PerformanceChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\RecentActivity.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\QuickActions.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\CalendarWidget.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\FinancialChart.js", ["506"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\NotificationCenter.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeToggle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeProvider.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\layout\\ResponsiveSidebar.js", ["507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\feeManagement\\FeeManagement.js", ["518"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\transport\\TransportManagement.js", ["519", "520", "521"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\documents\\DocumentGeneration.js", ["522"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentInfo\\StudentInfo.js", ["523", "524", "525", "526"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminSettings.js", ["527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\EnhancedViewStudent.js", ["539", "540", "541", "542", "543", "544"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\inventory\\InventoryManagement.js", ["545", "546", "547", "548", "549"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\inventory\\AddInventoryItem.js", ["550"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\cms\\PageBuilder.js", ["551", "552", "553", "554"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\cms\\CMSManagement.js", ["555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\cms\\MediaManager.js", ["574"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\EnhancedLoginPage.js", ["575", "576", "577", "578", "579"], [], {"ruleId": "580", "severity": 1, "message": "581", "line": 8, "column": 8, "nodeType": "582", "messageId": "583", "endLine": 8, "endColumn": 17}, {"ruleId": "580", "severity": 1, "message": "584", "line": 44, "column": 8, "nodeType": "582", "messageId": "583", "endLine": 44, "endColumn": 19}, {"ruleId": "580", "severity": 1, "message": "585", "line": 31, "column": 17, "nodeType": "582", "messageId": "583", "endLine": 31, "endColumn": 31}, {"ruleId": "580", "severity": 1, "message": "586", "line": 32, "column": 13, "nodeType": "582", "messageId": "583", "endLine": 32, "endColumn": 23}, {"ruleId": "580", "severity": 1, "message": "587", "line": 39, "column": 11, "nodeType": "582", "messageId": "583", "endLine": 39, "endColumn": 19}, {"ruleId": "580", "severity": 1, "message": "588", "line": 60, "column": 11, "nodeType": "582", "messageId": "583", "endLine": 60, "endColumn": 25}, {"ruleId": "580", "severity": 1, "message": "589", "line": 61, "column": 11, "nodeType": "582", "messageId": "583", "endLine": 61, "endColumn": 23}, {"ruleId": "590", "severity": 1, "message": "591", "line": 46, "column": 8, "nodeType": "592", "endLine": 46, "endColumn": 19, "suggestions": "593"}, {"ruleId": "590", "severity": 1, "message": "594", "line": 52, "column": 8, "nodeType": "592", "endLine": 52, "endColumn": 31, "suggestions": "595"}, {"ruleId": "590", "severity": 1, "message": "591", "line": 45, "column": 8, "nodeType": "592", "endLine": 45, "endColumn": 19, "suggestions": "596"}, {"ruleId": "590", "severity": 1, "message": "594", "line": 51, "column": 8, "nodeType": "592", "endLine": 51, "endColumn": 31, "suggestions": "597"}, {"ruleId": "580", "severity": 1, "message": "598", "line": 23, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 23, "endColumn": 10}, {"ruleId": "580", "severity": 1, "message": "599", "line": 26, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 26, "endColumn": 8}, {"ruleId": "580", "severity": 1, "message": "600", "line": 33, "column": 18, "nodeType": "582", "messageId": "583", "endLine": 33, "endColumn": 33}, {"ruleId": "580", "severity": 1, "message": "601", "line": 98, "column": 12, "nodeType": "582", "messageId": "583", "endLine": 98, "endColumn": 26}, {"ruleId": "580", "severity": 1, "message": "602", "line": 5, "column": 10, "nodeType": "582", "messageId": "583", "endLine": 5, "endColumn": 20}, {"ruleId": "580", "severity": 1, "message": "602", "line": 3, "column": 10, "nodeType": "582", "messageId": "583", "endLine": 3, "endColumn": 20}, {"ruleId": "580", "severity": 1, "message": "603", "line": 24, "column": 12, "nodeType": "582", "messageId": "583", "endLine": 24, "endColumn": 19}, {"ruleId": "580", "severity": 1, "message": "604", "line": 24, "column": 21, "nodeType": "582", "messageId": "583", "endLine": 24, "endColumn": 31}, {"ruleId": "580", "severity": 1, "message": "605", "line": 49, "column": 22, "nodeType": "582", "messageId": "583", "endLine": 49, "endColumn": 33}, {"ruleId": "580", "severity": 1, "message": "606", "line": 93, "column": 11, "nodeType": "582", "messageId": "583", "endLine": 93, "endColumn": 24}, {"ruleId": "580", "severity": 1, "message": "602", "line": 5, "column": 10, "nodeType": "582", "messageId": "583", "endLine": 5, "endColumn": 20}, {"ruleId": "580", "severity": 1, "message": "602", "line": 9, "column": 10, "nodeType": "582", "messageId": "583", "endLine": 9, "endColumn": 20}, {"ruleId": "590", "severity": 1, "message": "591", "line": 33, "column": 8, "nodeType": "592", "endLine": 33, "endColumn": 19, "suggestions": "607"}, {"ruleId": "580", "severity": 1, "message": "608", "line": 23, "column": 8, "nodeType": "582", "messageId": "583", "endLine": 23, "endColumn": 14}, {"ruleId": "580", "severity": 1, "message": "602", "line": 5, "column": 10, "nodeType": "582", "messageId": "583", "endLine": 5, "endColumn": 20}, {"ruleId": "580", "severity": 1, "message": "609", "line": 12, "column": 5, "nodeType": "582", "messageId": "583", "endLine": 12, "endColumn": 10}, {"ruleId": "580", "severity": 1, "message": "610", "line": 13, "column": 5, "nodeType": "582", "messageId": "583", "endLine": 13, "endColumn": 9}, {"ruleId": "580", "severity": 1, "message": "611", "line": 14, "column": 5, "nodeType": "582", "messageId": "583", "endLine": 14, "endColumn": 16}, {"ruleId": "580", "severity": 1, "message": "612", "line": 15, "column": 5, "nodeType": "582", "messageId": "583", "endLine": 15, "endColumn": 9}, {"ruleId": "580", "severity": 1, "message": "613", "line": 16, "column": 5, "nodeType": "582", "messageId": "583", "endLine": 16, "endColumn": 9}, {"ruleId": "580", "severity": 1, "message": "614", "line": 17, "column": 5, "nodeType": "582", "messageId": "583", "endLine": 17, "endColumn": 11}, {"ruleId": "580", "severity": 1, "message": "598", "line": 18, "column": 5, "nodeType": "582", "messageId": "583", "endLine": 18, "endColumn": 12}, {"ruleId": "580", "severity": 1, "message": "615", "line": 19, "column": 5, "nodeType": "582", "messageId": "583", "endLine": 19, "endColumn": 9}, {"ruleId": "580", "severity": 1, "message": "616", "line": 20, "column": 5, "nodeType": "582", "messageId": "583", "endLine": 20, "endColumn": 13}, {"ruleId": "580", "severity": 1, "message": "617", "line": 21, "column": 5, "nodeType": "582", "messageId": "583", "endLine": 21, "endColumn": 17}, {"ruleId": "580", "severity": 1, "message": "618", "line": 22, "column": 5, "nodeType": "582", "messageId": "583", "endLine": 22, "endColumn": 17}, {"ruleId": "580", "severity": 1, "message": "619", "line": 23, "column": 5, "nodeType": "582", "messageId": "583", "endLine": 23, "endColumn": 11}, {"ruleId": "580", "severity": 1, "message": "620", "line": 28, "column": 10, "nodeType": "582", "messageId": "583", "endLine": 28, "endColumn": 23}, {"ruleId": "580", "severity": 1, "message": "621", "line": 30, "column": 8, "nodeType": "582", "messageId": "583", "endLine": 30, "endColumn": 17}, {"ruleId": "580", "severity": 1, "message": "622", "line": 31, "column": 8, "nodeType": "582", "messageId": "583", "endLine": 31, "endColumn": 17}, {"ruleId": "580", "severity": 1, "message": "623", "line": 32, "column": 8, "nodeType": "582", "messageId": "583", "endLine": 32, "endColumn": 16}, {"ruleId": "580", "severity": 1, "message": "624", "line": 33, "column": 8, "nodeType": "582", "messageId": "583", "endLine": 33, "endColumn": 21}, {"ruleId": "580", "severity": 1, "message": "625", "line": 35, "column": 8, "nodeType": "582", "messageId": "583", "endLine": 35, "endColumn": 21}, {"ruleId": "580", "severity": 1, "message": "626", "line": 36, "column": 8, "nodeType": "582", "messageId": "583", "endLine": 36, "endColumn": 18}, {"ruleId": "590", "severity": 1, "message": "627", "line": 20, "column": 8, "nodeType": "592", "endLine": 20, "endColumn": 18, "suggestions": "628"}, {"ruleId": "580", "severity": 1, "message": "629", "line": 2, "column": 34, "nodeType": "582", "messageId": "583", "endLine": 2, "endColumn": 44}, {"ruleId": "580", "severity": 1, "message": "630", "line": 2, "column": 61, "nodeType": "582", "messageId": "583", "endLine": 2, "endColumn": 67}, {"ruleId": "580", "severity": 1, "message": "631", "line": 15, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 15, "endColumn": 11}, {"ruleId": "580", "severity": 1, "message": "632", "line": 27, "column": 13, "nodeType": "582", "messageId": "583", "endLine": 27, "endColumn": 23}, {"ruleId": "580", "severity": 1, "message": "586", "line": 38, "column": 13, "nodeType": "582", "messageId": "583", "endLine": 38, "endColumn": 23}, {"ruleId": "580", "severity": 1, "message": "624", "line": 39, "column": 16, "nodeType": "582", "messageId": "583", "endLine": 39, "endColumn": 29}, {"ruleId": "580", "severity": 1, "message": "633", "line": 40, "column": 21, "nodeType": "582", "messageId": "583", "endLine": 40, "endColumn": 39}, {"ruleId": "580", "severity": 1, "message": "634", "line": 41, "column": 11, "nodeType": "582", "messageId": "583", "endLine": 41, "endColumn": 19}, {"ruleId": "580", "severity": 1, "message": "635", "line": 42, "column": 15, "nodeType": "582", "messageId": "583", "endLine": 42, "endColumn": 27}, {"ruleId": "580", "severity": 1, "message": "636", "line": 44, "column": 12, "nodeType": "582", "messageId": "583", "endLine": 44, "endColumn": 21}, {"ruleId": "580", "severity": 1, "message": "637", "line": 45, "column": 15, "nodeType": "582", "messageId": "583", "endLine": 45, "endColumn": 27}, {"ruleId": "580", "severity": 1, "message": "638", "line": 46, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 46, "endColumn": 13}, {"ruleId": "580", "severity": 1, "message": "639", "line": 47, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 47, "endColumn": 13}, {"ruleId": "580", "severity": 1, "message": "640", "line": 35, "column": 18, "nodeType": "582", "messageId": "583", "endLine": 35, "endColumn": 33}, {"ruleId": "580", "severity": 1, "message": "641", "line": 32, "column": 15, "nodeType": "582", "messageId": "583", "endLine": 32, "endColumn": 27}, {"ruleId": "580", "severity": 1, "message": "642", "line": 34, "column": 14, "nodeType": "582", "messageId": "583", "endLine": 34, "endColumn": 25}, {"ruleId": "580", "severity": 1, "message": "640", "line": 35, "column": 18, "nodeType": "582", "messageId": "583", "endLine": 35, "endColumn": 33}, {"ruleId": "580", "severity": 1, "message": "643", "line": 11, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 11, "endColumn": 12}, {"ruleId": "580", "severity": 1, "message": "644", "line": 43, "column": 11, "nodeType": "582", "messageId": "583", "endLine": 43, "endColumn": 19}, {"ruleId": "580", "severity": 1, "message": "645", "line": 52, "column": 22, "nodeType": "582", "messageId": "583", "endLine": 52, "endColumn": 36}, {"ruleId": "580", "severity": 1, "message": "646", "line": 94, "column": 34, "nodeType": "582", "messageId": "583", "endLine": 94, "endColumn": 39}, {"ruleId": "580", "severity": 1, "message": "647", "line": 94, "column": 41, "nodeType": "582", "messageId": "583", "endLine": 94, "endColumn": 49}, {"ruleId": "580", "severity": 1, "message": "648", "line": 1, "column": 27, "nodeType": "582", "messageId": "583", "endLine": 1, "endColumn": 36}, {"ruleId": "580", "severity": 1, "message": "598", "line": 14, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 14, "endColumn": 10}, {"ruleId": "580", "severity": 1, "message": "629", "line": 15, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 15, "endColumn": 13}, {"ruleId": "580", "severity": 1, "message": "649", "line": 26, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 26, "endColumn": 9}, {"ruleId": "580", "severity": 1, "message": "650", "line": 27, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 27, "endColumn": 14}, {"ruleId": "580", "severity": 1, "message": "651", "line": 28, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 28, "endColumn": 16}, {"ruleId": "580", "severity": 1, "message": "652", "line": 29, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 29, "endColumn": 16}, {"ruleId": "580", "severity": 1, "message": "653", "line": 39, "column": 11, "nodeType": "582", "messageId": "583", "endLine": 39, "endColumn": 19}, {"ruleId": "580", "severity": 1, "message": "654", "line": 43, "column": 15, "nodeType": "582", "messageId": "583", "endLine": 43, "endColumn": 27}, {"ruleId": "580", "severity": 1, "message": "655", "line": 114, "column": 9, "nodeType": "582", "messageId": "583", "endLine": 114, "endColumn": 17}, {"ruleId": "580", "severity": 1, "message": "656", "line": 121, "column": 10, "nodeType": "582", "messageId": "583", "endLine": 121, "endColumn": 20}, {"ruleId": "580", "severity": 1, "message": "657", "line": 121, "column": 22, "nodeType": "582", "messageId": "583", "endLine": 121, "endColumn": 35}, {"ruleId": "580", "severity": 1, "message": "598", "line": 19, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 19, "endColumn": 10}, {"ruleId": "580", "severity": 1, "message": "658", "line": 32, "column": 12, "nodeType": "582", "messageId": "583", "endLine": 32, "endColumn": 21}, {"ruleId": "580", "severity": 1, "message": "659", "line": 33, "column": 12, "nodeType": "582", "messageId": "583", "endLine": 33, "endColumn": 21}, {"ruleId": "580", "severity": 1, "message": "660", "line": 35, "column": 11, "nodeType": "582", "messageId": "583", "endLine": 35, "endColumn": 19}, {"ruleId": "580", "severity": 1, "message": "661", "line": 36, "column": 16, "nodeType": "582", "messageId": "583", "endLine": 36, "endColumn": 29}, {"ruleId": "580", "severity": 1, "message": "647", "line": 83, "column": 24, "nodeType": "582", "messageId": "583", "endLine": 83, "endColumn": 32}, {"ruleId": "580", "severity": 1, "message": "662", "line": 26, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 26, "endColumn": 19}, {"ruleId": "580", "severity": 1, "message": "599", "line": 37, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 37, "endColumn": 8}, {"ruleId": "580", "severity": 1, "message": "640", "line": 50, "column": 18, "nodeType": "582", "messageId": "583", "endLine": 50, "endColumn": 33}, {"ruleId": "580", "severity": 1, "message": "663", "line": 59, "column": 15, "nodeType": "582", "messageId": "583", "endLine": 59, "endColumn": 27}, {"ruleId": "580", "severity": 1, "message": "664", "line": 62, "column": 19, "nodeType": "582", "messageId": "583", "endLine": 62, "endColumn": 35}, {"ruleId": "580", "severity": 1, "message": "598", "line": 19, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 19, "endColumn": 10}, {"ruleId": "580", "severity": 1, "message": "665", "line": 29, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 29, "endColumn": 9}, {"ruleId": "580", "severity": 1, "message": "666", "line": 30, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 30, "endColumn": 19}, {"ruleId": "580", "severity": 1, "message": "667", "line": 78, "column": 10, "nodeType": "582", "messageId": "583", "endLine": 78, "endColumn": 22}, {"ruleId": "580", "severity": 1, "message": "668", "line": 79, "column": 10, "nodeType": "582", "messageId": "583", "endLine": 79, "endColumn": 20}, {"ruleId": "580", "severity": 1, "message": "648", "line": 1, "column": 27, "nodeType": "582", "messageId": "583", "endLine": 1, "endColumn": 36}, {"ruleId": "580", "severity": 1, "message": "614", "line": 18, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 18, "endColumn": 9}, {"ruleId": "580", "severity": 1, "message": "598", "line": 19, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 19, "endColumn": 10}, {"ruleId": "580", "severity": 1, "message": "669", "line": 29, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 29, "endColumn": 12}, {"ruleId": "580", "severity": 1, "message": "670", "line": 30, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 30, "endColumn": 19}, {"ruleId": "580", "severity": 1, "message": "671", "line": 31, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 31, "endColumn": 19}, {"ruleId": "580", "severity": 1, "message": "615", "line": 32, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 32, "endColumn": 7}, {"ruleId": "580", "severity": 1, "message": "616", "line": 33, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 33, "endColumn": 11}, {"ruleId": "580", "severity": 1, "message": "617", "line": 34, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 34, "endColumn": 15}, {"ruleId": "580", "severity": 1, "message": "618", "line": 35, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 35, "endColumn": 15}, {"ruleId": "580", "severity": 1, "message": "672", "line": 36, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 36, "endColumn": 26}, {"ruleId": "580", "severity": 1, "message": "673", "line": 43, "column": 10, "nodeType": "582", "messageId": "583", "endLine": 43, "endColumn": 17}, {"ruleId": "580", "severity": 1, "message": "653", "line": 44, "column": 11, "nodeType": "582", "messageId": "583", "endLine": 44, "endColumn": 19}, {"ruleId": "580", "severity": 1, "message": "674", "line": 50, "column": 17, "nodeType": "582", "messageId": "583", "endLine": 50, "endColumn": 31}, {"ruleId": "580", "severity": 1, "message": "675", "line": 51, "column": 20, "nodeType": "582", "messageId": "583", "endLine": 51, "endColumn": 37}, {"ruleId": "580", "severity": 1, "message": "676", "line": 54, "column": 17, "nodeType": "582", "messageId": "583", "endLine": 54, "endColumn": 31}, {"ruleId": "580", "severity": 1, "message": "677", "line": 59, "column": 20, "nodeType": "582", "messageId": "583", "endLine": 59, "endColumn": 37}, {"ruleId": "580", "severity": 1, "message": "678", "line": 63, "column": 13, "nodeType": "582", "messageId": "583", "endLine": 63, "endColumn": 23}, {"ruleId": "580", "severity": 1, "message": "679", "line": 64, "column": 13, "nodeType": "582", "messageId": "583", "endLine": 64, "endColumn": 23}, {"ruleId": "580", "severity": 1, "message": "680", "line": 218, "column": 11, "nodeType": "582", "messageId": "583", "endLine": 218, "endColumn": 16}, {"ruleId": "580", "severity": 1, "message": "681", "line": 25, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 25, "endColumn": 9}, {"ruleId": "580", "severity": 1, "message": "682", "line": 26, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 26, "endColumn": 11}, {"ruleId": "580", "severity": 1, "message": "683", "line": 27, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 27, "endColumn": 14}, {"ruleId": "580", "severity": 1, "message": "684", "line": 28, "column": 3, "nodeType": "582", "messageId": "583", "endLine": 28, "endColumn": 13}, {"ruleId": "580", "severity": 1, "message": "685", "line": 66, "column": 22, "nodeType": "582", "messageId": "583", "endLine": 66, "endColumn": 35}, "no-unused-vars", "'LoginPage' is defined but never used.", "Identifier", "unusedVar", "'ViewStudent' is defined but never used.", "'TrendingUpIcon' is defined but never used.", "'PeopleIcon' is defined but never used.", "'isMobile' is assigned a value but never used.", "'attendanceRate' is assigned a value but never used.", "'averageGrade' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch' and 'params'. Either include them or remove the dependency array.", "ArrayExpression", ["686"], "React Hook useEffect has a missing dependency: 'situation'. Either include it or remove the dependency array.", ["687"], ["688"], ["689"], "'Divider' is defined but never used.", "'Badge' is defined but never used.", "'PhotoCameraIcon' is defined but never used.", "'profilePicture' is assigned a value but never used.", "'deleteUser' is defined but never used.", "'showTab' is assigned a value but never used.", "'setShowTab' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'submitHandler' is assigned a value but never used.", ["690"], "'styled' is defined but never used.", "'Paper' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'Grid' is defined but never used.", "'Chip' is defined but never used.", "'Avatar' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'ListItemIcon' is defined but never used.", "'Button' is defined but never used.", "'resetSubjects' is defined but never used.", "'ClassIcon' is defined but never used.", "'GroupIcon' is defined but never used.", "'BookIcon' is defined but never used.", "'PersonAddIcon' is defined but never used.", "'ArrowBackIcon' is defined but never used.", "'SchoolIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'currentRole', 'currentUser._id', and 'currentUser.school._id'. Either include them or remove the dependency array.", ["691"], "'IconButton' is defined but never used.", "'Legend' is defined but never used.", "'Collapse' is defined but never used.", "'ReportIcon' is defined but never used.", "'EventAvailableIcon' is defined but never used.", "'QuizIcon' is defined but never used.", "'MenuBookIcon' is defined but never used.", "'HotelIcon' is defined but never used.", "'BusinessIcon' is defined but never used.", "'ExpandLess' is defined but never used.", "'ExpandMore' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'TextField' is defined but never used.", "'HomeIcon' is defined but never used.", "'getUserDetails' is defined but never used.", "'error' is assigned a value but never used.", "'response' is assigned a value but never used.", "'useEffect' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'EditIcon' is defined but never used.", "'LanguageIcon' is defined but never used.", "'dispatch' is assigned a value but never used.", "'logoDialog' is assigned a value but never used.", "'setLogoDialog' is assigned a value but never used.", "'PhoneIcon' is defined but never used.", "'EmailIcon' is defined but never used.", "'CakeIcon' is defined but never used.", "'BloodtypeIcon' is defined but never used.", "'CircularProgress' is defined but never used.", "'DownloadIcon' is defined but never used.", "'TrendingDownIcon' is defined but never used.", "'Switch' is defined but never used.", "'FormControlLabel' is defined but never used.", "'selectedPage' is assigned a value but never used.", "'editDialog' is assigned a value but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'WebIcon' is defined but never used.", "'VisibilityIcon' is defined but never used.", "'VisibilityOffIcon' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'NotificationsIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'CreateIcon' is defined but never used.", "'files' is assigned a value but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'setCmsContent' is assigned a value but never used.", {"desc": "692", "fix": "693"}, {"desc": "694", "fix": "695"}, {"desc": "692", "fix": "696"}, {"desc": "694", "fix": "697"}, {"desc": "692", "fix": "698"}, {"desc": "699", "fix": "700"}, "Update the dependencies array to be: [dispatch, params, situation]", {"range": "701", "text": "702"}, "Update the dependencies array to be: [dispatch, situation, userDetails]", {"range": "703", "text": "704"}, {"range": "705", "text": "702"}, {"range": "706", "text": "704"}, {"range": "707", "text": "702"}, "Update the dependencies array to be: [currentRole, currentUser._id, currentUser.school._id, dispatch]", {"range": "708", "text": "709"}, [1899, 1910], "[dispatch, params, situation]", [2122, 2145], "[dispatch, situation, userDetails]", [1867, 1878], [2090, 2113], [1458, 1469], [747, 757], "[currentR<PERSON>, currentUser._id, currentUser.school._id, dispatch]"]