[{"A:\\11\\MERN-School-Management-System\\frontend\\src\\index.js": "1", "A:\\11\\MERN-School-Management-System\\frontend\\src\\App.js": "2", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\store.js": "3", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Homepage.js": "4", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\LoginPage.js": "5", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\ChooseUser.js": "6", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminDashboard.js": "7", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminRegisterPage.js": "8", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherDashboard.js": "9", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentDashboard.js": "10", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userSlice.js": "11", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentSlice.js": "12", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeSlice.js": "13", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassSlice.js": "14", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherSlice.js": "15", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainSlice.js": "16", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Logout.js": "17", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userHandle.js": "18", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminProfile.js": "19", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\SideBar.js": "20", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminHomePage.js": "21", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\Popup.js": "22", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\styles.js": "23", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\AccountMenu.js": "24", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\buttonStyles.js": "25", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentAttendance.js": "26", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentExamMarks.js": "27", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\SeeComplains.js": "28", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\AddStudent.js": "29", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ShowStudents.js": "30", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ViewStudent.js": "31", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\AddNotice.js": "32", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\ShowNotices.js": "33", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ViewSubject.js": "34", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ShowSubjects.js": "35", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherSideBar.js": "36", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherClassDetails.js": "37", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\SubjectForm.js": "38", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseClass.js": "39", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ShowTeachers.js": "40", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseSubject.js": "41", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\AddTeacher.js": "42", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherComplain.js": "43", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherHomePage.js": "44", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\TeacherDetails.js": "45", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentHomePage.js": "46", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherProfile.js": "47", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherViewStudent.js": "48", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSideBar.js": "49", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\AddClass.js": "50", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ShowClasses.js": "51", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSubjects.js": "52", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ClassDetails.js": "53", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentProfile.js": "54", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentComplain.js": "55", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\ViewStdAttendance.js": "56", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SeeNotice.js": "57", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentHandle.js": "58", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassHandle.js": "59", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherHandle.js": "60", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableTemplate.js": "61", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SpeedDialTemplate.js": "62", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainHandle.js": "63", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\attendanceCalculator.js": "64", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomBarChart.js": "65", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomPieChart.js": "66", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeHandle.js": "67", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableViewTemplate.js": "68", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\EnhancedStatCard.js": "69", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\AttendanceChart.js": "70", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\PerformanceChart.js": "71", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\RecentActivity.js": "72", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\QuickActions.js": "73", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\CalendarWidget.js": "74", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\FinancialChart.js": "75", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\NotificationCenter.js": "76", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeToggle.js": "77", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeProvider.js": "78", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\layout\\ResponsiveSidebar.js": "79", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\feeManagement\\FeeManagement.js": "80", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\transport\\TransportManagement.js": "81", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\documents\\DocumentGeneration.js": "82", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentInfo\\StudentInfo.js": "83"}, {"size": 388, "mtime": 1751091352643, "results": "84", "hashOfConfig": "85"}, {"size": 1577, "mtime": 1751100721027, "results": "86", "hashOfConfig": "85"}, {"size": 713, "mtime": 1751091352677, "results": "87", "hashOfConfig": "85"}, {"size": 2910, "mtime": 1751100774610, "results": "88", "hashOfConfig": "85"}, {"size": 11299, "mtime": 1751100761015, "results": "89", "hashOfConfig": "85"}, {"size": 2833, "mtime": 1751100705093, "results": "90", "hashOfConfig": "85"}, {"size": 10833, "mtime": 1751104205722, "results": "91", "hashOfConfig": "85"}, {"size": 9826, "mtime": 1751091352650, "results": "92", "hashOfConfig": "85"}, {"size": 4699, "mtime": 1751091352670, "results": "93", "hashOfConfig": "85"}, {"size": 4230, "mtime": 1751091352662, "results": "94", "hashOfConfig": "85"}, {"size": 2909, "mtime": 1751091352677, "results": "95", "hashOfConfig": "85"}, {"size": 1483, "mtime": 1751091352677, "results": "96", "hashOfConfig": "85"}, {"size": 1014, "mtime": 1751091352677, "results": "97", "hashOfConfig": "85"}, {"size": 2643, "mtime": 1751091352677, "results": "98", "hashOfConfig": "85"}, {"size": 1440, "mtime": 1751091352677, "results": "99", "hashOfConfig": "85"}, {"size": 1028, "mtime": 1751091352672, "results": "100", "hashOfConfig": "85"}, {"size": 1800, "mtime": 1751091352643, "results": "101", "hashOfConfig": "85"}, {"size": 3589, "mtime": 1751092999549, "results": "102", "hashOfConfig": "85"}, {"size": 4480, "mtime": 1751091352648, "results": "103", "hashOfConfig": "85"}, {"size": 4467, "mtime": 1751091352650, "results": "104", "hashOfConfig": "105"}, {"size": 7554, "mtime": 1751104361797, "results": "106", "hashOfConfig": "85"}, {"size": 1562, "mtime": 1751091352637, "results": "107", "hashOfConfig": "85"}, {"size": 2382, "mtime": 1751091352643, "results": "108", "hashOfConfig": "85"}, {"size": 3408, "mtime": 1751091352629, "results": "109", "hashOfConfig": "85"}, {"size": 1933, "mtime": 1751091352639, "results": "110", "hashOfConfig": "85"}, {"size": 8909, "mtime": 1751091352656, "results": "111", "hashOfConfig": "85"}, {"size": 7905, "mtime": 1751091352656, "results": "112", "hashOfConfig": "85"}, {"size": 2165, "mtime": 1751091352656, "results": "113", "hashOfConfig": "85"}, {"size": 19487, "mtime": 1751103578808, "results": "114", "hashOfConfig": "85"}, {"size": 8747, "mtime": 1751091352656, "results": "115", "hashOfConfig": "85"}, {"size": 22050, "mtime": 1751091352656, "results": "116", "hashOfConfig": "85"}, {"size": 2792, "mtime": 1751091352653, "results": "117", "hashOfConfig": "85"}, {"size": 3697, "mtime": 1751091352653, "results": "118", "hashOfConfig": "85"}, {"size": 7183, "mtime": 1751091352659, "results": "119", "hashOfConfig": "85"}, {"size": 4297, "mtime": 1751091352659, "results": "120", "hashOfConfig": "85"}, {"size": 2939, "mtime": 1751091352672, "results": "121", "hashOfConfig": "85"}, {"size": 7137, "mtime": 1751091352669, "results": "122", "hashOfConfig": "85"}, {"size": 6961, "mtime": 1751091352659, "results": "123", "hashOfConfig": "85"}, {"size": 2823, "mtime": 1751091352662, "results": "124", "hashOfConfig": "85"}, {"size": 7878, "mtime": 1751091352662, "results": "125", "hashOfConfig": "85"}, {"size": 5134, "mtime": 1751091352662, "results": "126", "hashOfConfig": "85"}, {"size": 3772, "mtime": 1751091352662, "results": "127", "hashOfConfig": "85"}, {"size": 146, "mtime": 1751091352669, "results": "128", "hashOfConfig": "85"}, {"size": 3747, "mtime": 1751091352670, "results": "129", "hashOfConfig": "85"}, {"size": 2405, "mtime": 1751091352662, "results": "130", "hashOfConfig": "85"}, {"size": 5332, "mtime": 1751091352662, "results": "131", "hashOfConfig": "85"}, {"size": 1317, "mtime": 1751091352670, "results": "132", "hashOfConfig": "85"}, {"size": 12169, "mtime": 1751091352672, "results": "133", "hashOfConfig": "85"}, {"size": 3195, "mtime": 1751091352662, "results": "134", "hashOfConfig": "85"}, {"size": 4208, "mtime": 1751091352651, "results": "135", "hashOfConfig": "85"}, {"size": 6277, "mtime": 1751091352652, "results": "136", "hashOfConfig": "85"}, {"size": 6014, "mtime": 1751091352662, "results": "137", "hashOfConfig": "85"}, {"size": 10926, "mtime": 1751091352652, "results": "138", "hashOfConfig": "85"}, {"size": 3931, "mtime": 1751091352662, "results": "139", "hashOfConfig": "85"}, {"size": 4228, "mtime": 1751091352662, "results": "140", "hashOfConfig": "85"}, {"size": 9767, "mtime": 1751091352662, "results": "141", "hashOfConfig": "85"}, {"size": 2233, "mtime": 1751091352639, "results": "142", "hashOfConfig": "85"}, {"size": 1558, "mtime": 1751093107198, "results": "143", "hashOfConfig": "85"}, {"size": 2895, "mtime": 1751093149585, "results": "144", "hashOfConfig": "85"}, {"size": 1383, "mtime": 1751093059721, "results": "145", "hashOfConfig": "85"}, {"size": 3386, "mtime": 1751091352639, "results": "146", "hashOfConfig": "85"}, {"size": 887, "mtime": 1751091352639, "results": "147", "hashOfConfig": "85"}, {"size": 621, "mtime": 1751093204280, "results": "148", "hashOfConfig": "85"}, {"size": 1996, "mtime": 1751091352639, "results": "149", "hashOfConfig": "85"}, {"size": 6469, "mtime": 1751091352629, "results": "150", "hashOfConfig": "85"}, {"size": 3161, "mtime": 1751091352629, "results": "151", "hashOfConfig": "85"}, {"size": 617, "mtime": 1751093177974, "results": "152", "hashOfConfig": "85"}, {"size": 3018, "mtime": 1751091352639, "results": "153", "hashOfConfig": "85"}, {"size": 3781, "mtime": 1751104338304, "results": "154", "hashOfConfig": "85"}, {"size": 2540, "mtime": 1751101038420, "results": "155", "hashOfConfig": "85"}, {"size": 2516, "mtime": 1751101053307, "results": "156", "hashOfConfig": "85"}, {"size": 4394, "mtime": 1751101121649, "results": "157", "hashOfConfig": "85"}, {"size": 3664, "mtime": 1751104279550, "results": "158", "hashOfConfig": "85"}, {"size": 5436, "mtime": 1751101223218, "results": "159", "hashOfConfig": "85"}, {"size": 3811, "mtime": 1751101073787, "results": "160", "hashOfConfig": "85"}, {"size": 6826, "mtime": 1751101255344, "results": "161", "hashOfConfig": "85"}, {"size": 908, "mtime": 1751101186045, "results": "162", "hashOfConfig": "85"}, {"size": 3711, "mtime": 1751101175499, "results": "163", "hashOfConfig": "85"}, {"size": 9855, "mtime": 1751104011940, "results": "164", "hashOfConfig": "85"}, {"size": 14046, "mtime": 1751102262286, "results": "165", "hashOfConfig": "85"}, {"size": 12806, "mtime": 1751102695142, "results": "166", "hashOfConfig": "85"}, {"size": 11821, "mtime": 1751102469493, "results": "167", "hashOfConfig": "85"}, {"size": 15373, "mtime": 1751103458993, "results": "168", "hashOfConfig": "85"}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kq195y", {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, "1pf3lve", {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "A:\\11\\MERN-School-Management-System\\frontend\\src\\index.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\App.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\store.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Homepage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\LoginPage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\ChooseUser.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminRegisterPage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Logout.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userHandle.js", ["419"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\SideBar.js", [], [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminHomePage.js", ["420", "421", "422", "423", "424"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\Popup.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\styles.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\AccountMenu.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\buttonStyles.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentAttendance.js", ["425", "426"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentExamMarks.js", ["427", "428"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\SeeComplains.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\AddStudent.js", ["429"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ShowStudents.js", ["430"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ViewStudent.js", ["431", "432", "433", "434", "435"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\AddNotice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\ShowNotices.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ViewSubject.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ShowSubjects.js", ["436"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherSideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherClassDetails.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\SubjectForm.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseClass.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ShowTeachers.js", ["437"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseSubject.js", ["438"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\AddTeacher.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherComplain.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\TeacherDetails.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherViewStudent.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\AddClass.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ShowClasses.js", ["439"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSubjects.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ClassDetails.js", ["440", "441"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentComplain.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\ViewStdAttendance.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SeeNotice.js", ["442"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SpeedDialTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\attendanceCalculator.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomBarChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomPieChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableViewTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\EnhancedStatCard.js", ["443"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\AttendanceChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\PerformanceChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\RecentActivity.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\QuickActions.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\CalendarWidget.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\FinancialChart.js", ["444"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\NotificationCenter.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeToggle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeProvider.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\layout\\ResponsiveSidebar.js", ["445", "446", "447", "448", "449", "450", "451", "452", "453", "454", "455", "456"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\feeManagement\\FeeManagement.js", ["457"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\transport\\TransportManagement.js", ["458", "459", "460"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\documents\\DocumentGeneration.js", ["461"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentInfo\\StudentInfo.js", ["462", "463", "464", "465"], [], {"ruleId": "466", "severity": 1, "message": "467", "line": 10, "column": 5, "nodeType": "468", "messageId": "469", "endLine": 10, "endColumn": 21}, {"ruleId": "466", "severity": 1, "message": "470", "line": 31, "column": 17, "nodeType": "468", "messageId": "469", "endLine": 31, "endColumn": 31}, {"ruleId": "466", "severity": 1, "message": "471", "line": 32, "column": 13, "nodeType": "468", "messageId": "469", "endLine": 32, "endColumn": 23}, {"ruleId": "466", "severity": 1, "message": "472", "line": 39, "column": 11, "nodeType": "468", "messageId": "469", "endLine": 39, "endColumn": 19}, {"ruleId": "466", "severity": 1, "message": "473", "line": 60, "column": 11, "nodeType": "468", "messageId": "469", "endLine": 60, "endColumn": 25}, {"ruleId": "466", "severity": 1, "message": "474", "line": 61, "column": 11, "nodeType": "468", "messageId": "469", "endLine": 61, "endColumn": 23}, {"ruleId": "475", "severity": 1, "message": "476", "line": 46, "column": 8, "nodeType": "477", "endLine": 46, "endColumn": 19, "suggestions": "478"}, {"ruleId": "475", "severity": 1, "message": "479", "line": 52, "column": 8, "nodeType": "477", "endLine": 52, "endColumn": 31, "suggestions": "480"}, {"ruleId": "475", "severity": 1, "message": "476", "line": 45, "column": 8, "nodeType": "477", "endLine": 45, "endColumn": 19, "suggestions": "481"}, {"ruleId": "475", "severity": 1, "message": "479", "line": 51, "column": 8, "nodeType": "477", "endLine": 51, "endColumn": 31, "suggestions": "482"}, {"ruleId": "466", "severity": 1, "message": "483", "line": 23, "column": 3, "nodeType": "468", "messageId": "469", "endLine": 23, "endColumn": 10}, {"ruleId": "466", "severity": 1, "message": "484", "line": 5, "column": 10, "nodeType": "468", "messageId": "469", "endLine": 5, "endColumn": 20}, {"ruleId": "466", "severity": 1, "message": "484", "line": 3, "column": 10, "nodeType": "468", "messageId": "469", "endLine": 3, "endColumn": 20}, {"ruleId": "466", "severity": 1, "message": "485", "line": 24, "column": 12, "nodeType": "468", "messageId": "469", "endLine": 24, "endColumn": 19}, {"ruleId": "466", "severity": 1, "message": "486", "line": 24, "column": 21, "nodeType": "468", "messageId": "469", "endLine": 24, "endColumn": 31}, {"ruleId": "466", "severity": 1, "message": "487", "line": 49, "column": 22, "nodeType": "468", "messageId": "469", "endLine": 49, "endColumn": 33}, {"ruleId": "466", "severity": 1, "message": "488", "line": 93, "column": 11, "nodeType": "468", "messageId": "469", "endLine": 93, "endColumn": 24}, {"ruleId": "466", "severity": 1, "message": "484", "line": 5, "column": 10, "nodeType": "468", "messageId": "469", "endLine": 5, "endColumn": 20}, {"ruleId": "466", "severity": 1, "message": "484", "line": 9, "column": 10, "nodeType": "468", "messageId": "469", "endLine": 9, "endColumn": 20}, {"ruleId": "475", "severity": 1, "message": "476", "line": 33, "column": 8, "nodeType": "477", "endLine": 33, "endColumn": 19, "suggestions": "489"}, {"ruleId": "466", "severity": 1, "message": "484", "line": 6, "column": 10, "nodeType": "468", "messageId": "469", "endLine": 6, "endColumn": 20}, {"ruleId": "466", "severity": 1, "message": "484", "line": 5, "column": 10, "nodeType": "468", "messageId": "469", "endLine": 5, "endColumn": 20}, {"ruleId": "466", "severity": 1, "message": "490", "line": 12, "column": 10, "nodeType": "468", "messageId": "469", "endLine": 12, "endColumn": 23}, {"ruleId": "475", "severity": 1, "message": "491", "line": 20, "column": 8, "nodeType": "477", "endLine": 20, "endColumn": 18, "suggestions": "492"}, {"ruleId": "466", "severity": 1, "message": "493", "line": 2, "column": 34, "nodeType": "468", "messageId": "469", "endLine": 2, "endColumn": 44}, {"ruleId": "466", "severity": 1, "message": "494", "line": 2, "column": 61, "nodeType": "468", "messageId": "469", "endLine": 2, "endColumn": 67}, {"ruleId": "466", "severity": 1, "message": "495", "line": 15, "column": 3, "nodeType": "468", "messageId": "469", "endLine": 15, "endColumn": 11}, {"ruleId": "466", "severity": 1, "message": "496", "line": 27, "column": 13, "nodeType": "468", "messageId": "469", "endLine": 27, "endColumn": 23}, {"ruleId": "466", "severity": 1, "message": "471", "line": 36, "column": 13, "nodeType": "468", "messageId": "469", "endLine": 36, "endColumn": 23}, {"ruleId": "466", "severity": 1, "message": "497", "line": 37, "column": 16, "nodeType": "468", "messageId": "469", "endLine": 37, "endColumn": 29}, {"ruleId": "466", "severity": 1, "message": "498", "line": 38, "column": 21, "nodeType": "468", "messageId": "469", "endLine": 38, "endColumn": 39}, {"ruleId": "466", "severity": 1, "message": "499", "line": 39, "column": 11, "nodeType": "468", "messageId": "469", "endLine": 39, "endColumn": 19}, {"ruleId": "466", "severity": 1, "message": "500", "line": 40, "column": 15, "nodeType": "468", "messageId": "469", "endLine": 40, "endColumn": 27}, {"ruleId": "466", "severity": 1, "message": "501", "line": 42, "column": 12, "nodeType": "468", "messageId": "469", "endLine": 42, "endColumn": 21}, {"ruleId": "466", "severity": 1, "message": "502", "line": 43, "column": 10, "nodeType": "468", "messageId": "469", "endLine": 43, "endColumn": 17}, {"ruleId": "466", "severity": 1, "message": "503", "line": 44, "column": 15, "nodeType": "468", "messageId": "469", "endLine": 44, "endColumn": 27}, {"ruleId": "466", "severity": 1, "message": "504", "line": 45, "column": 3, "nodeType": "468", "messageId": "469", "endLine": 45, "endColumn": 13}, {"ruleId": "466", "severity": 1, "message": "505", "line": 46, "column": 3, "nodeType": "468", "messageId": "469", "endLine": 46, "endColumn": 13}, {"ruleId": "466", "severity": 1, "message": "506", "line": 35, "column": 18, "nodeType": "468", "messageId": "469", "endLine": 35, "endColumn": 33}, {"ruleId": "466", "severity": 1, "message": "507", "line": 32, "column": 15, "nodeType": "468", "messageId": "469", "endLine": 32, "endColumn": 27}, {"ruleId": "466", "severity": 1, "message": "508", "line": 34, "column": 14, "nodeType": "468", "messageId": "469", "endLine": 34, "endColumn": 25}, {"ruleId": "466", "severity": 1, "message": "506", "line": 35, "column": 18, "nodeType": "468", "messageId": "469", "endLine": 35, "endColumn": 33}, {"ruleId": "466", "severity": 1, "message": "509", "line": 11, "column": 3, "nodeType": "468", "messageId": "469", "endLine": 11, "endColumn": 12}, {"ruleId": "466", "severity": 1, "message": "510", "line": 28, "column": 3, "nodeType": "468", "messageId": "469", "endLine": 28, "endColumn": 8}, {"ruleId": "466", "severity": 1, "message": "511", "line": 38, "column": 11, "nodeType": "468", "messageId": "469", "endLine": 38, "endColumn": 19}, {"ruleId": "466", "severity": 1, "message": "512", "line": 81, "column": 34, "nodeType": "468", "messageId": "469", "endLine": 81, "endColumn": 39}, {"ruleId": "466", "severity": 1, "message": "513", "line": 81, "column": 41, "nodeType": "468", "messageId": "469", "endLine": 81, "endColumn": 49}, "no-unused-vars", "'getDeleteSuccess' is defined but never used.", "Identifier", "unusedVar", "'TrendingUpIcon' is defined but never used.", "'PeopleIcon' is defined but never used.", "'isMobile' is assigned a value but never used.", "'attendanceRate' is assigned a value but never used.", "'averageGrade' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch' and 'params'. Either include them or remove the dependency array.", "ArrayExpression", ["514"], "React Hook useEffect has a missing dependency: 'situation'. Either include it or remove the dependency array.", ["515"], ["516"], ["517"], "'Divider' is defined but never used.", "'deleteUser' is defined but never used.", "'showTab' is assigned a value but never used.", "'setShowTab' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'submitHandler' is assigned a value but never used.", ["518"], "'resetSubjects' is defined but never used.", "React Hook useEffect has missing dependencies: 'currentRole', 'currentUser._id', and 'currentUser.school._id'. Either include them or remove the dependency array.", ["519"], "'IconButton' is defined but never used.", "'Legend' is defined but never used.", "'Collapse' is defined but never used.", "'ReportIcon' is defined but never used.", "'PersonAddIcon' is defined but never used.", "'EventAvailableIcon' is defined but never used.", "'QuizIcon' is defined but never used.", "'MenuBookIcon' is defined but never used.", "'HotelIcon' is defined but never used.", "'WebIcon' is defined but never used.", "'BusinessIcon' is defined but never used.", "'ExpandLess' is defined but never used.", "'ExpandMore' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'TextField' is defined but never used.", "'Alert' is defined but never used.", "'HomeIcon' is defined but never used.", "'error' is assigned a value but never used.", "'response' is assigned a value but never used.", {"desc": "520", "fix": "521"}, {"desc": "522", "fix": "523"}, {"desc": "520", "fix": "524"}, {"desc": "522", "fix": "525"}, {"desc": "520", "fix": "526"}, {"desc": "527", "fix": "528"}, "Update the dependencies array to be: [dispatch, params, situation]", {"range": "529", "text": "530"}, "Update the dependencies array to be: [dispatch, situation, userDetails]", {"range": "531", "text": "532"}, {"range": "533", "text": "530"}, {"range": "534", "text": "532"}, {"range": "535", "text": "530"}, "Update the dependencies array to be: [currentRole, currentUser._id, currentUser.school._id, dispatch]", {"range": "536", "text": "537"}, [1899, 1910], "[dispatch, params, situation]", [2122, 2145], "[dispatch, situation, userDetails]", [1867, 1878], [2090, 2113], [1458, 1469], [747, 757], "[currentR<PERSON>, currentUser._id, currentUser.school._id, dispatch]"]