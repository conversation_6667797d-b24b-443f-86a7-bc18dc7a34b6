[{"A:\\11\\MERN-School-Management-System\\frontend\\src\\index.js": "1", "A:\\11\\MERN-School-Management-System\\frontend\\src\\App.js": "2", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\store.js": "3", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Homepage.js": "4", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\LoginPage.js": "5", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\ChooseUser.js": "6", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminDashboard.js": "7", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminRegisterPage.js": "8", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherDashboard.js": "9", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentDashboard.js": "10", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userSlice.js": "11", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentSlice.js": "12", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeSlice.js": "13", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassSlice.js": "14", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherSlice.js": "15", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainSlice.js": "16", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Logout.js": "17", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userHandle.js": "18", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminProfile.js": "19", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\SideBar.js": "20", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminHomePage.js": "21", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\Popup.js": "22", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\styles.js": "23", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\AccountMenu.js": "24", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\buttonStyles.js": "25", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentAttendance.js": "26", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentExamMarks.js": "27", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\SeeComplains.js": "28", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\AddStudent.js": "29", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ShowStudents.js": "30", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ViewStudent.js": "31", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\AddNotice.js": "32", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\ShowNotices.js": "33", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ViewSubject.js": "34", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ShowSubjects.js": "35", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherSideBar.js": "36", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherClassDetails.js": "37", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\SubjectForm.js": "38", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseClass.js": "39", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ShowTeachers.js": "40", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseSubject.js": "41", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\AddTeacher.js": "42", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherComplain.js": "43", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherHomePage.js": "44", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\TeacherDetails.js": "45", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentHomePage.js": "46", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherProfile.js": "47", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherViewStudent.js": "48", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSideBar.js": "49", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\AddClass.js": "50", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ShowClasses.js": "51", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSubjects.js": "52", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ClassDetails.js": "53", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentProfile.js": "54", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentComplain.js": "55", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\ViewStdAttendance.js": "56", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SeeNotice.js": "57", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentHandle.js": "58", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassHandle.js": "59", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherHandle.js": "60", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableTemplate.js": "61", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SpeedDialTemplate.js": "62", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainHandle.js": "63", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\attendanceCalculator.js": "64", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomBarChart.js": "65", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomPieChart.js": "66", "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeHandle.js": "67", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableViewTemplate.js": "68", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\EnhancedStatCard.js": "69", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\AttendanceChart.js": "70", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\PerformanceChart.js": "71", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\RecentActivity.js": "72", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\QuickActions.js": "73", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\CalendarWidget.js": "74", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\FinancialChart.js": "75", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\NotificationCenter.js": "76", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeToggle.js": "77", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeProvider.js": "78", "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\layout\\ResponsiveSidebar.js": "79", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\feeManagement\\FeeManagement.js": "80", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\transport\\TransportManagement.js": "81", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\documents\\DocumentGeneration.js": "82", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentInfo\\StudentInfo.js": "83", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminSettings.js": "84", "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\EnhancedViewStudent.js": "85"}, {"size": 388, "mtime": 1751091352643, "results": "86", "hashOfConfig": "87"}, {"size": 1577, "mtime": 1751100721027, "results": "88", "hashOfConfig": "87"}, {"size": 713, "mtime": 1751091352677, "results": "89", "hashOfConfig": "87"}, {"size": 2910, "mtime": 1751100774610, "results": "90", "hashOfConfig": "87"}, {"size": 11299, "mtime": 1751100761015, "results": "91", "hashOfConfig": "87"}, {"size": 2833, "mtime": 1751100705093, "results": "92", "hashOfConfig": "87"}, {"size": 11132, "mtime": 1751112079969, "results": "93", "hashOfConfig": "87"}, {"size": 9826, "mtime": 1751091352650, "results": "94", "hashOfConfig": "87"}, {"size": 4699, "mtime": 1751091352670, "results": "95", "hashOfConfig": "87"}, {"size": 4230, "mtime": 1751091352662, "results": "96", "hashOfConfig": "87"}, {"size": 2909, "mtime": 1751091352677, "results": "97", "hashOfConfig": "87"}, {"size": 1483, "mtime": 1751091352677, "results": "98", "hashOfConfig": "87"}, {"size": 1014, "mtime": 1751091352677, "results": "99", "hashOfConfig": "87"}, {"size": 2643, "mtime": 1751091352677, "results": "100", "hashOfConfig": "87"}, {"size": 1440, "mtime": 1751091352677, "results": "101", "hashOfConfig": "87"}, {"size": 1028, "mtime": 1751091352672, "results": "102", "hashOfConfig": "87"}, {"size": 1800, "mtime": 1751091352643, "results": "103", "hashOfConfig": "87"}, {"size": 3835, "mtime": 1751111833332, "results": "104", "hashOfConfig": "87"}, {"size": 4480, "mtime": 1751091352648, "results": "105", "hashOfConfig": "87"}, {"size": 4467, "mtime": 1751091352650, "results": "106", "hashOfConfig": "107"}, {"size": 7554, "mtime": 1751104361797, "results": "108", "hashOfConfig": "87"}, {"size": 1562, "mtime": 1751091352637, "results": "109", "hashOfConfig": "87"}, {"size": 2382, "mtime": 1751091352643, "results": "110", "hashOfConfig": "87"}, {"size": 3501, "mtime": 1751111367409, "results": "111", "hashOfConfig": "87"}, {"size": 1933, "mtime": 1751091352639, "results": "112", "hashOfConfig": "87"}, {"size": 8909, "mtime": 1751091352656, "results": "113", "hashOfConfig": "87"}, {"size": 7905, "mtime": 1751091352656, "results": "114", "hashOfConfig": "87"}, {"size": 2165, "mtime": 1751091352656, "results": "115", "hashOfConfig": "87"}, {"size": 23300, "mtime": 1751111313379, "results": "116", "hashOfConfig": "87"}, {"size": 8747, "mtime": 1751091352656, "results": "117", "hashOfConfig": "87"}, {"size": 22050, "mtime": 1751091352656, "results": "118", "hashOfConfig": "87"}, {"size": 2792, "mtime": 1751091352653, "results": "119", "hashOfConfig": "87"}, {"size": 3697, "mtime": 1751091352653, "results": "120", "hashOfConfig": "87"}, {"size": 7183, "mtime": 1751091352659, "results": "121", "hashOfConfig": "87"}, {"size": 4297, "mtime": 1751091352659, "results": "122", "hashOfConfig": "87"}, {"size": 2939, "mtime": 1751091352672, "results": "123", "hashOfConfig": "87"}, {"size": 7137, "mtime": 1751091352669, "results": "124", "hashOfConfig": "87"}, {"size": 6961, "mtime": 1751091352659, "results": "125", "hashOfConfig": "87"}, {"size": 2823, "mtime": 1751091352662, "results": "126", "hashOfConfig": "87"}, {"size": 7878, "mtime": 1751091352662, "results": "127", "hashOfConfig": "87"}, {"size": 5134, "mtime": 1751091352662, "results": "128", "hashOfConfig": "87"}, {"size": 3772, "mtime": 1751091352662, "results": "129", "hashOfConfig": "87"}, {"size": 146, "mtime": 1751091352669, "results": "130", "hashOfConfig": "87"}, {"size": 3747, "mtime": 1751091352670, "results": "131", "hashOfConfig": "87"}, {"size": 2405, "mtime": 1751091352662, "results": "132", "hashOfConfig": "87"}, {"size": 5332, "mtime": 1751091352662, "results": "133", "hashOfConfig": "87"}, {"size": 1317, "mtime": 1751091352670, "results": "134", "hashOfConfig": "87"}, {"size": 12169, "mtime": 1751091352672, "results": "135", "hashOfConfig": "87"}, {"size": 3195, "mtime": 1751091352662, "results": "136", "hashOfConfig": "87"}, {"size": 4208, "mtime": 1751091352651, "results": "137", "hashOfConfig": "87"}, {"size": 6277, "mtime": 1751091352652, "results": "138", "hashOfConfig": "87"}, {"size": 6014, "mtime": 1751091352662, "results": "139", "hashOfConfig": "87"}, {"size": 10926, "mtime": 1751091352652, "results": "140", "hashOfConfig": "87"}, {"size": 3931, "mtime": 1751091352662, "results": "141", "hashOfConfig": "87"}, {"size": 4228, "mtime": 1751091352662, "results": "142", "hashOfConfig": "87"}, {"size": 9767, "mtime": 1751091352662, "results": "143", "hashOfConfig": "87"}, {"size": 2233, "mtime": 1751091352639, "results": "144", "hashOfConfig": "87"}, {"size": 1558, "mtime": 1751093107198, "results": "145", "hashOfConfig": "87"}, {"size": 2895, "mtime": 1751093149585, "results": "146", "hashOfConfig": "87"}, {"size": 1383, "mtime": 1751093059721, "results": "147", "hashOfConfig": "87"}, {"size": 3386, "mtime": 1751091352639, "results": "148", "hashOfConfig": "87"}, {"size": 887, "mtime": 1751091352639, "results": "149", "hashOfConfig": "87"}, {"size": 621, "mtime": 1751093204280, "results": "150", "hashOfConfig": "87"}, {"size": 1996, "mtime": 1751091352639, "results": "151", "hashOfConfig": "87"}, {"size": 6469, "mtime": 1751091352629, "results": "152", "hashOfConfig": "87"}, {"size": 3161, "mtime": 1751091352629, "results": "153", "hashOfConfig": "87"}, {"size": 617, "mtime": 1751093177974, "results": "154", "hashOfConfig": "87"}, {"size": 3018, "mtime": 1751091352639, "results": "155", "hashOfConfig": "87"}, {"size": 3781, "mtime": 1751104338304, "results": "156", "hashOfConfig": "87"}, {"size": 2540, "mtime": 1751101038420, "results": "157", "hashOfConfig": "87"}, {"size": 2516, "mtime": 1751101053307, "results": "158", "hashOfConfig": "87"}, {"size": 4394, "mtime": 1751101121649, "results": "159", "hashOfConfig": "87"}, {"size": 3664, "mtime": 1751104279550, "results": "160", "hashOfConfig": "87"}, {"size": 5436, "mtime": 1751101223218, "results": "161", "hashOfConfig": "87"}, {"size": 3811, "mtime": 1751101073787, "results": "162", "hashOfConfig": "87"}, {"size": 6826, "mtime": 1751101255344, "results": "163", "hashOfConfig": "87"}, {"size": 908, "mtime": 1751101186045, "results": "164", "hashOfConfig": "87"}, {"size": 3711, "mtime": 1751101175499, "results": "165", "hashOfConfig": "87"}, {"size": 10577, "mtime": 1751104810799, "results": "166", "hashOfConfig": "87"}, {"size": 14046, "mtime": 1751102262286, "results": "167", "hashOfConfig": "87"}, {"size": 12806, "mtime": 1751102695142, "results": "168", "hashOfConfig": "87"}, {"size": 11821, "mtime": 1751102469493, "results": "169", "hashOfConfig": "87"}, {"size": 19126, "mtime": 1751111818968, "results": "170", "hashOfConfig": "87"}, {"size": 25082, "mtime": 1751111169508, "results": "171", "hashOfConfig": "87"}, {"size": 17065, "mtime": 1751112046968, "results": "172", "hashOfConfig": "87"}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kq195y", {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "233"}, "1pf3lve", {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "A:\\11\\MERN-School-Management-System\\frontend\\src\\index.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\App.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\store.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Homepage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\LoginPage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\ChooseUser.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminDashboard.js", ["429"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminRegisterPage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentDashboard.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainSlice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\Logout.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\userRelated\\userHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\SideBar.js", [], [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminHomePage.js", ["430", "431", "432", "433", "434"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\Popup.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\styles.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\AccountMenu.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\buttonStyles.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentAttendance.js", ["435", "436"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\StudentExamMarks.js", ["437", "438"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\SeeComplains.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\AddStudent.js", ["439", "440", "441", "442"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ShowStudents.js", ["443"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\ViewStudent.js", ["444", "445", "446", "447", "448"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\AddNotice.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\noticeRelated\\ShowNotices.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ViewSubject.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\ShowSubjects.js", ["449"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherSideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherClassDetails.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\subjectRelated\\SubjectForm.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseClass.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ShowTeachers.js", ["450"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\ChooseSubject.js", ["451"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\AddTeacher.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherComplain.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\teacherRelated\\TeacherDetails.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentHomePage.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\teacher\\TeacherViewStudent.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSideBar.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\AddClass.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ShowClasses.js", ["452"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentSubjects.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\classRelated\\ClassDetails.js", ["453", "454"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentProfile.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\StudentComplain.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\student\\ViewStdAttendance.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SeeNotice.js", ["455"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\studentRelated\\studentHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\sclassRelated\\sclassHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\teacherRelated\\teacherHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\SpeedDialTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\complainRelated\\complainHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\attendanceCalculator.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomBarChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\CustomPieChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\redux\\noticeRelated\\noticeHandle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\TableViewTemplate.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\EnhancedStatCard.js", ["456"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\AttendanceChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\PerformanceChart.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\RecentActivity.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\QuickActions.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\CalendarWidget.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\analytics\\FinancialChart.js", ["457"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\widgets\\NotificationCenter.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeToggle.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\theme\\ThemeProvider.js", [], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\components\\layout\\ResponsiveSidebar.js", ["458", "459", "460", "461", "462", "463", "464", "465", "466", "467", "468", "469"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\feeManagement\\FeeManagement.js", ["470"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\transport\\TransportManagement.js", ["471", "472", "473"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\documents\\DocumentGeneration.js", ["474"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentInfo\\StudentInfo.js", ["475", "476", "477", "478"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\AdminSettings.js", ["479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489", "490"], [], "A:\\11\\MERN-School-Management-System\\frontend\\src\\pages\\admin\\studentRelated\\EnhancedViewStudent.js", ["491", "492", "493", "494", "495", "496"], [], {"ruleId": "497", "severity": 1, "message": "498", "line": 39, "column": 8, "nodeType": "499", "messageId": "500", "endLine": 39, "endColumn": 19}, {"ruleId": "497", "severity": 1, "message": "501", "line": 31, "column": 17, "nodeType": "499", "messageId": "500", "endLine": 31, "endColumn": 31}, {"ruleId": "497", "severity": 1, "message": "502", "line": 32, "column": 13, "nodeType": "499", "messageId": "500", "endLine": 32, "endColumn": 23}, {"ruleId": "497", "severity": 1, "message": "503", "line": 39, "column": 11, "nodeType": "499", "messageId": "500", "endLine": 39, "endColumn": 19}, {"ruleId": "497", "severity": 1, "message": "504", "line": 60, "column": 11, "nodeType": "499", "messageId": "500", "endLine": 60, "endColumn": 25}, {"ruleId": "497", "severity": 1, "message": "505", "line": 61, "column": 11, "nodeType": "499", "messageId": "500", "endLine": 61, "endColumn": 23}, {"ruleId": "506", "severity": 1, "message": "507", "line": 46, "column": 8, "nodeType": "508", "endLine": 46, "endColumn": 19, "suggestions": "509"}, {"ruleId": "506", "severity": 1, "message": "510", "line": 52, "column": 8, "nodeType": "508", "endLine": 52, "endColumn": 31, "suggestions": "511"}, {"ruleId": "506", "severity": 1, "message": "507", "line": 45, "column": 8, "nodeType": "508", "endLine": 45, "endColumn": 19, "suggestions": "512"}, {"ruleId": "506", "severity": 1, "message": "510", "line": 51, "column": 8, "nodeType": "508", "endLine": 51, "endColumn": 31, "suggestions": "513"}, {"ruleId": "497", "severity": 1, "message": "514", "line": 23, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 23, "endColumn": 10}, {"ruleId": "497", "severity": 1, "message": "515", "line": 26, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 26, "endColumn": 8}, {"ruleId": "497", "severity": 1, "message": "516", "line": 33, "column": 18, "nodeType": "499", "messageId": "500", "endLine": 33, "endColumn": 33}, {"ruleId": "497", "severity": 1, "message": "517", "line": 98, "column": 12, "nodeType": "499", "messageId": "500", "endLine": 98, "endColumn": 26}, {"ruleId": "497", "severity": 1, "message": "518", "line": 5, "column": 10, "nodeType": "499", "messageId": "500", "endLine": 5, "endColumn": 20}, {"ruleId": "497", "severity": 1, "message": "518", "line": 3, "column": 10, "nodeType": "499", "messageId": "500", "endLine": 3, "endColumn": 20}, {"ruleId": "497", "severity": 1, "message": "519", "line": 24, "column": 12, "nodeType": "499", "messageId": "500", "endLine": 24, "endColumn": 19}, {"ruleId": "497", "severity": 1, "message": "520", "line": 24, "column": 21, "nodeType": "499", "messageId": "500", "endLine": 24, "endColumn": 31}, {"ruleId": "497", "severity": 1, "message": "521", "line": 49, "column": 22, "nodeType": "499", "messageId": "500", "endLine": 49, "endColumn": 33}, {"ruleId": "497", "severity": 1, "message": "522", "line": 93, "column": 11, "nodeType": "499", "messageId": "500", "endLine": 93, "endColumn": 24}, {"ruleId": "497", "severity": 1, "message": "518", "line": 5, "column": 10, "nodeType": "499", "messageId": "500", "endLine": 5, "endColumn": 20}, {"ruleId": "497", "severity": 1, "message": "518", "line": 9, "column": 10, "nodeType": "499", "messageId": "500", "endLine": 9, "endColumn": 20}, {"ruleId": "506", "severity": 1, "message": "507", "line": 33, "column": 8, "nodeType": "508", "endLine": 33, "endColumn": 19, "suggestions": "523"}, {"ruleId": "497", "severity": 1, "message": "518", "line": 6, "column": 10, "nodeType": "499", "messageId": "500", "endLine": 6, "endColumn": 20}, {"ruleId": "497", "severity": 1, "message": "518", "line": 5, "column": 10, "nodeType": "499", "messageId": "500", "endLine": 5, "endColumn": 20}, {"ruleId": "497", "severity": 1, "message": "524", "line": 12, "column": 10, "nodeType": "499", "messageId": "500", "endLine": 12, "endColumn": 23}, {"ruleId": "506", "severity": 1, "message": "525", "line": 20, "column": 8, "nodeType": "508", "endLine": 20, "endColumn": 18, "suggestions": "526"}, {"ruleId": "497", "severity": 1, "message": "527", "line": 2, "column": 34, "nodeType": "499", "messageId": "500", "endLine": 2, "endColumn": 44}, {"ruleId": "497", "severity": 1, "message": "528", "line": 2, "column": 61, "nodeType": "499", "messageId": "500", "endLine": 2, "endColumn": 67}, {"ruleId": "497", "severity": 1, "message": "529", "line": 15, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 15, "endColumn": 11}, {"ruleId": "497", "severity": 1, "message": "530", "line": 27, "column": 13, "nodeType": "499", "messageId": "500", "endLine": 27, "endColumn": 23}, {"ruleId": "497", "severity": 1, "message": "502", "line": 36, "column": 13, "nodeType": "499", "messageId": "500", "endLine": 36, "endColumn": 23}, {"ruleId": "497", "severity": 1, "message": "531", "line": 37, "column": 16, "nodeType": "499", "messageId": "500", "endLine": 37, "endColumn": 29}, {"ruleId": "497", "severity": 1, "message": "532", "line": 38, "column": 21, "nodeType": "499", "messageId": "500", "endLine": 38, "endColumn": 39}, {"ruleId": "497", "severity": 1, "message": "533", "line": 39, "column": 11, "nodeType": "499", "messageId": "500", "endLine": 39, "endColumn": 19}, {"ruleId": "497", "severity": 1, "message": "534", "line": 40, "column": 15, "nodeType": "499", "messageId": "500", "endLine": 40, "endColumn": 27}, {"ruleId": "497", "severity": 1, "message": "535", "line": 42, "column": 12, "nodeType": "499", "messageId": "500", "endLine": 42, "endColumn": 21}, {"ruleId": "497", "severity": 1, "message": "536", "line": 43, "column": 10, "nodeType": "499", "messageId": "500", "endLine": 43, "endColumn": 17}, {"ruleId": "497", "severity": 1, "message": "537", "line": 44, "column": 15, "nodeType": "499", "messageId": "500", "endLine": 44, "endColumn": 27}, {"ruleId": "497", "severity": 1, "message": "538", "line": 45, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 45, "endColumn": 13}, {"ruleId": "497", "severity": 1, "message": "539", "line": 46, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 46, "endColumn": 13}, {"ruleId": "497", "severity": 1, "message": "540", "line": 35, "column": 18, "nodeType": "499", "messageId": "500", "endLine": 35, "endColumn": 33}, {"ruleId": "497", "severity": 1, "message": "541", "line": 32, "column": 15, "nodeType": "499", "messageId": "500", "endLine": 32, "endColumn": 27}, {"ruleId": "497", "severity": 1, "message": "542", "line": 34, "column": 14, "nodeType": "499", "messageId": "500", "endLine": 34, "endColumn": 25}, {"ruleId": "497", "severity": 1, "message": "540", "line": 35, "column": 18, "nodeType": "499", "messageId": "500", "endLine": 35, "endColumn": 33}, {"ruleId": "497", "severity": 1, "message": "543", "line": 11, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 11, "endColumn": 12}, {"ruleId": "497", "severity": 1, "message": "544", "line": 43, "column": 11, "nodeType": "499", "messageId": "500", "endLine": 43, "endColumn": 19}, {"ruleId": "497", "severity": 1, "message": "545", "line": 52, "column": 22, "nodeType": "499", "messageId": "500", "endLine": 52, "endColumn": 36}, {"ruleId": "497", "severity": 1, "message": "546", "line": 94, "column": 34, "nodeType": "499", "messageId": "500", "endLine": 94, "endColumn": 39}, {"ruleId": "497", "severity": 1, "message": "547", "line": 94, "column": 41, "nodeType": "499", "messageId": "500", "endLine": 94, "endColumn": 49}, {"ruleId": "497", "severity": 1, "message": "548", "line": 1, "column": 27, "nodeType": "499", "messageId": "500", "endLine": 1, "endColumn": 36}, {"ruleId": "497", "severity": 1, "message": "514", "line": 14, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 14, "endColumn": 10}, {"ruleId": "497", "severity": 1, "message": "527", "line": 15, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 15, "endColumn": 13}, {"ruleId": "497", "severity": 1, "message": "549", "line": 26, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 26, "endColumn": 9}, {"ruleId": "497", "severity": 1, "message": "550", "line": 27, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 27, "endColumn": 14}, {"ruleId": "497", "severity": 1, "message": "551", "line": 28, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 28, "endColumn": 16}, {"ruleId": "497", "severity": 1, "message": "552", "line": 29, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 29, "endColumn": 16}, {"ruleId": "497", "severity": 1, "message": "553", "line": 39, "column": 11, "nodeType": "499", "messageId": "500", "endLine": 39, "endColumn": 19}, {"ruleId": "497", "severity": 1, "message": "554", "line": 43, "column": 15, "nodeType": "499", "messageId": "500", "endLine": 43, "endColumn": 27}, {"ruleId": "497", "severity": 1, "message": "555", "line": 114, "column": 9, "nodeType": "499", "messageId": "500", "endLine": 114, "endColumn": 17}, {"ruleId": "497", "severity": 1, "message": "556", "line": 121, "column": 10, "nodeType": "499", "messageId": "500", "endLine": 121, "endColumn": 20}, {"ruleId": "497", "severity": 1, "message": "557", "line": 121, "column": 22, "nodeType": "499", "messageId": "500", "endLine": 121, "endColumn": 35}, {"ruleId": "497", "severity": 1, "message": "514", "line": 19, "column": 3, "nodeType": "499", "messageId": "500", "endLine": 19, "endColumn": 10}, {"ruleId": "497", "severity": 1, "message": "558", "line": 32, "column": 12, "nodeType": "499", "messageId": "500", "endLine": 32, "endColumn": 21}, {"ruleId": "497", "severity": 1, "message": "559", "line": 33, "column": 12, "nodeType": "499", "messageId": "500", "endLine": 33, "endColumn": 21}, {"ruleId": "497", "severity": 1, "message": "560", "line": 35, "column": 11, "nodeType": "499", "messageId": "500", "endLine": 35, "endColumn": 19}, {"ruleId": "497", "severity": 1, "message": "561", "line": 36, "column": 16, "nodeType": "499", "messageId": "500", "endLine": 36, "endColumn": 29}, {"ruleId": "497", "severity": 1, "message": "547", "line": 83, "column": 24, "nodeType": "499", "messageId": "500", "endLine": 83, "endColumn": 32}, "no-unused-vars", "'ViewStudent' is defined but never used.", "Identifier", "unusedVar", "'TrendingUpIcon' is defined but never used.", "'PeopleIcon' is defined but never used.", "'isMobile' is assigned a value but never used.", "'attendanceRate' is assigned a value but never used.", "'averageGrade' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch' and 'params'. Either include them or remove the dependency array.", "ArrayExpression", ["562"], "React Hook useEffect has a missing dependency: 'situation'. Either include it or remove the dependency array.", ["563"], ["564"], ["565"], "'Divider' is defined but never used.", "'Badge' is defined but never used.", "'PhotoCameraIcon' is defined but never used.", "'profilePicture' is assigned a value but never used.", "'deleteUser' is defined but never used.", "'showTab' is assigned a value but never used.", "'setShowTab' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'submitHandler' is assigned a value but never used.", ["566"], "'resetSubjects' is defined but never used.", "React Hook useEffect has missing dependencies: 'currentRole', 'currentUser._id', and 'currentUser.school._id'. Either include them or remove the dependency array.", ["567"], "'IconButton' is defined but never used.", "'Legend' is defined but never used.", "'Collapse' is defined but never used.", "'ReportIcon' is defined but never used.", "'PersonAddIcon' is defined but never used.", "'EventAvailableIcon' is defined but never used.", "'QuizIcon' is defined but never used.", "'MenuBookIcon' is defined but never used.", "'HotelIcon' is defined but never used.", "'WebIcon' is defined but never used.", "'BusinessIcon' is defined but never used.", "'ExpandLess' is defined but never used.", "'ExpandMore' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'TextField' is defined but never used.", "'HomeIcon' is defined but never used.", "'getUserDetails' is defined but never used.", "'error' is assigned a value but never used.", "'response' is assigned a value but never used.", "'useEffect' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'EditIcon' is defined but never used.", "'LanguageIcon' is defined but never used.", "'dispatch' is assigned a value but never used.", "'logoDialog' is assigned a value but never used.", "'setLogoDialog' is assigned a value but never used.", "'PhoneIcon' is defined but never used.", "'EmailIcon' is defined but never used.", "'CakeIcon' is defined but never used.", "'BloodtypeIcon' is defined but never used.", {"desc": "568", "fix": "569"}, {"desc": "570", "fix": "571"}, {"desc": "568", "fix": "572"}, {"desc": "570", "fix": "573"}, {"desc": "568", "fix": "574"}, {"desc": "575", "fix": "576"}, "Update the dependencies array to be: [dispatch, params, situation]", {"range": "577", "text": "578"}, "Update the dependencies array to be: [dispatch, situation, userDetails]", {"range": "579", "text": "580"}, {"range": "581", "text": "578"}, {"range": "582", "text": "580"}, {"range": "583", "text": "578"}, "Update the dependencies array to be: [currentRole, currentUser._id, currentUser.school._id, dispatch]", {"range": "584", "text": "585"}, [1899, 1910], "[dispatch, params, situation]", [2122, 2145], "[dispatch, situation, userDetails]", [1867, 1878], [2090, 2113], [1458, 1469], [747, 757], "[currentR<PERSON>, currentUser._id, currentUser.school._id, dispatch]"]