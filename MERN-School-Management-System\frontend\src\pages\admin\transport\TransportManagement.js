import React, { useState } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  DirectionsBus as DirectionsBusIcon,
  LocationOn as LocationOnIcon,
  Speed as SpeedIcon,
  Person as PersonIcon,
  Route as RouteIcon,
  Schedule as ScheduleIcon,
  Gps as GpsIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Phone as PhoneIcon
} from '@mui/icons-material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
}));

const VehicleCard = styled(Card)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
  },
}));

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`transport-tabpanel-${index}`}
    aria-labelledby={`transport-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const TransportManagement = () => {
  const [tabValue, setTabValue] = useState(0);

  // Sample transport data
  const vehicles = [
    {
      id: 1,
      vehicleNumber: 'BUS-001',
      driverName: 'John Smith',
      route: 'Route A - Downtown',
      capacity: 40,
      currentOccupancy: 35,
      status: 'Active',
      location: 'Main Street',
      speed: 25,
      lastUpdate: '2 mins ago'
    },
    {
      id: 2,
      vehicleNumber: 'BUS-002',
      driverName: 'Mike Johnson',
      route: 'Route B - Suburbs',
      capacity: 45,
      currentOccupancy: 42,
      status: 'Active',
      location: 'Oak Avenue',
      speed: 30,
      lastUpdate: '1 min ago'
    },
    {
      id: 3,
      vehicleNumber: 'BUS-003',
      driverName: 'Sarah Wilson',
      route: 'Route C - Industrial',
      capacity: 35,
      currentOccupancy: 0,
      status: 'Maintenance',
      location: 'School Garage',
      speed: 0,
      lastUpdate: '1 hour ago'
    },
  ];

  const routes = [
    {
      id: 1,
      name: 'Route A - Downtown',
      stops: ['Main St', 'Central Park', 'City Mall', 'School'],
      distance: '15 km',
      duration: '45 mins',
      students: 35
    },
    {
      id: 2,
      name: 'Route B - Suburbs',
      stops: ['Oak Ave', 'Pine St', 'Maple Dr', 'School'],
      distance: '20 km',
      duration: '55 mins',
      students: 42
    },
  ];

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Active': return 'success';
      case 'Maintenance': return 'warning';
      case 'Offline': return 'error';
      default: return 'default';
    }
  };

  const renderVehicleCard = (vehicle) => (
    <Grid item xs={12} md={6} lg={4} key={vehicle.id}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <VehicleCard>
          <CardContent>
            <Box display="flex" alignItems="center" mb={2}>
              <Avatar
                sx={{
                  bgcolor: vehicle.status === 'Active' ? 'success.main' : 'warning.main',
                  mr: 2,
                  width: 56,
                  height: 56
                }}
              >
                <DirectionsBusIcon fontSize="large" />
              </Avatar>
              <Box flex={1}>
                <Typography variant="h6" fontWeight="bold">
                  {vehicle.vehicleNumber}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Driver: {vehicle.driverName}
                </Typography>
                <Chip
                  label={vehicle.status}
                  color={getStatusColor(vehicle.status)}
                  size="small"
                  sx={{ mt: 0.5 }}
                />
              </Box>
            </Box>

            <Box mb={2}>
              <Box display="flex" alignItems="center" mb={1}>
                <RouteIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">{vehicle.route}</Typography>
              </Box>
              <Box display="flex" alignItems="center" mb={1}>
                <LocationOnIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">{vehicle.location}</Typography>
              </Box>
              <Box display="flex" alignItems="center" mb={1}>
                <SpeedIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">{vehicle.speed} km/h</Typography>
              </Box>
              <Box display="flex" alignItems="center" mb={1}>
                <PersonIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">
                  {vehicle.currentOccupancy}/{vehicle.capacity} students
                </Typography>
              </Box>
            </Box>

            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="caption" color="text.secondary">
                Updated: {vehicle.lastUpdate}
              </Typography>
              <Box>
                <Tooltip title="Track Live">
                  <IconButton size="small" color="primary">
                    <GpsIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Contact Driver">
                  <IconButton size="small" color="secondary">
                    <PhoneIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
          </CardContent>
        </VehicleCard>
      </motion.div>
    </Grid>
  );

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box mb={4}>
          <Typography variant="h4" fontWeight="bold" color="primary" gutterBottom>
            🚌 Transport Management System
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Vehicle tracking, GPS monitoring, and route management for parents
          </Typography>
        </Box>
      </motion.div>

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={3}>
          <StyledPaper sx={{ textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>
            <Typography variant="h3" fontWeight="bold">
              {vehicles.filter(v => v.status === 'Active').length}
            </Typography>
            <Typography variant="body1">Active Vehicles</Typography>
          </StyledPaper>
        </Grid>
        <Grid item xs={12} sm={3}>
          <StyledPaper sx={{ textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>
            <Typography variant="h3" fontWeight="bold">
              {vehicles.reduce((sum, v) => sum + v.currentOccupancy, 0)}
            </Typography>
            <Typography variant="body1">Students Onboard</Typography>
          </StyledPaper>
        </Grid>
        <Grid item xs={12} sm={3}>
          <StyledPaper sx={{ textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>
            <Typography variant="h3" fontWeight="bold">
              {routes.length}
            </Typography>
            <Typography variant="body1">Active Routes</Typography>
          </StyledPaper>
        </Grid>
        <Grid item xs={12} sm={3}>
          <StyledPaper sx={{ textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>
            <Typography variant="h3" fontWeight="bold">
              {vehicles.filter(v => v.status === 'Maintenance').length}
            </Typography>
            <Typography variant="body1">In Maintenance</Typography>
          </StyledPaper>
        </Grid>
      </Grid>

      <StyledPaper>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h6" fontWeight="bold">
            Transport Dashboard
          </Typography>
          <Box display="flex" gap={2}>
            <Button variant="contained" color="primary" startIcon={<GpsIcon />}>
              Live Tracking
            </Button>
            <Button variant="outlined" color="secondary" startIcon={<RouteIcon />}>
              Manage Routes
            </Button>
            <Button variant="outlined" color="info" startIcon={<DirectionsBusIcon />}>
              Add Vehicle
            </Button>
          </Box>
        </Box>

        <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="Live Tracking" />
          <Tab label="Routes" />
          <Tab label="Drivers" />
          <Tab label="Maintenance" />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            {vehicles.map(renderVehicleCard)}
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <TableContainer component={Paper} sx={{ borderRadius: '16px' }}>
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: 'primary.main' }}>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Route Name</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Stops</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Distance</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Duration</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Students</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {routes.map((route) => (
                  <TableRow key={route.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {route.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" flexWrap="wrap" gap={0.5}>
                        {route.stops.map((stop, index) => (
                          <Chip key={index} label={stop} size="small" variant="outlined" />
                        ))}
                      </Box>
                    </TableCell>
                    <TableCell>{route.distance}</TableCell>
                    <TableCell>{route.duration}</TableCell>
                    <TableCell>
                      <Chip label={route.students} color="primary" size="small" />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <Tooltip title="View Route">
                          <IconButton size="small" color="primary">
                            <RouteIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Track">
                          <IconButton size="small" color="success">
                            <GpsIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Driver Management
          </Typography>
          <Typography color="text.secondary">
            Driver profiles, contact information, and performance tracking will be displayed here.
          </Typography>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>
            Vehicle Maintenance
          </Typography>
          <Typography color="text.secondary">
            Maintenance schedules, service records, and vehicle health monitoring will be displayed here.
          </Typography>
        </TabPanel>
      </StyledPaper>
    </Container>
  );
};

export default TransportManagement;
