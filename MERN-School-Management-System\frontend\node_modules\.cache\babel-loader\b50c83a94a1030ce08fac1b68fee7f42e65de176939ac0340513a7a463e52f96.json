{"ast": null, "code": "import { cubicBezier } from './cubic-bezier.mjs';\nimport { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\nconst backOut = /*@__PURE__*/cubicBezier(0.33, 1.53, 0.69, 0.99);\nconst backIn = /*@__PURE__*/reverseEasing(backOut);\nconst backInOut = /*@__PURE__*/mirrorEasing(backIn);\nexport { backIn, backInOut, backOut };", "map": {"version": 3, "names": ["cubicBezier", "mirrorEasing", "reverseEasing", "backOut", "backIn", "backInOut"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/motion-utils/dist/es/easing/back.mjs"], "sourcesContent": ["import { cubicBezier } from './cubic-bezier.mjs';\nimport { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst backOut = /*@__PURE__*/ cubicBezier(0.33, 1.53, 0.69, 0.99);\nconst backIn = /*@__PURE__*/ reverseEasing(backOut);\nconst backInOut = /*@__PURE__*/ mirrorEasing(backIn);\n\nexport { backIn, backInOut, backOut };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,yBAAyB;AAEvD,MAAMC,OAAO,GAAG,aAAcH,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACjE,MAAMI,MAAM,GAAG,aAAcF,aAAa,CAACC,OAAO,CAAC;AACnD,MAAME,SAAS,GAAG,aAAcJ,YAAY,CAACG,MAAM,CAAC;AAEpD,SAASA,MAAM,EAAEC,SAAS,EAAEF,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}