{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\pages\\\\Homepage.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Container, Grid, Box } from '@mui/material';\nimport styled from 'styled-components';\nimport Students from \"../assets/students.svg\";\nimport { LightPurpleButton } from '../components/buttonStyles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Homepage = () => {\n  return /*#__PURE__*/_jsxDEV(StyledContainer, {\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 0,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: Students,\n          alt: \"students\",\n          style: {\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(StyledPaper, {\n          elevation: 3,\n          children: [/*#__PURE__*/_jsxDEV(StyledTitle, {\n            children: [\"Welcome to\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 29\n            }, this), \"School Management\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 29\n            }, this), \"System\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(StyledText, {\n            children: \"Streamline school management, class organization, and add students and faculty. Seamlessly track attendance, assess performance, and provide feedback. Access records, view marks, and communicate effortlessly.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(StyledBox, {\n            children: [/*#__PURE__*/_jsxDEV(StyledLink, {\n              to: \"/choose\",\n              children: /*#__PURE__*/_jsxDEV(LightPurpleButton, {\n                variant: \"contained\",\n                fullWidth: true,\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(StyledText, {\n              children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/Adminregister\",\n                style: {\n                  color: \"#550080\"\n                },\n                children: \"Sign up\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 9\n  }, this);\n};\n_c = Homepage;\nexport default Homepage;\nconst StyledContainer = styled(Container)`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n`;\n_c2 = StyledContainer;\nconst StyledPaper = styled.div`\n  padding: 24px;\n  height: 100vh;\n`;\n_c3 = StyledPaper;\nconst StyledBox = styled(Box)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content:center;\n  gap: 16px;\n  padding: 24px;\n`;\n_c4 = StyledBox;\nconst StyledTitle = styled.h1`\n  font-size: 3rem;\n  color: #252525;\n  /* font-family: \"Manrope\"; */\n  font-weight: bold;\n  padding-top: 0;\n  letter-spacing: normal;\n  line-height: normal;\n`;\n_c5 = StyledTitle;\nconst StyledText = styled.p`\n  /* color: #550080; */\n  margin-top: 30px;\n  margin-bottom: 30px; \n  letter-spacing: normal;\n  line-height: normal;\n`;\n_c6 = StyledText;\nconst StyledLink = styled(Link)`\n  text-decoration: none;\n`;\n_c7 = StyledLink;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"Homepage\");\n$RefreshReg$(_c2, \"StyledContainer\");\n$RefreshReg$(_c3, \"StyledPaper\");\n$RefreshReg$(_c4, \"StyledBox\");\n$RefreshReg$(_c5, \"StyledTitle\");\n$RefreshReg$(_c6, \"StyledText\");\n$RefreshReg$(_c7, \"StyledLink\");", "map": {"version": 3, "names": ["React", "Link", "Container", "Grid", "Box", "styled", "Students", "LightPurpleButton", "jsxDEV", "_jsxDEV", "Homepage", "StyledContainer", "children", "container", "spacing", "item", "xs", "md", "src", "alt", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "StyledPaper", "elevation", "StyledTitle", "StyledText", "StyledBox", "StyledLink", "to", "variant", "fullWidth", "color", "_c", "_c2", "div", "_c3", "_c4", "h1", "_c5", "p", "_c6", "_c7", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/pages/Homepage.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { Container, Grid, Box } from '@mui/material';\r\nimport styled from 'styled-components';\r\nimport Students from \"../assets/students.svg\";\r\nimport { LightPurpleButton } from '../components/buttonStyles';\r\n\r\nconst Homepage = () => {\r\n    return (\r\n        <StyledContainer>\r\n            <Grid container spacing={0}>\r\n                <Grid item xs={12} md={6}>\r\n                    <img src={Students} alt=\"students\" style={{ width: '100%' }} />\r\n                </Grid>\r\n                <Grid item xs={12} md={6}>\r\n                    <StyledPaper elevation={3}>\r\n                        <StyledTitle>\r\n                            Welcome to\r\n                            <br />\r\n                            School Management\r\n                            <br />\r\n                            System\r\n                        </StyledTitle>\r\n                        <StyledText>\r\n                            Streamline school management, class organization, and add students and faculty.\r\n                            Seamlessly track attendance, assess performance, and provide feedback.\r\n                            Access records, view marks, and communicate effortlessly.\r\n                        </StyledText>\r\n                        <StyledBox>\r\n                            <StyledLink to=\"/choose\">\r\n                                <LightPurpleButton variant=\"contained\" fullWidth>\r\n                                    Login\r\n                                </LightPurpleButton>\r\n                            </StyledLink>\r\n                            <StyledText>\r\n                                Don't have an account?{' '}\r\n                                <Link to=\"/Adminregister\" style={{color:\"#550080\"}}>\r\n                                    Sign up\r\n                                </Link>\r\n                            </StyledText>\r\n                        </StyledBox>\r\n                    </StyledPaper>\r\n                </Grid>\r\n            </Grid>\r\n        </StyledContainer>\r\n    );\r\n};\r\n\r\nexport default Homepage;\r\n\r\nconst StyledContainer = styled(Container)`\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100vh;\r\n`;\r\n\r\nconst StyledPaper = styled.div`\r\n  padding: 24px;\r\n  height: 100vh;\r\n`;\r\n\r\nconst StyledBox = styled(Box)`\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content:center;\r\n  gap: 16px;\r\n  padding: 24px;\r\n`;\r\n\r\nconst StyledTitle = styled.h1`\r\n  font-size: 3rem;\r\n  color: #252525;\r\n  /* font-family: \"Manrope\"; */\r\n  font-weight: bold;\r\n  padding-top: 0;\r\n  letter-spacing: normal;\r\n  line-height: normal;\r\n`;\r\n\r\nconst StyledText = styled.p`\r\n  /* color: #550080; */\r\n  margin-top: 30px;\r\n  margin-bottom: 30px; \r\n  letter-spacing: normal;\r\n  line-height: normal;\r\n`;\r\n\r\nconst StyledLink = styled(Link)`\r\n  text-decoration: none;\r\n`;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,SAAS,EAAEC,IAAI,EAAEC,GAAG,QAAQ,eAAe;AACpD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,SAASC,iBAAiB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACnB,oBACID,OAAA,CAACE,eAAe;IAAAC,QAAA,eACZH,OAAA,CAACN,IAAI;MAACU,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAF,QAAA,gBACvBH,OAAA,CAACN,IAAI;QAACY,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACrBH,OAAA;UAAKS,GAAG,EAAEZ,QAAS;UAACa,GAAG,EAAC,UAAU;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC5D,eACPhB,OAAA,CAACN,IAAI;QAACY,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACrBH,OAAA,CAACiB,WAAW;UAACC,SAAS,EAAE,CAAE;UAAAf,QAAA,gBACtBH,OAAA,CAACmB,WAAW;YAAAhB,QAAA,GAAC,YAET,eAAAH,OAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAM,qBAEN,eAAAhB,OAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAM,UAEV;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAc,eACdhB,OAAA,CAACoB,UAAU;YAAAjB,QAAA,EAAC;UAIZ;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAa,eACbhB,OAAA,CAACqB,SAAS;YAAAlB,QAAA,gBACNH,OAAA,CAACsB,UAAU;cAACC,EAAE,EAAC,SAAS;cAAApB,QAAA,eACpBH,OAAA,CAACF,iBAAiB;gBAAC0B,OAAO,EAAC,WAAW;gBAACC,SAAS;gBAAAtB,QAAA,EAAC;cAEjD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAoB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACX,eACbhB,OAAA,CAACoB,UAAU;cAAAjB,QAAA,GAAC,wBACc,EAAC,GAAG,eAC1BH,OAAA,CAACR,IAAI;gBAAC+B,EAAE,EAAC,gBAAgB;gBAACZ,KAAK,EAAE;kBAACe,KAAK,EAAC;gBAAS,CAAE;gBAAAvB,QAAA,EAAC;cAEpD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAO;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACL;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACX;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACJ;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACO;AAE1B,CAAC;AAACW,EAAA,GAvCI1B,QAAQ;AAyCd,eAAeA,QAAQ;AAEvB,MAAMC,eAAe,GAAGN,MAAM,CAACH,SAAS,CAAE;AAC1C;AACA;AACA;AACA;AACA,CAAC;AAACmC,GAAA,GALI1B,eAAe;AAOrB,MAAMe,WAAW,GAAGrB,MAAM,CAACiC,GAAI;AAC/B;AACA;AACA,CAAC;AAACC,GAAA,GAHIb,WAAW;AAKjB,MAAMI,SAAS,GAAGzB,MAAM,CAACD,GAAG,CAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,GAAA,GAPIV,SAAS;AASf,MAAMF,WAAW,GAAGvB,MAAM,CAACoC,EAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GARId,WAAW;AAUjB,MAAMC,UAAU,GAAGxB,MAAM,CAACsC,CAAE;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIf,UAAU;AAQhB,MAAME,UAAU,GAAG1B,MAAM,CAACJ,IAAI,CAAE;AAChC;AACA,CAAC;AAAC4C,GAAA,GAFId,UAAU;AAAA,IAAAK,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}