{"ast": null, "code": "import { invariant, warning } from 'motion-utils';\nimport { useContext } from 'react';\nimport { MotionContext } from '../context/MotionContext/index.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nimport { useTransform } from './use-transform.mjs';\n\n// Keep things reasonable and avoid scale: Infinity. In practise we might need\n// to add another value, opacity, that could interpolate scaleX/Y [0,0.01] => [0,1]\n// to simply hide content at unreasonable scales.\nconst maxScale = 100000;\nconst invertScale = scale => scale > 0.001 ? 1 / scale : maxScale;\nlet hasWarned = false;\n/**\n * Returns a `MotionValue` each for `scaleX` and `scaleY` that update with the inverse\n * of their respective parent scales.\n *\n * This is useful for undoing the distortion of content when scaling a parent component.\n *\n * By default, `useInvertedScale` will automatically fetch `scaleX` and `scaleY` from the nearest parent.\n * By passing other `MotionValue`s in as `useInvertedScale({ scaleX, scaleY })`, it will invert the output\n * of those instead.\n *\n * ```jsx\n * const MyComponent = () => {\n *   const { scaleX, scaleY } = useInvertedScale()\n *   return <motion.div style={{ scaleX, scaleY }} />\n * }\n * ```\n *\n * @deprecated\n */\nfunction useInvertedScale(scale) {\n  let parentScaleX = useMotionValue(1);\n  let parentScaleY = useMotionValue(1);\n  const {\n    visualElement\n  } = useContext(MotionContext);\n  invariant(!!(scale || visualElement), \"If no scale values are provided, useInvertedScale must be used within a child of another motion component.\");\n  warning(hasWarned, \"useInvertedScale is deprecated and will be removed in 3.0. Use the layout prop instead.\");\n  hasWarned = true;\n  if (scale) {\n    parentScaleX = scale.scaleX || parentScaleX;\n    parentScaleY = scale.scaleY || parentScaleY;\n  } else if (visualElement) {\n    parentScaleX = visualElement.getValue(\"scaleX\", 1);\n    parentScaleY = visualElement.getValue(\"scaleY\", 1);\n  }\n  const scaleX = useTransform(parentScaleX, invertScale);\n  const scaleY = useTransform(parentScaleY, invertScale);\n  return {\n    scaleX,\n    scaleY\n  };\n}\nexport { invertScale, useInvertedScale };", "map": {"version": 3, "names": ["invariant", "warning", "useContext", "MotionContext", "useMotionValue", "useTransform", "maxScale", "invertScale", "scale", "hasWarned", "useInvertedScale", "parentScaleX", "parentScaleY", "visualElement", "scaleX", "scaleY", "getValue"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/value/use-inverted-scale.mjs"], "sourcesContent": ["import { invariant, warning } from 'motion-utils';\nimport { useContext } from 'react';\nimport { MotionContext } from '../context/MotionContext/index.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nimport { useTransform } from './use-transform.mjs';\n\n// Keep things reasonable and avoid scale: Infinity. In practise we might need\n// to add another value, opacity, that could interpolate scaleX/Y [0,0.01] => [0,1]\n// to simply hide content at unreasonable scales.\nconst maxScale = 100000;\nconst invertScale = (scale) => scale > 0.001 ? 1 / scale : maxScale;\nlet hasWarned = false;\n/**\n * Returns a `MotionValue` each for `scaleX` and `scaleY` that update with the inverse\n * of their respective parent scales.\n *\n * This is useful for undoing the distortion of content when scaling a parent component.\n *\n * By default, `useInvertedScale` will automatically fetch `scaleX` and `scaleY` from the nearest parent.\n * By passing other `MotionValue`s in as `useInvertedScale({ scaleX, scaleY })`, it will invert the output\n * of those instead.\n *\n * ```jsx\n * const MyComponent = () => {\n *   const { scaleX, scaleY } = useInvertedScale()\n *   return <motion.div style={{ scaleX, scaleY }} />\n * }\n * ```\n *\n * @deprecated\n */\nfunction useInvertedScale(scale) {\n    let parentScaleX = useMotionValue(1);\n    let parentScaleY = useMotionValue(1);\n    const { visualElement } = useContext(MotionContext);\n    invariant(!!(scale || visualElement), \"If no scale values are provided, useInvertedScale must be used within a child of another motion component.\");\n    warning(hasWarned, \"useInvertedScale is deprecated and will be removed in 3.0. Use the layout prop instead.\");\n    hasWarned = true;\n    if (scale) {\n        parentScaleX = scale.scaleX || parentScaleX;\n        parentScaleY = scale.scaleY || parentScaleY;\n    }\n    else if (visualElement) {\n        parentScaleX = visualElement.getValue(\"scaleX\", 1);\n        parentScaleY = visualElement.getValue(\"scaleY\", 1);\n    }\n    const scaleX = useTransform(parentScaleX, invertScale);\n    const scaleY = useTransform(parentScaleY, invertScale);\n    return { scaleX, scaleY };\n}\n\nexport { invertScale, useInvertedScale };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,OAAO,QAAQ,cAAc;AACjD,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,YAAY,QAAQ,qBAAqB;;AAElD;AACA;AACA;AACA,MAAMC,QAAQ,GAAG,MAAM;AACvB,MAAMC,WAAW,GAAIC,KAAK,IAAKA,KAAK,GAAG,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAGF,QAAQ;AACnE,IAAIG,SAAS,GAAG,KAAK;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACF,KAAK,EAAE;EAC7B,IAAIG,YAAY,GAAGP,cAAc,CAAC,CAAC,CAAC;EACpC,IAAIQ,YAAY,GAAGR,cAAc,CAAC,CAAC,CAAC;EACpC,MAAM;IAAES;EAAc,CAAC,GAAGX,UAAU,CAACC,aAAa,CAAC;EACnDH,SAAS,CAAC,CAAC,EAAEQ,KAAK,IAAIK,aAAa,CAAC,EAAE,4GAA4G,CAAC;EACnJZ,OAAO,CAACQ,SAAS,EAAE,yFAAyF,CAAC;EAC7GA,SAAS,GAAG,IAAI;EAChB,IAAID,KAAK,EAAE;IACPG,YAAY,GAAGH,KAAK,CAACM,MAAM,IAAIH,YAAY;IAC3CC,YAAY,GAAGJ,KAAK,CAACO,MAAM,IAAIH,YAAY;EAC/C,CAAC,MACI,IAAIC,aAAa,EAAE;IACpBF,YAAY,GAAGE,aAAa,CAACG,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;IAClDJ,YAAY,GAAGC,aAAa,CAACG,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;EACtD;EACA,MAAMF,MAAM,GAAGT,YAAY,CAACM,YAAY,EAAEJ,WAAW,CAAC;EACtD,MAAMQ,MAAM,GAAGV,YAAY,CAACO,YAAY,EAAEL,WAAW,CAAC;EACtD,OAAO;IAAEO,MAAM;IAAEC;EAAO,CAAC;AAC7B;AAEA,SAASR,WAAW,EAAEG,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}