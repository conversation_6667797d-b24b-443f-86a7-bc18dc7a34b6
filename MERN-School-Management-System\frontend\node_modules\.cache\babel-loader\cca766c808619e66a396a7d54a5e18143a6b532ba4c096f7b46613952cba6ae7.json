{"ast": null, "code": "var _jsxFileName = \"A:\\\\11\\\\MERN-School-Management-System\\\\frontend\\\\src\\\\pages\\\\admin\\\\classRelated\\\\AddClass.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Button, CircularProgress, Stack, TextField, Typography, Paper, Container, Card, CardContent, Grid, Chip, Avatar } from \"@mui/material\";\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { addStuff } from '../../../redux/userRelated/userHandle';\nimport { underControl } from '../../../redux/userRelated/userSlice';\nimport { BlueButton } from \"../../../components/buttonStyles\";\nimport Popup from \"../../../components/Popup\";\nimport Classroom from \"../../../assets/classroom.png\";\nimport styled from \"styled-components\";\nimport ClassIcon from '@mui/icons-material/Class';\nimport SchoolIcon from '@mui/icons-material/School';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddClass = () => {\n  _s();\n  const [sclassName, setSclassName] = useState(\"\");\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const userState = useSelector(state => state.user);\n  const {\n    status,\n    currentUser,\n    response,\n    error,\n    tempDetails\n  } = userState;\n  const adminID = currentUser._id;\n  const address = \"Sclass\";\n  const [loader, setLoader] = useState(false);\n  const [message, setMessage] = useState(\"\");\n  const [showPopup, setShowPopup] = useState(false);\n  const fields = {\n    sclassName,\n    adminID\n  };\n  const submitHandler = event => {\n    event.preventDefault();\n    setLoader(true);\n    dispatch(addStuff(fields, address));\n  };\n  useEffect(() => {\n    if (status === 'added' && tempDetails) {\n      navigate(\"/Admin/classes/class/\" + tempDetails._id);\n      dispatch(underControl());\n      setLoader(false);\n    } else if (status === 'failed') {\n      setMessage(response);\n      setShowPopup(true);\n      setLoader(false);\n    } else if (status === 'error') {\n      setMessage(\"Network Error\");\n      setShowPopup(true);\n      setLoader(false);\n    }\n  }, [status, navigate, error, response, dispatch, tempDetails]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(StyledContainer, {\n      children: /*#__PURE__*/_jsxDEV(StyledBox, {\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          sx: {\n            alignItems: 'center',\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: Classroom,\n            alt: \"classroom\",\n            style: {\n              width: '80%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: submitHandler,\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              label: \"Create a class\",\n              variant: \"outlined\",\n              value: sclassName,\n              onChange: event => {\n                setSclassName(event.target.value);\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(BlueButton, {\n              fullWidth: true,\n              size: \"large\",\n              sx: {\n                mt: 3\n              },\n              variant: \"contained\",\n              type: \"submit\",\n              disabled: loader,\n              children: loader ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 24,\n                color: \"inherit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 43\n              }, this) : \"Create\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              onClick: () => navigate(-1),\n              children: \"Go Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Popup, {\n      message: message,\n      setShowPopup: setShowPopup,\n      showPopup: showPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(AddClass, \"IL9v/YSOx/tg8MCx2zUIGVp6+2U=\", false, function () {\n  return [useDispatch, useNavigate, useSelector];\n});\n_c = AddClass;\nexport default AddClass;\nconst StyledContainer = styled(Box)`\n  flex: 1 1 auto;\n  align-items: center;\n  display: flex;\n  justify-content: center;\n`;\n_c2 = StyledContainer;\nconst StyledBox = styled(Box)`\n  max-width: 550px;\n  padding: 50px 3rem 50px;\n  margin-top: 1rem;\n  background-color: white;\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);\n  border: 1px solid #ccc;\n  border-radius: 4px;\n`;\n_c3 = StyledBox;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AddClass\");\n$RefreshReg$(_c2, \"StyledContainer\");\n$RefreshReg$(_c3, \"StyledBox\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "TextField", "Typography", "Paper", "Container", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "Chip", "Avatar", "useNavigate", "useDispatch", "useSelector", "addStuff", "underControl", "BlueButton", "Popup", "Classroom", "styled", "ClassIcon", "SchoolIcon", "ArrowBackIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddClass", "_s", "sclassName", "setSclassName", "dispatch", "navigate", "userState", "state", "user", "status", "currentUser", "response", "error", "tempDetails", "adminID", "_id", "address", "loader", "<PERSON><PERSON><PERSON><PERSON>", "message", "setMessage", "showPopup", "setShowPopup", "fields", "<PERSON><PERSON><PERSON><PERSON>", "event", "preventDefault", "children", "StyledContainer", "StyledBox", "sx", "alignItems", "mb", "src", "alt", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "spacing", "label", "variant", "value", "onChange", "target", "required", "fullWidth", "size", "mt", "type", "disabled", "color", "onClick", "_c", "_c2", "_c3", "$RefreshReg$"], "sources": ["A:/11/MERN-School-Management-System/frontend/src/pages/admin/classRelated/AddClass.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n  Box,\r\n  Button,\r\n  CircularProgress,\r\n  Stack,\r\n  TextField,\r\n  Typography,\r\n  Paper,\r\n  Container,\r\n  Card,\r\n  CardContent,\r\n  Grid,\r\n  Chip,\r\n  Avatar\r\n} from \"@mui/material\";\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { addStuff } from '../../../redux/userRelated/userHandle';\r\nimport { underControl } from '../../../redux/userRelated/userSlice';\r\nimport { BlueButton } from \"../../../components/buttonStyles\";\r\nimport Popup from \"../../../components/Popup\";\r\nimport Classroom from \"../../../assets/classroom.png\";\r\nimport styled from \"styled-components\";\r\nimport ClassIcon from '@mui/icons-material/Class';\r\nimport SchoolIcon from '@mui/icons-material/School';\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\n\r\nconst AddClass = () => {\r\n    const [sclassName, setSclassName] = useState(\"\");\r\n\r\n    const dispatch = useDispatch()\r\n    const navigate = useNavigate()\r\n\r\n    const userState = useSelector(state => state.user);\r\n    const { status, currentUser, response, error, tempDetails } = userState;\r\n\r\n    const adminID = currentUser._id\r\n    const address = \"Sclass\"\r\n\r\n    const [loader, setLoader] = useState(false)\r\n    const [message, setMessage] = useState(\"\");\r\n    const [showPopup, setShowPopup] = useState(false);\r\n\r\n    const fields = {\r\n        sclassName,\r\n        adminID,\r\n    };\r\n\r\n    const submitHandler = (event) => {\r\n        event.preventDefault()\r\n        setLoader(true)\r\n        dispatch(addStuff(fields, address))\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (status === 'added' && tempDetails) {\r\n            navigate(\"/Admin/classes/class/\" + tempDetails._id)\r\n            dispatch(underControl())\r\n            setLoader(false)\r\n        }\r\n        else if (status === 'failed') {\r\n            setMessage(response)\r\n            setShowPopup(true)\r\n            setLoader(false)\r\n        }\r\n        else if (status === 'error') {\r\n            setMessage(\"Network Error\")\r\n            setShowPopup(true)\r\n            setLoader(false)\r\n        }\r\n    }, [status, navigate, error, response, dispatch, tempDetails]);\r\n    return (\r\n        <>\r\n            <StyledContainer>\r\n                <StyledBox>\r\n                    <Stack sx={{\r\n                        alignItems: 'center',\r\n                        mb: 3\r\n                    }}>\r\n                        <img\r\n                            src={Classroom}\r\n                            alt=\"classroom\"\r\n                            style={{ width: '80%' }}\r\n                        />\r\n                    </Stack>\r\n                    <form onSubmit={submitHandler}>\r\n                        <Stack spacing={3}>\r\n                            <TextField\r\n                                label=\"Create a class\"\r\n                                variant=\"outlined\"\r\n                                value={sclassName}\r\n                                onChange={(event) => {\r\n                                    setSclassName(event.target.value);\r\n                                }}\r\n                                required\r\n                            />\r\n                            <BlueButton\r\n                                fullWidth\r\n                                size=\"large\"\r\n                                sx={{ mt: 3 }}\r\n                                variant=\"contained\"\r\n                                type=\"submit\"\r\n                                disabled={loader}\r\n                            >\r\n                                {loader ? <CircularProgress size={24} color=\"inherit\" /> : \"Create\"}\r\n                            </BlueButton>\r\n                            <Button variant=\"outlined\" onClick={() => navigate(-1)}>\r\n                                Go Back\r\n                            </Button>\r\n                        </Stack>\r\n                    </form>\r\n                </StyledBox>\r\n            </StyledContainer>\r\n            <Popup message={message} setShowPopup={setShowPopup} showPopup={showPopup} />\r\n        </>\r\n    )\r\n}\r\n\r\nexport default AddClass\r\n\r\nconst StyledContainer = styled(Box)`\r\n  flex: 1 1 auto;\r\n  align-items: center;\r\n  display: flex;\r\n  justify-content: center;\r\n`;\r\n\r\nconst StyledBox = styled(Box)`\r\n  max-width: 550px;\r\n  padding: 50px 3rem 50px;\r\n  margin-top: 1rem;\r\n  background-color: white;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);\r\n  border: 1px solid #ccc;\r\n  border-radius: 4px;\r\n`;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,MAAM,EACNC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,IAAI,EACJC,MAAM,QACD,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,QAAQ,uCAAuC;AAChE,SAASC,YAAY,QAAQ,sCAAsC;AACnE,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,OAAOC,SAAS,MAAM,+BAA+B;AACrD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,aAAa,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMkC,QAAQ,GAAGnB,WAAW,EAAE;EAC9B,MAAMoB,QAAQ,GAAGrB,WAAW,EAAE;EAE9B,MAAMsB,SAAS,GAAGpB,WAAW,CAACqB,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EAClD,MAAM;IAAEC,MAAM;IAAEC,WAAW;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAY,CAAC,GAAGP,SAAS;EAEvE,MAAMQ,OAAO,GAAGJ,WAAW,CAACK,GAAG;EAC/B,MAAMC,OAAO,GAAG,QAAQ;EAExB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmD,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMqD,MAAM,GAAG;IACXrB,UAAU;IACVY;EACJ,CAAC;EAED,MAAMU,aAAa,GAAIC,KAAK,IAAK;IAC7BA,KAAK,CAACC,cAAc,EAAE;IACtBR,SAAS,CAAC,IAAI,CAAC;IACfd,QAAQ,CAACjB,QAAQ,CAACoC,MAAM,EAAEP,OAAO,CAAC,CAAC;EACvC,CAAC;EAED/C,SAAS,CAAC,MAAM;IACZ,IAAIwC,MAAM,KAAK,OAAO,IAAII,WAAW,EAAE;MACnCR,QAAQ,CAAC,uBAAuB,GAAGQ,WAAW,CAACE,GAAG,CAAC;MACnDX,QAAQ,CAAChB,YAAY,EAAE,CAAC;MACxB8B,SAAS,CAAC,KAAK,CAAC;IACpB,CAAC,MACI,IAAIT,MAAM,KAAK,QAAQ,EAAE;MAC1BW,UAAU,CAACT,QAAQ,CAAC;MACpBW,YAAY,CAAC,IAAI,CAAC;MAClBJ,SAAS,CAAC,KAAK,CAAC;IACpB,CAAC,MACI,IAAIT,MAAM,KAAK,OAAO,EAAE;MACzBW,UAAU,CAAC,eAAe,CAAC;MAC3BE,YAAY,CAAC,IAAI,CAAC;MAClBJ,SAAS,CAAC,KAAK,CAAC;IACpB;EACJ,CAAC,EAAE,CAACT,MAAM,EAAEJ,QAAQ,EAAEO,KAAK,EAAED,QAAQ,EAAEP,QAAQ,EAAES,WAAW,CAAC,CAAC;EAC9D,oBACIhB,OAAA,CAAAE,SAAA;IAAA4B,QAAA,gBACI9B,OAAA,CAAC+B,eAAe;MAAAD,QAAA,eACZ9B,OAAA,CAACgC,SAAS;QAAAF,QAAA,gBACN9B,OAAA,CAACvB,KAAK;UAACwD,EAAE,EAAE;YACPC,UAAU,EAAE,QAAQ;YACpBC,EAAE,EAAE;UACR,CAAE;UAAAL,QAAA,eACE9B,OAAA;YACIoC,GAAG,EAAE1C,SAAU;YACf2C,GAAG,EAAC,WAAW;YACfC,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAC1B;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACE,eACR3C,OAAA;UAAM4C,QAAQ,EAAEjB,aAAc;UAAAG,QAAA,eAC1B9B,OAAA,CAACvB,KAAK;YAACoE,OAAO,EAAE,CAAE;YAAAf,QAAA,gBACd9B,OAAA,CAACtB,SAAS;cACNoE,KAAK,EAAC,gBAAgB;cACtBC,OAAO,EAAC,UAAU;cAClBC,KAAK,EAAE3C,UAAW;cAClB4C,QAAQ,EAAGrB,KAAK,IAAK;gBACjBtB,aAAa,CAACsB,KAAK,CAACsB,MAAM,CAACF,KAAK,CAAC;cACrC,CAAE;cACFG,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACV,eACF3C,OAAA,CAACR,UAAU;cACP4D,SAAS;cACTC,IAAI,EAAC,OAAO;cACZpB,EAAE,EAAE;gBAAEqB,EAAE,EAAE;cAAE,CAAE;cACdP,OAAO,EAAC,WAAW;cACnBQ,IAAI,EAAC,QAAQ;cACbC,QAAQ,EAAEpC,MAAO;cAAAU,QAAA,EAEhBV,MAAM,gBAAGpB,OAAA,CAACxB,gBAAgB;gBAAC6E,IAAI,EAAE,EAAG;gBAACI,KAAK,EAAC;cAAS;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAG,GAAG;YAAQ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAC1D,eACb3C,OAAA,CAACzB,MAAM;cAACwE,OAAO,EAAC,UAAU;cAACW,OAAO,EAAEA,CAAA,KAAMlD,QAAQ,CAAC,CAAC,CAAC,CAAE;cAAAsB,QAAA,EAAC;YAExD;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAS;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACL;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACE,eAClB3C,OAAA,CAACP,KAAK;MAAC6B,OAAO,EAAEA,OAAQ;MAACG,YAAY,EAAEA,YAAa;MAACD,SAAS,EAAEA;IAAU;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;EAAA,gBAC9E;AAEX,CAAC;AAAAvC,EAAA,CAzFKD,QAAQ;EAAA,QAGOf,WAAW,EACXD,WAAW,EAEVE,WAAW;AAAA;AAAAsE,EAAA,GAN3BxD,QAAQ;AA2Fd,eAAeA,QAAQ;AAEvB,MAAM4B,eAAe,GAAGpC,MAAM,CAACrB,GAAG,CAAE;AACpC;AACA;AACA;AACA;AACA,CAAC;AAACsF,GAAA,GALI7B,eAAe;AAOrB,MAAMC,SAAS,GAAGrC,MAAM,CAACrB,GAAG,CAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuF,GAAA,GARI7B,SAAS;AAAA,IAAA2B,EAAA,EAAAC,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}