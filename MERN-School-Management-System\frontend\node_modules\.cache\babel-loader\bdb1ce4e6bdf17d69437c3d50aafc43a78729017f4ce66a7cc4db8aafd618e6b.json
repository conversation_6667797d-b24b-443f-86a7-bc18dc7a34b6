{"ast": null, "code": "import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attributes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, _ref, isSVGTag, transformTemplate, styleProp) {\n  let {\n    attrX,\n    attrY,\n    attrScale,\n    pathLength,\n    pathSpacing = 1,\n    pathOffset = 0,\n    // This is object creation, which we try to avoid per-frame.\n    ...latest\n  } = _ref;\n  buildHTMLStyles(state, latest, transformTemplate);\n  /**\n   * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n   * as normal HTML tags.\n   */\n  if (isSVGTag) {\n    if (state.style.viewBox) {\n      state.attrs.viewBox = state.style.viewBox;\n    }\n    return;\n  }\n  state.attrs = state.style;\n  state.style = {};\n  const {\n    attrs,\n    style\n  } = state;\n  /**\n   * However, we apply transforms as CSS transforms.\n   * So if we detect a transform, transformOrigin we take it from attrs and copy it into style.\n   */\n  if (attrs.transform) {\n    style.transform = attrs.transform;\n    delete attrs.transform;\n  }\n  if (style.transform || attrs.transformOrigin) {\n    style.transformOrigin = attrs.transformOrigin ?? \"50% 50%\";\n    delete attrs.transformOrigin;\n  }\n  if (style.transform) {\n    /**\n     * SVG's element transform-origin uses its own median as a reference.\n     * Therefore, transformBox becomes a fill-box\n     */\n    style.transformBox = styleProp?.transformBox ?? \"fill-box\";\n    delete attrs.transformBox;\n  }\n  // Render attrX/attrY/attrScale as attributes\n  if (attrX !== undefined) attrs.x = attrX;\n  if (attrY !== undefined) attrs.y = attrY;\n  if (attrScale !== undefined) attrs.scale = attrScale;\n  // Build SVG path if one has been defined\n  if (pathLength !== undefined) {\n    buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n  }\n}\nexport { buildSVGAttrs };", "map": {"version": 3, "names": ["buildHTMLStyles", "buildSVGPath", "buildSVGAttrs", "state", "_ref", "isSVGTag", "transformTemplate", "styleProp", "attrX", "attrY", "attrScale", "<PERSON><PERSON><PERSON><PERSON>", "pathSpacing", "pathOffset", "latest", "style", "viewBox", "attrs", "transform", "transform<PERSON><PERSON>in", "transformBox", "undefined", "x", "y", "scale"], "sources": ["A:/11/MERN-School-Management-System/frontend/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs"], "sourcesContent": ["import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attributes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, { attrX, attrY, attrScale, pathLength, pathSpacing = 1, pathOffset = 0, \n// This is object creation, which we try to avoid per-frame.\n...latest }, isSVGTag, transformTemplate, styleProp) {\n    buildHTMLStyles(state, latest, transformTemplate);\n    /**\n     * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n     * as normal HTML tags.\n     */\n    if (isSVGTag) {\n        if (state.style.viewBox) {\n            state.attrs.viewBox = state.style.viewBox;\n        }\n        return;\n    }\n    state.attrs = state.style;\n    state.style = {};\n    const { attrs, style } = state;\n    /**\n     * However, we apply transforms as CSS transforms.\n     * So if we detect a transform, transformOrigin we take it from attrs and copy it into style.\n     */\n    if (attrs.transform) {\n        style.transform = attrs.transform;\n        delete attrs.transform;\n    }\n    if (style.transform || attrs.transformOrigin) {\n        style.transformOrigin = attrs.transformOrigin ?? \"50% 50%\";\n        delete attrs.transformOrigin;\n    }\n    if (style.transform) {\n        /**\n         * SVG's element transform-origin uses its own median as a reference.\n         * Therefore, transformBox becomes a fill-box\n         */\n        style.transformBox = styleProp?.transformBox ?? \"fill-box\";\n        delete attrs.transformBox;\n    }\n    // Render attrX/attrY/attrScale as attributes\n    if (attrX !== undefined)\n        attrs.x = attrX;\n    if (attrY !== undefined)\n        attrs.y = attrY;\n    if (attrScale !== undefined)\n        attrs.scale = attrScale;\n    // Build SVG path if one has been defined\n    if (pathLength !== undefined) {\n        buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n    }\n}\n\nexport { buildSVGAttrs };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,mCAAmC;AACnE,SAASC,YAAY,QAAQ,YAAY;;AAEzC;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAAC,IAAA,EAEfC,QAAQ,EAAEC,iBAAiB,EAAEC,SAAS,EAAE;EAAA,IAFvB;IAAEC,KAAK;IAAEC,KAAK;IAAEC,SAAS;IAAEC,UAAU;IAAEC,WAAW,GAAG,CAAC;IAAEC,UAAU,GAAG,CAAC;IACpG;IACA,GAAGC;EAAO,CAAC,GAAAV,IAAA;EACPJ,eAAe,CAACG,KAAK,EAAEW,MAAM,EAAER,iBAAiB,CAAC;EACjD;AACJ;AACA;AACA;EACI,IAAID,QAAQ,EAAE;IACV,IAAIF,KAAK,CAACY,KAAK,CAACC,OAAO,EAAE;MACrBb,KAAK,CAACc,KAAK,CAACD,OAAO,GAAGb,KAAK,CAACY,KAAK,CAACC,OAAO;IAC7C;IACA;EACJ;EACAb,KAAK,CAACc,KAAK,GAAGd,KAAK,CAACY,KAAK;EACzBZ,KAAK,CAACY,KAAK,GAAG,CAAC,CAAC;EAChB,MAAM;IAAEE,KAAK;IAAEF;EAAM,CAAC,GAAGZ,KAAK;EAC9B;AACJ;AACA;AACA;EACI,IAAIc,KAAK,CAACC,SAAS,EAAE;IACjBH,KAAK,CAACG,SAAS,GAAGD,KAAK,CAACC,SAAS;IACjC,OAAOD,KAAK,CAACC,SAAS;EAC1B;EACA,IAAIH,KAAK,CAACG,SAAS,IAAID,KAAK,CAACE,eAAe,EAAE;IAC1CJ,KAAK,CAACI,eAAe,GAAGF,KAAK,CAACE,eAAe,IAAI,SAAS;IAC1D,OAAOF,KAAK,CAACE,eAAe;EAChC;EACA,IAAIJ,KAAK,CAACG,SAAS,EAAE;IACjB;AACR;AACA;AACA;IACQH,KAAK,CAACK,YAAY,GAAGb,SAAS,EAAEa,YAAY,IAAI,UAAU;IAC1D,OAAOH,KAAK,CAACG,YAAY;EAC7B;EACA;EACA,IAAIZ,KAAK,KAAKa,SAAS,EACnBJ,KAAK,CAACK,CAAC,GAAGd,KAAK;EACnB,IAAIC,KAAK,KAAKY,SAAS,EACnBJ,KAAK,CAACM,CAAC,GAAGd,KAAK;EACnB,IAAIC,SAAS,KAAKW,SAAS,EACvBJ,KAAK,CAACO,KAAK,GAAGd,SAAS;EAC3B;EACA,IAAIC,UAAU,KAAKU,SAAS,EAAE;IAC1BpB,YAAY,CAACgB,KAAK,EAAEN,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAE,KAAK,CAAC;EACnE;AACJ;AAEA,SAASX,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}